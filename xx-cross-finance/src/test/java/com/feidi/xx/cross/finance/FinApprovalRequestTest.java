package com.feidi.xx.cross.finance;

import com.feidi.xx.cross.finance.mapper.FinApprovalRequestDetailsMapper;
import com.feidi.xx.cross.finance.mapper.FinApprovalRequestsMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class FinApprovalRequestTest {

        @Autowired
    FinApprovalRequestDetailsMapper finApprovalRequestDetailsMapper;

    @Autowired
    FinApprovalRequestsMapper finApprovalRequestsMapper;

    @Test
    public void test() {
//        int i = finApprovalRequestsMapper.selectCountByOrderNoAndStatus("20250421142208222594180", 1, -1L);
//        System.out.println(i);
    }
}
