package com.feidi.xx.cross.finance.controller.admin;

import com.feidi.xx.common.mail.utils.MailAccount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RefreshScope
@RequestMapping("/test")
@RestController
public class TestController {

    @Autowired
    MailAccount mailAccount;

    @RequestMapping("/test")
    public String test() {
        System.out.println(mailAccount);
        return "test";
    }
}
