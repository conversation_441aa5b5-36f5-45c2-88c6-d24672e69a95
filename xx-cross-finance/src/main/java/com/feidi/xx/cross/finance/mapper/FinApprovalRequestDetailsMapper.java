package com.feidi.xx.cross.finance.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.finance.domain.FinApprovalRequestDetails;
import com.feidi.xx.cross.finance.domain.vo.FinApprovalRequestDetailsVo;

import java.util.Collections;
import java.util.List;

/**
 * 账单审批关联订单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface FinApprovalRequestDetailsMapper extends BaseMapperPlus<FinApprovalRequestDetails, FinApprovalRequestDetailsVo> {

    default List<FinApprovalRequestDetailsVo> queryByRequestId(Long requestId) {
        return selectVoList(Wrappers.<FinApprovalRequestDetails>lambdaQuery()
                .eq(FinApprovalRequestDetails::getApprovalRequestId, requestId)
                .orderByDesc(FinApprovalRequestDetails::getCreateTime));
    }

    default List<FinApprovalRequestDetailsVo> queryByRequestIds(List<Long> requestIds) {
        if (CollUtils.isEmpty(requestIds)) {
            return Collections.emptyList();
        }
        return selectVoList(Wrappers.<FinApprovalRequestDetails>lambdaQuery()
                .in(FinApprovalRequestDetails::getApprovalRequestId, requestIds)
                .orderByDesc(FinApprovalRequestDetails::getCreateTime));
    }

    default void deleteByRequestId(Long requestId) {
        boolean b = delete(Wrappers.<FinApprovalRequestDetails>lambdaQuery()
                .eq(FinApprovalRequestDetails::getApprovalRequestId, requestId)) > 0;
    }
}
