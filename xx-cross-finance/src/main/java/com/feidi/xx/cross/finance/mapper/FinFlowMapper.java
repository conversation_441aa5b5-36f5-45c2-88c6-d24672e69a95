package com.feidi.xx.cross.finance.mapper;

import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.enums.DirectionEnum;
import com.feidi.xx.common.core.enums.JoinEnum;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.finance.domain.FinFlow;
import com.feidi.xx.cross.finance.domain.vo.FinFlowVo;

import java.util.Arrays;
import java.util.List;

/**
 * 资金流水Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
public interface FinFlowMapper extends BaseMapperPlus<FinFlow, FinFlowVo> {

    /**
     * 获取司机最新一条流水
     * @param driverId
     * @return
     */
    default FinFlow getLastFlow(Long driverId) {
        LambdaQueryWrapper<FinFlow> lqw = Wrappers.lambdaQuery();
        lqw.eq(FinFlow::getDriverId, driverId);
        lqw.orderByDesc(FinFlow::getCreateTime);
        lqw.orderByDesc(FinFlow::getId);
        lqw.last("limit 1");
        return selectOne(lqw);
    }

    /**
     * 根据关联关系查找流水，可能多条，但是反向是相反的
     * @param page
     * @param driverId
     * @param joinTable
     * @return
     */
    default IPage<FinFlow> listByJoin(IPage<FinFlow> page, Long driverId, JoinEnum... joinTable) {
        LambdaQueryWrapper<FinFlow> lqw = Wrappers.lambdaQuery();
        lqw.eq(driverId != null, FinFlow::getDriverId, driverId);
        if (joinTable != null) {
            lqw.in(FinFlow::getJoinTable, Arrays.stream(joinTable).map(JoinEnum::getCode).toList());
        }
        lqw.orderByDesc(FinFlow::getCreateTime);
        return selectPage(page, lqw);
    }

    /**
     * 根据关联关系查找流水，可能多条，但是反向是相反的
     * @param page
     * @param driverId
     * @param joinTable
     * @return
     */
    default IPage<FinFlow> listByJoinExceptCash(IPage<FinFlow> page, Long driverId, JoinEnum... joinTable) {
        LambdaQueryWrapper<FinFlow> lqw = Wrappers.lambdaQuery();
        lqw.eq(driverId != null, FinFlow::getDriverId, driverId);
        lqw.ne(FinFlow::getAmount, 0);
        if (joinTable != null) {
            lqw.in(FinFlow::getJoinTable, Arrays.stream(joinTable).map(JoinEnum::getCode).toList());
        }
        lqw.orderByDesc(FinFlow::getCreateTime);
        return selectPage(page, lqw);
    }

    /**
     * 根据关联关系查找流水，可能多条，但是反向是相反的
     * @param driverId
     * @param joinId
     * @param joinTable
     * @return
     */
    default List<FinFlow> listByJoin(Long driverId, Long joinId, JoinEnum... joinTable) {
        LambdaQueryWrapper<FinFlow> lqw = Wrappers.lambdaQuery();
        lqw.eq(driverId != null, FinFlow::getDriverId, driverId);
        lqw.eq(joinId != null, FinFlow::getJoinId, joinId);
        if (joinTable != null) {
            lqw.in(FinFlow::getJoinTable, Arrays.stream(joinTable).map(JoinEnum::getCode).toList());
        }
        lqw.orderByDesc(FinFlow::getCreateTime);
        return selectList(lqw);
    }

    /**
     * 只能是有一条记录是这样的
     * @param driverId
     * @param joinId
     * @param joinTable
     * @param direction
     * @param type 流水类型可以有多个，但是查出来的结果必须是一个
     * @return
     */
    default FinFlow getByJoin(Long driverId, Long joinId, String joinTable, DirectionEnum direction, String... type) {
        LambdaQueryWrapper<FinFlow> lqw = Wrappers.lambdaQuery();
        lqw.eq(FinFlow::getDriverId, driverId);
        lqw.eq(FinFlow::getJoinId, joinId);
        lqw.eq(FinFlow::getJoinTable, joinTable);
        lqw.eq(FinFlow::getDirection, direction.getCode());
        lqw.in(ArrayUtil.isNotEmpty(type), FinFlow::getType, Arrays.asList(type));
        return selectOne(lqw);
    }

    /**
     * 根据司机id和joinId查询订单流水
     *
     * @param driverId 司机id
     * @param joinId 关联id
     * @param joinTable 关联表
     * @param direction 流水方向
     *
     * @return 订单流水
     */
    default List<FinFlow> queryByDriverIdAndJoinId(Long driverId, Long joinId, String joinTable, String direction) {
        LambdaQueryWrapper<FinFlow> lqw = Wrappers.lambdaQuery();
        lqw.eq(FinFlow::getDriverId, driverId);
        lqw.eq(FinFlow::getJoinId, joinId);
        lqw.eq(FinFlow::getJoinTable, joinTable);
        lqw.eq(FinFlow::getDirection, direction);
        return selectList(lqw);
    }

    /**
     * 根据司机id和joinId查询订单流水
     *
     * @param driverId 司机id
     * @param joinId 关联id
     * @param joinTable 关联表
     * @param direction 流水方向
     *
     * @return 订单流水
     */
    default FinFlow queryByDriverIdAndJoinIdWithType(Long driverId, Long joinId, String joinTable, String direction, String type) {
        LambdaQueryWrapper<FinFlow> lqw = Wrappers.lambdaQuery();
        lqw.eq(FinFlow::getDriverId, driverId);
        lqw.eq(FinFlow::getJoinId, joinId);
        lqw.eq(FinFlow::getJoinTable, joinTable);
        lqw.eq(FinFlow::getDirection, direction);
        lqw.eq(FinFlow::getType, type);
        return selectOne(lqw);
    }
}
