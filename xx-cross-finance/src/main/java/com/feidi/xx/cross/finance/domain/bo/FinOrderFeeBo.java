package com.feidi.xx.cross.finance.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.finance.domain.FinOrderFee;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单应收服务费业务对象 fin_order_fee
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinOrderFee.class, reverseConvertGenerate = false)
public class FinOrderFeeBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 账单周期(精确到日)
     */
    @NotNull(message = "账单周期(精确到日)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date financeDate;

    /**
     * 代理商id
     */
    @NotNull(message = "代理商id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long agentId;

    /**
     * 代理商名称
     */
    @NotBlank(message = "代理商名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String agentName;

    /**
     * 司机id
     */
    @NotNull(message = "司机id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long driverId;

    /**
     * 司机姓名
     */
    @NotBlank(message = "司机姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String driverName;

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderNo;

    /**
     * 订单渠道
     */
    @NotBlank(message = "订单渠道不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platformCode;

    /**
     * 订单金额
     */
    @NotBlank(message = "订单金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderPrice;

    /**
     * 实际支付金额
     */
    @NotBlank(message = "实际支付金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long payPrice;

    /**
     * 账单对账类型[FinanceTypeEnum]
     */
    @NotBlank(message = "账单对账类型[FinanceTypeEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String financeType;

    /**
     * 下单类型[CreateModelEnum]
     */
    @NotBlank(message = "下单类型[CreateModelEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String createModel;

    /**
     * 代收款
     */
    @NotNull(message = "代收款不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long proxyAmount;

    /**
     * 奖励金额
     */
    @NotNull(message = "奖励金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long rewardAmount;

    /**
     * 客诉金额
     */
    @NotNull(message = "客诉金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long complainAmount;

    /**
     * 技术服务比例
     */
    @NotNull(message = "技术服务比例不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal technicalServiceRate;

    /**
     * 技术服务费
     */
    @NotNull(message = "技术服务费不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long technicalServiceFee;

    /**
     * 应收信息服务费比例
     */
    @NotNull(message = "应收信息服务费比例不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal infoServiceRate;

    /**
     * 应收信息服务费
     */
    @NotNull(message = "应收信息服务费不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long infoServiceFee;

    /**
     * 备注
     */
    private String remark;

    /**
     * id集合
     */
    private List<Long> ids;

    /**
     * 账单开始日期
     */
    private String financeStartTime;

    /**
     * 账单结束日期
     */
    private String financeEndTime;
}
