package com.feidi.xx.cross.finance.mapper;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.common.enums.finance.CashAuditStatusEnum;
import com.feidi.xx.cross.finance.domain.FinCash;
import com.feidi.xx.cross.finance.domain.bo.FinCashQueryBo;
import com.feidi.xx.cross.finance.domain.vo.FinCashListVo;
import com.feidi.xx.cross.finance.domain.vo.FinCashVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 提现Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
public interface FinCashMapper extends BaseMapperPlus<FinCash, FinCashVo> {

    /**
     * 获取最新的提现记录
     */
    default FinCash getLast(Long driverId) {
        return selectOne(Wrappers.<FinCash>lambdaQuery()
                .eq(FinCash::getDriverId, driverId)
                .orderByDesc(FinCash::getCreateTime)
                .last("limit 1"));
    }

    default List<FinCash> listByDriverId(Long driverId) {
        return selectList(Wrappers.<FinCash>lambdaQuery()
                .eq(FinCash::getDriverId, driverId)
                .orderByDesc(FinCash::getCreateTime));
    }

    default IPage<FinCash> pageByDriverId(Long driverId, IPage<FinCash> page) {
        return selectPage(page, Wrappers.<FinCash>lambdaQuery()
                .eq(FinCash::getDriverId, driverId)
                .orderByDesc(FinCash::getCreateTime)
        );
    }

    /**
     * 获取提现记录
     */
    default List<FinCash> listByStatus(Long driverId, CashAuditStatusEnum status) {
        return selectList(Wrappers.<FinCash>lambdaQuery()
                .eq(FinCash::getDriverId, driverId)
                .eq(FinCash::getStatus, status.getCode()));
    }

    /**
     * 是否存在提现记录
     */
    default boolean existByStatus(Long driverId, CashAuditStatusEnum status) {
        return exists(Wrappers.<FinCash>lambdaQuery()
                .eq(FinCash::getDriverId, driverId)
                .eq(FinCash::getStatus, status.getCode()));
    }

    /**
     * 获取提现记录
     */
    default List<FinCash> listToday(Long driverId) {
        return selectList(Wrappers.<FinCash>lambdaQuery()
                .eq(FinCash::getDriverId, driverId)
                .ge(FinCash::getCreateTime, DateUtil.beginOfDay(DateUtil.date()))
                .le(FinCash::getCreateTime, DateUtil.endOfDay(DateUtil.date()))
                .ne(FinCash::getStatus, CashAuditStatusEnum.REJECT.getCode())
        );
    }

    /**
     * 获取提现记录
     */
    default List<FinCash> listByTime(Long driverId, Date startTime, Date endTime) {
        return selectList(Wrappers.<FinCash>lambdaQuery()
                .eq(FinCash::getDriverId, driverId)
                .ge(FinCash::getCreateTime, startTime)
                .le(FinCash::getCreateTime, endTime)
        );
    }

    IPage<FinCashListVo> listByQuery(@Param("bo") FinCashQueryBo bo, IPage<FinCash> iPage);

    List<FinCashListVo> listByQuery(@Param("bo") FinCashQueryBo bo);

}
