package com.feidi.xx.cross.finance.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.finance.domain.FinPaymentRecord;
import com.feidi.xx.cross.finance.domain.bo.FinPaymentRecordBo;
import com.feidi.xx.cross.finance.domain.vo.FinPaymentRecordVo;
import com.feidi.xx.cross.finance.mapper.FinPaymentRecordMapper;
import com.feidi.xx.cross.finance.service.IFinPaymentRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 支付记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinPaymentRecordServiceImpl implements IFinPaymentRecordService {

    private final FinPaymentRecordMapper baseMapper;

    /**
     * 查询支付记录
     *
     * @param id 主键
     * @return 支付记录
     */
    @Override
    public FinPaymentRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询支付记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 支付记录分页列表
     */
    @Override
    public TableDataInfo<FinPaymentRecordVo> queryPageList(FinPaymentRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinPaymentRecord> lqw = buildQueryWrapper(bo);
        Page<FinPaymentRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的支付记录列表
     *
     * @param bo 查询条件
     * @return 支付记录列表
     */
    @Override
    public List<FinPaymentRecordVo> queryList(FinPaymentRecordBo bo) {
        LambdaQueryWrapper<FinPaymentRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinPaymentRecord> buildQueryWrapper(FinPaymentRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinPaymentRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentType()), FinPaymentRecord::getPaymentType, bo.getPaymentType());
        lqw.eq(StringUtils.isNotBlank(bo.getAppId()), FinPaymentRecord::getAppId, bo.getAppId());
        lqw.eq(StringUtils.isNotBlank(bo.getOutBizNo()), FinPaymentRecord::getOutBizNo, bo.getOutBizNo());
        lqw.eq(StringUtils.isNotBlank(bo.getFlowNo()), FinPaymentRecord::getFlowNo, bo.getFlowNo());
        lqw.eq(StringUtils.isNotBlank(bo.getTradeTime()), FinPaymentRecord::getTradeTime, bo.getTradeTime());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), FinPaymentRecord::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getDirection()), FinPaymentRecord::getDirection, bo.getDirection());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinPaymentRecord::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getIsRefund()), FinPaymentRecord::getIsRefund, bo.getIsRefund());
        return lqw;
    }

    /**
     * 新增支付记录
     *
     * @param bo 支付记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinPaymentRecordBo bo) {
        FinPaymentRecord add = MapstructUtils.convert(bo, FinPaymentRecord.class);
        validEntityBeforeSave(add);
        boolean flag;
        if (ObjectUtil.isNull(add.getId())) {
            flag = baseMapper.insert(add) > 0;
            bo.setId(add.getId());
        } else {
            flag = baseMapper.updateById(add) > 0;
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinPaymentRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

}
