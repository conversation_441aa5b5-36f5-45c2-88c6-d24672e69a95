package com.feidi.xx.cross.finance.mq;

import com.feidi.xx.common.rocketmq.base.BaseSendExtendDTO;
import com.feidi.xx.common.rocketmq.enums.MqMessageTypeEnum;
import com.feidi.xx.common.rocketmq.util.MQMessageUtil;
import com.feidi.xx.cross.common.constant.finance.FinanceMQConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;

/**
 * 订单取消消息消费者
 */
@Slf4j
@RequiredArgsConstructor
public class AutoPaymentProducer {

    private static BaseSendExtendDTO buildBaseSendExtendParam(AutoPaymentEvent event) {
        return BaseSendExtendDTO.builder()
                .eventName("自动打款")
                .keys(event.getCashId() + "gggg")
                .messageType(MqMessageTypeEnum.SYNC)
                .topic(FinanceMQConstants.FINANCE_AUTO_PAYMENT_TOPIC)
                .sentTimeout(2000L)
                .build();
    }

    public static SendResult sendMessage(AutoPaymentEvent event) {
        return MQMessageUtil.sendMessage(event, AutoPaymentProducer::buildBaseSendExtendParam);
    }

}

