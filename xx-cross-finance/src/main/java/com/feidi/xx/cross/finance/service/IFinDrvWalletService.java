package com.feidi.xx.cross.finance.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.finance.domain.FinDrvWallet;
import com.feidi.xx.cross.finance.domain.bo.FinDrvWalletBo;
import com.feidi.xx.cross.finance.domain.bo.FinWalletQueryBo;
import com.feidi.xx.cross.finance.domain.vo.FinDrvWalletVo;
import com.feidi.xx.cross.finance.domain.vo.FinWalletListVo;

import java.util.List;

/**
 * 司机钱包Service接口
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
public interface IFinDrvWalletService {

    /**
     * 获取司机钱包
     *
     * @param driverId
     * @return
     */
    FinDrvWalletVo queryByDriverId(Long driverId);

    /**
     * 查询司机钱包
     *
     * @param id 主键
     * @return 司机钱包
     */
    FinDrvWalletVo queryById(Long id);

    /**
     * 分页查询司机钱包列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机钱包分页列表
     */
    TableDataInfo<FinWalletListVo> queryPageList(FinWalletQueryBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的司机钱包列表
     *
     * @param bo 查询条件
     * @return 司机钱包列表
     */
    List<FinWalletListVo> queryList(FinWalletQueryBo bo);

    /**
     * 修改司机钱包
     *
     * @param bo 司机钱包
     * @return 是否修改成功
     */
    Boolean updateByBo(FinDrvWalletBo bo);

    /**
     * 禁用/启用司机钱包
     * @param  driverId 司机id
     * @param status 钱包状态
     * @return 是否禁用/启用成功
     */
    Boolean disableWallet(Long driverId, String status);

    /**
     * 根据司机id查询司机钱包
     *
     * @param driverIds 司机id
     * @return 司机钱包
     */
    List<FinDrvWallet> queryByDriverIds(List<Long> driverIds);
}

