package com.feidi.xx.cross.finance.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.finance.domain.FinApprovalLog;
import com.feidi.xx.cross.finance.domain.vo.FinApprovalLogVo;

import java.util.Collections;
import java.util.List;

/**
 * 账单审批操作记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface FinApprovalLogMapper extends BaseMapperPlus<FinApprovalLog, FinApprovalLogVo> {

    default List<FinApprovalLogVo> listByRequestId(Long requestId) {
        return selectVoList(Wrappers.<FinApprovalLog>lambdaQuery()
                .eq(FinApprovalLog::getApprovalId, requestId)
                .orderByDesc(FinApprovalLog::getOperateTime)
        );
    }

    default List<FinApprovalLogVo> listByRequestIds(List<Long> requestIds) {
        if (CollUtils.isEmpty(requestIds)) {
            return Collections.emptyList();
        }
        return selectVoList(Wrappers.<FinApprovalLog>lambdaQuery()
                .in(FinApprovalLog::getApprovalId, requestIds)
                .orderByDesc(FinApprovalLog::getOperateTime)
        );
    }
}
