package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.feidi.xx.cross.common.enums.finance.ApprTransactionTypeEnum;
import com.feidi.xx.cross.finance.domain.FinApprovalRequestDetails;
import com.feidi.xx.cross.finance.domain.FinApprovalRequestRelateOrder;
import com.feidi.xx.cross.finance.domain.FinDrvWallet;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;


/**
 * 账单审批关联订单视图对象 fin_approval_request_details
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinApprovalRequestDetails.class)
public class FinApprovalRequestDetailsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 账单审批记录ID
     */
    private Long approvalRequestId;
    /**
     * 司机id
     */
    @ExcelProperty(value = "司机id")
    private Long driverId;
    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 司机姓名
     */
    private String driverName;
    /**
     * 关联订单
     */
    @ExcelProperty(value = "关联订单")
    private List<FinApprovalRequestRelateOrder> relateOrders;

    /**
     * ApprTransactionTypeEnum 交易类型，1:人工调账
     */
    private Integer transactionType;

    @JsonProperty("transactionTypeText")
    public String getTransactionTypeText() {
        return Objects.requireNonNull(ApprTransactionTypeEnum.getInfoByCode(this.transactionType)).getInfo();
    }

    /**
     * 账户余额(冻结金额+可提现金额)
     */
    @ExcelProperty(value = "账户余额(冻结金额+可提现金额)")
    private Long accountAmount;

    /**
     * 可提现金额
     */
    @ExcelProperty(value = "可提现金额")
    private Long balance;

    /**
     * 冻结金额
     */
    @ExcelProperty(value = "冻结金额")
    private Long freeze;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 租户编号
     */
    private String tenantId;

    public static FinApprovalRequestDetailsVo from(RemoteDriverVo driverInfo, FinDrvWallet finDrvWallet) {
        var far = new FinApprovalRequestDetailsVo();

        far.setDriverId(driverInfo.getId());
        far.setDriverPhone(driverInfo.getPhone());
        far.setDriverName(driverInfo.getName());
        far.setAgentId(driverInfo.getAgentId());

        far.setBalance(finDrvWallet.getBalance());
        far.setFreeze(finDrvWallet.getFreeze());
        far.setAccountAmount(finDrvWallet.getAccountAmount());
        return far;
    }
}
