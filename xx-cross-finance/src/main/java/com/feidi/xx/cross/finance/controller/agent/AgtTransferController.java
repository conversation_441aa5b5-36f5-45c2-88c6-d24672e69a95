package com.feidi.xx.cross.finance.controller.agent;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.domain.bo.FinAccountBo;
import com.feidi.xx.cross.finance.domain.bo.FinTransferQueryBo;
import com.feidi.xx.cross.finance.domain.vo.*;
import com.feidi.xx.cross.finance.service.IFinTransferService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 代理商 - 转账
 * 前端访问路由地址为:/finance/transfer
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Validated
@RestController
@RequiredArgsConstructor
@SaCheckRole(UserTypeEnum.UserType.AGENT_USER)
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/transfer")
public class AgtTransferController extends BaseController {

    private final IFinTransferService FinTransferService;

    /**
     * 查询转账列表
     */
    @GetMapping("/list")
    public TableDataInfo<FinTransferListVo> list(FinTransferQueryBo bo, PageQuery pageQuery) {
        Long agentId = LoginHelper.getAgentId();
        if (bo.getAgentId() != null) {
            bo.setAgentIds(CollUtil.newArrayList(bo.getAgentId()));
        } else {
            bo.setAgentId(agentId);
        }
        return FinTransferService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出转账列表
     */
    @Log(title = "转账", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public Object export(@RequestBody FinTransferQueryBo bo, HttpServletResponse response) {
        Long agentId = LoginHelper.getAgentId();
        if (bo.getAgentId() != null) {
            bo.setAgentIds(CollUtil.newArrayList(bo.getAgentId()));
        } else {
            bo.setAgentId(agentId);
        }
        List<FinTransferListVo> list = FinTransferService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        List<FinTransferExportVo> convert = MapstructUtils.convert(list, FinTransferExportVo.class);
        ExcelUtil.exportExcel(convert, "转账", FinTransferExportVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取转账详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<FinTransferVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(FinTransferService.queryById(id));
    }

}
