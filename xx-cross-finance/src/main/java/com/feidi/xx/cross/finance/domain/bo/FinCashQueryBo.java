package com.feidi.xx.cross.finance.domain.bo;

import com.feidi.xx.common.core.domain.StartEndTime;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 提现业务对象 Fin_cash
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FinCashQueryBo extends PageQuery {

    /**
     * 主键
     */
    private String unionId;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 代理商id
     */
    private List<Long> agentIds;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 审核状态
     */
    private String status;

    /**
     * 打款状态 {@link com.feidi.xx.common.core.enums.CashTransStatusEnum}
     */
    private String transStatus;

    /**
     * 打款方式
     */
    private String isAuto;

    /**
     * 时间搜索
     */

    /**
     * 申请时间
     */
    private StartEndTime create;

    /**
     * 处理时间
     */
    private StartEndTime review;

    /**
     * 用户类型
     */
    private String userType;

}
