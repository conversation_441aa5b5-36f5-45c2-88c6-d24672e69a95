package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.cross.common.enums.finance.AccountTypeEnum;
import com.feidi.xx.cross.common.enums.finance.CashAuditStatusEnum;
import com.feidi.xx.cross.common.enums.finance.CashTransStatusEnum;
import com.feidi.xx.cross.finance.domain.FinCash;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 提现视图对象 Fin_cash
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinCash.class, convertGenerate = false)
public class FinCashListVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 提现单号
     */
    @ExcelProperty(value = "提现单号")
    private String cashNo;

    /**
     * 代理商id
     */
    @ExcelProperty(value = "代理商id")
    private Long agentId;
    private String agentName;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机id")
    private Long driverId;
    private String driverName;
    private String driverPhone;

    /**
     * 账户姓名
     */
    private String name;

    /**
     * 账户手机号
     */
    private String phone;

    /**
     * 账号
     */
    private String account;

    /**
     * 类型
     */
    @Enum2Text(enumClass = AccountTypeEnum.class)
    @ExcelProperty(value = "类型", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = AccountTypeEnum.class)
    private String accountType;

    /**
     * 提现金额
     */
    @ExcelProperty(value = "提现金额")
    private Long amount;

    /**
     * 是否自动
     */
    @Enum2Text(enumClass = IsYesEnum.class)
    @ExcelProperty(value = "是否自动", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = IsYesEnum.class)
    private String isAuto;

    /**
     * 状态
     */
    @Enum2Text(enumClass = CashAuditStatusEnum.class)
    @ExcelProperty(value = "状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = CashAuditStatusEnum.class)
    private String status;

    /**
     * 处理人
     */
    @ExcelProperty(value = "处理人")
    private String userName;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 审批时间
     */
    @ExcelProperty(value = "审批时间")
    private Date reviewTime;

    /**
     * 打款流水号
     */
    @ExcelProperty(value = "打款流水号")
    private String flowNo;

    /**
     * 打款时间
     */
    @ExcelProperty(value = "打款时间")
    private Date transDate;

    /**
     * 打款状态 {@link CashTransStatusEnum}
     */
    @Enum2Text(enumClass = CashTransStatusEnum.class)
    @ExcelProperty(value = "打款状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = CashTransStatusEnum.class)
    private String transStatus;

    /**
     * 打款信息
     */
    @ExcelProperty(value = "打款信息")
    private String transInfo;

    /**
     * 司机组类型
     */
    private String groupTypeText;

    /**
     * 司机类型
     */
    private String driverTypeText;

}
