package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 司机钱包流水视图对象 fin_drv_wallet_flow
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinDrvWalletFlowVo.class, convertGenerate = false)
public class FinDrvWalletFlowExportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机ID")
    private Long driverId;

    /**
     * 司机名称
     */
    @ExcelProperty(value = "司机姓名")
    private String driverName;

    /**
     * 代理商名称
     */
    @ExcelProperty(value = "所属代理商")
    private String agentName;

    /**
     * 关联单号
     */
    @ExcelProperty(value = "关联单号")
    private String orderNo;

    /**
     * 佣金收益
     */
    @ExcelProperty(value = "佣金收益")
    @ReverseAutoMapping(target = "orderProfit", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getOrderProfit()))")
    private String orderProfit;

    /**
     * 奖励收益
     */
    @ExcelProperty(value = "奖励收益")
    @ReverseAutoMapping(target = "rewardProfit", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getRewardProfit()))")
    private String rewardProfit;

    /**
     * 订单转卖收益
     */
    @ExcelProperty(value = "订单转卖")
    @ReverseAutoMapping(target = "resellProfit", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getResellProfit()))")
    private String resellProfit;

    /**
     * 累计提现收益
     */
    @ExcelProperty(value = "累计提现收益")
    @ReverseAutoMapping(target = "expend", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getExpend()))")
    private String expend;

    /**
     * 账户余额(冻结金额+可提现金额)
     */
    @ExcelProperty(value = "账户余额")
    @ReverseAutoMapping(target = "accountAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getAccountAmount()))")
    private String accountAmount;

    /**
     * 冻结金额
     */
    @ExcelProperty(value = "冻结金额")
    @ReverseAutoMapping(target = "freeze", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getFreeze()))")
    private String freeze;

    /**
     * 可提现金额
     */
    @ExcelProperty(value = "可提现金额")
    @ReverseAutoMapping(target = "balance", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getBalance()))")
    private String balance;

    /**
     * 提现中金额
     */
    @ExcelProperty(value = "提现中金额")
    @ReverseAutoMapping(target = "withdrawalAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getWithdrawalAmount()))")
    private String withdrawalAmount;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
