package com.feidi.xx.cross.finance.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.finance.domain.bo.FinFlowDrvBo;
import com.feidi.xx.cross.finance.domain.vo.driver.FinCashFlowDrvVo;
import com.feidi.xx.cross.finance.domain.vo.driver.FinFlowIncomeOrderVo;
import com.feidi.xx.cross.finance.domain.vo.driver.FinTransferFlowDrvVo;

/**
 * 资金流水司机Service接口
 */
public interface IFinFlowDrvService {

    /**
     * 收入
     */
    TableDataInfo<FinFlowIncomeOrderVo> income(FinFlowDrvBo bo, PageQuery pageQuery);

    /**
     * 提现
     */
    TableDataInfo<FinCashFlowDrvVo> cash(FinFlowDrvBo bo, PageQuery pageQuery);

    /**
     * 转账
     */
    TableDataInfo<FinTransferFlowDrvVo> transfer(FinFlowDrvBo bo, PageQuery pageQuery);

}
