package com.feidi.xx.cross.finance.controller.driver;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.common.enums.finance.TransferTypeEnum;
import com.feidi.xx.cross.finance.domain.bo.FinTransBo;
import com.feidi.xx.cross.finance.domain.vo.FinTransferAccountVo;
import com.feidi.xx.cross.finance.domain.vo.FinTransferVo;
import com.feidi.xx.cross.finance.service.IFinTransferService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 司机 - 转账
 * 前端访问路由地址为:/finance/transfer
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@SaCheckRole(UserTypeEnum.UserType.DRIVER_USER)
@RequestMapping(WebConstants.DRIVER_ROUTE_PREFIX + "/transfer")
public class DrvTransferController extends BaseController {

    private final IFinTransferService FinTransferService;

    /**
     * 最近转账司机列表
     */
    @GetMapping("/recent")
    public R<List<FinTransferAccountVo>> recent() {
        return R.ok(FinTransferService.recent());
    }
    
    /**
     * 获取转账详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<FinTransferVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(FinTransferService.queryById(id));
    }

    /**
     * 新增转账
     */
    @Log(title = "转账", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinTransBo bo) {
        bo.setType(TransferTypeEnum.REPLACE_CASH.getCode());
        bo.setOutDriverId(LoginHelper.getUserId());
        return toAjax(FinTransferService.insertByBo(bo));
    }
}
