package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.cross.common.enums.finance.FinanceTypeEnum;
import com.feidi.xx.cross.common.enums.order.CreateModelEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 订单应收服务费视图对象 fin_order_fee
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinOrderFeeVo.class, convertGenerate = false)
public class FinOrderFeeExportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "代理商名称")
    private String agentName;

    @ExcelProperty(value = "平台", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = PlatformCodeEnum.class)
    private String platformCode;

    @ExcelProperty(value = "订单编号")
    private String orderNo;

    @ExcelProperty(value = "司机id")
    private Long driverId;

    @ExcelProperty(value = "司机姓名")
    private String driverName;

    @ExcelProperty(value = "订单类型", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = FinanceTypeEnum.class)
    private String financeType;

    @ExcelProperty(value = "下单类型", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = CreateModelEnum.class)
    private String createModel;

    @ExcelProperty(value = "订单金额")
    @ReverseAutoMapping(target = "orderPrice", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getOrderPrice()))")
    private String orderPrice;

    @ExcelProperty(value = "实付金额")
    @ReverseAutoMapping(target = "payPrice", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getPayPrice()))")
    private String payPrice;

    @ExcelProperty(value = "代收款")
    @ReverseAutoMapping(target = "proxyAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getProxyAmount()))")
    private String proxyAmount;

    @ExcelProperty(value = "奖励金额")
    @ReverseAutoMapping(target = "rewardAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getRewardAmount()))")
    private String rewardAmount;

    @ExcelProperty(value = "客诉金额")
    @ReverseAutoMapping(target = "complainAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getComplainAmount()))")
    private String complainAmount;

    @ExcelProperty(value = "信息服务费比例")
    @ReverseAutoMapping(target = "infoServiceRate", expression = "java(source.getInfoServiceRate() + \"%\")")
    private String infoServiceRate;

    @ExcelProperty(value = "信息服务费金额")
    @ReverseAutoMapping(target = "infoServiceFee", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getInfoServiceFee()))")
    private String infoServiceFee;

    @ExcelProperty(value = "技术服务费比例")
    @ReverseAutoMapping(target = "technicalServiceRate", expression = "java(source.getTechnicalServiceRate() + \"%\")")
    private String technicalServiceRate;

    @ExcelProperty(value = "技术服务费金额")
    @ReverseAutoMapping(target = "technicalServiceFee", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getTechnicalServiceFee()))")
    private String technicalServiceFee;

    @ExcelProperty(value = "创建时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
