package com.feidi.xx.cross.finance.domain.vo.driver;

import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.sensitive.annotation.Sensitive;
import com.feidi.xx.common.sensitive.core.SensitiveStrategy;
import com.feidi.xx.cross.common.enums.finance.AccountTypeEnum;
import com.feidi.xx.cross.finance.domain.FinCash;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * 提现视图对象 Fin_cash
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinCash.class)
public class FinCashFlowDrvVo extends FinFlowDrvBase {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 账户姓名
     */
    private String name;

    /**
     * 账号
     */
    @Sensitive(strategy = SensitiveStrategy.PHONE)
    private String account;

    /**
     * 类型
     */
    @Enum2Text(enumClass = AccountTypeEnum.class)
    @ExcelEnumFormat(enumClass = AccountTypeEnum.class)
    private String accountType;

    /**
     * 提现金额
     */
    private Long amount;

}
