package com.feidi.xx.cross.finance.mapper;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.finance.domain.FinPaymentRecord;
import com.feidi.xx.cross.finance.domain.vo.FinPaymentRecordVo;

import java.util.List;

/**
 * 支付记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
public interface FinPaymentRecordMapper extends BaseMapperPlus<FinPaymentRecord, FinPaymentRecordVo> {

    default FinPaymentRecord getPaymentRecord(String tenantId, long id, String joinTable, String paymentType) {
        return selectOne(Wrappers.<FinPaymentRecord>lambdaQuery()
                .eq(FinPaymentRecord::getTenantId, tenantId)
                .eq(ObjectUtil.isNotNull(id), FinPaymentRecord::getJoinId, id)
                .eq(StrUtil.isNotBlank(joinTable), FinPaymentRecord::getJoinTable, joinTable)
                .eq(StrUtil.isNotBlank(paymentType), FinPaymentRecord::getPaymentType, paymentType)
                .isNull(FinPaymentRecord::getNotify)
                .orderByDesc(FinPaymentRecord::getCreateTime)
                .last("LIMIT 1")
        );
    }

    // FIXME: 2024/12/26 测试一下重复提现会报错吗，跟这个方法调用相关的
    default List<FinPaymentRecord> getPaymentRecord(String tenantId, long id, String joinTable, String paymentType, String status) {
        return selectList(Wrappers.<FinPaymentRecord>lambdaQuery()
                .eq(FinPaymentRecord::getTenantId, tenantId)
                .eq(ObjectUtil.isNotNull(id), FinPaymentRecord::getJoinId, id)
                .eq(StrUtil.isNotBlank(joinTable), FinPaymentRecord::getJoinTable, joinTable)
                .eq(StrUtil.isNotBlank(paymentType), FinPaymentRecord::getPaymentType, paymentType)
                .eq(StrUtil.isNotBlank(status), FinPaymentRecord::getStatus, status)
                .isNull(FinPaymentRecord::getNotify)
                .orderByDesc(FinPaymentRecord::getCreateTime)
        );
    }

    default FinPaymentRecord getPaymentRecord(String tenantId, String appId, String outBizNo, String paymentType, String status) {
        return selectOne(Wrappers.<FinPaymentRecord>lambdaQuery()
                .eq(FinPaymentRecord::getTenantId, tenantId)
                .eq(StrUtil.isNotBlank(appId), FinPaymentRecord::getAppId, appId)
                .eq(StrUtil.isNotBlank(outBizNo), FinPaymentRecord::getOutBizNo, outBizNo)
                .eq(StrUtil.isNotBlank(paymentType), FinPaymentRecord::getPaymentType, paymentType)
                .eq(StrUtil.isNotBlank(status), FinPaymentRecord::getStatus, status)
                .isNull(FinPaymentRecord::getNotify)
                .orderByDesc(FinPaymentRecord::getCreateTime)
                .last("LIMIT 1")
        );
    }

    default List<FinPaymentRecord> listByJoinIds(String joinTable, List<Long> joinIds) {
        return selectList(Wrappers.<FinPaymentRecord>lambdaQuery()
                .eq(FinPaymentRecord::getJoinTable, joinTable)
                .in(FinPaymentRecord::getJoinId, joinIds)
        );
    }

}
