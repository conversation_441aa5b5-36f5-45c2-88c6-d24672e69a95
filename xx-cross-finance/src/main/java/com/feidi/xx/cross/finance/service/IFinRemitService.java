package com.feidi.xx.cross.finance.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.finance.domain.bo.FinRemitBo;
import com.feidi.xx.cross.finance.domain.vo.FinRemitVo;

import java.util.Collection;
import java.util.List;

/**
 * 打款记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface IFinRemitService {

    /**
     * 查询打款记录
     *
     * @param id 主键
     * @return 打款记录
     */
    FinRemitVo queryById(Long id);

    /**
     * 分页查询打款记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 打款记录分页列表
     */
    TableDataInfo<FinRemitVo> queryPageList(FinRemitBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的打款记录列表
     *
     * @param bo 查询条件
     * @return 打款记录列表
     */
    List<FinRemitVo> queryList(FinRemitBo bo);

    /**
     * 新增打款记录
     *
     * @param bo 打款记录
     * @return 是否新增成功
     */
    Boolean insertByBo(FinRemitBo bo);

    /**
     * 修改打款记录
     *
     * @param bo 打款记录
     * @return 是否修改成功
     */
    Boolean updateByBo(FinRemitBo bo);

    /**
     * 校验并批量删除打款记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
