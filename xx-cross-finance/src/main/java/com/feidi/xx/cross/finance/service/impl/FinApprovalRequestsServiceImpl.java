package com.feidi.xx.cross.finance.service.impl;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mail.utils.MailUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.enums.finance.ApprTransactionTypeEnum;
import com.feidi.xx.cross.common.enums.finance.ApprovalStatusEnum;
import com.feidi.xx.cross.finance.domain.*;
import com.feidi.xx.cross.finance.domain.bo.FinApprovalRequestDetailsBo;
import com.feidi.xx.cross.finance.domain.bo.FinApprovalRequestsBo;
import com.feidi.xx.cross.finance.domain.bo.FinApprovalRequestsImportBo;
import com.feidi.xx.cross.finance.domain.vo.FinApprovalLogVo;
import com.feidi.xx.cross.finance.domain.vo.FinApprovalRequestDetailsVo;
import com.feidi.xx.cross.finance.domain.vo.FinApprovalRequestsVo;
import com.feidi.xx.cross.finance.mapper.FinApprovalLogMapper;
import com.feidi.xx.cross.finance.mapper.FinApprovalRequestDetailsMapper;
import com.feidi.xx.cross.finance.mapper.FinApprovalRequestsMapper;
import com.feidi.xx.cross.finance.service.IFinApprovalRequestsService;
import com.feidi.xx.cross.finance.service.IFinDrvWalletService;
import com.feidi.xx.cross.finance.service.IFinFlowService;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import com.feidi.xx.cross.power.api.RemoteDriverService;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.system.api.RemoteUserService;
import com.feidi.xx.system.api.domain.vo.RemoteUserVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 账单审批记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinApprovalRequestsServiceImpl implements IFinApprovalRequestsService {

    private final FinApprovalRequestsMapper baseMapper;

    @DubboReference
    private final RemoteDriverService remoteDriverService;

    private final IFinDrvWalletService finDrvWalletService;

    @DubboReference
    private final RemoteOrderService remoteOrderService;
    private final FinApprovalRequestDetailsMapper finApprovalRequestDetailsMapper;

    @DubboReference
    private final RemoteUserService remoteUserService;

    private final FinApprovalLogMapper finApprovalLogMapper;

    private final IFinFlowService finFlowService;

    /**
     * 查询账单审批记录
     *
     * @param id 主键
     * @return 账单审批记录
     */
    @Override
    public FinApprovalRequestsVo queryById(Long id) {
        FinApprovalRequestsVo farv = baseMapper.selectVoById(id);
        if (farv == null) {
            throw new ServiceException("账单审批记录不存在");
        }
        farv.setDetails(finApprovalRequestDetailsMapper.queryByRequestId(farv.getId()));
        farv.setLogs(finApprovalLogMapper.listByRequestId(id));

        List<Long> userIds = Stream.of(List.of(farv.getCreateBy(), farv.getUpdateBy()), StreamUtils.toList(farv.getLogs(), FinApprovalLogVo::getOperatorId))
                .flatMap(Collection::stream)
                .distinct().filter(Objects::nonNull).toList();
        remoteUserService.selectListByIds(userIds).forEach(user -> {
            if (Objects.equals(user.getUserId(), farv.getCreateBy())) {
                farv.setCreateName(user.getUserName());
            }
            if (Objects.equals(user.getUserId(), farv.getUpdateBy())) {
                farv.setUpdateName(user.getUserName());
            }
        });

        return farv;
    }

    /**
     * 分页查询账单审批记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 账单审批记录分页列表
     */
    @Override
    public TableDataInfo<FinApprovalRequestsVo> queryPageList(FinApprovalRequestsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinApprovalRequests> lqw = buildQueryWrapper(bo);
        Page<FinApprovalRequestsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<Long> ids = Stream.of(
                        StreamUtils.toList(result.getRecords(), FinApprovalRequestsVo::getCreateBy),
                        StreamUtils.toList(result.getRecords(), FinApprovalRequestsVo::getUpdateBy)
                ).distinct().filter(Objects::nonNull)
                .flatMap(Collection::stream).toList();

        if (CollUtils.isNotEmpty(ids)) {
            var userInfoMap = remoteUserService.selectListByIds(ids).stream().collect(Collectors.toMap(RemoteUserVo::getUserId, Function.identity()));
            result.getRecords().forEach(vo -> {
                if (userInfoMap.containsKey(vo.getCreateBy())) {
                    vo.setCreateName(userInfoMap.get(vo.getCreateBy()).getUserName());
                }
                if (userInfoMap.containsKey(vo.getUpdateBy())) {
                    vo.setUpdateName(userInfoMap.get(vo.getUpdateBy()).getUserName());
                }
            });
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的账单审批记录列表
     *
     * @param bo 查询条件
     * @return 账单审批记录列表
     */
    @Override
    public List<FinApprovalRequestsVo> queryList(FinApprovalRequestsBo bo) {
        LambdaQueryWrapper<FinApprovalRequests> lqw = buildQueryWrapper(bo);
        List<FinApprovalRequestsVo> finApprovalRequestsVos = baseMapper.selectVoList(lqw);
        if (CollUtils.isEmpty(finApprovalRequestsVos)) {
            return Collections.emptyList();
        }
        List<Long> ids = finApprovalRequestsVos.stream().map(FinApprovalRequestsVo::getId).toList();
        var fardvMap = finApprovalRequestDetailsMapper.queryByRequestIds(ids).stream().collect(Collectors.groupingBy(FinApprovalRequestDetailsVo::getApprovalRequestId));
        for (FinApprovalRequestsVo farv : finApprovalRequestsVos) {
            if (fardvMap.containsKey(farv.getId())) {
                farv.setDetails(fardvMap.get(farv.getId()));
            }
        }
        return finApprovalRequestsVos;
    }

    private LambdaQueryWrapper<FinApprovalRequests> buildQueryWrapper(FinApprovalRequestsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinApprovalRequests> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), FinApprovalRequests::getName, bo.getName());
        lqw.eq(bo.getStatus() != null, FinApprovalRequests::getStatus, bo.getStatus());
        lqw.eq(bo.getAmount() != null, FinApprovalRequests::getAmount, bo.getAmount());
        lqw.eq(bo.getApprovalTime() != null, FinApprovalRequests::getApprovalTime, bo.getApprovalTime());
        lqw.eq(bo.getApproverId() != null, FinApprovalRequests::getApproverId, bo.getApproverId());
        lqw.eq(StrUtil.isNotBlank(bo.getApproverName()), FinApprovalRequests::getApproverName, bo.getApproverName());
        lqw.eq(bo.getCreateBy() != null, FinApprovalRequests::getCreateBy, bo.getCreateBy());

        lqw.ge(bo.getStartCreateTime() != null, FinApprovalRequests::getCreateTime, bo.getStartCreateTime());
        lqw.le(bo.getEndCreateTime() != null, FinApprovalRequests::getCreateTime, bo.getEndCreateTime());
        lqw.ge(bo.getStartApprovalTime() != null, FinApprovalRequests::getApprovalTime, bo.getStartApprovalTime());
        lqw.le(bo.getEndApprovalTime() != null, FinApprovalRequests::getApprovalTime, bo.getEndApprovalTime());
        lqw.ge(bo.getStartUpdateTime() != null, FinApprovalRequests::getUpdateTime, bo.getStartUpdateTime());
        lqw.le(bo.getEndUpdateTime() != null, FinApprovalRequests::getUpdateTime, bo.getEndUpdateTime());
        return lqw;
    }

    /**
     * 新增账单审批记录
     *
     * @param bo 账单审批记录
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(FinApprovalRequestsBo bo) {
        // 前置校验
        preCheck(bo);
        FinApprovalRequests add = MapstructUtils.convert(bo, FinApprovalRequests.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        bo.getDetails().forEach(e -> e.setApprovalRequestId(add.getId()));
        flag = finApprovalRequestDetailsMapper.insertBatch(BeanUtils.copyToList(bo.getDetails(), FinApprovalRequestDetails.class));
        Assert.isTrue(flag, "新增账单审批关联订单失败");
        //邮件通知财务
        List<RemoteUserVo> finance = remoteUserService.selectUserIdsByRoleKey("cwjl");
        List<String> financeEmails = StreamUtils.toList(finance, RemoteUserVo::getEmail);
        ThreadUtil.execAsync(() -> sendEmailToFinanceStaff(financeEmails));
        return true;
    }

    /**
     * 修改账单审批记录
     *
     * @param bo 账单审批记录
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(FinApprovalRequestsBo bo) {
        FinApprovalRequests finApprovalRequests = baseMapper.selectById(bo.getId());
        if (finApprovalRequests == null) {
            throw new ServiceException("账单审批记录不存在");
        }
        Assert.isTrue(ApprovalStatusEnum.PENDING.getCode().equals(finApprovalRequests.getStatus()), "仅待审批状态可编辑");
        // 前置校验
        preCheck(bo);

        FinApprovalRequests update = MapstructUtils.convert(bo, FinApprovalRequests.class);
        boolean b = baseMapper.updateById(update) > 0;
        if (b) {
            // 删除详情
            finApprovalRequestDetailsMapper.deleteByRequestId(bo.getId());
            // 重新插入
            bo.getDetails().forEach(e -> e.setApprovalRequestId(bo.getId()));
            b = finApprovalRequestDetailsMapper.insertBatch(BeanUtils.copyToList(bo.getDetails(), FinApprovalRequestDetails.class));
        }

        //邮件通知财务
        List<RemoteUserVo> finance = remoteUserService.selectUserIdsByRoleKey("cwjl");
        List<String> financeEmails = StreamUtils.toList(finance, RemoteUserVo::getEmail);
        ThreadUtil.execAsync(() -> sendEmailToFinanceStaff(financeEmails));
        return b;
    }

    private static void sendEmailToFinanceStaff(List<String> financeEmails) {
        for (String email : financeEmails) {
            try {
                MailUtils.sendText(email, "账单审批提醒", "您有一条新的账单审批待处理。");
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 校验并设置信息。
     * 设置关联订单号
     *
     * @param bo
     */
    private void preCheck(FinApprovalRequestsBo bo) {
        Set<String> orderNoSet = new HashSet<>();
        bo.getDetails().stream()
                .flatMap(d -> d.getRelateOrders().stream())
                .forEach(o -> {
                    if (!orderNoSet.add(o.getOrderNo())) {
                        throw new ServiceException("存在重复订单号 " + o.getOrderNo());
                    }
                });
        Assert.isTrue(bo.getDetails().stream().map(FinApprovalRequestDetailsBo::getRelateOrders)
                .flatMap(Collection::stream).allMatch(o -> o.getAdjustAmount() != 0), "调整金额不能为0");
        //查询订单信息
        var orderNos = bo.getDetails().stream().map(FinApprovalRequestDetailsBo::getRelateOrders)
                .flatMap(Collection::stream).map(FinApprovalRequestRelateOrder::getOrderNo)
                .filter(StringUtils::isNotBlank).distinct().toList();
        var orderInfoMap = StreamUtils.toIdentityMap(remoteOrderService.queryOrderByOrderNos(orderNos), RemoteOrderVo::getOrderNo);
        //查询司机信息
        var driverIds = bo.getDetails().stream().map(FinApprovalRequestDetailsBo::getDriverId).distinct().collect(Collectors.toList());
        var driverInfoMap = StreamUtils.toIdentityMap(remoteDriverService.getDriverByIds(driverIds), RemoteDriverVo::getId);

        //查询司机钱包信息
        var drvWalletMap = StreamUtils.toIdentityMap(finDrvWalletService.queryByDriverIds(driverIds), FinDrvWallet::getDriverId);

        for (FinApprovalRequestDetailsBo detail : bo.getDetails()) {
            if (!driverInfoMap.containsKey(detail.getDriverId())) {
                throw new ServiceException(String.format("司机账户Id为 %s 的账户不存在！", detail.getDriverId()));
            }
            RemoteDriverVo driverVo = driverInfoMap.get(detail.getDriverId());
            FinDrvWallet finDrvWallet = drvWalletMap.getOrDefault(detail.getDriverId(), FinDrvWallet.defaultWallet(detail.getDriverId()));
            //设置关联信息
            detail.setAssociatedInfo(driverVo, finDrvWallet);
            for (FinApprovalRequestRelateOrder relateOrder : detail.getRelateOrders()) {
                if (!orderInfoMap.containsKey(relateOrder.getOrderNo())) {
                    throw new ServiceException(String.format("订单号为 %s 的订单不存在！", relateOrder.getOrderNo()));
                }
                //司机信息校验
                RemoteOrderVo orderVo = orderInfoMap.get(relateOrder.getOrderNo());
                if (!Objects.equals(detail.getDriverId(), orderVo.getDriverId())) {
                    throw new ServiceException(String.format("订单号为 %s 的订单司机与申请调整的司机不一致！", relateOrder.getOrderNo()));
                }
                //设置关联订单号
                relateOrder.setOrderId(orderVo.getId());
            }
        }
        //计算总金额
        bo.setAmount(bo.getDetails().stream()
                .map(FinApprovalRequestDetailsBo::getRelateOrders).flatMap(Collection::stream)
                .mapToLong(FinApprovalRequestRelateOrder::getAdjustAmount).sum());
        bo.setStatus(ApprovalStatusEnum.PENDING.getCode());
    }


    /**
     * 校验并批量删除账单审批记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 状态说明:
     * 运营提交申请->待审批
     * 运营撤销->已撤销
     * 运营提交申请后财务通过->已通过
     * 运营提交申请后财务驳回->已驳回
     *
     * @param bo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#bo.id"}, expire = 15 * 1000, acquireTimeout = 5 * 1000)
    public void updateStatus(FinApprovalRequestsBo bo) {
        log.debug("updateStatus: {}", bo);
        FinApprovalRequests finApprovalRequests = baseMapper.selectById(bo.getId());
        if (finApprovalRequests == null) {
            throw new ServiceException("账单审批记录不存在");
        }
        var upw = Wrappers.<FinApprovalRequests>lambdaUpdate()
                .eq(FinApprovalRequests::getId, bo.getId())
                .set(FinApprovalRequests::getStatus, bo.getStatus());
        var approvalStatusEnum = ApprovalStatusEnum.fromCode(bo.getStatus());
        switch (approvalStatusEnum) {
            case CANCELED -> {
                Assert.isTrue(ApprovalStatusEnum.PENDING.getCode().equals(finApprovalRequests.getStatus()), "仅待审批状态下可撤销");
                //撤销
                upw.set(FinApprovalRequests::getUpdateTime, new Date())
                        .set(FinApprovalRequests::getUpdateBy, LoginHelper.getUserId());
            }
            case APPROVED -> {
                Assert.isTrue(ApprovalStatusEnum.PENDING.getCode().equals(finApprovalRequests.getStatus()), "仅待审批状态下可操作！");
                // 通过
                upw.set(FinApprovalRequests::getApprovalTime, new Date())
                        .set(FinApprovalRequests::getApproverId, LoginHelper.getUserId())
                        .set(FinApprovalRequests::getApproverName, LoginHelper.getUsername())
                        .set(FinApprovalRequests::getApprovalUserType, LoginHelper.getUserType());
            }
            case REJECTED -> {
                Assert.isTrue(ApprovalStatusEnum.PENDING.getCode().equals(finApprovalRequests.getStatus()), "仅待审批状态下可操作！");
                // 驳回
                upw.set(FinApprovalRequests::getUpdateTime, new Date())
                        .set(FinApprovalRequests::getUpdateBy, LoginHelper.getUserId());
            }
            default -> {
                throw new ServiceException("不支持的状态");
            }
        }
        baseMapper.update(upw);

        finApprovalLogMapper.insert(FinApprovalLog.builder()
                .approvalId(bo.getId())
                .operatorId(LoginHelper.getUserId())
                .operation(approvalStatusEnum.getInfo())
                .operateTime(new Date())
                .build());
        if (ApprovalStatusEnum.APPROVED.getCode().equals(bo.getStatus())) {
            // 通过后处理
            FinApprovalRequestsBo far = BeanUtils.copyProperties(finApprovalRequests, FinApprovalRequestsBo.class);
            far.setDetails(BeanUtils.copyToList(finApprovalRequestDetailsMapper.queryByRequestId(bo.getId()), FinApprovalRequestDetailsBo.class));
            finFlowService.artificialAdjustmentAddFlow(far);
        }
    }

    @Override
    public FinApprovalRequestsVo importDataProcess(List<FinApprovalRequestsImportBo> list) {
        //查询司机信息
        var driverIds = list.stream().map(FinApprovalRequestsImportBo::getDriverId).distinct().collect(Collectors.toList());
        var driverInfoMap = StreamUtils.toIdentityMap(remoteDriverService.getDriverByIds(driverIds), RemoteDriverVo::getId);
        for (Long id : driverIds) {
            if (!driverInfoMap.containsKey(id)) {
                throw new ServiceException(String.format("司机账户Id为 %s 的账户不存在！", id));
            }
        }
        //查询订单信息
        var orderNos = list.stream().map(FinApprovalRequestsImportBo::getRelatedOrder).filter(StringUtils::isNotBlank).distinct().toList();
        var orderInfoMap = StreamUtils.toIdentityMap(remoteOrderService.queryOrderByOrderNos(orderNos), RemoteOrderVo::getOrderNo);

        //查询司机钱包信息
        var drvWalletMap = StreamUtils.toIdentityMap(finDrvWalletService.queryByDriverIds(driverIds), FinDrvWallet::getDriverId);
        //导入的调账信息按照司机分组
        var driverGroupListMap = list.stream().collect(Collectors.groupingBy(FinApprovalRequestsImportBo::getDriverId));
        List<FinApprovalRequestDetailsVo> details = new ArrayList<>();
        for (var entry : driverGroupListMap.entrySet()) {
            RemoteDriverVo driverInfo = driverInfoMap.get(entry.getKey());
            FinDrvWallet finDrvWallet = drvWalletMap.getOrDefault(entry.getKey(), FinDrvWallet.defaultWallet(entry.getKey()));

            var far = FinApprovalRequestDetailsVo.from(driverInfo, finDrvWallet);
            far.setTransactionType(ApprTransactionTypeEnum.ARTIFICIAL_ADJUSTMENT.getCode());
            List<FinApprovalRequestRelateOrder> relateOrders = entry.getValue().stream()
                    .map(FinApprovalRequestsImportBo::toRelateOrder)
                    .peek(e -> {
                        if (orderInfoMap.containsKey(e.getOrderNo())) {
                            RemoteOrderVo remoteOrderVo = orderInfoMap.get(e.getOrderNo());
                            e.setOrderId(remoteOrderVo.getId());
                            e.setOrderNo(remoteOrderVo.getOrderNo());
                        }

                    }).toList();
            far.setRelateOrders(relateOrders);
            details.add(far);
        }
        var finApprovalRequestsVo = new FinApprovalRequestsVo();
        finApprovalRequestsVo.setStatus(ApprovalStatusEnum.PENDING.getCode());
        Long amount = details.stream().flatMap(e -> e.getRelateOrders().stream())
                .mapToLong(FinApprovalRequestRelateOrder::getAdjustAmount).sum();
        finApprovalRequestsVo.setAmount(amount);
        finApprovalRequestsVo.setDetails(details);
        return finApprovalRequestsVo;
    }
}
