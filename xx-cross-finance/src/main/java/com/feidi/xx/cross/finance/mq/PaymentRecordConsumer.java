package com.feidi.xx.cross.finance.mq;

import com.alibaba.fastjson.JSON;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.idempotent.annotation.NoMQDuplicateConsume;
import com.feidi.xx.common.payment.mq.PaymentRecordEvent;
import com.feidi.xx.common.rocketmq.base.MessageWrapper;
import com.feidi.xx.common.rocketmq.constants.MQTopicConstants;
import com.feidi.xx.cross.finance.domain.bo.FinPaymentRecordBo;
import com.feidi.xx.cross.finance.service.IFinPaymentRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 支付记录消费者
 */
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = MQTopicConstants.TOPIC_PAY_MSG,
        consumerGroup = MQTopicConstants.TOPIC_PAY_MSG_CG
)
@Slf4j(topic = "PaymentRecordConsumer")
public class PaymentRecordConsumer implements RocketMQListener<MessageWrapper<PaymentRecordEvent>> {

    private final IFinPaymentRecordService paymentRecordService;

    @NoMQDuplicateConsume(
            keyPrefix = "global:topic_pay_msg:",
            key = "#messageWrapper.keys",
            keyTimeout = 600
    )
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void onMessage(MessageWrapper<PaymentRecordEvent> messageWrapper) {
        log.info("[消费者] 支付记录消息 - 执行消费逻辑，消息体：{}", JSON.toJSONString(messageWrapper));
        PaymentRecordEvent event = messageWrapper.getMessage();
        FinPaymentRecordBo convert = MapstructUtils.convert(event, FinPaymentRecordBo.class);
        paymentRecordService.insertByBo(convert);
    }
}