package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.cross.common.enums.finance.ComplainFeeTypeEnum;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 订单客诉费用视图对象 fin_complain_fee
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinComplainFeeVo.class, convertGenerate = false)
public class FinComplainFeeExportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "代理商名称")
    private String agentName;

    @ExcelProperty(value = "订单编号")
    private String orderNo;

    @ExcelProperty(value = "司机id")
    private Long driverId;

    @ExcelProperty(value = "司机姓名")
    private String driverName;

    @ExcelProperty(value = "类型", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = ComplainFeeTypeEnum.class)
    private String feeType;

    @ExcelProperty(value = "订单金额")
    @ReverseAutoMapping(target = "orderPrice", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getOrderPrice()))")
    private String orderPrice;

    @ExcelProperty(value = "实际支付金额")
    @ReverseAutoMapping(target = "payPrice", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getPayPrice()))")
    private String payPrice;

    @ExcelProperty(value = "金额")
    @ReverseAutoMapping(target = "amount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getAmount()))")
    private String amount;

    @ExcelProperty(value = "备注")
    private String remark;


}
