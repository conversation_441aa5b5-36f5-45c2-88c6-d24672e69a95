package com.feidi.xx.cross.finance.controller.driver;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.domain.vo.FinDrvWalletVo;
import com.feidi.xx.cross.finance.service.IFinDrvWalletService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 司机 - 司机钱包
 * 前端访问路由地址为:/finance/drvWallet
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@SaCheckRole(UserTypeEnum.UserType.DRIVER_USER)
@RequestMapping(WebConstants.DRIVER_ROUTE_PREFIX + "/wallet")
public class DrvWalletController extends BaseController {

    private final IFinDrvWalletService FinDrvWalletService;

    /**
     * 获取司机钱包详细信息
     */
    @GetMapping()
    public R<FinDrvWalletVo> getInfo() {
        Long driverId = LoginHelper.getUserId();
        return R.ok(FinDrvWalletService.queryByDriverId(driverId));
    }

}
