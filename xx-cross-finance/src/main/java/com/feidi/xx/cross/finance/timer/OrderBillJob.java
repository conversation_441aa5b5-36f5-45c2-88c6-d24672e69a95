package com.feidi.xx.cross.finance.timer;

import com.feidi.xx.cross.finance.service.IFinBillService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 订单对账定时器
 *
 * <AUTHOR>
 * @date 2025/5/27
 */

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderBillJob {

    private final IFinBillService finBillService;

    /**
     * 订单对账
     *
     * 代理商账单总收益 = 司机订单结算收益 + 司机拉新完单奖励 + 司机订单转卖收益 + 代理商拉新完单奖励
     */
    @XxlJob("orderBillJob")
    public void orderBillJob() {
        if (log.isInfoEnabled()) {
            log.info("============== 订单对账-定时器开始执行 ==============");
        }

        finBillService.orderBill();

        if (log.isInfoEnabled()) {
            log.info("============== 订单对账-定时器执行结束 ==============");
        }
    }
}
