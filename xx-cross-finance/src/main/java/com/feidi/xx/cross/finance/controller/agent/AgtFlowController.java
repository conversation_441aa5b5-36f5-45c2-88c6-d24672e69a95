package com.feidi.xx.cross.finance.controller.agent;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.domain.bo.FinFlowQueryBo;
import com.feidi.xx.cross.finance.domain.vo.ExportVo;
import com.feidi.xx.cross.finance.domain.vo.FinFlowExportVo;
import com.feidi.xx.cross.finance.domain.vo.FinFlowVo;
import com.feidi.xx.cross.finance.service.IFinFlowService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 代理商 - 资金流水
 * 前端访问路由地址为:/finance/flow
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Validated
@RestController
@RequiredArgsConstructor
@SaCheckRole(UserTypeEnum.UserType.AGENT_USER)
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX+"/flow")
public class AgtFlowController extends BaseController {

    private final IFinFlowService FinFlowService;

    /**
     * 查询资金流水列表
     */
    @PostMapping("/list")
    public TableDataInfo<FinFlowVo> list(@Validated @RequestBody FinFlowQueryBo bo) {
        Long agentId = LoginHelper.getAgentId();
        if (bo.getAgentId() != null) {
            bo.setAgentIds(CollUtil.newArrayList(bo.getAgentId()));
        } else {
            bo.setAgentId(agentId);
        }
        return FinFlowService.queryPageList(bo, bo.buildPageQuery());
    }

    /**
     * 导出资金流水列表
     */
    @Log(title = "资金流水", businessType = BusinessType.EXPORT)
    @Download(name="资金流水",module = ModuleConstants.FINANCE,mode="no")
    @PostMapping("/export")
    public Object export(@RequestBody @Validated FinFlowQueryBo bo, HttpServletResponse response) {
        Long agentId = LoginHelper.getAgentId();
        if (bo.getAgentId() != null) {
            bo.setAgentIds(CollUtil.newArrayList(bo.getAgentId()));
        } else {
            bo.setAgentId(agentId);
        }
        List<FinFlowExportVo> list = FinFlowService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(list, "资金流水", FinFlowExportVo.class, outputStream);
        return outputStream.toByteArray();
    }

}
