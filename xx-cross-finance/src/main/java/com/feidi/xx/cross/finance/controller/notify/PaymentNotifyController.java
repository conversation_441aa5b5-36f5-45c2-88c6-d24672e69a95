package com.feidi.xx.cross.finance.controller.notify;

import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.PaymentChannelEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.service.INotifyService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 公共 - 支付通知
 *
 * <AUTHOR>
 * @date 2024-05-25
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/notify")
public class PaymentNotifyController extends BaseController {

    private final INotifyService notifyService;

    /**
     * 支付通知
     */
    @PostMapping("/payment/wx/{appId}")
    public R<?> wxPayment(HttpServletRequest request, HttpServletResponse response, @RequestBody String requestBody,
                          @PathVariable("appId") String appId) {
        boolean payment = notifyService.payment(PaymentChannelEnum.WX_PAY, request, response, requestBody, appId);
        if (payment) {
            return R.ok();
        } else {
            Map<String, String> res = new HashMap<>();
            res.put("code", "FAIL");
            res.put("message", "失败");
            return R.fail(res);
        }
    }

    /**
     * 支付通知
     */
    @PostMapping("/payment/alipay/{appId}")
    public void aliPayment(HttpServletRequest request, HttpServletResponse response, @RequestBody String requestBody,
                           @PathVariable("appId") String appId) {
        try {
            boolean payment = notifyService.payment(PaymentChannelEnum.ALIPAY, request, response, requestBody, appId);
            if (payment) {
                response.getWriter().write("success");
            } else {
                response.getWriter().write("fail");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("支付宝回调异常");
        }
    }

    /**
     * 退款通知
     */
    @PostMapping("/refund/wx/{appId}")
    public R<?> wxRefund(HttpServletRequest request, HttpServletResponse response, @RequestBody String requestBody,
                         @PathVariable("appId") String appId) {
        boolean refund = notifyService.refund(PaymentChannelEnum.WX_PAY, request, response, requestBody, appId);
        if (refund) {
            return R.ok();
        } else {
            Map<String, String> res = new HashMap<>();
            res.put("code", "FAIL");
            res.put("message", "失败");
            return R.fail(res);
        }
    }

    /**
     * 退款通知
     */
    @PostMapping("/refund/alipay/{appId}")
    public void aliRefund(HttpServletRequest request, HttpServletResponse response, @RequestBody String requestBody,
                          @PathVariable("appId") String appId) {
        try {
            boolean refund = notifyService.refund(PaymentChannelEnum.ALIPAY, request, response, requestBody, appId);
            if (refund) {
                response.getWriter().write("success");
            } else {
                response.getWriter().write("fail");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("支付宝回调异常");
        }
    }

}
