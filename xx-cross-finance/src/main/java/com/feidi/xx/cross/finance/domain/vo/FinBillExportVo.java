package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.cross.common.enums.finance.FinanceStatusEnum;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 订单对账视图对象 fin_bill
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinBillVo.class, convertGenerate = false)
public class FinBillExportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 代理商名称
     */
    @ExcelProperty(value = "代理商名称")
    private String agentName;

    /**
     * 账单月份
     */
    @ExcelProperty(value = "账单日期", format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    private Date month;

    /**
     * 对账状态[FinanceStatusEnum]
     */
    @ExcelProperty(value = "对账状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = FinanceStatusEnum.class)
    private String financeStatus;

    /**
     * 订单量
     */
    @ExcelProperty(value = "订单量")
    private Long orderNumber;

    /**
     * 订单金额
     */
    @ExcelProperty(value = "订单金额")
    @ReverseAutoMapping(target = "orderAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getOrderAmount()))")
    private String orderAmount;

    /**
     * 司机收益
     */
    @ExcelProperty(value = "司机收益")
    @ReverseAutoMapping(target = "driverProfit", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getDriverProfit()))")
    private String driverProfit;

    /**
     * 代收款
     */
    @ExcelProperty(value = "代收款")
    @ReverseAutoMapping(target = "proxyAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getProxyAmount()))")
    private String proxyAmount;

    /**
     * 应收信息服务费
     */
    @ExcelProperty(value = "应收信息服务费")
    @ReverseAutoMapping(target = "infoServiceFee", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getInfoServiceFee()))")
    private String infoServiceFee;

    /**
     * 技术服务费
     */
    @ExcelProperty(value = "技术服务费")
    @ReverseAutoMapping(target = "technicalServiceFee", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getTechnicalServiceFee()))")
    private String technicalServiceFee;

    /**
     * 奖励金额
     */
    @ExcelProperty(value = "奖励金额")
    @ReverseAutoMapping(target = "rewardAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getRewardAmount()))")
    private String rewardAmount;

    /**
     * 客诉金额
     */
    @ExcelProperty(value = "客诉金额")
    @ReverseAutoMapping(target = "complainAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getComplainAmount()))")
    private String complainAmount;

    /**
     * 优惠券使用总金额
     */
    @ExcelProperty(value = "优惠券使用总金额")
    @ReverseAutoMapping(target = "couponQuotaAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getCouponQuotaAmount()))")
    private String couponQuotaAmount;

    /**
     * 其他金额
     */
    @ExcelProperty(value = "其他金额")
    @ReverseAutoMapping(target = "otherAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getOtherAmount()))")
    private String otherAmount;

    /**
     * 应结算金额
     */
    @ExcelProperty(value = "应结算金额")
    @ReverseAutoMapping(target = "rebateAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getRebateAmount()))")
    private String rebateAmount;

    /**
     * 已结算金额
     */
    @ExcelProperty(value = "已结算金额")
    @ReverseAutoMapping(target = "alreadyRebateAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getAlreadyRebateAmount()))")
    private String alreadyRebateAmount;

    /**
     * 待结算金额
     */
    @ExcelProperty(value = "待结算金额")
    @ReverseAutoMapping(target = "notRebateAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getNotRebateAmount()))")
    private String notRebateAmount;

    /**
     * 已提现金额
     */
    @ExcelProperty(value = "已提现金额")
    @ReverseAutoMapping(target = "alreadyCashAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getAlreadyCashAmount()))")
    private String alreadyCashAmount;

    /**
     * 可提现金额
     */
    @ExcelProperty(value = "可提现金额")
    @ReverseAutoMapping(target = "notCashAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getNotCashAmount()))")
    private String notCashAmount;
}
