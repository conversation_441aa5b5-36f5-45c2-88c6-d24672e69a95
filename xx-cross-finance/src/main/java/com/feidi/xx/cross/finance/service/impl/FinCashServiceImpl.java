package com.feidi.xx.cross.finance.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.enums.*;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.ValidatorUtils;
import com.feidi.xx.common.core.utils.xx.MoneyConvertUtils;
import com.feidi.xx.common.mail.utils.MailUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.payment.common.enums.TransferErrorEnum;
import com.feidi.xx.common.payment.config.AliPayConfig;
import com.feidi.xx.common.payment.domain.trans.bo.TransferBo;
import com.feidi.xx.common.payment.domain.trans.vo.TransferVo;
import com.feidi.xx.common.payment.mq.PaymentRecordEvent;
import com.feidi.xx.common.payment.mq.PaymentRecordProducer;
import com.feidi.xx.common.payment.strategy.ITransService;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.power.manager.PowCacheManager;
import com.feidi.xx.cross.common.constant.finance.FinanceCacheConstants;
import com.feidi.xx.cross.common.constant.finance.FinanceConstants;
import com.feidi.xx.cross.common.enums.finance.*;
import com.feidi.xx.cross.common.enums.order.OrderFlowTypeEnum;
import com.feidi.xx.cross.common.enums.power.DriverTypeEnum;
import com.feidi.xx.cross.finance.common.validate.ReviewPassGroup;
import com.feidi.xx.cross.finance.common.validate.ReviewRejectGroup;
import com.feidi.xx.cross.finance.domain.*;
import com.feidi.xx.cross.finance.domain.bo.FinCashApplyBo;
import com.feidi.xx.cross.finance.domain.bo.FinCashQueryBo;
import com.feidi.xx.cross.finance.domain.bo.FinCashReviewBo;
import com.feidi.xx.cross.finance.domain.factory.FinFlowFactory;
import com.feidi.xx.cross.finance.domain.vo.FinCashListVo;
import com.feidi.xx.cross.finance.domain.vo.FinCashVo;
import com.feidi.xx.cross.finance.domain.vo.FinWalletVo;
import com.feidi.xx.cross.finance.mapper.*;
import com.feidi.xx.cross.finance.mq.AutoPaymentEvent;
import com.feidi.xx.cross.finance.mq.AutoPaymentProducer;
import com.feidi.xx.cross.finance.service.IFinCashService;
import com.feidi.xx.cross.finance.service.helper.WalletHelper;
import com.feidi.xx.cross.message.api.RemoteImService;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDetailVo;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.RemoteDriverAccountService;
import com.feidi.xx.cross.power.api.RemoteDriverService;
import com.feidi.xx.cross.power.api.RemoteGroupService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.api.domain.driver.bo.RemoteDriverQueryBo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverAccountVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.push.common.constants.VoiceConstant;
import com.feidi.xx.push.common.enums.PushTypeEnum;
import com.feidi.xx.push.mq.PushEvent;
import com.feidi.xx.push.mq.PushMsgProducer;
import com.feidi.xx.system.api.RemoteConfigService;
import com.feidi.xx.system.api.RemoteDictService;
import com.feidi.xx.system.api.RemoteUserService;
import com.feidi.xx.system.api.domain.vo.RemoteDictDataVo;
import com.feidi.xx.system.api.domain.vo.RemoteUserVo;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 提现Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinCashServiceImpl implements IFinCashService {

    private static final byte[] KEYS = "xI-XiNg*Chu^x1nG".getBytes(StandardCharsets.UTF_8);

    private final PowCacheManager powCacheManager;
    private final FinCashMapper baseMapper;
    private final FinFlowMapper flowMapper;
    private final FinDrvWalletMapper walletMapper;
    private final FinAccountMapper finAccountMapper;
    private final FinPaymentRecordMapper paymentRecordMapper;
    private final WalletHelper walletHelper;
    private final ITransService transService;
    private final ScheduledExecutorService scheduledExecutorService;
    @DubboReference
    private final RemoteConfigService remoteConfigService;
    @DubboReference
    private final RemoteDriverAccountService remoteDriverAccountService;
    @DubboReference
    private final RemoteDriverService remoteDriverService;
    @DubboReference
    private final RemoteUserService remoteUserService;
    @DubboReference
    private final RemoteDictService remoteDictService;
    @DubboReference
    private final RemoteImService remoteImService;
    @DubboReference
    private final RemoteOrderService remoteOrderService;
    @DubboReference
    private final RemoteAgentService remoteAgentService;
    @DubboReference
    private final RemoteGroupService remoteGroupService;

    /**
     * 万能访问令牌
     */
    private static final String ANY_ACCESS_TOKEN = "$ANY@_!ACCESS!_%TOKEN#";

    /**
     * 查询提现
     *
     * @param id 主键
     * @return 提现
     */
    @Override
    public FinCashVo queryById(Long id) {
        FinCashVo vo = baseMapper.selectVoById(id);
        RemoteDriverVo driverInfo = powCacheManager.getDriverInfoById(vo.getDriverId());
        if (ObjectUtil.isNotNull(driverInfo)) {
            vo.setDriverTypeText(DriverTypeEnum.getInfoByCode(driverInfo.getType()));

            LambdaQueryWrapper<FinAccount> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FinAccount::getChannel, PaymentChannelEnum.ALIPAY.getCode())
                    .eq(FinAccount::getDirection, PaymentDirectionEnum.OUTGOING.getCode())
                    .eq(FinAccount::getTenantId, driverInfo.getTenantId())
                    .orderByDesc(FinAccount::getCreateTime)
                    .last(Constants.LIMIT_ONE);
            Object payAccountConfig = null;
            FinAccount finAccount = null;
            if (DriverTypeEnum.PARTNER.getCode().equals(driverInfo.getType())) {
                log.info("司机提现详情-司机类型加盟");
                String accountConfig = RedisUtils.getCacheObject(com.feidi.xx.cross.common.cache.finance.constants.FinanceCacheConstants.FIN_CASH_PAYOUTS_KEY_PREFIX);
                if (StringUtils.isNotBlank(accountConfig)) {
                    payAccountConfig = JSONUtil.toBean(SecureUtil.aes(KEYS).decryptStr(accountConfig), Object.class);
                    queryWrapper.eq(FinAccount::getConfig, accountConfig);
                    finAccount = finAccountMapper.selectOne(queryWrapper);
                }
            } else {
                queryWrapper.eq(FinAccount::getAgentId, driverInfo.getAgentId());
                finAccount = finAccountMapper.selectOne(queryWrapper);
                if (finAccount != null) {
                    payAccountConfig = finAccount.getConfig();
                }
            }

            if (finAccount != null) {
                vo.setPayAgentName(finAccount.getAgentName());
                vo.setPayAccountTypeText(PaymentChannelEnum.getInfoByCode(finAccount.getChannel()));
            }

            if (payAccountConfig != null) {
                AliPayConfig config = JSONUtil.toBean((JSONObject) payAccountConfig, AliPayConfig.class);
                vo.setPayAppid(config.getAppId());
            }

            vo.setDriverName(driverInfo.getName());
            vo.setDriverPhone(driverInfo.getPhone());
            RemoteAgentVo agentInfo = powCacheManager.getAgentInfoById(driverInfo.getAgentId());
            if (ObjectUtil.isNotNull(agentInfo)) {
                vo.setAgentName(agentInfo.getCompanyName());
            }
        }

        FinDrvWallet wallet = walletMapper.selectByDriverId(vo.getDriverId());
        if (wallet != null) {
            FinWalletVo walletVo = MapstructUtils.convert(wallet, FinWalletVo.class);
            walletVo.setTotal(NumberUtil.add(walletVo.getBalance(), walletVo.getFreeze()).longValue() + vo.getAmount());
            walletVo.setBalance(walletVo.getBalance() + vo.getAmount());
            vo.setWallet(walletVo);
        }
        return vo;
    }

    /**
     * 分页查询提现列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 提现分页列表
     */
    @Override
    public TableDataInfo<FinCashListVo> queryPageList(FinCashQueryBo bo, PageQuery pageQuery) {
        IPage<FinCashListVo> result = baseMapper.listByQuery(bo, pageQuery.build());
        List<FinCashListVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            for (FinCashListVo record : records) {
                // 补充信息
                RemoteDriverVo driverInfo = powCacheManager.getDriverInfoById(record.getDriverId());
                if (ObjectUtil.isNotNull(driverInfo)) {
                    record.setDriverTypeText(DriverTypeEnum.getInfoByCode(driverInfo.getType()));
                    record.setDriverName(driverInfo.getName());
                    record.setDriverPhone(driverInfo.getPhone());
                    RemoteAgentVo agentInfo = powCacheManager.getAgentInfoById(driverInfo.getAgentId());
                    if (ObjectUtil.isNotNull(agentInfo)) {
                        record.setAgentName(agentInfo.getCompanyName());
                    }
                }
            }
        }
        return TableDataInfo.build(records, result.getTotal());
    }

    /**
     * 查询符合条件的提现列表
     *
     * @param bo 查询条件
     * @return 提现列表
     */
    @Override
    public List<FinCashListVo> queryList(FinCashQueryBo bo) {
        List<FinCashListVo> records = baseMapper.listByQuery(bo);
        if (CollUtil.isNotEmpty(records)) {
            for (FinCashListVo record : records) {
                // 补充信息
                RemoteDriverVo driverInfo = powCacheManager.getDriverInfoById(record.getDriverId());
                if (ObjectUtil.isNotNull(driverInfo)) {
                    record.setDriverName(driverInfo.getName());
                    record.setDriverPhone(driverInfo.getPhone());
                    RemoteAgentVo agentInfo = powCacheManager.getAgentInfoById(driverInfo.getAgentId());
                    if (ObjectUtil.isNotNull(agentInfo)) {
                        record.setAgentName(agentInfo.getCompanyName());
                    }
                }
            }
        }
        return records;
    }

    private Map<Long, RemoteAgentVo> getAgentMap() {
        List<RemoteAgentVo> pxAgents = remoteAgentService.getAllAgentInfo();
        return pxAgents.stream().collect(Collectors.toMap(RemoteAgentVo::getId, Function.identity()));
    }

    /**
     * 提现前验证
     */
    private List<FinCash> validAndGenerateFLow(FinFlow lastFlow, FinCash cash, String accessToken) {
        Long driverId = cash.getDriverId();
        // 补充信息
        RemoteDriverVo driverInfo = powCacheManager.getDriverInfoById(driverId);
        Assert.notNull(driverInfo, "司机不存在");

        // 万能令牌不进行校验
        if (!ANY_ACCESS_TOKEN.equals(accessToken)) {
            // 重置密码
            walletHelper.needChangePwd(driverId, false);
            // 密码校验
            walletHelper.verifyToken(driverId, PwdVerifyEnum.CASH, accessToken, false);
            // 是否有未处理提现
            walletHelper.cashApplicable(driverId, false);
        }

        // 余额是否够提现
        Assert.notNull(lastFlow, FinanceConstants.NO_FLOW_MSG);
        // 提现金额、是否可提现
        Assert.isTrue(lastFlow.getAfterCash() >= cash.getAmount(), FinanceConstants.CASH_OUT_MSG);

        if (cash.getAccountId() != null) {
            // 验证账户是否开启
            RemoteDriverAccountVo driverAccount = remoteDriverAccountService.getDriverAccount(cash.getAccountId());
            Assert.notNull(driverAccount, "提现账户不存在");
            Assert.isTrue(driverAccount.getStatus().equals(StatusEnum.ENABLE.getCode()), "账户未启用");

            // 填充基础数据
            cash.setName(driverAccount.getName());
            cash.setAccount(driverAccount.getAccount());
            cash.setAccountType(driverAccount.getType());
            cash.setPhone(LoginHelper.getUserPhone());
        }

        // 更新订单资金流向状态，需要同步处理
        List<RemoteOrderDetailVo> orders = remoteOrderService.updateOrderFlowStatus(cash.getDriverId(), OrderFlowTypeEnum.CASHING.getCode(), OrderFlowTypeEnum.NORMAL.getCode());
        // 订单总收入
        Long totalProfit = orders.stream().map(RemoteOrderDetailVo::getDriverProfit).reduce(0L, Long::sum);
        // 差值就是别人转入的钱或者其他收入
        long otherProfit = cash.getAmount() - totalProfit;

        Map<Long, List<RemoteOrderDetailVo>> agentMap = orders.stream().collect(Collectors.groupingBy(RemoteOrderDetailVo::getAgentId));
        // 至少有一个是当前代理商的，比如只有别人转的钱
        if (CollUtil.isEmpty(agentMap)) {
            agentMap.put(driverInfo.getAgentId(), new ArrayList<>());
        }

        Map<Long, RemoteAgentVo> allAgentMap = getAgentMap();

        // 几个代理商对应的几个提现单
        ArrayDeque<FinCash> cashes = new ArrayDeque<>(agentMap.size());

        for (Map.Entry<Long, List<RemoteOrderDetailVo>> entry : agentMap.entrySet()) {
            Long agentId = entry.getKey();
            // 是否当前代理商
            boolean isCurAgent = ObjectUtil.equal(driverInfo.getAgentId(), agentId);
            // 当前代理商下的收入
            Long agentProfit = entry.getValue().stream().map(RemoteOrderDetailVo::getDriverProfit).reduce(0L, Long::sum);
            // 相关订单id
            List<Long> orderIds = StreamUtils.toList(entry.getValue(), RemoteOrderDetailVo::getId);

            // 基础数据拷贝，填充子提现单数据
            FinCash tmpCash = BeanUtil.copyProperties(cash, FinCash.class);
            tmpCash.setAgentId(agentId);
            if (isCurAgent) {
                // 其他收入算在当前代理商下，也肯定是在当前代理商下
                tmpCash.setAmount(agentProfit + otherProfit);
            } else {
                tmpCash.setAmount(agentProfit);
            }
            tmpCash.setServiceFee(serviceFee(tmpCash.getAmount()));
            tmpCash.setRelatedOrder(orderIds);
            // 单号重新生成
            tmpCash.setCashNo(FinCash.generateNo());

            // 之前的代理商先生成流水
            if (isCurAgent) {
                cashes.addLast(tmpCash);
            } else {
                cashes.addFirst(tmpCash);
            }
        }
        return new ArrayList<>(cashes);
    }

    private Long serviceFee(Long amount) {
        // 计算服务费
        return 0L;
    }

    /**
     * 新增提现
     *
     * @param bo 提现
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean insertByBo(FinCashApplyBo bo) {
        FinCash add = MapstructUtils.convert(bo, FinCash.class);
        // token可能是旧的
        RemoteDriverVo driverInfo = powCacheManager.getDriverInfoById(add.getDriverId());
        if (driverInfo != null) {
            add.setAgentId(driverInfo.getAgentId());
            walletHelper.transferOrCashDisable(driverInfo.getId());

            if (Objects.equals(DriverTypeEnum.getByCode(driverInfo.getType()), DriverTypeEnum.SELF)) {
                throw new ServiceException(FinanceConstants.PROPRIETARY_EXPERIENCE_DISABLING_PROMPT);
            }
        }

        FinFlow lastFlow = flowMapper.getLastFlow(add.getDriverId());
        // 相关提现单
        List<FinCash> cashes = validAndGenerateFLow(lastFlow, add, bo.getAccessToken());
        for (FinCash cash : cashes) {
            boolean cashFlag = baseMapper.insert(cash) > 0;
            Assert.isTrue(cashFlag, "提现申请失败，请重新申请");
            FinFlow nextFlow = FinFlowFactory.createFlow(lastFlow, FinFlowFactory.FlowCreateType.CASH, DirectionEnum.OUT, FlowTypeEnum.CASHING, cash, driverInfo);
            boolean flowFlag = flowMapper.insert(nextFlow) > 0;
            Assert.isTrue(flowFlag, "提现申请失败，请重新申请");
            // 更新最新一条流水
            lastFlow = flowMapper.getLastFlow(add.getDriverId());
        }
        boolean flag = walletMapper.updateByFlow(lastFlow) > 0;
        Assert.isTrue(flag, "钱包更新失败，请重试");
        return flag;
    }

    /**
     * 修改提现
     *
     * @param bo 提现
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean review(FinCashReviewBo bo) {
        FinCash cash = baseMapper.selectById(bo.getId());
        Assert.notNull(cash, "提现记录不存在");
        validBeforeReview(bo, cash);
        // 没问题再进行更新
        cash = MapstructUtils.convert(bo, cash);
        cash.setUserId(LoginHelper.getUserId());
        cash.setUserName(LoginHelper.getUsername());
        cash.setReviewTime(new Date());
        cash.setTransStatus(CashTransStatusEnum.TRADING.getCode());

        boolean flag = baseMapper.updateById(cash) > 0;
        if (flag) {
            CashAuditStatusEnum auditStatus = CashAuditStatusEnum.getByCode(cash.getStatus());
            switch (auditStatus) {
                case REJECT -> {
                    // 驳回 - 解冻、修改状态
                    flag = cashFail(cash);
                }
                case SUCCESS -> {
                    // 成功 - 解冻、扣余额、修改状态
                    if (cash.getIsAuto().equals(IsYesEnum.YES.getCode())) {
                        // 自动打款、直接丢到队列处理 延迟处理，消息队列可能比数据库快
                        AutoPaymentEvent event = new AutoPaymentEvent();
                        event.setCashId(cash.getId());
                        event.setTenantId(cash.getTenantId());
                        scheduledExecutorService.schedule(() -> {
                            AutoPaymentProducer.sendMessage(event);
                        }, 1, TimeUnit.SECONDS);
                    } else {
                        // 手动打款
                        flag = cashSuccess(cash);
                    }
                }
            }
        }
        return flag;
    }

    /**
     * 审核前的数据校验
     */
    private FinCash validBeforeReview(FinCashReviewBo bo, FinCash old) {
        Assert.isTrue(CashAuditStatusEnum.ING.getCode().equals(old.getStatus()), "当前提现已处理");

        CashAuditStatusEnum auditStatus = CashAuditStatusEnum.getByCode(bo.getStatus());
        Assert.notNull(auditStatus, "请选择正确的审批状态");
        Assert.isTrue(!CashAuditStatusEnum.ING.equals(auditStatus), "请选择正确的审批状态");
        switch (auditStatus) {
            case REJECT -> {
                ValidatorUtils.validate(bo, ReviewRejectGroup.class);
            }
            case SUCCESS -> {
                ValidatorUtils.validate(bo, ReviewPassGroup.class);
                // 金额是否匹配
                Long amount = old.getAmount();
                // 默认全部
                Long actualAmount = bo.getActualAmount() == null ? old.getAmount() : bo.getActualAmount();
                if (!Objects.equals(actualAmount, amount)) {
                    throw new ServiceException("打款金额加服务费不等于提现金额");
                }
                // 非自动打款需要填写打款凭证流水号
                String isAuto = bo.getIsAuto() == null ? IsYesEnum.NO.getCode() : bo.getIsAuto();
                // 自动打款配置
                if (isAuto.equals(IsYesEnum.YES.getCode())) {
                    String open = remoteConfigService.selectValueByKey(FinanceConstants.CASH_AUTO_OPEN);
                    Assert.notNull(open, "请先配置自动打款");
                    Assert.isTrue(IsYesEnum.YES.getCode().equals(open), "自动打款开关未开启");
                    // 自动打款限额
                    long cashAutoMaxLong = 0L;
                    String cashAutoMax = remoteConfigService.selectValueByKey(FinanceConstants.CASH_AUTO_MAX);
                    if (StringUtils.isNotBlank(cashAutoMax)) {
                        cashAutoMaxLong = MoneyConvertUtils.yuan2FenLong(cashAutoMax);
                    }
                    // 0 不限额、提现金额必须小于、等于限制金额
                    Assert.isTrue(cashAutoMaxLong == 0L || (bo.getActualAmount() <= cashAutoMaxLong), "自动打款限额为" + cashAutoMax + "元");
                } else {
                    Assert.isTrue(StrUtil.isNotBlank(bo.getFlowNo()), "请填写打款流水号");
                    Assert.isTrue(StrUtil.isNotBlank(bo.getVoucher()), "请上传打款图片");
                }
            }
        }
        return MapstructUtils.convert(bo, old);
    }

    /**
     * 自动打款
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void autoPayment(Long id) {
        FinCash ftCash = baseMapper.selectById(id);
        Assert.notNull(ftCash, "提现单不存在");
        autoPayment(ftCash);
    }

    /**
     * 校验并批量删除提现信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 自动打款
     *
     * @param cash
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoPayment(FinCash cash) {
        log.info("自动打款开始，id：{}", cash.getId());
        RemoteDriverVo driverByDriver = remoteDriverService.getDriverByDriverId(cash.getDriverId());
        if (driverByDriver == null) {
            log.error("自动打款失败，司机不存在!");
            return;
        }
        //司机 加盟的单独打款账户
        String payAccountConfig = null;
        if (DriverTypeEnum.PARTNER.getCode().equals(driverByDriver.getType())) {
            log.info("司机组为加盟， 打款账户走redis配置的账户");
            payAccountConfig = RedisUtils.getCacheObject(com.feidi.xx.cross.common.cache.finance.constants.FinanceCacheConstants.FIN_CASH_PAYOUTS_KEY_PREFIX);
        }

        RedissonClient client = RedisUtils.getClient();
        RLock lock = client.getLock(FinanceCacheConstants.LOCK_AUTO_PAY_KEY + cash.getId());
        try {
            boolean tryLock = lock.tryLock(FinanceCacheConstants.LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
            if (tryLock) {
                // 金额为0的不处理
                if (cash.getAmount().equals(0L)) {
                    return;
                }

                Assert.isTrue(CashAuditStatusEnum.SUCCESS.getCode().equals(cash.getStatus()), "提现单状态无法打款");
                Assert.isTrue(CashTransStatusEnum.TRADING.getCode().equals(cash.getTransStatus()), "交易状态错误");
                // 判断该笔订单是否已经存在历史打款
                List<FinPaymentRecord> record = paymentRecordMapper.getPaymentRecord(cash.getTenantId(), cash.getId(), JoinEnum.CASH.getCode(), null, PaymentStatusEnum.SUCCESS.getCode());
                if (CollUtil.isNotEmpty(record)) {
                    List<Long> list = record.stream().map(FinPaymentRecord::getId).toList();
                    log.error("当前提现: {}已完成，请勿重复提现, 相关支付流水: {}", cash.getId(), CollUtil.join(list, ","));
                    cash.setStatus(CashAuditStatusEnum.SUCCESS.getCode());
                    cash.setRemark("当前提现已完成，请勿重复提现");
                    baseMapper.updateById(cash);
                    return;
                }
                /// TODO 构造打款参数
                TransferBo transferBo = new TransferBo();
                transferBo.setTenantId(cash.getTenantId());
                transferBo.setChannel(PaymentChannelEnum.ALIPAY);

                // APPID取租户ID
                transferBo.setAppId(cash.getTenantId());
                transferBo.setPaymentType(cash.getAccountType());
                transferBo.setOutBizNo(cash.getCashNo());
                transferBo.setName(cash.getName());
                transferBo.setAccount(cash.getAccount());
                transferBo.setTransAmount(cash.getAmount());
                transferBo.setTransTypeEnum(RecordTypeEnum.DRIVER_CASH);
                transferBo.setRemark(RecordTypeEnum.DRIVER_CASH.getInfo());
                if (StrUtil.isNotBlank(payAccountConfig)) {
                    transferBo.setPayConfig(payAccountConfig);
                }

                final TransferVo transfer;
                try {
                    String sysAgentPay = remoteConfigService.selectValueByKey(FinanceConstants.SYS_AGENT_PAY);
                    if (IsYesEnum.YES.getCode().equals(sysAgentPay)) {
                        transferBo.setAgentId(cash.getAgentId());
                    }
                    transfer = transService.transfer(transferBo);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    // 恢复审核中状态
                    cash.setStatus(CashAuditStatusEnum.ING.getCode());
                    cash.setTransStatus(CashTransStatusEnum.TRADE_FAIL.getCode());
                    cash.setRemark("发起交易失败:" + e.getMessage());
                    baseMapper.updateById(cash);
                    return;
                }

                cash.setAppId(transfer.getAppId());
                cash.setRemark(transfer.getMsg());
                /// 处理结果集
                if (transfer.getStatus().equals(SuccessFailEnum.SUCCESS)) {
                    /// TODO 成功
                    cash.setStatus(CashAuditStatusEnum.SUCCESS.getCode());
                    cash.setFlowNo(transfer.getFlowNo());
                    cash.setTransDate(DateUtil.parse(transfer.getTransDate()));
                    cash.setTransStatus(CashTransStatusEnum.TRADE_SUCCESS.getCode());
                    cash.setTransInfo(transfer.getResponseJson());
                    cash.setVoucher(transfer.getOrderId());
                    cashSuccess(cash);
                } else {
                    // 成功或者失败的都要记录信息
                    cash.setTransStatus(CashTransStatusEnum.TRADE_FAIL.getCode());
                    cash.setTransInfo(transfer.getResponseJson());
                    // 错误处理
                    if (TransferErrorEnum.getPayeeErrorList().contains(transfer.getTransferError())) {
                        // 收款账户异常的直接驳回并发短信通知司机
                        cash.setStatus(CashAuditStatusEnum.REJECT.getCode());
                        cashFail(cash);
                        cash.setRemark("提现失败，请检查提现账户信息或状态是否正确");
                        // todo 给司机发短信
                    } else {
                        //恢复成审核中状态，可以再次发起审核
                        cash.setStatus(CashAuditStatusEnum.ING.getCode());
                        try {
                            RemoteDriverVo driver = powCacheManager.getDriverInfoById(cash.getDriverId());
                            List<RemoteUserVo> finance = remoteUserService.selectUserIdsByRoleKey("cwjl");
                            List<String> financeEmails = StreamUtils.toList(finance, RemoteUserVo::getEmail);
                            ThreadUtil.execAsync(() -> sendEmail2Platform(cash, driver, transfer, financeEmails));
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
                baseMapper.updateById(cash);
                // 异步发布事件
                scheduledExecutorService.submit(() -> publishEvent(cash, transferBo, transfer));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("自动提现错误，" + e.getMessage());
        } finally {
            lock.unlock();
        }
    }

    private void publishEvent(FinCash cash, TransferBo transferBo, TransferVo transfer) {
        PaymentRecordEvent paymentRecordEvent = new PaymentRecordEvent();
        paymentRecordEvent.setTenantId(cash.getTenantId());
        paymentRecordEvent.setJoinTable("Fin_cash");
        paymentRecordEvent.setJoinId(cash.getId());
        paymentRecordEvent.setJoinNo(transferBo.getOutBizNo());
        paymentRecordEvent.setPaymentType(transfer.getPaymentType());
        // 记录哪个代理商支付的
        paymentRecordEvent.setAppId(transfer.getAppId());
        paymentRecordEvent.setMchId(transfer.getMchId());
        paymentRecordEvent.setOutBizNo(transferBo.getOutBizNo());
        if (transfer.getTransTypeEnum() != null) {
            RecordTypeEnum recordType = transfer.getTransTypeEnum();
            paymentRecordEvent.setType(recordType.getCode());
        }
        paymentRecordEvent.setCode(transfer.getCode());
        paymentRecordEvent.setMsg(transfer.getMsg());
        paymentRecordEvent.setFlowNo(transfer.getFlowNo());
        paymentRecordEvent.setOrderId(transfer.getOrderId());
        paymentRecordEvent.setAmount(transfer.getAmount());
        paymentRecordEvent.setStatus(transfer.getStatus().getCode());
        paymentRecordEvent.setDirection(DirectionEnum.OUT.getCode());
        paymentRecordEvent.setParamsJson(transfer.getParamsJson());
        paymentRecordEvent.setResponseJson(transfer.getResponseJson());
        paymentRecordEvent.setTradeTime(transfer.getTransDate());
        PaymentRecordProducer.sendMessage(paymentRecordEvent);
    }

    private void sendEmail2Platform(FinCash cash, RemoteDriverVo driver, TransferVo transfer, List<String> emails) {
        String title = "城际顺风车《喜行出行》系统：处理司机提现异常";
        String content = StrUtil.format(
                "<p>各位同学好！</p><p>\t\t我们在处理提现单时遇到了问题，具体情况如下：</p><ul><li>处理人: {}</li><li>处理时间: {}</li><li>提现单编号：{}</li><li>提现金额：{}元</li><li>提现账户名：{}</li><li>提现账户：{}</li><li>司机ID：{}</li><li>司机姓名：{}</li><li>司机手机：{}</li></ul><p><br></p><ul><li>失败原因：{}</li><li>具体错误信息：{}</li></ul><p><br></p><p>\t\t请大家注意查看，如有疑问，请随时联系研发同学，谢谢。</p><p>\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t技术研发部  {}</p><p><br></p><p><br></p><p>---------------------------------------原始错误报文---------------------------------------- </p><p>请求参数：{}</p><p>返回报文：{}</p>"
                ,
                cash.getUserName(),
                DateUtil.formatDateTime(cash.getReviewTime()),
                cash.getId(),
                MoneyConvertUtils.fen2YuanStr(cash.getAmount()),
                cash.getName(),
                cash.getAccount(),
                cash.getDriverId(),
                driver != null ? driver.getName() : null,
                cash.getPhone(),
                transfer.getSubMsg(),
                transfer.getCode() + ": " + transfer.getMsg() + "，" + transfer.getSubCode() + ": " + transfer.getSubMsg(),
                DateUtil.formatDateTime(new Date()),
                transfer.getParamsJson(),
                transfer.getResponseJson()
        );
        if (CollUtil.isEmpty(emails)) {
            log.error("相关通知邮箱为空，内容：{}", content);
        } else {
            MailUtils.sendText(emails, title, content);
        }
    }


    /**
     * 成功 - 更改提现状态和流水状态
     *
     * @param update
     * @return
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional(rollbackFor = Exception.class)
    public Boolean cashSuccess(FinCash update) {
        String tableName = TableInfoHelper.getTableInfo(FinCash.class).getTableName();
        FinFlow flowOut = flowMapper.getByJoin(update.getDriverId(), update.getId(), tableName, DirectionEnum.OUT, FlowTypeEnum.CASHING.getCode());
        Assert.notNull(flowOut, "系统错误，未找到对应的流水，请联系管理员");
        flowOut.setType(FlowTypeEnum.CASH_SUB.getCode());
        // 取备注内容
        flowOut.setRemark(update.getRemark());
        boolean flag = flowMapper.updateById(flowOut) > 0;
        if (flag) {
            FinFlow lastFlow = flowMapper.getLastFlow(update.getDriverId());
            // 应该用最新的流水去更新钱包
            flag = walletMapper.updateByFlowAndAddExpend(lastFlow, flowOut.getCash()) > 0;
            // 更新订单资金流向状态，需要同步处理
            try {
                remoteOrderService.updateOrderFlowStatus(update.getDriverId(), OrderFlowTypeEnum.CASHED.getCode(), OrderFlowTypeEnum.CASHING.getCode());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                log.error("提现完成订单资金流向状态修改失败，{}， {}", JSONUtil.toJsonStr(update), e.getMessage());
            }
            // 推送消息
            scheduledExecutorService.submit(() -> pushMessage(update, PushTypeEnum.WITHDRAWAL_SUCCESS));
        }
        return flag;
    }

    /**
     * 失败 - 解冻
     *
     * @param update
     * @return
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional(rollbackFor = Exception.class)
    public Boolean cashFail(FinCash update) {
        String tableName = TableInfoHelper.getTableInfo(FinCash.class).getTableName();
        FinFlow flowOut = flowMapper.getByJoin(update.getDriverId(), update.getId(), tableName, DirectionEnum.IN, FlowTypeEnum.CASH_ADD.getCode());
        Assert.isNull(flowOut, "系统错误，该笔提现已退回");
        // 原来的提现记录，需要更新状态为失败
        FinFlow cashing = flowMapper.getByJoin(update.getDriverId(), update.getId(), tableName, DirectionEnum.OUT, FlowTypeEnum.CASHING.getCode());
        cashing.setType(FlowTypeEnum.CASH_FAIL.getCode());
        boolean flag = flowMapper.updateById(cashing) > 0;
        if (flag) {
            RemoteDriverVo driverInfo = powCacheManager.getDriverInfoById(update.getDriverId());
            FinFlow lastFlow = flowMapper.getLastFlow(update.getDriverId());
            FinFlow flowIn = FinFlowFactory.createFlow(lastFlow, FinFlowFactory.FlowCreateType.CASH, DirectionEnum.IN, FlowTypeEnum.CASH_ADD, update, driverInfo);
            // 驳回的备注
            flowIn.setRemark(update.getRemark());
            flowIn.setTenantId(update.getTenantId());
            flag = flowMapper.insert(flowIn) > 0;
            if (flag) {
                flag = walletMapper.updateByFlow(flowIn) > 0;
                // 更新订单资金流向状态，需要同步处理
                try {
                    remoteOrderService.updateOrderFlowStatus(update.getDriverId(), OrderFlowTypeEnum.NORMAL.getCode(), OrderFlowTypeEnum.CASHING.getCode());
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    log.error("提现失败订单资金流向状态修改失败，{}， {}", JSONUtil.toJsonStr(update), e.getMessage());
                }
                // 推送消息
                scheduledExecutorService.submit(() -> pushMessage(update, PushTypeEnum.WITHDRAWAL_FAILURE));
            }
        }
        return flag;
    }

    /**
     * 批量提现
     *
     * @param agentIds
     */
    @Override
    public void batchWithdraw(List<Long> agentIds) {
        RemoteDriverQueryBo bo = new RemoteDriverQueryBo();
        bo.setAgentIds(agentIds);
        List<RemoteDriverVo> driverVos = remoteDriverService.queryDriverInfo(bo);
        Map<Long, RemoteDriverVo> driverVoMap = StreamUtils.toMap(driverVos, RemoteDriverVo::getId, Function.identity());
        List<Long> driverIds = StreamUtils.toList(driverVos, RemoteDriverVo::getId);
        if (CollUtil.isNotEmpty(driverVos)) {
            List<FinDrvWallet> wallets = walletMapper.listByDriverId(driverIds);
            List<FinDrvWallet> drvWallets = wallets.parallelStream().filter(e -> e.getBalance() > 0).toList();
            if (CollUtil.isNotEmpty(drvWallets)) {
                List<RemoteDriverAccountVo> accounts = remoteDriverAccountService.getDriverAccounts(driverIds);
                Map<Long, List<RemoteDriverAccountVo>> accountMap = StreamUtils.groupByKey(accounts, RemoteDriverAccountVo::getDriverId);
                for (FinDrvWallet drvWallet : drvWallets) {
                    FinCashApplyBo applyBo = new FinCashApplyBo();
                    RemoteDriverVo driverVo = driverVoMap.get(drvWallet.getDriverId());
                    try {
                        // 全部提现
                        applyBo.setTenantId(drvWallet.getTenantId());
                        applyBo.setAmount(drvWallet.getBalance());
                        applyBo.setAgentId(driverVo.getAgentId());
                        applyBo.setDriverId(driverVo.getId());
                        // 万能令牌
                        applyBo.setAccessToken(ANY_ACCESS_TOKEN);
                        // 账号不存在也可以提现
                        List<RemoteDriverAccountVo> alipay = accountMap.getOrDefault(driverVo.getId(), new ArrayList<>())
                                .stream().filter(e -> e.getType().equals(AccountTypeEnum.ALIPAY.getCode())).toList();
                        if (CollUtil.isNotEmpty(alipay)) {
                            RemoteDriverAccountVo driverAccountVo = alipay.get(0);
                            applyBo.setAccountId(driverAccountVo.getId());
                        }
                        log.info("开始处理司机【{}】提现：{}", driverVo.getName(), JSONUtil.toJsonStr(applyBo));
                        insertByBo(applyBo);
                        log.info("结束处理司机【{}】提现", driverVo.getName());
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        log.error("处理司机【{}】提现异常：{}", driverVo.getName(), JSONUtil.toJsonStr(applyBo));
                    }
                }

            }
        }

    }

    private void pushMessage(FinCash cash, PushTypeEnum pushType) {
        try {
            PushEvent event = new PushEvent(pushType);

            event.setTenantId(cash.getTenantId());

            // 接收人
            event.getReceiverParam().setUserType(UserTypeEnum.DRIVER_USER.getUserType());
            event.getReceiverParam().setUserId(cash.getDriverId());

            // 语音
            if (pushType.getVoice() != null) {
                RemoteDictDataVo dataVo = remoteDictService.selectDictDataByTypeAndLabel(VoiceConstant.SYSTEM_VOICE_BROADCAST, pushType.getVoice());
                if (dataVo != null) {
                    PushEvent.GtParam gtParam = event.getGtParam();
                    gtParam.setPayload(dataVo.getDictValue());
                    event.setGtParam(gtParam);
                }
            }

            List<String> cid = remoteImService.getCid(UserTypeEnum.DRIVER_USER.getUserType(), cash.getDriverId());
            event.setCids(cid);
            PushMsgProducer.sendMessage(event);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("提现消息推送异常");
        }
    }
}
