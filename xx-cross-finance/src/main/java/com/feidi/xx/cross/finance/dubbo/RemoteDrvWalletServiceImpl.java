package com.feidi.xx.cross.finance.dubbo;

import com.feidi.xx.common.core.enums.DirectionEnum;
import com.feidi.xx.common.core.enums.JoinEnum;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.cross.common.cache.power.manager.PowCacheManager;
import com.feidi.xx.cross.common.enums.finance.CashAuditStatusEnum;
import com.feidi.xx.cross.common.enums.finance.FlowTypeEnum;
import com.feidi.xx.cross.common.enums.order.RebateStatusEnum;
import com.feidi.xx.cross.finance.mq.event.FinDrvWalletFlowEvent;
import com.feidi.xx.cross.finance.mq.producer.FinDrvWalletFlowProducer;
import com.feidi.xx.cross.finance.api.RemoteDrvWalletService;
import com.feidi.xx.cross.finance.api.domain.bo.RemoteRebateBo;
import com.feidi.xx.cross.finance.api.domain.vo.RemoteCashVo;
import com.feidi.xx.cross.finance.api.domain.vo.RemoteWalletVo;
import com.feidi.xx.cross.finance.domain.FinCash;
import com.feidi.xx.cross.finance.domain.FinDrvWallet;
import com.feidi.xx.cross.finance.domain.FinFlow;
import com.feidi.xx.cross.finance.domain.factory.FinFlowFactory;
import com.feidi.xx.cross.finance.mapper.FinCashMapper;
import com.feidi.xx.cross.finance.mapper.FinDrvWalletMapper;
import com.feidi.xx.cross.finance.mapper.FinFlowMapper;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/***
 *  司机钱包管理
 *
 * <AUTHOR>
 * @version 1.0.0
 * @create 2024/8/31 13:11
 **/
@Slf4j
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteDrvWalletServiceImpl implements RemoteDrvWalletService {

    private final FinDrvWalletFlowProducer drvWalletFlowProducer;
    private final FinDrvWalletMapper drvWalletMapper;
    private final FinCashMapper cashMapper;
    private final FinFlowMapper flowMapper;
    private final PowCacheManager powCacheManager;

    @Override
    public RemoteWalletVo getWallet(Long driverId) {
        FinDrvWallet drvWallet = drvWalletMapper.selectByDriverId(driverId);
        if (drvWallet == null) {
            drvWallet = new FinDrvWallet();
            drvWallet.setDriverId(driverId);
            drvWalletMapper.insert(drvWallet);
        }
        RemoteWalletVo vo = BeanUtils.copyProperties(drvWallet, RemoteWalletVo.class);
//        RemoteWalletVo vo = MapstructUtils.convert(drvWallet, RemoteWalletVo.class);
        RemoteCashVo cashVo = cashStatus(driverId);
        vo.setCashing(cashVo.getCashingAmount());
        return vo;
    }

    /**
     * 创建司机钱包
     *
     * @param driverId
     * @return
     */
    @Override
    public void makeDrvWallet(Long driverId) {
        FinDrvWallet drvWallet = drvWalletMapper.selectByDriverId(driverId);
        if (drvWallet == null) {
            drvWallet = new FinDrvWallet();
            drvWallet.setDriverId(driverId);
            drvWalletMapper.insert(drvWallet);
        }
    }

    @Override
    public RemoteCashVo cashStatus(Long driverId) {
        RemoteCashVo vo = new RemoteCashVo();
        List<FinCash> cashes = cashMapper.listByDriverId(driverId);
        Map<String, List<FinCash>> map = cashes.stream().collect(Collectors.groupingBy(FinCash::getStatus));
        long total = map.getOrDefault(CashAuditStatusEnum.SUCCESS.getCode(), Collections.emptyList())
                .stream().map(FinCash::getAmount).reduce(0L, Long::sum);
        long cashing = map.getOrDefault(CashAuditStatusEnum.ING.getCode(), Collections.emptyList())
                .stream().map(FinCash::getAmount).reduce(0L, Long::sum);
        vo.setTotalAmount(total);
        vo.setCashingAmount(cashing);
        return vo;
    }

    /**
     * 订单客诉
     *
     * @param remoteRebateBo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean complain(RemoteRebateBo remoteRebateBo) {
        boolean flag;
        FinFlow lastFlow = flowMapper.getLastFlow(remoteRebateBo.getDriverId());
        FinFlow nextFlow = FinFlowFactory.createFlow(lastFlow, FinFlowFactory.FlowCreateType.COMPLAIN, DirectionEnum.OUT, FlowTypeEnum.ORDER_SUB, remoteRebateBo);
        // 防止通过第三方操作回调接口，导致租户id为空
        nextFlow.setTenantId(remoteRebateBo.getTenantId());
        nextFlow.setCreateBy(remoteRebateBo.getDriverId());
        flag = flowMapper.insert(nextFlow) > 0;

        // 邀请客诉
        long inviteComplainPrice = 0L;
        if (flag && ObjectUtils.isNotNull(remoteRebateBo.getInviteComplainPrice()) && remoteRebateBo.getInviteComplainPrice() > 0) {
            inviteComplainPrice = remoteRebateBo.getInviteComplainPrice();
            FinFlow inviteLastFlow = flowMapper.getLastFlow(remoteRebateBo.getInviteDriverId());
            // 当接单司机和邀请司机不是同一个人，重新查询邀请司机
            if (!Objects.equals(remoteRebateBo.getDriverId(), remoteRebateBo.getInviteDriverId())) {
                RemoteDriverVo remoteDriverVo = powCacheManager.getDriverInfoById(remoteRebateBo.getInviteDriverId());
                if (ObjectUtils.isNotNull(remoteDriverVo)) {
                    remoteRebateBo.setDriverName(remoteDriverVo.getName());
                    remoteRebateBo.setDriverPhone(remoteDriverVo.getPhone());
                }
            }
            FinFlow nextInviteFlow = FinFlowFactory.createFlow(inviteLastFlow, FinFlowFactory.FlowCreateType.INVITE_COMPLAIN, DirectionEnum.OUT, FlowTypeEnum.COMPENSATION_REFUND, remoteRebateBo);
            // 防止通过第三方操作回调接口，导致租户id为空
            nextInviteFlow.setTenantId(remoteRebateBo.getTenantId());
            nextInviteFlow.setCreateBy(remoteRebateBo.getInviteDriverId());
            flowMapper.insert(nextInviteFlow);
            if (!Objects.equals(remoteRebateBo.getDriverId(), remoteRebateBo.getInviteDriverId())) {
                flag = drvWalletMapper.updateByFlow(nextInviteFlow) > 0;

                // 司机钱包流水
                FinDrvWalletFlowEvent inviteWalletFlowEvent = buildFinDrvWalletFlowEvent(nextInviteFlow, 0L, -inviteComplainPrice, 0L);
                drvWalletFlowProducer.sendMessage(inviteWalletFlowEvent);
                inviteComplainPrice = 0L;
            } else {
                nextFlow = nextInviteFlow;
            }
        }

        // 客诉状态为返利中，则更新
        if (Objects.equals(remoteRebateBo.getRebateStatus(), RebateStatusEnum.ING.getCode())) {
            // 变更原来的流水中的入账状态 入账中 -> 已关闭
            FinFlow finFlow = flowMapper.queryByDriverIdAndJoinIdWithType(remoteRebateBo.getDriverId(), remoteRebateBo.getOrderId(), JoinEnum.ORDER.getCode(),
                    DirectionEnum.IN.getCode(), FlowTypeEnum.ORDER_FREEZE_ADD.getCode());

            if (ObjectUtils.isNotNull(finFlow)) {
                finFlow.setType(FlowTypeEnum.ORDER_CLOSED.getCode());
                finFlow.setUpdateTime(DateUtils.getNowDate());
                flowMapper.updateById(finFlow);
            }
        }

        if (flag) {
            flag = drvWalletMapper.updateByFlow(nextFlow) > 0;

            // 司机钱包流水
            FinDrvWalletFlowEvent inviteWalletFlowEvent = buildFinDrvWalletFlowEvent(nextFlow, -remoteRebateBo.getDriverComplainPrice(), -inviteComplainPrice, 0L);
            drvWalletFlowProducer.sendMessage(inviteWalletFlowEvent);
        }
        return flag;
    }

    /**
     * 订单返利-资金冻结
     *
     * @param remoteRebateBo 订单返利参数
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean orderFreezeAdd(RemoteRebateBo remoteRebateBo) {
        boolean flag;
        FinFlow lastFlow = flowMapper.getLastFlow(remoteRebateBo.getDriverId());
        FinFlow nextFlow = FinFlowFactory.createFlow(lastFlow, FinFlowFactory.FlowCreateType.FREEZE_ADD, DirectionEnum.IN, FlowTypeEnum.ORDER_FREEZE_ADD, remoteRebateBo);
        // 防止通过第三方操作回调接口，导致租户id为空
        nextFlow.setTenantId(remoteRebateBo.getTenantId());
        nextFlow.setCreateBy(remoteRebateBo.getDriverId());
        flag = flowMapper.insert(nextFlow) > 0;

        long rewardProfit = 0L;
        if (flag && ObjectUtils.isNotNull(remoteRebateBo.getInviteProfit()) && remoteRebateBo.getInviteProfit() > 0) {
            rewardProfit = remoteRebateBo.getInviteProfit();
            FinFlow inviteLastFlow = flowMapper.getLastFlow(remoteRebateBo.getInviteDriverId());

            // 当接单司机和邀请司机不是同一个人，重新查询邀请司机
            if (!Objects.equals(remoteRebateBo.getDriverId(), remoteRebateBo.getInviteDriverId())) {
                RemoteDriverVo remoteDriverVo = powCacheManager.getDriverInfoById(remoteRebateBo.getInviteDriverId());
                if (ObjectUtils.isNotNull(remoteDriverVo)) {
                    remoteRebateBo.setDriverName(remoteDriverVo.getName());
                    remoteRebateBo.setDriverPhone(remoteDriverVo.getPhone());
                }
            }
            FinFlow inviteNextFlow = FinFlowFactory.createFlow(inviteLastFlow, FinFlowFactory.FlowCreateType.INVITE_FREEZE_ADD, DirectionEnum.IN, FlowTypeEnum.INVITE_ORDER_REWARD, remoteRebateBo);
            // 防止通过第三方操作回调接口，导致租户id为空
            inviteNextFlow.setTenantId(remoteRebateBo.getTenantId());
            inviteNextFlow.setCreateBy(remoteRebateBo.getDriverId());
            flowMapper.insert(inviteNextFlow);

            // 邀请司机和接单司机如果不是同一人，则需要更新邀请司机钱包金额
            if (!Objects.equals(remoteRebateBo.getDriverId(), remoteRebateBo.getInviteDriverId())) {
                flag = drvWalletMapper.updateByFlow(inviteNextFlow) > 0;

                // 司机钱包流水
                FinDrvWalletFlowEvent inviteWalletFlowEvent = buildFinDrvWalletFlowEvent(inviteNextFlow, 0L, rewardProfit, 0L);
                drvWalletFlowProducer.sendMessage(inviteWalletFlowEvent);
                rewardProfit = 0L;
            } else {
                nextFlow = inviteNextFlow;
            }
        }

        if (flag) {
            flag = drvWalletMapper.updateByFlow(nextFlow) > 0;

            // 司机钱包流水
            FinDrvWalletFlowEvent inviteWalletFlowEvent = buildFinDrvWalletFlowEvent(nextFlow, remoteRebateBo.getDriverProfit(), rewardProfit, 0L);
            drvWalletFlowProducer.sendMessage(inviteWalletFlowEvent);
        }
        return flag;
    }

    /**
     * 记录司机可结算订单的总利润流水
     *
     * @param remoteRebateBo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean insertTotalProfitFlow(RemoteRebateBo remoteRebateBo) {
        FinFlow lastFlow = flowMapper.getLastFlow(remoteRebateBo.getDriverId());
        FinFlow nextFlow = FinFlowFactory.createFlow(lastFlow, FinFlowFactory.FlowCreateType.REBATE, DirectionEnum.IN, FlowTypeEnum.ORDER_REBATE, remoteRebateBo);
        // 防止通过第三方操作回调接口，导致租户id为空
        nextFlow.setTenantId(remoteRebateBo.getTenantId());
        nextFlow.setCreateBy(remoteRebateBo.getDriverId());
        nextFlow.setRemark(remoteRebateBo.getRemark());
        nextFlow.setRelatedOrder(remoteRebateBo.getRelatedOrder());
        boolean flag = flowMapper.insert(nextFlow) > 0;

        if (flag) {
            // 更新司机钱包
            flag = drvWalletMapper.updateByFlow(nextFlow) > 0;
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertTotalProfitFlowForResell(RemoteRebateBo remoteRebateBo) {
        FinFlow lastFlow = flowMapper.getLastFlow(remoteRebateBo.getResellDriverId());
        FinFlow nextFlow = FinFlowFactory.createFlow(lastFlow, FinFlowFactory.FlowCreateType.RESELL_REBATE, DirectionEnum.IN, FlowTypeEnum.RESELL_ORDER_REBATE, remoteRebateBo);
        // 防止通过第三方操作回调接口，导致租户id为空
        nextFlow.setTenantId(remoteRebateBo.getTenantId());
        nextFlow.setCreateBy(remoteRebateBo.getResellDriverId());
        nextFlow.setRemark(remoteRebateBo.getRemark());
        nextFlow.setRelatedOrder(remoteRebateBo.getRelatedOrder());
        boolean flag = flowMapper.insert(nextFlow) > 0;

        if (flag) {
            // 更新司机钱包
            flag = drvWalletMapper.updateByFlow(nextFlow) > 0;
        }
        return flag;
    }

    /**
     * 更改钱包状态
     *
     * @param driverId 司机id
     * @param status   钱包状态
     * @return
     */
    @Override
    public Boolean updateWallerStatus(Long driverId, String status) {
        //钱包禁用或开启
        FinDrvWallet FinDrvWallet = drvWalletMapper.selectByDriverId(driverId);
        if (!FinDrvWallet.getStatus().equals(status) && StatusEnum.getByCode(status) != null) {
            FinDrvWallet.setStatus(status);
        }
        return drvWalletMapper.updateByBo(FinDrvWallet) > 0;
    }

    /**
     * 订单返利-订单转卖收益冻结
     *
     * @param remoteRebateBo
     * @return
     */
    @Override
    public boolean orderFreezeAddForResell(RemoteRebateBo remoteRebateBo) {
        boolean flag;
        FinFlow lastFlow = flowMapper.getLastFlow(remoteRebateBo.getResellDriverId());
        FinFlow nextFlow = FinFlowFactory.createFlow(lastFlow, FinFlowFactory.FlowCreateType.RESELL_FREEZE_ADD, DirectionEnum.IN, FlowTypeEnum.RESELL_ORDER_FREEZE_ADD, remoteRebateBo);
        // 防止通过第三方操作回调接口，导致租户id为空
        nextFlow.setTenantId(remoteRebateBo.getTenantId());
        nextFlow.setCreateBy(remoteRebateBo.getResellDriverId());
        flag = flowMapper.insert(nextFlow) > 0;

        if (flag) {
            flag = drvWalletMapper.updateByFlow(nextFlow) > 0;

            // 司机钱包流水
            FinDrvWalletFlowEvent inviteWalletFlowEvent = buildFinDrvWalletFlowEvent(nextFlow, 0L, 0L, remoteRebateBo.getResellDriverProfit());
            drvWalletFlowProducer.sendMessage(inviteWalletFlowEvent);
        }
        return flag;
    }

    /**
     * 订单转卖客诉
     *
     * @param remoteRebateBo
     * @return
     */
    @Override
    public boolean resellComplain(RemoteRebateBo remoteRebateBo) {
        // 当前司机的最新的一条流水
        FinFlow resellLastFlow = flowMapper.getLastFlow(remoteRebateBo.getResellDriverId());
        FinFlow nextResellFlow = FinFlowFactory.createFlow(resellLastFlow, FinFlowFactory.FlowCreateType.RESELL_COMPLAIN, DirectionEnum.OUT, FlowTypeEnum.RESELL_ORDER_SUB, remoteRebateBo);
        // 防止通过第三方操作回调接口，导致租户id为空
        nextResellFlow.setTenantId(remoteRebateBo.getTenantId());
        nextResellFlow.setCreateBy(remoteRebateBo.getResellDriverId());
        boolean flag = flowMapper.insert(nextResellFlow) > 0;

        // 客诉状态为返利中，则更新
        if (Objects.equals(remoteRebateBo.getRebateStatus(), RebateStatusEnum.ING.getCode())) {
            // 变更原来的流水中的入账状态 入账中 -> 已关闭
            FinFlow finFlow = flowMapper.queryByDriverIdAndJoinIdWithType(remoteRebateBo.getResellDriverId(), remoteRebateBo.getOrderId(), JoinEnum.ORDER.getCode(),
                    DirectionEnum.IN.getCode(), FlowTypeEnum.RESELL_ORDER_FREEZE_ADD.getCode());

            if (ObjectUtils.isNotNull(finFlow)) {
                finFlow.setType(FlowTypeEnum.RESELL_ORDER_CLOSED.getCode());
                finFlow.setUpdateTime(DateUtils.getNowDate());
                flowMapper.updateById(finFlow);
            }
        }

        if (flag) {
            flag = drvWalletMapper.updateByFlow(nextResellFlow) > 0;
            // 司机钱包流水
            FinDrvWalletFlowEvent inviteWalletFlowEvent = buildFinDrvWalletFlowEvent(nextResellFlow, 0L, 0L, -remoteRebateBo.getResellDriverProfit());
            drvWalletFlowProducer.sendMessage(inviteWalletFlowEvent);
        }
        return flag;
    }

    /**
     * 构建司机钱包流水事件
     *
     * @param flow 最新流水
     * @param orderProfit 订单收益
     * @param rewardProfit 奖励收益
     * @param resellProfit 订单专卖收益
     * @return
     */
    private FinDrvWalletFlowEvent buildFinDrvWalletFlowEvent(FinFlow flow, Long orderProfit, Long rewardProfit, Long resellProfit) {
        log.info("[构建司机钱包流水事件] - 订单收益：{}，奖励收益：{}，订单专卖收益：{}， 流水：【{}】", orderProfit, rewardProfit, resellProfit, JsonUtils.toJsonString(flow));
        return FinDrvWalletFlowEvent.builder()
                .tenantId(flow.getTenantId())
                .flowType(flow.getType())
                .orderId(flow.getJoinId())
                .driverId(flow.getDriverId())
                .orderNo(flow.getJoinNo())
                .agentId(flow.getAgentId())
                .orderProfit(orderProfit)
                .rewardProfit(rewardProfit)
                .resellProfit(resellProfit)
                .balance(flow.getAfterCash())
                .freeze(flow.getAfterAmount() - flow.getAfterCash())
                .accountAmount(flow.getAfterAmount())
                .build();
    }
}

