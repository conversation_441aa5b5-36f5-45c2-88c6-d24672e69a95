package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.finance.domain.FinOrderFee;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 订单应收服务费视图对象 fin_order_fee
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinOrderFee.class)
public class FinOrderFeeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 代理商名称
     */
    @ExcelProperty(value = "代理商名称")
    private String agentName;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机id")
    private Long driverId;

    /**
     * 司机姓名
     */
    @ExcelProperty(value = "司机姓名")
    private String driverName;

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 订单渠道
     */
    @ExcelProperty(value = "订单渠道")
    private String platformCode;

    /**
     * 订单渠道
     */
    private String platformCodeText;

    /**
     * 订单金额
     */
    @ExcelProperty(value = "订单金额")
    private Long orderPrice;

    /**
     * 实际支付金额
     */
    @ExcelProperty(value = "实际支付金额")
    private Long payPrice;

    /**
     * 账单对账类型[FinanceTypeEnum]
     */
    @ExcelProperty(value = "账单对账类型[FinanceTypeEnum]")
    private String financeType;

    /**
     * 账单对账类型
     */
    private String financeTypeText;

    /**
     * 下单类型[CreateModelEnum]
     */
    @ExcelProperty(value = "下单类型[CreateModelEnum]")
    private String createModel;

    /**
     * 下单类型
     */
    private String createModelText;

    /**
     * 代收款
     */
    @ExcelProperty(value = "代收款")
    private Long proxyAmount;

    /**
     * 奖励金额
     */
    @ExcelProperty(value = "奖励金额")
    private Long rewardAmount;

    /**
     * 客诉金额
     */
    @ExcelProperty(value = "客诉金额")
    private Long complainAmount;

    /**
     * 技术服务比例
     */
    @ExcelProperty(value = "技术服务比例")
    private BigDecimal technicalServiceRate;

    /**
     * 技术服务费
     */
    @ExcelProperty(value = "技术服务费")
    private Long technicalServiceFee;

    /**
     * 应收信息服务费比例
     */
    @ExcelProperty(value = "应收信息服务费比例")
    private BigDecimal infoServiceRate;

    /**
     * 应收信息服务费
     */
    @ExcelProperty(value = "应收信息服务费")
    private Long infoServiceFee;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
