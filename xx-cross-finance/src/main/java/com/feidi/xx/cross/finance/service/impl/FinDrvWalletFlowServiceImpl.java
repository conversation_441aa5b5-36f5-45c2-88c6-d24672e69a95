package com.feidi.xx.cross.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.common.enums.finance.FlowTypeEnum;
import com.feidi.xx.cross.finance.domain.FinDrvWalletFlow;
import com.feidi.xx.cross.finance.domain.FinFlow;
import com.feidi.xx.cross.finance.domain.bo.FinDrvWalletFlowBo;
import com.feidi.xx.cross.finance.domain.vo.FinDrvWalletFlowVo;
import com.feidi.xx.cross.finance.mapper.FinDrvWalletFlowMapper;
import com.feidi.xx.cross.finance.service.IFinDrvWalletFlowService;
import com.feidi.xx.cross.finance.service.IFinFlowService;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.RemoteDriverService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 司机钱包流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@RequiredArgsConstructor
@Service
public class FinDrvWalletFlowServiceImpl implements IFinDrvWalletFlowService {


    private final IFinFlowService finFlowService;
    private final FinDrvWalletFlowMapper baseMapper;
    @DubboReference
    private final RemoteAgentService remoteAgentService;
    @DubboReference
    private final RemoteDriverService remoteDriverService;


    /**
     * 查询司机钱包流水
     *
     * @param id 主键
     * @return 司机钱包流水
     */
    @Override
    public FinDrvWalletFlowVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询司机钱包流水列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机钱包流水分页列表
     */
    @Override
    public TableDataInfo<FinDrvWalletFlowVo> queryPageList(FinDrvWalletFlowBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinDrvWalletFlow> lqw = buildQueryWrapper(bo);
        Page<FinDrvWalletFlowVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        if (CollUtils.isNotEmpty(result.getRecords())) {
            Set<Long> driverIds = result.getRecords().stream().map(FinDrvWalletFlowVo::getDriverId).collect(Collectors.toSet());
            Map<Long, String> driverId2NameMap = remoteDriverService.getDriverByIds(new ArrayList<>(driverIds))
                    .stream().collect(Collectors.toMap(RemoteDriverVo::getId, RemoteDriverVo::getName));
            Map<Long, String> agentId2NameMap = remoteAgentService.getAllAgentInfo()
                    .stream().collect(Collectors.toMap(RemoteAgentVo::getId, RemoteAgentVo::getCompanyName));
            result.getRecords().forEach(e -> {
                e.setAgentName(agentId2NameMap.getOrDefault(e.getAgentId(), ""));
                e.setDriverName(driverId2NameMap.getOrDefault(e.getDriverId(), ""));
            });
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的司机钱包流水列表
     *
     * @param bo 查询条件
     * @return 司机钱包流水列表
     */
    @Override
    public List<FinDrvWalletFlowVo> queryList(FinDrvWalletFlowBo bo) {
        LambdaQueryWrapper<FinDrvWalletFlow> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinDrvWalletFlow> buildQueryWrapper(FinDrvWalletFlowBo bo) {
        LambdaQueryWrapper<FinDrvWalletFlow> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), FinDrvWalletFlow::getOrderNo, bo.getOrderNo());
        lqw.eq(bo.getAgentId() != null, FinDrvWalletFlow::getAgentId, bo.getAgentId());
        lqw.eq(bo.getDriverId() != null, FinDrvWalletFlow::getDriverId, bo.getDriverId());
        if (StringUtils.isNotBlank(bo.getStartCreateTime()) && StringUtils.isNotBlank(bo.getEndCreateTime())) {
            lqw.between(FinDrvWalletFlow::getCreateTime, bo.getStartCreateTime(), bo.getEndCreateTime());
        }
        return lqw;
    }

    /**
     * 新增司机钱包流水
     *
     * @param bo 司机钱包流水
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinDrvWalletFlowBo bo) {
        FinDrvWalletFlow add = MapstructUtils.convert(bo, FinDrvWalletFlow.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改司机钱包流水
     *
     * @param bo 司机钱包流水
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinDrvWalletFlowBo bo) {
        FinDrvWalletFlow update = MapstructUtils.convert(bo, FinDrvWalletFlow.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinDrvWalletFlow entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除司机钱包流水信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 添加司机钱包流水
     *
     * @param bo
     */
    @Override
    public void addDrvWalletFlow(FinDrvWalletFlowBo bo) {
        FinDrvWalletFlow finDrvWalletFlow = BeanUtils.copyProperties(bo, FinDrvWalletFlow.class);

        List<String> flowTypes = Stream.of(FlowTypeEnum.CASHING.getCode(), FlowTypeEnum.CASH_SUB.getCode()).collect(Collectors.toList());
        List<FinFlow> finFlows = finFlowService.queryByOrderIdsAndTypes(Collections.singletonList(bo.getId()), flowTypes);
        if (CollUtils.isNotEmpty(finFlows)) {
            long withdrawalAmount = finFlows.stream().filter(e -> Objects.equals(e.getType(), FlowTypeEnum.CASHING.getCode()))
                    .mapToLong(FinFlow::getAmount).sum();
            long expend = finFlows.stream().filter(e -> Objects.equals(e.getType(), FlowTypeEnum.CASH_SUB.getCode()))
                    .mapToLong(FinFlow::getAmount).sum();
            finDrvWalletFlow.setWithdrawalAmount(withdrawalAmount);
            finDrvWalletFlow.setExpend(expend);
        }
        baseMapper.insert(finDrvWalletFlow);
    }
}
