package com.feidi.xx.cross.finance.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.finance.domain.bo.FinBillDetailBo;
import com.feidi.xx.cross.finance.domain.vo.FinBillDetailVo;

import java.util.Collection;
import java.util.List;

/**
 * 流水明细Service接口
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface IFinBillDetailService {

    /**
     * 查询流水明细
     *
     * @param id 主键
     * @return 流水明细
     */
    FinBillDetailVo queryById(Long id);

    /**
     * 分页查询流水明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 流水明细分页列表
     */
    TableDataInfo<FinBillDetailVo> queryPageList(FinBillDetailBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的流水明细列表
     *
     * @param bo 查询条件
     * @return 流水明细列表
     */
    List<FinBillDetailVo> queryList(FinBillDetailBo bo);

    /**
     * 新增流水明细
     *
     * @param bo 流水明细
     * @return 是否新增成功
     */
    Boolean insertByBo(FinBillDetailBo bo);

    /**
     * 修改流水明细
     *
     * @param bo 流水明细
     * @return 是否修改成功
     */
    Boolean updateByBo(FinBillDetailBo bo);

    /**
     * 校验并批量删除流水明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 订单对账明细
     */
    void orderBillDetail();
}
