package com.feidi.xx.cross.finance.mq;

import com.alibaba.fastjson.JSON;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.idempotent.annotation.NoMQDuplicateConsume;
import com.feidi.xx.common.payment.mq.PaymentRecordEvent;
import com.feidi.xx.common.rocketmq.base.MessageWrapper;
import com.feidi.xx.common.rocketmq.constants.MQTopicConstants;
import com.feidi.xx.common.tenant.helper.TenantHelper;
import com.feidi.xx.cross.common.constant.finance.FinanceMQConstants;
import com.feidi.xx.cross.finance.domain.bo.FinPaymentRecordBo;
import com.feidi.xx.cross.finance.service.IFinCashService;
import com.feidi.xx.cross.finance.service.IFinPaymentRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 自动打款消费者
 */
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = FinanceMQConstants.FINANCE_AUTO_PAYMENT_TOPIC,
        consumerGroup = FinanceMQConstants.FINANCE_AUTO_PAYMENT_CG_TOPIC
)
@Slf4j(topic = "AutoPaymentConsumer")
public class AutoPaymentConsumer implements RocketMQListener<MessageWrapper<AutoPaymentEvent>> {

    private final IFinCashService cashService;

    @NoMQDuplicateConsume(
            keyPrefix = "global:finance-auto-payment:",
            key = "#messageWrapper.keys",
            keyTimeout = 600
    )
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void onMessage(MessageWrapper<AutoPaymentEvent> messageWrapper) {
        log.info("[消费者] 自动打款消息 - 执行消费逻辑，消息体：{}", JSON.toJSONString(messageWrapper));
        AutoPaymentEvent event = messageWrapper.getMessage();
        TenantHelper.setDynamic(event.getTenantId());
        try {
            cashService.autoPayment(event.getCashId());
        } finally {
            TenantHelper.clearDynamic();
        }
    }
}