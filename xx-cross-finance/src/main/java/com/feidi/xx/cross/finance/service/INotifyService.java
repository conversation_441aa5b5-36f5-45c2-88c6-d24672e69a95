package com.feidi.xx.cross.finance.service;

import com.feidi.xx.common.core.enums.PaymentChannelEnum;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.RequestBody;

public interface INotifyService {

    /**
     * 支付回调
     * @param channel
     * @param request
     * @param response
     * @param requestBody
     * @return
     */
    boolean payment(PaymentChannelEnum channel, HttpServletRequest request, HttpServletResponse response, String requestBody, String appId);

    /**
     * 退款回调
     * @param channel
     * @param request
     * @param response
     * @param requestBody
     * @return
     */
    boolean refund(PaymentChannelEnum channel, HttpServletRequest request, HttpServletResponse response, String requestBody, String appId);
}
