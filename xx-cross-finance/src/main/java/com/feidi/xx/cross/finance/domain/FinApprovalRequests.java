package com.feidi.xx.cross.finance.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 账单审批记录对象 fin_approval_requests
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("fin_approval_requests")
public class FinApprovalRequests extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 审批ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 审批名称
     */
    private String name;

    /**
     * 审批状态：0待审批，1已通过，2已驳回，3已撤销
     */
    private Integer status;

    /**
     * 调账金额，可为负，单位分
     */
    private Long amount;

    /**
     * 审核时间，审核通过时间
     */
    private Date approvalTime;

    /**
     * 审批人ID
     */
    private Long approverId;
    /**
     * 审核人姓名
     */
    private String approverName;

    /**
     * 审核人类型[UserTypeEnum]
     */
    private String approvalUserType;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 审核备注
     */
    private String reviewNotes;
}
