package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.cross.common.enums.finance.ApprovalStatusEnum;
import com.feidi.xx.cross.finance.domain.FinApprovalRequests;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 账单审批记录视图对象 fin_approval_requests
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinApprovalRequests.class)
public class FinApprovalRequestsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 审批ID
     */
    @ExcelProperty(value = "审批ID")
    private Long id;

    /**
     * 审批名称
     */
    @ExcelProperty(value = "审批名称")
    private String name;

    /**
     * 审批状态：0待审批，1已通过，2已驳回，3已撤销
     */
    @ExcelProperty(value = "审批状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = ApprovalStatusEnum.class)
    private Integer status;

    @JsonProperty("statusText")
    public String getStatusText() {
        if (status == null) {
            return null;
        }
        return ApprovalStatusEnum.fromCode(status).getInfo();
    }

    /**
     * 调账金额，可为负，单位分
     */
    @ExcelProperty(value = "调账金额")
    private Long amount;

    /**
     * 审核时间，审核通过时间
     */
    @ExcelProperty(value = "审核时间")
    private Date approvalTime;

    /**
     * 审批人ID
     */
    private Long approverId;

    /**
     * 审核人姓名
     */
    @ExcelProperty(value = "审批人")
    private String approverName;

    /**
     * 审核人类型[UserTypeEnum]
     */
    @ExcelProperty(value = "审核人类型[UserTypeEnum]")
    private String approvalUserType;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "申请时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    private Long createBy;

    @ExcelProperty(value = "申请人")
    private String createName;

    private Long updateBy;

    private String updateName;

    /**
     * (批次）审批按司机分组列表
     */
    private List<FinApprovalRequestDetailsVo> details = new ArrayList<>();

    /**
     * 审批日志
     */
    private List<FinApprovalLogVo> logs = new ArrayList<>();

    /**
     * 审核备注
     */
    private String reviewNotes;
}
