package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.finance.domain.FinDrvWallet;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 司机钱包视图对象 Fin_drv_wallet
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinDrvWallet.class, convertGenerate = false)
public class FinDrvWalletVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机id")
    private Long driverId;

    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 司机头像
     */
    private String driverAvatar;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 累计金额(佣金收益+奖励收益+订单专卖你收益+账户调整金额)
     */
    private Long totalAmount;

    /**
     * 佣金收益
     */
    private Long orderAmount;

    /**
     * 奖励收益
     */
    private Long rewardAmount;


    /**
     * 订单转卖收益
     */
    private Long resellAmount;

    /**
     * 账户调整金额
     */
    private Long adjustAmount;

    /**
     * 账户余额(冻结金额+可提现金额)
     */
    private Long accountAmount;

    /**
     * 提现中金额
     */
    private Long withdrawalAmount;

    /**
     * 余额 单位：分
     */
    @ExcelProperty(value = "余额 单位：分")
    private Long total;

    /**
     * 可提现余额 单位：分
     */
    @ExcelProperty(value = "可提现余额 单位：分")
    private Long balance;

    /**
     * 冻结金额 单位：分
     */
    @ExcelProperty(value = "冻结金额 单位：分")
    private Long freeze;

    /**
     * 已出账 单位：分
     */
    @ExcelProperty(value = "已出账 单位：分")
    private Long expend;

    /**
     * 提现申请次数
     */
    private int cashApplyTimes = 0;

    /**
     * 转账次数
     */
    private int transferTimes = 0;

    /**
     * 是否可提现 true是 false否
     */
    private boolean cashApplicable = true;

    /**
     * 是否可转账 true是 false否
     */
    private boolean transferApplicable = true;

    /**
     * 是否需要修改密码 true是 false否
     */
    private boolean changePwd = false;

    /**
     * 是否需要添加账号 true是 false否
     */
    private boolean initAccount = false;

    /**
     * 司机端版本发布状态【Y.发布中|N.未发布】
     */
    private String version = "Y";

}
