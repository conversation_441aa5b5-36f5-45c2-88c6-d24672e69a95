package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.finance.domain.FinApprovalLog;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 账单审批操作记录视图对象 fin_approval_log
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinApprovalLog.class)
public class FinApprovalLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 关联账单审批ID
     */
    @ExcelProperty(value = "关联账单审批ID")
    private Long approvalId;

    /**
     * 操作人ID
     */
    @ExcelProperty(value = "操作人ID")
    private Long operatorId;
    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作类型，如提交、审批通过、驳回等
     */
    @ExcelProperty(value = "操作类型，如提交、审批通过、驳回等")
    private String operation;

    /**
     * 操作时间
     */
    @ExcelProperty(value = "操作时间")
    private Date operateTime;

    /**
     * 操作备注
     */
    @ExcelProperty(value = "操作备注")
    private String remark;

    /**
     * 租户编号
     */
    private String tenantId;
}
