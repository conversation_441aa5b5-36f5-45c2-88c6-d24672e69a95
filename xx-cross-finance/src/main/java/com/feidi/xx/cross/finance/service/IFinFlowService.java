package com.feidi.xx.cross.finance.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.finance.domain.FinApprovalRequests;
import com.feidi.xx.cross.finance.domain.FinFlow;
import com.feidi.xx.cross.finance.domain.bo.FinApprovalRequestsBo;
import com.feidi.xx.cross.finance.domain.bo.FinFlowBo;
import com.feidi.xx.cross.finance.domain.bo.FinFlowQueryBo;
import com.feidi.xx.cross.finance.domain.vo.FinFlowExportVo;
import com.feidi.xx.cross.finance.domain.vo.FinFlowVo;

import java.util.Collection;
import java.util.List;

/**
 * 资金流水Service接口
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
public interface IFinFlowService {

    /**
     * 查询资金流水
     *
     * @param id 主键
     * @return 资金流水
     */
    FinFlowVo queryById(Long id);

    /**
     * 分页查询资金流水列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 资金流水分页列表
     */
    TableDataInfo<FinFlowVo> queryPageList(FinFlowQueryBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的资金流水列表
     *
     * @param bo 查询条件
     * @return 资金流水列表
     */
    List<FinFlowExportVo> queryList(FinFlowQueryBo bo);


    /**
     * 新增资金流水
     *
     * @param bo 资金流水
     * @return 是否新增成功
     */
    Boolean insertByBo(FinFlowBo bo);

    /**
     * 修改资金流水
     *
     * @param bo 资金流水
     * @return 是否修改成功
     */
    Boolean updateByBo(FinFlowBo bo);

    /**
     * 校验并批量删除资金流水信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据流水类型查询今日流水
     *
     * @param flowTypes 流水类型
     * @return 今日流水集合
     */
    List<FinFlow> queryTodayFlowByTypes(List<String> flowTypes);

    /**
     * 根据订单ID和流水类型查询流水
     *
     * @param orderIds 订单id集合
     * @param flowType 流水类型
     * @return 流水集合
     */
    List<FinFlow> queryByOrderIdsAndType(List<Long> orderIds, String flowType);

    /**
     * 根据订单ID和流水类型查询流水
     *
     * @param orderIds 订单id集合
     * @param flowTypes 流水类型集合
     * @return 流水集合
     */
    List<FinFlow> queryByOrderIdsAndTypes(List<Long> orderIds, List<String> flowTypes);

    /**
     * 人工调账
     *
     * @param finApprovalRequests
     */
    void artificialAdjustmentAddFlow(FinApprovalRequestsBo finApprovalRequests);
}
