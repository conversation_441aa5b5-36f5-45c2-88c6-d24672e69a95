package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.PaymentChannelEnum;
import com.feidi.xx.common.core.enums.PaymentDirectionEnum;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.cross.finance.domain.FinAccount;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 商家账户视图对象 fin_account
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinAccount.class)
public class FinAccountListVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 代理商ID
     */
    @ExcelProperty(value = "代理商ID")
    private Long agentId;

    /**
     * 代理商名称
     */
    @ExcelProperty(value = "代理商名称")
    private String agentName;

    /**
     * 渠道
     */
    @ExcelProperty(value = "渠道", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = PaymentChannelEnum.class)
    private String channel;

    /**
     * 应用ID
     */
    @ExcelProperty(value = "应用ID")
    private String appId;

    /**
     * mchId
     */
    @ExcelProperty(value = "mchId")
    private String mchId;

    /**
     * 进账/出账
     */
    @ExcelProperty(value = "进账/出账", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = PaymentDirectionEnum.class)
    private String direction;

    /**
     * 是否主账号
     */
    @ExcelProperty(value = "是否主账号", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = IsYesEnum.class)
    private String main;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 配置appid
     */
    @ExcelProperty(value = "配置appid")
    private String configAppId;

}
