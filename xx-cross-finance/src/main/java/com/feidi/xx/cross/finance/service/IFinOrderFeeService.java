package com.feidi.xx.cross.finance.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.finance.domain.bo.FinOrderFeeBo;
import com.feidi.xx.cross.finance.domain.vo.FinOrderFeeVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 订单应收服务费Service接口
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface IFinOrderFeeService {

    /**
     * 查询订单应收服务费
     *
     * @param id 主键
     * @return 订单应收服务费
     */
    FinOrderFeeVo queryById(Long id);

    /**
     * 分页查询订单应收服务费列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单应收服务费分页列表
     */
    TableDataInfo<FinOrderFeeVo> queryPageList(FinOrderFeeBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的订单应收服务费列表
     *
     * @param bo 查询条件
     * @return 订单应收服务费列表
     */
    List<FinOrderFeeVo> queryList(FinOrderFeeBo bo);

    /**
     * 新增订单应收服务费
     *
     * @param bo 订单应收服务费
     * @return 是否新增成功
     */
    Boolean insertByBo(FinOrderFeeBo bo);

    /**
     * 修改订单应收服务费
     *
     * @param bo 订单应收服务费
     * @return 是否修改成功
     */
    Boolean updateByBo(FinOrderFeeBo bo);

    /**
     * 校验并批量删除订单应收服务费信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 订单应收服务费
     *
     * @param orderVos 订单集合
     * @return
     */
    Boolean orderFee(List<RemoteOrderVo> orderVos, List<Long> complainOrderIds, Date financeDate);
}
