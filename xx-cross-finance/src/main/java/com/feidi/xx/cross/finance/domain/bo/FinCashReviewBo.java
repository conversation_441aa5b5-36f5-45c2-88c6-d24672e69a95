package com.feidi.xx.cross.finance.domain.bo;

import com.feidi.xx.cross.finance.common.validate.ReviewGroup;
import com.feidi.xx.cross.finance.common.validate.ReviewPassGroup;
import com.feidi.xx.cross.finance.common.validate.ReviewRejectGroup;
import com.feidi.xx.cross.finance.domain.FinCash;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 提现审核
 */
@Data
@AutoMapper(target = FinCash.class, reverseConvertGenerate = false)
public class FinCashReviewBo {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { ReviewGroup.class })
    private Long id;

    /**
     * 是否自动打款
     */
    @NotNull(message = "打款方式不能为空", groups = { ReviewPassGroup.class })
    private String isAuto;

    /**
     * 审批状态 {@link com.feidi.xx.cross.common.core.enums.CashAuditStatusEnum}
     */
    @NotNull(message = "审批结果不能为空", groups = { ReviewGroup.class })
    private String status;

    /**
     * 打款流水号
     */
    private String flowNo;

    /**
     * 凭证ossId
     */
    private String voucher;

    /**
     * 打款金额
     */
    private Long actualAmount;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { ReviewRejectGroup.class })
    private String remark;

}
