package com.feidi.xx.cross.finance.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 订单客诉费用对象 fin_complain_fee
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("fin_complain_fee")
public class FinComplainFee extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 账单周期(精确到日)
     */
    private Date financeDate;

    /**
     * 客诉费用类型[ComplainFeeTypeEnum]
     */
    private String feeType;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单渠道
     */
    private String platformCode;

    /**
     * 订单金额
     */
    private Long orderPrice;

    /**
     * 实际支付金额
     */
    private Long payPrice;

    /**
     * 金额
     */
    private Long amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
