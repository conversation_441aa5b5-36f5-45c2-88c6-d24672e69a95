package com.feidi.xx.cross.finance.controller.admin;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.encrypt.annotation.ApiEncrypt;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.cross.finance.domain.vo.ExportVo;
import com.feidi.xx.cross.finance.domain.vo.FinAccountListVo;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.cross.finance.domain.vo.FinAccountVo;
import com.feidi.xx.cross.finance.domain.bo.FinAccountBo;
import com.feidi.xx.cross.finance.service.IFinAccountService;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 商家账户
 * 前端访问路由地址为:/finance/account
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/account")
public class FinAccountController extends BaseController {

    private final IFinAccountService finAccountService;

    /**
     * 查询商家账户列表
     */
    @SaCheckPermission("finance:account:list")
    @GetMapping("/list")
    public TableDataInfo<FinAccountListVo> list(FinAccountBo bo, PageQuery pageQuery) {
        return finAccountService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出商家账户列表
     */
    @SaCheckPermission("finance:account:export")
    @Log(title = "商家账户", businessType = BusinessType.EXPORT)
    @Download(name="商家账户",module = ModuleConstants.FINANCE,mode="no")
    @PostMapping("/export")
    public Object export(FinAccountBo bo,HttpServletResponse response) {
        List<FinAccountListVo> list = finAccountService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(list, "商家账户", FinAccountListVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取商家账户详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("finance:account:query")
    @GetMapping("/{id}")
    public R<FinAccountVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(finAccountService.queryById(id));
    }

    /**
     * 新增商家账户
     */
    @ApiEncrypt
    @SaCheckPermission("finance:account:add")
    @Log(title = "商家账户", businessType = BusinessType.INSERT, isSaveRequestData = false)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinAccountBo bo) {
        return toAjax(finAccountService.insertByBo(bo));
    }

    /**
     * 修改商家账户
     */
    @ApiEncrypt
    @SaCheckPermission("finance:account:edit")
    @Log(title = "商家账户", businessType = BusinessType.UPDATE, isSaveRequestData = false)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinAccountBo bo) {
        return toAjax(finAccountService.updateByBo(bo));
    }

    /**
     * 禁用/启用
     */
    @SaCheckPermission("finance:account:edit")
    @Log(title = "商家账户", businessType = BusinessType.UPDATE, isSaveRequestData = false)
    @RepeatSubmit()
    @GetMapping("/{id}/{status}")
    public R<Void> updateStatus(@PathVariable Long id, @PathVariable String status){
        return toAjax(finAccountService.updateStatus(id,status));
    }

    /**
     * 删除商家账户
     *
     * @param ids 主键串
     */
    @SaCheckPermission("finance:account:remove")
    @Log(title = "商家账户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(finAccountService.deleteWithValidByIds(List.of(ids), true));
    }
}
