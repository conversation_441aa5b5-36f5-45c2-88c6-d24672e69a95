package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinWalletListVo.class, convertGenerate = false)
public class FinWalletExportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机id")
    private Long driverId;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机名称")
    private String driverName;

    @ExcelProperty(value = "所属代理商")
    private String agentName;

    /**
     * 累计收益
     */
    @ExcelProperty(value = "累计收益")
    @ReverseAutoMapping(target = "reduceProfit", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getReduceProfit()))")
    private String reduceProfit;

    /**
     * 佣金收益
     */
    @ExcelProperty(value = "佣金收益")
    @ReverseAutoMapping(target = "orderAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getOrderAmount()))")
    private String orderAmount;

    /**
     * 奖励收益
     */
    @ExcelProperty(value = "奖励收益")
    @ReverseAutoMapping(target = "rewardAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getRewardAmount()))")
    private String rewardAmount;

    /**
     * 订单转卖收益
     */
    @ExcelProperty(value = "订单转卖")
    @ReverseAutoMapping(target = "resellAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getResellAmount()))")
    private String resellAmount;

    /**
     * 账户调整金额
     */
    @ExcelProperty(value = "账户调整")
    @ReverseAutoMapping(target = "adjustAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getAdjustAmount()))")
    private String adjustAmount;

    /**
     * 累计提现
     */
    @ExcelProperty(value = "累计提现收益")
    @ReverseAutoMapping(target = "reduceCash", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getReduceCash()))")
    private String reduceCash;

    /**
     * 总金额
     */
    @ExcelProperty(value = "账户余额")
    @ReverseAutoMapping(target = "total", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getTotal()))")
    private String total;

    /**
     * 冻结金额
     */
    @ExcelProperty(value = "冻结金额")
    @ReverseAutoMapping(target = "freeze", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getFreeze()))")
    private String freeze;

    /**
     * 余额
     */
    @ExcelProperty(value = "可提现金额")
    @ReverseAutoMapping(target = "balance", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getBalance()))")
    private String balance;

    /**
     * 提现中金额
     */
    @ExcelProperty(value = "提现中金额")
    @ReverseAutoMapping(target = "cashing", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getCashing()))")
    private String cashing;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

}
