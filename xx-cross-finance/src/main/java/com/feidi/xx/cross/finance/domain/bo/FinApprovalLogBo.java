package com.feidi.xx.cross.finance.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.finance.domain.FinApprovalLog;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 账单审批操作记录业务对象 fin_approval_log
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinApprovalLog.class, reverseConvertGenerate = false)
public class FinApprovalLogBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 关联账单审批ID
     */
    @NotNull(message = "关联账单审批ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long approvalId;

    /**
     * 操作人ID
     */
    @NotNull(message = "操作人ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long operatorId;

    /**
     * 操作类型，如提交、审批通过、驳回等
     */
    @NotBlank(message = "操作类型，如提交、审批通过、驳回等不能为空", groups = {AddGroup.class, EditGroup.class})
    private String operation;

    /**
     * 操作时间
     */
    @NotNull(message = "操作时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date operateTime;

    /**
     * 操作备注
     */
    @NotBlank(message = "操作备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String remark;


}
