package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.feidi.xx.cross.finance.domain.FinTransfer;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 转账视图对象 Fin_transfer
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinTransfer.class, convertGenerate = false)
public class FinTransferAccountVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 司机姓名
     */
    private String name;

    /**
     * 司机手机号
     */
    private String phone;


}
