package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.cross.common.enums.finance.TransferTypeEnum;
import com.feidi.xx.cross.finance.domain.FinTransfer;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 转账视图对象 Fin_transfer
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinTransfer.class, convertGenerate = false)
public class FinTransferVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 转账编号
     */
    @ExcelProperty(value = "转账编号")
    private String transferNo;

    /**
     * 代理商id
     */
    @ExcelProperty(value = "代理商id")
    private Long agentId;

    /**
     * 转出司机id
     */
    @ExcelProperty(value = "转出司机id")
    private String outDriverId;

    /**
     * 转出司机姓名
     */
    @ExcelProperty(value = "转出司机姓名")
    private String outDriverName;

    /**
     * 转出司机手机号
     */
    @ExcelProperty(value = "转出司机手机号")
    private String outDriverPhone;

    /**
     * 转入司机id
     */
    @ExcelProperty(value = "转入司机id")
    private String inDriverId;

    /**
     * 转入司机姓名
     */
    @ExcelProperty(value = "转入司机姓名")
    private String inDriverName;

    /**
     * 转入司机手机号
     */
    @ExcelProperty(value = "转入司机手机号")
    private String inDriverPhone;

    /**
     * 金额
     */
    @ExcelProperty(value = "金额")
    private Long amount;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = TransferTypeEnum.class)
    private String type;

    /**
     * 操作人类型
     */
    @ExcelProperty(value = "操作人类型", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = UserTypeEnum.class)
    private String userType;

    /**
     * 操作姓名
     */
    @ExcelProperty(value = "操作姓名")
    private String userName;


}
