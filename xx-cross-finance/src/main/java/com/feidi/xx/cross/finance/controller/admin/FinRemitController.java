package com.feidi.xx.cross.finance.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.domain.bo.FinRemitBo;
import com.feidi.xx.cross.finance.domain.vo.FinRemitVo;
import com.feidi.xx.cross.finance.service.IFinRemitService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 后台 - 打款记录
 * 前端访问路由地址为:/finance/remit
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/remit")
public class FinRemitController extends BaseController {

    private final IFinRemitService finRemitService;

    /**
     * 查询打款记录列表
     */
    @SaCheckPermission("finance:remit:list")
    @GetMapping("/list")
    public TableDataInfo<FinRemitVo> list(FinRemitBo bo, PageQuery pageQuery) {
        return finRemitService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出打款记录列表
     */
    @SaCheckPermission("finance:remit:export")
    @Log(title = "打款记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FinRemitBo bo, HttpServletResponse response) {
        List<FinRemitVo> list = finRemitService.queryList(bo);
        ExcelUtil.exportExcel(list, "打款记录", FinRemitVo.class, response);
    }

    /**
     * 获取打款记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("finance:remit:query")
    @GetMapping("/{id}")
    public R<FinRemitVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(finRemitService.queryById(id));
    }

    /**
     * 新增打款记录
     */
    @SaCheckPermission("finance:remit:add")
    @Log(title = "打款记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinRemitBo bo) {
        return toAjax(finRemitService.insertByBo(bo));
    }

    /**
     * 修改打款记录
     */
    @SaCheckPermission("finance:remit:edit")
    @Log(title = "打款记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinRemitBo bo) {
        return toAjax(finRemitService.updateByBo(bo));
    }

    /**
     * 删除打款记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("finance:remit:remove")
    @Log(title = "打款记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(finRemitService.deleteWithValidByIds(List.of(ids), true));
    }
}
