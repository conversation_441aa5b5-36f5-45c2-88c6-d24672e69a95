package com.feidi.xx.cross.finance.mapper;

import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.finance.domain.FinApprovalRequests;
import com.feidi.xx.cross.finance.domain.vo.FinApprovalRequestsVo;
import org.apache.ibatis.annotations.Param;

/**
 * 账单审批记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface FinApprovalRequestsMapper extends BaseMapperPlus<FinApprovalRequests, FinApprovalRequestsVo> {

    int selectCountByOrderNoAndStatus(@Param("orderNo") String orderNo, @Param("status") Integer status, @Param("excludeId") Long excludeId);

}
