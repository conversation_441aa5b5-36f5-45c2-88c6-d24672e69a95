package com.feidi.xx.cross.finance.mq;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 提现事件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AutoPaymentEvent implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 提现单ID
     */
    private Long cashId;

    /**
     * 租户ID
     */
    private String tenantId;

}
