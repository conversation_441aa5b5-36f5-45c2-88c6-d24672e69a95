package com.feidi.xx.cross.finance.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.finance.domain.FinRemit;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 打款记录业务对象 fin_remit
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinRemit.class, reverseConvertGenerate = false)
public class FinRemitBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 订单对账id
     */
    @NotNull(message = "订单对账id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long billId;

    /**
     * 打款单号
     */
    @NotBlank(message = "打款单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remitNo;

    /**
     * 代理商id
     */
    @NotNull(message = "代理商id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long agentId;

    /**
     * 代理商名称
     */
    @NotBlank(message = "代理商名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String agentName;

    /**
     * 打款账户id
     */
    @NotNull(message = "打款账户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long remitAccountId;

    /**
     * 打款账号
     */
    @NotBlank(message = "打款账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remitAccount;

    /**
     * 打款账户姓名
     */
    @NotBlank(message = "打款账户姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remitAccountName;

    /**
     * 打款账户id
     */
    @NotNull(message = "打款账户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long receiveAccountId;

    /**
     * 打款账号
     */
    @NotBlank(message = "打款账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveAccount;

    /**
     * 打款账户姓名
     */
    @NotBlank(message = "打款账户姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveAccountName;

    /**
     * 打款类型[AccountTypeEnum]
     */
    @NotBlank(message = "打款类型[AccountTypeEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accountType;

    /**
     * 账单金额
     */
    @NotBlank(message = "账单金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private String financeAmount;

    /**
     * 打款金额
     */
    @NotBlank(message = "打款金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remitAmount;

    /**
     * 打款流水号
     */
    private String flowNo;

    /**
     * 交易单号
     */
    private String transNo;

    /**
     * 交易状态
     */
    private String transStatus;

    /**
     * 打款时间
     */
    @NotNull(message = "打款时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date remitTime;

    /**
     * 打款人id
     */
    private Long remitUserId;

    /**
     * 打款人姓名
     */
    private String remitUserName;

    /**
     * 打款类型[RemitTypeEnum]
     */
    @NotBlank(message = "打款类型[RemitTypeEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remitType;

    /**
     * 账单日期
     */
    @NotNull(message = "账单日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date financeDate;


}
