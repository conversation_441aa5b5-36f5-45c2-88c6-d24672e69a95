package com.feidi.xx.cross.finance.dubbo;

import com.feidi.xx.common.core.enums.PaymentStatusEnum;
import com.feidi.xx.common.core.enums.PaymentTypeEnum;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.finance.api.RemotePaymentRecordService;
import com.feidi.xx.cross.finance.api.domain.bo.RemotePaymentRecordBo;
import com.feidi.xx.cross.finance.api.domain.vo.RemotePaymentRecordVo;
import com.feidi.xx.cross.finance.domain.FinPaymentRecord;
import com.feidi.xx.cross.finance.domain.bo.FinPaymentRecordBo;
import com.feidi.xx.cross.finance.mapper.FinPaymentRecordMapper;
import com.feidi.xx.cross.finance.service.IFinPaymentRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 支付流水服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@DubboService
@RequiredArgsConstructor
public class RemotePaymentRecordServiceImpl implements RemotePaymentRecordService {

    private final FinPaymentRecordMapper baseMapper;

    final IFinPaymentRecordService paymentRecordService;

    /**
     * 根据joinTable和joinIds查询支付流水
     * @param joinTable
     * @param joinIds
     * @return
     */
    @Override
    public List<RemotePaymentRecordVo> queryList(String joinTable, List<Long> joinIds) {
        if (joinIds == null || joinIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<FinPaymentRecord> records = baseMapper.listByJoinIds(joinTable, joinIds);
        return BeanUtils.copyToList(records, RemotePaymentRecordVo.class);
    }

    /**
     * 保存支付流水
     * @param bo
     */
    @Override
    public void savePaymentRecord(RemotePaymentRecordBo bo) {
        FinPaymentRecordBo recordBo = BeanUtils.copyProperties(bo, FinPaymentRecordBo.class);
        paymentRecordService.insertByBo(recordBo);
    }

    @Override
    public RemotePaymentRecordVo getPaymentRecord(String tenantId, Long id, String joinTable, PaymentTypeEnum paymentType) {
        FinPaymentRecord paymentFlow = baseMapper.getPaymentRecord(tenantId, id, joinTable, paymentType.getCode());
        return BeanUtils.copyProperties(paymentFlow, RemotePaymentRecordVo.class);
    }

    @Override
    public RemotePaymentRecordVo getPaymentRecord(String tenantId, String appId, String outBizNo, PaymentTypeEnum paymentType, PaymentStatusEnum status) {
        FinPaymentRecord paymentFlow = baseMapper.getPaymentRecord(tenantId, appId, outBizNo, paymentType.getCode(), status.getCode());
        return BeanUtils.copyProperties(paymentFlow, RemotePaymentRecordVo.class);
    }



}
