package com.feidi.xx.cross.finance.domain;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.feidi.xx.common.core.utils.xx.RandomUtils;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.io.Serial;
import java.util.List;

/**
 * 提现对象 fin_cash
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("fin_cash")
public class FinCash extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 单号
     */
    private String cashNo;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 账户id
     */
    private Long accountId;

    /**
     * 账户姓名
     */
    private String name;

    /**
     * 账户手机号
     */
    private String phone;

    /**
     * 账号
     */
    private String account;

    /**
     * 类型
     */
    private String accountType;

    /**
     * 提现金额
     */
    private Long amount;

    /**
     * 服务费
     */
    private Long serviceFee;

    /**
     * 是否自动[IsYesEnum]
     */
    private String isAuto;

    /**
     * 状态[CashAuditStatusEnum]
     */
    private String status;

    /**
     * 处理人id
     */
    private Long userId;

    /**
     * 处理人
     */
    private String userName;

    /**
     * 打款流水号
     */
    private String flowNo;

    /**
     * 审批时间
     */
    private Date reviewTime;

    /**
     * 交易时间
     */
    private Date transDate;

    /**
     * 交易状态
     */
    private String transStatus;

    /**
     * 交易信息
     */
    private String transInfo;

    /**
     * 凭证 自动打款保存的是支付订单号，手动打款保存的是打款凭证照片的ossId
     */
    private String voucher;

    /**
     * AppId
     */
    private String appId;

    /**
     * 商户ID|OpenId
     */
    private String mchId;

    /**
     * 打款金额
     */
    private Long actualAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 相关订单id
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> relatedOrder;

    public static String generateNo() {
        return "cash" + DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_MS_PATTERN) + RandomUtils.randomInt(10, 99);
    }

}
