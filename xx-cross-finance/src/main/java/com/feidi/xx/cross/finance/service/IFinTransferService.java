package com.feidi.xx.cross.finance.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.finance.domain.bo.FinTransBo;
import com.feidi.xx.cross.finance.domain.bo.FinTransferBo;
import com.feidi.xx.cross.finance.domain.bo.FinTransferQueryBo;
import com.feidi.xx.cross.finance.domain.vo.FinTransferAccountVo;
import com.feidi.xx.cross.finance.domain.vo.FinTransferListVo;
import com.feidi.xx.cross.finance.domain.vo.FinTransferVo;

import java.util.Collection;
import java.util.List;

/**
 * 转账Service接口
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
public interface IFinTransferService {

    /**
     * 最近转账账号
     * @return
     */
    List<FinTransferAccountVo> recent();

    /**
     * 查询转账
     *
     * @param id 主键
     * @return 转账
     */
    FinTransferVo queryById(Long id);

    /**
     * 分页查询转账列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 转账分页列表
     */
    TableDataInfo<FinTransferListVo> queryPageList(FinTransferQueryBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的转账列表
     *
     * @param bo 查询条件
     * @return 转账列表
     */
    List<FinTransferListVo> queryList(FinTransferQueryBo bo);

    /**
     * 新增转账
     *
     * @param bo 转账
     * @return 是否新增成功
     */
    Boolean insertByBo(FinTransBo bo);
    /**
     * 修改转账
     *
     * @param bo 转账
     * @return 是否修改成功
     */
    Boolean updateByBo(FinTransferBo bo);

    /**
     * 校验并批量删除转账信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
