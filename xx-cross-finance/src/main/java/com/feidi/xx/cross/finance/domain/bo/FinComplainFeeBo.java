package com.feidi.xx.cross.finance.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.finance.domain.FinComplainFee;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 订单客诉费用业务对象 fin_complain_fee
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinComplainFee.class, reverseConvertGenerate = false)
public class FinComplainFeeBo extends BaseEntity {

    /**
     * 主键
     */
    //@NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 账单周期(精确到日)
     */
    @NotNull(message = "账单周期(精确到日)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date financeDate;

    /**
     * 客诉费用类型[ComplainFeeTypeEnum]
     */
    @NotBlank(message = "客诉费用类型[ComplainFeeTypeEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String feeType;

    /**
     * 代理商id
     */
    @NotNull(message = "代理商id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long agentId;

    /**
     * 代理商名称
     */
    //@NotBlank(message = "代理商名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String agentName;

    /**
     * 司机id
     */
    @NotNull(message = "司机id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long driverId;

    /**
     * 司机姓名
     */
    //@NotBlank(message = "司机姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String driverName;

    /**
     * 订单id
     */
    //@NotNull(message = "订单id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 订单号
     */
    //@NotBlank(message = "订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderNo;

    /**
     * 订单渠道
     */
    private String platformCode;

    /**
     * 订单金额
     */
    private Long orderPrice;

    /**
     * 实际支付金额
     */
    private Long payPrice;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * id集合
     */
    private List<Long> ids;

    /**
     * 账单开始日期
     */
    private String financeStartTime;

    /**
     * 账单结束日期
     */
    private String financeEndTime;

}
