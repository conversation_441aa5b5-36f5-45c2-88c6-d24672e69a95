package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.cross.common.enums.finance.FinanceTypeEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 流水明细视图对象 fin_bill_detail
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinBillDetailVo.class, convertGenerate = false)
public class FinBillDetailExportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 代理商名称
     */
    @ExcelProperty(value = "代理商名称")
    private String agentName;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机id")
    private Long driverId;

    /**
     * 司机姓名
     */
    @ExcelProperty(value = "司机姓名")
    private String driverName;

    /**
     * 司机电话
     */
    @ExcelProperty(value = "手机号码")
    private String driverPhone;

    /**
     * 账单周期(精确到日)
     */
    @ExcelProperty(value = "账单时间", format = "yyyy-MM-dd")
    @DateTimeFormat("yyyy-MM-dd")
    private Date financeDate;

    /**
     * 订单渠道
     */
    @ExcelProperty(value = "渠道", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = PlatformCodeEnum.class)
    private String platformCode;

    /**
     * 账单对账类型
     */
    @ExcelProperty(value = "类型", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = FinanceTypeEnum.class)
    private String financeType;

    /**
     * 总单量
     */
    @ExcelProperty(value = "总单量")
    private Long orderNumber;

    /**
     * 订单总金额
     */
    @ExcelProperty(value = "订单总金额")
    @ReverseAutoMapping(target = "orderAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getOrderAmount()))")
    private String orderAmount;

    /**
     * 总收益
     */
    @ExcelProperty(value = "总收益")
    @ReverseAutoMapping(target = "orderProfit", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getOrderProfit()))")
    private String orderProfit;

    /**
     * 其他收益
     */
    @ExcelProperty(value = "其他收益")
    @ReverseAutoMapping(target = "otherProfit", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getOtherProfit()))")
    private String otherProfit;

    /**
     * 钱包余额
     */
    @ExcelProperty(value = "钱包余额")
    @ReverseAutoMapping(target = "walletBalance", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getWalletBalance()))")
    private String walletBalance;

    /**
     * 客诉金额
     */
    @ReverseAutoMapping(target = "complainAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getComplainAmount()))")
    @ExcelProperty(value = "客诉金额")
    private String complainAmount;

    /**
     * 优惠券使用总金额
     */
    @ExcelProperty(value = "优惠券使用总金额")
    @ReverseAutoMapping(target = "couponQuotaAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getCouponQuotaAmount()))")
    private String couponQuotaAmount;

    @ExcelProperty(value = "订单编号")
    private String relatedOrder;

}
