package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinTransferListVo.class, convertGenerate = false)
public class FinTransferExportVo {

    /**
     * 流水编号
     */
    @ExcelProperty(value = "流水单号")
    private String transferNo;


    /**
     * 代理商id
     */
    @ExcelProperty(value = "代理商id")
    private Long agentId;

    /**
     * 代理商名称
     */
    @ExcelProperty(value = "代理商名称")
    private String agentName;
    /**
     * 转出司机id
     */
    @ExcelProperty(value = "转出司机id")
    private Long outDriverId;

    /**
     * 转出司机姓名
     */
    @ExcelProperty(value = "转出司机姓名")
    private String outDriverName;

    /**
     * 转出司机手机号
     */
    @ExcelProperty(value = "转出司机手机号")
    private String outDriverPhone;

    /**
     * 收款司机id
     */
    @ExcelProperty(value = "收款司机id")
    private Long inDriverId;

    /**
     * 收款司机姓名
     */
    @ExcelProperty(value = "收款司机姓名")
    private String inDriverName;

    /**
     * 收款司机手机号
     */
    @ExcelProperty(value = "收款司机手机号")
    private String inDriverPhone;

    /**
     * 金额
     */
    @ReverseAutoMapping(target = "amount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanBigDecimal(source.getAmount()))")
    @ExcelProperty(value = "金额")
    private BigDecimal amount;

    /**
     * 转账时间
     */
    @ExcelProperty(value = "转账时间")
    private Date createTime;

}
