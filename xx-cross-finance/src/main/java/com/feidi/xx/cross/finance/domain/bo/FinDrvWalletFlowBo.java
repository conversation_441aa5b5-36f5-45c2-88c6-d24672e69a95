package com.feidi.xx.cross.finance.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.finance.domain.FinDrvWalletFlow;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 司机钱包流水业务对象 fin_drv_wallet_flow
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinDrvWalletFlow.class, reverseConvertGenerate = false)
public class FinDrvWalletFlowBo extends TenantEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 关联单号
     */
    @NotBlank(message = "关联单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderNo;

    /**
     * 代理商id
     */
    @NotNull(message = "代理商id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long agentId;

    /**
     * 司机id
     */
    @NotNull(message = "司机id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long driverId;

    /**
     * 佣金收益
     */
    private Long orderProfit;

    /**
     * 奖励收益
     */
    private Long rewardProfit;

    /**
     * 订单转卖收益
     */
    private Long resellProfit;

    /**
     * 账户余额(冻结金额+可提现金额)
     */
    private Long accountAmount;

    /**
     * 可提现金额
     */
    private Long balance;

    /**
     * 冻结金额
     */
    private Long freeze;

    /**
     * 累计提现收益
     */
    private Long expend;

    /**
     * 提现中金额
     */
    private Long withdrawalAmount;

    /**
     * 开始创建时间
     */
    private String startCreateTime;

    /**
     * 结束创建时间
     */
    private String endCreateTime;

}
