package com.feidi.xx.cross.finance.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.finance.domain.bo.FinApprovalRequestsBo;
import com.feidi.xx.cross.finance.domain.bo.FinApprovalRequestsImportBo;
import com.feidi.xx.cross.finance.domain.vo.FinApprovalRequestsVo;

import java.util.Collection;
import java.util.List;

/**
 * 账单审批记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface IFinApprovalRequestsService {

    /**
     * 查询账单审批记录
     *
     * @param id 主键
     * @return 账单审批记录
     */
    FinApprovalRequestsVo queryById(Long id);

    /**
     * 分页查询账单审批记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 账单审批记录分页列表
     */
    TableDataInfo<FinApprovalRequestsVo> queryPageList(FinApprovalRequestsBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的账单审批记录列表
     *
     * @param bo 查询条件
     * @return 账单审批记录列表
     */
    List<FinApprovalRequestsVo> queryList(FinApprovalRequestsBo bo);

    /**
     * 新增账单审批记录
     *
     * @param bo 账单审批记录
     * @return 是否新增成功
     */
    Boolean insertByBo(FinApprovalRequestsBo bo);

    /**
     * 修改账单审批记录
     *
     * @param bo 账单审批记录
     * @return 是否修改成功
     */
    Boolean updateByBo(FinApprovalRequestsBo bo);

    /**
     * 校验并批量删除账单审批记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 修改账单审批状态
     *
     * @param bo
     */
    void updateStatus(FinApprovalRequestsBo bo);

    /**
     * 导入数据处理
     *
     * @param list
     * @return
     */
    FinApprovalRequestsVo importDataProcess(List<FinApprovalRequestsImportBo> list);
}
