package com.feidi.xx.cross.finance.mq.producer;

import com.feidi.xx.common.rocketmq.base.BaseSendExtendDTO;
import com.feidi.xx.common.rocketmq.enums.MqMessageTypeEnum;
import com.feidi.xx.common.rocketmq.producer.AbstractProducer;
import com.feidi.xx.common.rocketmq.util.MQMessageUtil;
import com.feidi.xx.cross.common.constant.finance.FinanceMQConstants;
import com.feidi.xx.cross.finance.mq.event.FinDrvWalletFlowEvent;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.stereotype.Component;

/**
 * 司机钱包流水生产者
 *
 * <AUTHOR>
 * @date 2025/7/14
 */
@Component
@RequiredArgsConstructor
public class FinDrvWalletFlowProducer extends AbstractProducer<FinDrvWalletFlowEvent> {
    private BaseSendExtendDTO buildBaseSendExtendParam(FinDrvWalletFlowEvent drvWalletFlowEvent) {
        return BaseSendExtendDTO.builder()
                .eventName("司机钱包流水")
                .keys(drvWalletFlowEvent.getOrderId() + drvWalletFlowEvent.getFlowType())
                .tag(drvWalletFlowEvent.getOrderId().toString())
                .messageType(MqMessageTypeEnum.SYNC)
                .topic(FinanceMQConstants.FINANCE_DRIVER_WALLET_FLOW_TOPIC_KEY)
                .sentTimeout(2000L)
                .build();
    }

    @Override
    public SendResult sendMessage(FinDrvWalletFlowEvent virtualPhoneEvent) {
        return MQMessageUtil.sendMessage(virtualPhoneEvent, this::buildBaseSendExtendParam);
    }
}
