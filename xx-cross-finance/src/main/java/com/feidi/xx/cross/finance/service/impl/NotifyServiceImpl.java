package com.feidi.xx.cross.finance.service.impl;

import cn.hutool.core.date.DateUtil;
import com.feidi.xx.common.core.enums.*;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.ServletUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.payment.domain.notify.bo.NotifyBo;
import com.feidi.xx.common.payment.domain.notify.vo.PaymentNotifyVo;
import com.feidi.xx.common.payment.domain.notify.vo.RefundNotifyVo;
import com.feidi.xx.common.payment.mq.PaymentRecordEvent;
import com.feidi.xx.common.payment.mq.PaymentRecordProducer;
import com.feidi.xx.common.payment.strategy.IPaymentService;
import com.feidi.xx.common.tenant.helper.TenantHelper;
import com.feidi.xx.cross.common.enums.order.OperateTypeEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.common.helper.OrdOrderOperateHelper;
import com.feidi.xx.cross.common.mq.event.OrdOrderOperateEvent;
import com.feidi.xx.cross.common.mq.producer.OrdOrderOperateProducer;
import com.feidi.xx.cross.finance.api.RemotePaymentRecordService;
import com.feidi.xx.cross.finance.api.domain.bo.RemotePaymentRecordBo;
import com.feidi.xx.cross.finance.api.domain.vo.RemotePaymentRecordVo;
import com.feidi.xx.cross.finance.service.INotifyService;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderPaymentBo;
import com.feidi.xx.system.api.RemoteTenantService;
import com.feidi.xx.system.api.domain.vo.RemoteTenantVo;
import com.wechat.pay.java.service.partnerpayments.app.model.Transaction;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

@Slf4j
@Service
@AllArgsConstructor
public class NotifyServiceImpl implements INotifyService {

    private final IPaymentService paymentService;

    private final OrdOrderOperateProducer operateProducer;

    @DubboReference
    private final RemotePaymentRecordService paymentRecordService;

    @DubboReference
    private final RemoteOrderService remoteOrderService;

    @DubboReference
    private final RemoteTenantService remoteTenantService;

    /**
     *  支付通知
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean payment(PaymentChannelEnum channel, HttpServletRequest request, HttpServletResponse response, @RequestBody String requestBody, String appId) {
        String domain = ServletUtils.getDomain();
        RemoteTenantVo tenantVo = remoteTenantService.queryByDomain(domain);
        if (tenantVo == null) {
            log.error("【支付回调】租户不存在：{}", domain);
            return false;
        }

        TenantHelper.setDynamic(tenantVo.getTenantId());
        try {
            NotifyBo notifyBo = new NotifyBo();
            notifyBo.setTenantId(tenantVo.getTenantId());
            notifyBo.setChannel(channel);
            notifyBo.setAppId(appId);
            PaymentNotifyVo notifyVo = paymentService.paymentNotify(request, requestBody, notifyBo);
            createPaymentRecord(notifyVo, channel, tenantVo.getTenantId());

            // TODO: 2024/12/26 回调后的逻辑在这里写
            RemotePaymentRecordVo paymentRecord = paymentRecordService.getPaymentRecord(tenantVo.getTenantId(), notifyVo.getAppId(), notifyVo.getBizNo(), channel.getType(), PaymentStatusEnum.ING);
            if (paymentRecord != null) {
                paymentRecord.setPaymentType(channel.getType().getCode());
                paymentRecord.setTradeTime(DateUtil.formatDateTime(notifyVo.getTradeTime()));
                paymentRecord.setFlowNo(notifyVo.getTransactionId());
                paymentRecord.setOrderId(notifyVo.getTransactionId());
                if (notifyVo.isSuccess()) {
                    paymentRecord.setStatus(PaymentStatusEnum.SUCCESS.getCode());
                } else {
                    paymentRecord.setStatus(PaymentStatusEnum.FAIL.getCode());
                }
                paymentRecordService.savePaymentRecord(BeanUtils.copyProperties(paymentRecord, RemotePaymentRecordBo.class));

                /// 更新订单状态
                if (paymentRecord.getType().equals(RecordTypeEnum.PASSENGER_PAYMENT.getCode())) {
                    OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildOrderOperateEvent(paymentRecord.getJoinId(), OperateTypeEnum.PAYMENT_SUCCESS.getCode(), JsonUtils.toJsonString(notifyVo));
                    operateEvent.setTenantId(tenantVo.getTenantId());
                    // 成功再做处理
                    try {
                        if (notifyVo.isSuccess()) {
                            RemoteOrderPaymentBo handleBo = new RemoteOrderPaymentBo();
                            handleBo.setOrderId(paymentRecord.getJoinId());
                            handleBo.setPlatformCode(PlatformCodeEnum.SELF.getCode());
                            handleBo.setPayTypeEnum(paymentRecord.getPaymentType());
                            handleBo.setPayStatusEnum(paymentRecord.getStatus());
                            handleBo.setPayTime(notifyVo.getTradeTime());
                            handleBo.setUserType(UserTypeEnum.PASSENGER_USER.getUserType());
                            handleBo.setUserId(0L);
                            handleBo.setRemark("乘客支付回调");
                            handleBo.setTimeStamp(DateUtils.getUnixTimeStamps());
                            remoteOrderService.paymentConfirm(handleBo);
                        } else {
                            operateEvent.setOperateType(OperateTypeEnum.PAYMENT_FAIL.getCode());
                        }
                    } finally {
                        operateProducer.sendMessage(operateEvent);
                    }
                }
            } else {
                log.error("【支付回调】无此交易单号：{}", notifyVo.getTradeNo());
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        } finally {
            TenantHelper.clearDynamic();
        }
    }

    /**
     * 退款通知
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean refund(PaymentChannelEnum channel, HttpServletRequest request, HttpServletResponse response, @RequestBody String requestBody, String appId) {
        String domain = ServletUtils.getDomain();
        RemoteTenantVo tenantVo = remoteTenantService.queryByDomain(domain);
        if (tenantVo == null) {
            log.error("【退款回调】租户不存在：{}", domain);
            return false;
        }

        TenantHelper.setDynamic(tenantVo.getTenantId());
        try {
            NotifyBo notifyBo = new NotifyBo();
            notifyBo.setTenantId(tenantVo.getTenantId());
            notifyBo.setChannel(channel);
            notifyBo.setAppId(appId);
            RefundNotifyVo notifyVo = paymentService.refundNotify(request, requestBody, notifyBo);
            createRefundRecord(notifyVo, channel, tenantVo.getTenantId());

            // TODO: 2024/12/26 回调后的逻辑 成功退款后再关闭订单
            // 找之前支付过的记录获取订单信息
            RemotePaymentRecordVo paymentRecord = paymentRecordService.getPaymentRecord(tenantVo.getTenantId(), notifyVo.getAppId(), notifyVo.getBizNo(), channel.getType(), PaymentStatusEnum.SUCCESS);
            if (paymentRecord != null) {
                paymentRecord.setPaymentType(channel.getType().getCode());
                paymentRecord.setTradeTime(DateUtil.formatDateTime(notifyVo.getTradeTime()));
                paymentRecord.setFlowNo(notifyVo.getTransactionId());
                paymentRecord.setOrderId(notifyVo.getTransactionId());
                if (notifyVo.isSuccess()) {
                    paymentRecord.setStatus(PaymentStatusEnum.SUCCESS.getCode());
                    paymentRecord.setIsRefund(IsYesEnum.YES.getCode());
                    paymentRecord.setRefundNo(notifyVo.getTradeNo());
                    paymentRecord.setRefundAmount(notifyVo.getRefundAmount());
                } else {
                    paymentRecord.setStatus(PaymentStatusEnum.FAIL.getCode());
                    paymentRecord.setRefundNo(notifyVo.getTradeNo());
                }
                paymentRecordService.savePaymentRecord(BeanUtils.copyProperties(paymentRecord, RemotePaymentRecordBo.class));
            } else {
                log.error("【退款回调】无此交易单号：{}", notifyVo.getBizNo());
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        } finally {
            TenantHelper.clearDynamic();
        }
    }

    /**
     * 创建支付流水
     * @param notifyVo
     */
    private void createPaymentRecord(PaymentNotifyVo notifyVo, PaymentChannelEnum channel, String tenantId) {
        PaymentRecordEvent event = new PaymentRecordEvent();
        try {
            event.setTenantId(tenantId);
            event.setNotify(IsYesEnum.YES.getCode());
            event.setPaymentType(channel.getType().getCode());
            event.setType(RecordTypeEnum.PASSENGER_PAYMENT.getCode());
            event.setResponseJson(JsonUtils.toJsonString(notifyVo));
            event.setJoinTable(JoinEnum.ORDER.getCode());
            event.setAppId(notifyVo.getAppId());
            event.setMchId(notifyVo.getMchId());
            event.setCode(notifyVo.getCode());

            // 状态
            if (notifyVo.isSuccess()) {
                event.setStatus(PaymentStatusEnum.SUCCESS.getCode());
            } else {
                event.setStatus(PaymentStatusEnum.FAIL.getCode());
            }

            event.setOutBizNo(notifyVo.getBizNo());
            event.setFlowNo(notifyVo.getTradeNo());
            event.setOrderId(notifyVo.getTransactionId());
            event.setTradeTime(DateUtil.formatDateTime(notifyVo.getTradeTime()));
            event.setAmount(notifyVo.getTotalAmount());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("记录支付回调流水异常，{}", e.getMessage());
        } finally {
            PaymentRecordProducer.sendMessage(event);
        }
    }

    /**
     * 创建退款流水
     * @param notifyVo
     */
    private void createRefundRecord(RefundNotifyVo notifyVo, PaymentChannelEnum channel, String tenantId) {
        PaymentRecordEvent event = new PaymentRecordEvent();
        try {
            event.setTenantId(tenantId);
            event.setNotify(IsYesEnum.YES.getCode());
            event.setPaymentType(channel.getType().getCode());
            event.setType(RecordTypeEnum.PASSENGER_REFUND.getCode());
            event.setResponseJson(JsonUtils.toJsonString(notifyVo));
            event.setJoinTable(JoinEnum.ORDER.getCode());
            event.setAppId(notifyVo.getAppId());
            event.setMchId(notifyVo.getMchId());
            event.setIsRefund(IsYesEnum.YES.getCode());
            event.setRefundNo(notifyVo.getRefundId());

            if (notifyVo.isSuccess()) {
                event.setStatus(PaymentStatusEnum.SUCCESS.getCode());
                event.setCode(Transaction.TradeStateEnum.SUCCESS.name());
            } else {
                event.setStatus(PaymentStatusEnum.FAIL.getCode());
                event.setCode(Transaction.TradeStateEnum.ACCEPT.name());
            }
            event.setOutBizNo(notifyVo.getBizNo());
            event.setFlowNo(notifyVo.getTradeNo());
            event.setOrderId(notifyVo.getRefundId());
            event.setAmount(notifyVo.getRefundAmount());
            event.setTradeTime(DateUtil.formatDateTime(notifyVo.getTradeTime()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("记录退款回调流水异常，{}", e.getMessage());
        } finally {
            PaymentRecordProducer.sendMessage(event);
        }
    }

}
