package com.feidi.xx.cross.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.finance.domain.FinRemit;
import com.feidi.xx.cross.finance.domain.bo.FinRemitBo;
import com.feidi.xx.cross.finance.domain.vo.FinRemitVo;
import com.feidi.xx.cross.finance.mapper.FinRemitMapper;
import com.feidi.xx.cross.finance.service.IFinRemitService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 打款记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@RequiredArgsConstructor
@Service
public class FinRemitServiceImpl implements IFinRemitService {

    private final FinRemitMapper baseMapper;

    /**
     * 查询打款记录
     *
     * @param id 主键
     * @return 打款记录
     */
    @Override
    public FinRemitVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询打款记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 打款记录分页列表
     */
    @Override
    public TableDataInfo<FinRemitVo> queryPageList(FinRemitBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinRemit> lqw = buildQueryWrapper(bo);
        Page<FinRemitVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的打款记录列表
     *
     * @param bo 查询条件
     * @return 打款记录列表
     */
    @Override
    public List<FinRemitVo> queryList(FinRemitBo bo) {
        LambdaQueryWrapper<FinRemit> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinRemit> buildQueryWrapper(FinRemitBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinRemit> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getAgentId() != null, FinRemit::getAgentId, bo.getAgentId());
        lqw.eq(bo.getRemitAccountId() != null, FinRemit::getRemitAccountId, bo.getRemitAccountId());
        lqw.eq(bo.getReceiveAccountId() != null, FinRemit::getReceiveAccountId, bo.getReceiveAccountId());
        lqw.between(params.get("beginFinanceDate") != null && params.get("endFinanceDate") != null,
            FinRemit::getFinanceDate ,params.get("beginFinanceDate"), params.get("endFinanceDate"));
        return lqw;
    }

    /**
     * 新增打款记录
     *
     * @param bo 打款记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinRemitBo bo) {
        FinRemit add = MapstructUtils.convert(bo, FinRemit.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改打款记录
     *
     * @param bo 打款记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinRemitBo bo) {
        FinRemit update = MapstructUtils.convert(bo, FinRemit.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinRemit entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除打款记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
