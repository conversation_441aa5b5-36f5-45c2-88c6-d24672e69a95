package com.feidi.xx.cross.finance.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.domain.bo.FinFlowQueryBo;
import com.feidi.xx.cross.finance.domain.bo.FinPaymentRecordBo;
import com.feidi.xx.cross.finance.domain.vo.ExportVo;
import com.feidi.xx.cross.finance.domain.vo.FinFlowExportVo;
import com.feidi.xx.cross.finance.domain.vo.FinPaymentRecordVo;
import com.feidi.xx.cross.finance.service.IFinPaymentRecordService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 支付记录
 * 前端访问路由地址为:/finance/paymentRecord
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@SaCheckRole(UserTypeEnum.UserType.SYS_USER)
@RequestMapping("/paymentRecord")
public class FinPaymentRecordController extends BaseController {

    private final IFinPaymentRecordService FinPaymentRecordService;

    /**
     * 查询支付记录列表
     */
    @SaCheckPermission("finance:paymentRecord:list")
    @GetMapping("/list")
    public TableDataInfo<FinPaymentRecordVo> list(FinPaymentRecordBo bo, PageQuery pageQuery) {
        return FinPaymentRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出支付记录列表
     */
    @SaCheckPermission("finance:paymentRecord:export")
    @Log(title = "支付记录", businessType = BusinessType.EXPORT)
    @Download(name="支付记录",module = ModuleConstants.FINANCE,mode="no")
    @PostMapping("/export")
    public Object export(@RequestBody FinPaymentRecordBo bo, HttpServletResponse response) {
        List<FinPaymentRecordVo> list = FinPaymentRecordService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(list, "支付记录", FinPaymentRecordVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取支付记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("finance:paymentRecord:query")
    @GetMapping("/{id}")
    public R<FinPaymentRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable Long id) {
        return R.ok(FinPaymentRecordService.queryById(id));
    }

    /**
     * 新增支付记录
     */
    @SaCheckPermission("finance:paymentRecord:add")
    @Log(title = "支付记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinPaymentRecordBo bo) {
        return toAjax(FinPaymentRecordService.insertByBo(bo));
    }

}
