package com.feidi.xx.cross.finance.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 订单对账对象 fin_bill
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("fin_bill")
public class FinBill extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 账单月份
     */
    private Date month;

    /**
     * 订单量
     */
    private Long orderNumber;

    /**
     * 订单金额
     */
    private Long orderAmount;

    /**
     * 司机收益
     */
    private Long driverProfit;

    /**
     * 代收款
     */
    private Long proxyAmount;

    /**
     * 应收信息服务费
     */
    private Long infoServiceFee;

    /**
     * 技术服务费
     */
    private Long technicalServiceFee;

    /**
     * 奖励金额
     */
    private Long rewardAmount;

    /**
     * 客诉金额
     */
    private Long complainAmount;

    /**
     * 优惠券使用总金额
     */
    private Long couponQuotaAmount;

    /**
     * 其他金额
     */
    private Long otherAmount;

    /**
     * 应结算金额
     */
    private Long rebateAmount;

    /**
     * 已结算金额
     */
    private Long alreadyRebateAmount;

    /**
     * 待结算金额
     */
    private Long notRebateAmount;

    /**
     * 已提现金额
     */
    private Long alreadyCashAmount;

    /**
     * 可提现金额
     */
    private Long notCashAmount;

    /**
     * 对账状态[---Status]
     */
    private String financeStatus;

    /**
     * 对账状态确认时间
     */
    private Date financeTime;

    /**
     * 对账确认人类型[UserTypeEnum]
     */
    private String financeUserType;

    /**
     * 对账确认人id
     */
    private Long financeUserId;

    /**
     * 相关单号
     */
    private String relatedOrder;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
