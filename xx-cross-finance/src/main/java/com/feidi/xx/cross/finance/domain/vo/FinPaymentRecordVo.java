package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.core.enums.DirectionEnum;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.PaymentStatusEnum;
import com.feidi.xx.common.core.enums.RecordTypeEnum;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.cross.finance.domain.FinPaymentRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 支付记录视图对象 Fin_payment_record
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinPaymentRecord.class, convertGenerate = false)
public class FinPaymentRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 关联id
     */
    @ExcelProperty(value = "关联id")
    private Long joinId;

    /**
     * 支付方式 {@link com.feidi.xx.common.core.enums.PaymentTypeEnum}
     */
    private String paymentType;

    /**
     * AppId
     */
    @ExcelProperty(value = "AppId")
    private String appId;

    /**
     * 业务单号
     */
    @ExcelProperty(value = "业务单号")
    private String outBizNo;

    /**
     * 流水单号
     */
    @ExcelProperty(value = "流水单号")
    private String flowNo;

    /**
     * 金额
     */
    @ExcelProperty(value = "金额")
    private Long amount;

    /**
     * 交易时间
     */
    @ExcelProperty(value = "交易时间")
    private String tradeTime;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = RecordTypeEnum.class)
    private String type;

    /**
     * 进出
     */
    @ExcelProperty(value = "进出", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = DirectionEnum.class)
    private String direction;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = PaymentStatusEnum.class)
    private String status;

    /**
     * 是否退款
     */
    @ExcelProperty(value = "是否退款", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = IsYesEnum.class)
    private String isRefund;

    /**
     * 交易状态码
     */
    @ExcelProperty(value = "交易状态码")
    private String code;

    /**
     * 交易提示信息
     */
    @ExcelProperty(value = "交易提示信息")
    private String msg;


}
