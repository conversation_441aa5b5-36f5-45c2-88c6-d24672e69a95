package com.feidi.xx.cross.finance.domain.vo.driver;

import com.feidi.xx.cross.finance.domain.FinTransfer;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * 转账视图对象 Fin_transfer
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinTransfer.class)
public class FinTransferFlowDrvVo extends FinFlowDrvBase {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 金额
     */
    private Long amount;

}
