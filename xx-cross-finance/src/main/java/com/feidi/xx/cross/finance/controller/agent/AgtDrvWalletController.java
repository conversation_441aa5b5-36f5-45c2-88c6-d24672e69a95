package com.feidi.xx.cross.finance.controller.agent;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.domain.bo.FinAccountBo;
import com.feidi.xx.cross.finance.domain.bo.FinWalletQueryBo;
import com.feidi.xx.cross.finance.domain.vo.*;
import com.feidi.xx.cross.finance.service.IFinDrvWalletService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 代理商 - 司机钱包
 * 前端访问路由地址为:/finance/drvWallet
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Validated
@RestController
@RequiredArgsConstructor
@SaCheckRole(UserTypeEnum.UserType.AGENT_USER)
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/drvWallet")
public class AgtDrvWalletController extends BaseController {

    private final IFinDrvWalletService FinDrvWalletService;

    /**
     * 查询司机钱包列表
     */
    @GetMapping("/list")
    public TableDataInfo<FinWalletListVo> list(FinWalletQueryBo bo, PageQuery pageQuery) {
        Long agentId = LoginHelper.getAgentId();
        if (bo.getAgentId() != null) {
            bo.setAgentIds(CollUtil.newArrayList(bo.getAgentId()));
        } else {
            bo.setAgentId(agentId);
        }
        bo.setUserType(UserTypeEnum.AGENT_USER.getUserType());
        return FinDrvWalletService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出司机钱包列表
     */
    @Log(title = "司机钱包", businessType = BusinessType.EXPORT)
    @Download(name="司机钱包",module = ModuleConstants.FINANCE,mode="no")
    @PostMapping("/export")
    public Object export(@RequestBody FinWalletQueryBo bo,HttpServletResponse response) {
        Long agentId = LoginHelper.getAgentId();
        if (bo.getAgentId() != null) {
            bo.setAgentIds(CollUtil.newArrayList(bo.getAgentId()));
        } else {
            bo.setAgentId(agentId);
        }
        bo.setUserType(UserTypeEnum.AGENT_USER.getUserType());
        List<FinWalletListVo> list = FinDrvWalletService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        List<FinWalletExportVo> convert = MapstructUtils.convert(list, FinWalletExportVo.class);
        ExcelUtil.exportExcel(convert, "司机钱包", FinWalletExportVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取司机钱包详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<FinDrvWalletVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(FinDrvWalletService.queryById(id));
    }

}
