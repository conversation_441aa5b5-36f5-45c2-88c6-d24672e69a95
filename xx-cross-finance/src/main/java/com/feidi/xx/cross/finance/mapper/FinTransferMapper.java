package com.feidi.xx.cross.finance.mapper;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.finance.domain.FinTransfer;
import com.feidi.xx.cross.finance.domain.vo.FinTransferVo;

import java.util.Date;
import java.util.List;

/**
 * 转账Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
public interface FinTransferMapper extends BaseMapperPlus<FinTransfer, FinTransferVo> {

    /**
     * 最近转账的司机
     */
    default List<Long> recent(long driverId, int num) {
        return selectObjs(Wrappers.<FinTransfer>lambdaQuery()
                .select(FinTransfer::getInDriverId)
                .eq(FinTransfer::getOutDriverId, driverId)
                .groupBy(FinTransfer::getInDriverId)
                .last(StrUtil.format("limit {}", num))
        );
    }


    /**
     * 获取最新的提现记录
     */
    default FinTransfer getLast(Long driverId) {
        return selectOne(Wrappers.<FinTransfer>lambdaQuery()
                .eq(FinTransfer::getOutDriverId, driverId)
                .orderByDesc(FinTransfer::getCreateTime)
                .last("limit 1"));
    }

    /**
     * 获取提现记录
     */
    default List<FinTransfer> listToday(Long driverId) {
        return selectList(Wrappers.<FinTransfer>lambdaQuery()
                .eq(FinTransfer::getOutDriverId, driverId)
                .ge(FinTransfer::getCreateTime, DateUtil.beginOfDay(DateUtil.date()))
                .le(FinTransfer::getCreateTime, DateUtil.endOfDay(DateUtil.date()))
        );
    }

    /**
     * 获取提现记录
     */
    default List<FinTransfer> listByTime(Long driverId, Date startTime, Date endTime) {
        return selectList(Wrappers.<FinTransfer>lambdaQuery()
                .eq(FinTransfer::getOutDriverId, driverId)
                .ge(FinTransfer::getCreateTime, startTime)
                .le(FinTransfer::getCreateTime, endTime)
        );
    }

    default List<FinTransfer> listByDriverId(Long driverId) {
        return selectList(Wrappers.<FinTransfer>lambdaQuery()
                .eq(FinTransfer::getOutDriverId, driverId));
    }

    default IPage<FinTransfer> pageByDriverId(Long driverId, IPage<FinTransfer> page) {
        return selectPage(page, Wrappers.<FinTransfer>lambdaQuery()
                .eq(FinTransfer::getOutDriverId, driverId));
    }

}
