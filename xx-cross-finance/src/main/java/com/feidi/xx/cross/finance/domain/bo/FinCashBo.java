package com.feidi.xx.cross.finance.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.finance.domain.FinCash;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 提现业务对象 fin_cash
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinCash.class, reverseConvertGenerate = false)
public class FinCashBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 单号
     */
    @NotBlank(message = "单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cashNo;

    /**
     * 代理商id
     */
    @NotNull(message = "代理商id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long agentId;

    /**
     * 司机id
     */
    @NotNull(message = "司机id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long driverId;

    /**
     * 账户id
     */
    @NotNull(message = "账户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long accountId;

    /**
     * 账户姓名
     */
    @NotBlank(message = "账户姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 账户手机号
     */
    @NotBlank(message = "账户手机号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phone;

    /**
     * 账号
     */
    @NotBlank(message = "账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String account;

    /**
     * 类型
     */
    @NotBlank(message = "类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accountType;

    /**
     * 提现金额
     */
    @NotNull(message = "提现金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long amount;

    /**
     * 服务费
     */
    private Long serviceFee;

    /**
     * 是否自动[IsYesEnum]
     */
    @NotBlank(message = "是否自动[IsYesEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isAuto;

    /**
     * 状态[CashAuditStatusEnum]
     */
    @NotBlank(message = "状态[CashAuditStatusEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 处理人id
     */
    @NotNull(message = "处理人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 处理人
     */
    @NotBlank(message = "处理人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userName;

    /**
     * 打款流水号
     */
    @NotBlank(message = "打款流水号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String flowNo;

    /**
     * 审批时间
     */
    private Date reviewTime;

    /**
     * 交易时间
     */
    private Date transDate;

    /**
     * 交易状态
     */
    private String transStatus;

    /**
     * 交易信息
     */
    private String transInfo;

    /**
     * 凭证 自动打款保存的是支付订单号，手动打款保存的是打款凭证照片的ossId
     */
    private String voucher;

    /**
     * AppId
     */
    private String appId;

    /**
     * 商户ID|OpenId
     */
    private String mchId;

    /**
     * 打款金额
     */
    private Long actualAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 相关单号
     */
    private List<Long> relatedOrder;


}
