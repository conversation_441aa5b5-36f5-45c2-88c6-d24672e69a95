package com.feidi.xx.cross.finance.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.common.payment.mq.PaymentRecordEvent;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.finance.domain.FinPaymentRecord;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 支付记录业务对象 Fin_payment_record
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMappers(value = {
        @AutoMapper(target = FinPaymentRecord.class, reverseConvertGenerate = false),
        @AutoMapper(target = PaymentRecordEvent.class, convertGenerate = false)
})
public class FinPaymentRecordBo extends TenantEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 支付方式 {@link com.feidi.xx.common.core.enums.PaymentTypeEnum} 就这里用到了！但也没用！
     */
    @NotBlank(message = "支付方式不能为空", groups = { AddGroup.class, EditGroup.class })
    private String paymentType;

    /**
     * AppId
     */
    @NotBlank(message = "AppId不能为空", groups = { AddGroup.class, EditGroup.class })
    private String appId;

    /**
     * 商户ID|OpenId
     */
    @NotBlank(message = "商户ID|OpenId不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mchId;

    /**
     * 关联表
     */
    @NotBlank(message = "关联表不能为空", groups = { AddGroup.class, EditGroup.class })
    private String joinTable;

    /**
     * 关联单号
     */
    @NotBlank(message = "关联单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String joinNo;

    /**
     * 关联id
     */
    @NotNull(message = "关联id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long joinId;

    /**
     * 业务单号
     */
    @NotBlank(message = "业务单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String outBizNo;

    /**
     * 流水单号
     */
    private String flowNo;

    /**
     * 交易ID
     */
    private String orderId;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long amount;

    /**
     * 交易时间
     */
    private String tradeTime;

    /**
     * 类型
     */
    private String type;

    /**
     * 进出
     */
    private String direction;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否退款
     */
    private String isRefund;

    /**
     * 退款单号
     */
    private String refundNo;

    /**
     * 退款金额
     */
    private Long refundAmount;

    /**
     * 交易状态码
     */
    private String code;

    /**
     * 交易提示信息
     */
    private String msg;

    /**
     * 请求参数
     */
    private String paramsJson;

    /**
     * 返回结果集
     */
    private String responseJson;

    /**
     * 是否回调插入
     */
    private String notify;

}
