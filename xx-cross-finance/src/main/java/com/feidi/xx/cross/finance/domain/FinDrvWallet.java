package com.feidi.xx.cross.finance.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 司机钱包对象 fin_drv_wallet
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("fin_drv_wallet")
public class FinDrvWallet extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 可提现金额
     */
    private Long balance;

    /**
     * 冻结金额
     */
    private Long freeze;

    /**
     * 累计提现收益
     */
    private Long expend;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 状态[WalletStatusEnum]
     */
    private String status;

    public Long getAccountAmount() {
        Long b = this.getBalance() == null ? 0L : this.getBalance();
        Long f = this.getFreeze() == null ? 0L : this.getFreeze();
        return b + f;
    }

    public static FinDrvWallet defaultWallet(Long driverId) {
        FinDrvWallet wallet = new FinDrvWallet();
        wallet.setDriverId(driverId);
        wallet.setBalance(0L);
        wallet.setFreeze(0L);
        wallet.setExpend(0L);
        return wallet;
    }
}
