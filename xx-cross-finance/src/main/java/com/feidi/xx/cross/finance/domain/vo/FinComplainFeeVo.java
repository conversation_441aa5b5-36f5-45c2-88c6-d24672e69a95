package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.cross.finance.domain.FinComplainFee;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 订单客诉费用视图对象 fin_complain_fee
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinComplainFee.class)
public class FinComplainFeeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 客诉费用类型
     */
    @ExcelProperty(value = "客诉费用类型[ComplainFeeTypeEnum]")
    private String feeType;

    /**
     * 客诉费用类型
     */
    private String feeTypeText;

    /**
     * 代理商名称
     */
    @ExcelProperty(value = "代理商名称")
    private String agentName;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机id")
    private Long driverId;

    /**
     * 司机姓名
     */
    @ExcelProperty(value = "司机姓名")
    private String driverName;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 订单金额
     */
    @ExcelProperty(value = "订单金额")
    private Long orderPrice;

    /**
     * 实际支付金额
     */
    @ExcelProperty(value = "实际支付金额")
    private Long payPrice;

    /**
     * 金额
     */
    @ExcelProperty(value = "金额")
    private Long amount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
