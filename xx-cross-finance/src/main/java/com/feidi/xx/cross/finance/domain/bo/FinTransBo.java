package com.feidi.xx.cross.finance.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.finance.domain.FinTransfer;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 转账业务对象 Fin_transfer
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@AutoMapper(target = FinTransfer.class, reverseConvertGenerate = false)
public class FinTransBo {

    /**
     * 转出司机id
     */
    private Long outDriverId;

    /**
     * 转出司机姓名
     */
    private String outDriverName;

    /**
     * 转出司机手机号
     */
    private String outDriverPhone;

    /**
     * 转入司机id
     */
    @NotNull(message = "转入司机id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long inDriverId;

    /**
     * 转入司机姓名
     */
    private String inDriverName;

    /**
     * 转入司机手机号
     */
    private String inDriverPhone;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long amount;

    /**
     * 类型
     */
    private String type;

    /**
     * 备注
     */
    private String remark;

    /**
     * accessToken
     */
    @NotBlank(message = "token不能为空", groups = { AddGroup.class })
    private String accessToken;

}
