package com.feidi.xx.cross.finance.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.idempotent.annotation.NoMQDuplicateConsume;
import com.feidi.xx.common.rocketmq.base.MessageWrapper;
import com.feidi.xx.cross.common.constant.finance.FinanceMQConstants;
import com.feidi.xx.cross.finance.domain.bo.FinDrvWalletFlowBo;
import com.feidi.xx.cross.finance.mq.event.FinDrvWalletFlowEvent;
import com.feidi.xx.cross.finance.service.IFinDrvWalletFlowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

/**
 * 司机钱包流水消费者
 *
 * <AUTHOR>
 * @date 2025/7/14
 */
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = FinanceMQConstants.FINANCE_DRIVER_WALLET_FLOW_TOPIC_KEY,
        consumerGroup = FinanceMQConstants.FINANCE_DRIVER_WALLET_FLOW_CG_KEY
)
@Slf4j(topic = "FinDrvWalletFlowConsumer")
public class FinDrvWalletFlowConsumer implements RocketMQListener<MessageWrapper<FinDrvWalletFlowEvent>> {

    private final IFinDrvWalletFlowService finDrvWalletFlowService;

    @NoMQDuplicateConsume(
            keyPrefix = "global:xx-finance-driver-wallet-flow:",
            key = "#messageWrapper.keys",
            keyTimeout = 60
    )
    @Override
    public void onMessage(MessageWrapper<FinDrvWalletFlowEvent> messageWrapper) {
        FinDrvWalletFlowEvent message = messageWrapper.getMessage();
        log.info("[消费者] 司机钱包流水 - 执行消费逻辑，消息体：{}", JSON.toJSONString(messageWrapper));
        // 记录司机钱包流水
        finDrvWalletFlowService.addDrvWalletFlow(BeanUtils.copyProperties(message, FinDrvWalletFlowBo.class));
    }
}
