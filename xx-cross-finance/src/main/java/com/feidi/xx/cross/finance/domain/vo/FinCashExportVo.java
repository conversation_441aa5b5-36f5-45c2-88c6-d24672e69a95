package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.cross.common.enums.finance.AccountTypeEnum;
import com.feidi.xx.cross.common.enums.finance.CashAuditStatusEnum;
import com.feidi.xx.cross.common.enums.finance.CashTransStatusEnum;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 提现视图对象 Fin_cash
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinCashListVo.class, convertGenerate = false)
public class FinCashExportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "提现单号")
    private String cashNo;

    @ExcelProperty(value = "司机id")
    private Long driverId;

    @ExcelProperty(value = "司机名称")
    private String driverName;

    @ExcelProperty(value = "司机手机号")
    private String driverPhone;

    @ExcelProperty(value = "代理商id")
    private Long agentId;

    @ExcelProperty(value = "代理商名称")
    private String agentName;

    @ExcelProperty(value = "提现账户真实名称")
    private String name;

    @ExcelProperty(value = "提现账户")
    private String account;

    @Enum2Text(enumClass = AccountTypeEnum.class)
    @ExcelProperty(value = "提现方式", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = AccountTypeEnum.class)
    private String accountType;

    @ReverseAutoMapping(target = "amount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanBigDecimal(source.getAmount()))")
    @ExcelProperty(value = "提现金额")
    private BigDecimal amount;

    @Enum2Text(enumClass = CashAuditStatusEnum.class)
    @ExcelProperty(value = "审核状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = CashAuditStatusEnum.class)
    private String status;

    @Enum2Text(enumClass = CashTransStatusEnum.class)
    @ExcelProperty(value = "打款状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = CashTransStatusEnum.class)
    private String transStatus;

    @ReverseAutoMapping(target = "isAuto", expression = "java(\"Y\".equals(source.getIsAuto()) ? \"自动打款\" : \"手动打款\")")
    @ExcelProperty(value = "打款方式")
    private String isAuto;

    @ExcelProperty(value = "打款流水号")
    private String flowNo;

    @ExcelProperty(value = "申请时间")
    private Date createTime;

    @ExcelProperty(value = "审核人")
    private String userName;

    @ExcelProperty(value = "审核时间")
    private Date reviewTime;

    @ExcelProperty(value = "打款时间")
    private Date transDate;

}
