package com.feidi.xx.cross.finance.dubbo;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.finance.api.RemoteFlowService;
import com.feidi.xx.cross.finance.api.domain.bo.RemoteFlowBo;
import com.feidi.xx.cross.finance.api.domain.vo.RemoteFlowVo;
import com.feidi.xx.cross.finance.domain.FinFlow;
import com.feidi.xx.cross.finance.mapper.FinFlowMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 流水服务
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteFlowServiceImpl implements RemoteFlowService {

    private final FinFlowMapper baseMapper;

    @Override
    public List<RemoteFlowVo> listByBo(RemoteFlowBo bo) {
        List<FinFlow> FinFlows = baseMapper.selectList(Wrappers.<FinFlow>lambdaQuery()
                .eq(bo.getId() != null, FinFlow::getId, bo.getId())
                .eq(bo.getFlowNo() != null, FinFlow::getFlowNo, bo.getFlowNo())
                .eq(bo.getAgentId() != null, FinFlow::getAgentId, bo.getAgentId())
                .eq(bo.getDriverId() != null, FinFlow::getDriverId, bo.getDriverId())
                .eq(bo.getDriverName() != null, FinFlow::getDriverName, bo.getDriverName())
                .eq(bo.getDriverPhone() != null, FinFlow::getDriverPhone, bo.getDriverPhone())
                .eq(bo.getJoinTable() != null, FinFlow::getJoinTable, bo.getJoinTable())
                .eq(bo.getJoinNo() != null, FinFlow::getJoinNo, bo.getJoinNo())
                .eq(bo.getJoinId() != null, FinFlow::getJoinId, bo.getJoinId())
                .eq(bo.getDirection() != null, FinFlow::getDirection, bo.getDirection())
                .eq(bo.getType() != null, FinFlow::getType, bo.getType())
                // 集合查询
                .in(bo.getJoinIds() != null, FinFlow::getJoinId, bo.getJoinIds())
        );
        return BeanUtils.copyToList(FinFlows, RemoteFlowVo.class);
    }

    @Override
    public int updateByBo(RemoteFlowBo updateBo, RemoteFlowBo queryBo) {
        return baseMapper.update(Wrappers.<FinFlow>lambdaUpdate()
                .set(updateBo.getFlowNo() != null, FinFlow::getFlowNo, updateBo.getFlowNo())
                .set(updateBo.getAgentId() != null, FinFlow::getAgentId, updateBo.getAgentId())
                .set(updateBo.getDriverId() != null, FinFlow::getDriverId, updateBo.getDriverId())
                .set(updateBo.getDriverName() != null, FinFlow::getDriverName, updateBo.getDriverName())
                .set(updateBo.getDriverPhone() != null, FinFlow::getDriverPhone, updateBo.getDriverPhone())
                .set(updateBo.getJoinTable() != null, FinFlow::getJoinTable, updateBo.getJoinTable())
                .set(updateBo.getJoinNo() != null, FinFlow::getJoinNo, updateBo.getJoinNo())
                .set(updateBo.getJoinId() != null, FinFlow::getJoinId, updateBo.getJoinId())
                .set(updateBo.getDirection() != null, FinFlow::getDirection, updateBo.getDirection())
                .set(updateBo.getType() != null, FinFlow::getType, updateBo.getType())
                // 查询
                .eq(queryBo.getId() != null, FinFlow::getId, queryBo.getId())
                .eq(queryBo.getFlowNo() != null, FinFlow::getFlowNo, queryBo.getFlowNo())
                .eq(queryBo.getAgentId() != null, FinFlow::getAgentId, queryBo.getAgentId())
                .eq(queryBo.getDriverId() != null, FinFlow::getDriverId, queryBo.getDriverId())
                .eq(queryBo.getDriverName() != null, FinFlow::getDriverName, queryBo.getDriverName())
                .eq(queryBo.getDriverPhone() != null, FinFlow::getDriverPhone, queryBo.getDriverPhone())
                .eq(queryBo.getJoinTable() != null, FinFlow::getJoinTable, queryBo.getJoinTable())
                .eq(queryBo.getJoinNo() != null, FinFlow::getJoinNo, queryBo.getJoinNo())
                .eq(queryBo.getJoinId() != null, FinFlow::getJoinId, queryBo.getJoinId())
                .eq(queryBo.getDirection() != null, FinFlow::getDirection, queryBo.getDirection())
                .eq(queryBo.getType() != null, FinFlow::getType, queryBo.getType())
                // 集合查询
                .in(queryBo.getJoinIds() != null, FinFlow::getJoinId, queryBo.getJoinIds())
        );
    }
}
