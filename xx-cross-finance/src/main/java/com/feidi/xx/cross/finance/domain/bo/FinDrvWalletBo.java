package com.feidi.xx.cross.finance.domain.bo;

import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.finance.domain.FinDrvWallet;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 司机钱包业务对象 Fin_drv_wallet
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinDrvWallet.class, reverseConvertGenerate = false)
public class FinDrvWalletBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 方向【1.进  0.出】
     */
    @NotNull(message = "进出方向不能为空", groups = { EditGroup.class })
    private String direction;

    /**
     * 调账金额
     */
    @NotNull(message = "调账金额不能为空", groups = { EditGroup.class })
    private Long adjustAmount;

    /**
     * 备注
     */
    @NotNull(message = "备注不能为空", groups = { EditGroup.class })
    private String remark;


}
