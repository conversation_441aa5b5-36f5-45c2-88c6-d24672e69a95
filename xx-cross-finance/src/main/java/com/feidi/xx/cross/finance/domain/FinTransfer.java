package com.feidi.xx.cross.finance.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.xx.RandomUtils;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;
import java.util.List;

/**
 * 转账对象 fin_transfer
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("fin_transfer")
public class FinTransfer extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 单号
     */
    private String transferNo;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 转出司机id
     */
    private Long outDriverId;

    /**
     * 转出司机姓名
     */
    private String outDriverName;

    /**
     * 转出司机手机号
     */
    private String outDriverPhone;

    /**
     * 转入司机id
     */
    private Long inDriverId;

    /**
     * 转入司机姓名
     */
    private String inDriverName;

    /**
     * 转入司机手机号
     */
    private String inDriverPhone;

    /**
     * 金额
     */
    private Long amount;

    /**
     * 类型[TransferTypeEnum]
     */
    private String type;

    /**
     * 操作人类型
     */
    private String userType;

    /**
     * 操作人id
     */
    private Long userId;

    /**
     * 操作姓名
     */
    private String userName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 相关订单id
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> relatedOrder;

    // TODO 生成单号 【trf+YYYYMMDDHHMMSS+2位随机数】
    public static String generateTransferNo() {
        return  "trf" + DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSS) + RandomUtils.randomInt(10, 99);
    }

}
