package com.feidi.xx.cross.finance.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.cross.finance.domain.FinAccount;
import com.feidi.xx.cross.finance.domain.vo.FinAccountVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 商家账户Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface FinAccountMapper extends BaseMapperPlus<FinAccount, FinAccountVo> {

    /**
     * 查询主账号
     * @param main
     * @param channel
     * @param direction
     * @return
     */
    default FinAccount queryByMain(String main, String channel, String appId, String direction) {
        return selectOne(Wrappers.<FinAccount>lambdaQuery()
                .eq(FinAccount::getMain, main)
                .eq(FinAccount::getChannel, channel)
                .eq(FinAccount::getAppId, appId)
                .eq(FinAccount::getDirection, direction)
        );
    }

    /**
     * 根据代理商ID查询
     * @param agentId
     * @param channel
     * @param direction
     * @return
     */
    default FinAccount queryByAgentId(Long agentId, String channel, String appId, String direction) {
        return selectOne(Wrappers.<FinAccount>lambdaQuery()
                .eq(FinAccount::getAgentId, agentId)
                .eq(FinAccount::getChannel, channel)
                .eq(FinAccount::getAppId, appId)
                .eq(FinAccount::getDirection, direction)
        );
    }

    /**
     * 重置主账号
     * @param channel
     * @return
     */
    default int resetMainAccount(String channel, String appId, String direction) {
        return update(null, Wrappers.<FinAccount>lambdaUpdate()
                .set(FinAccount::getMain, IsYesEnum.NO.getCode())
                .eq(FinAccount::getChannel, channel)
                .eq(FinAccount::getAppId, appId)
                .eq(FinAccount::getDirection, direction)
        );
    }

}
