package com.feidi.xx.cross.finance.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.finance.domain.bo.FinComplainFeeBo;
import com.feidi.xx.cross.finance.domain.vo.FinComplainFeeVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 订单客诉费用Service接口
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface IFinComplainFeeService {

    /**
     * 查询订单客诉费用
     *
     * @param id 主键
     * @return 订单客诉费用
     */
    FinComplainFeeVo queryById(Long id);

    /**
     * 分页查询订单客诉费用列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单客诉费用分页列表
     */
    TableDataInfo<FinComplainFeeVo> queryPageList(FinComplainFeeBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的订单客诉费用列表
     *
     * @param bo 查询条件
     * @return 订单客诉费用列表
     */
    List<FinComplainFeeVo> queryList(FinComplainFeeBo bo);

    /**
     * 新增订单客诉费用
     *
     * @param bo 订单客诉费用
     * @return 是否新增成功
     */
    Boolean insertByBo(FinComplainFeeBo bo);

    /**
     * 修改订单客诉费用
     *
     * @param bo 订单客诉费用
     * @return 是否修改成功
     */
    Boolean updateByBo(FinComplainFeeBo bo);

    /**
     * 批量修改
     *
     * @param bos
     * @return
     */
    Boolean updateByBos(List<FinComplainFeeBo> bos);

    /**
     * 校验并批量删除订单客诉费用信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 订单客诉费用
     *
     * @param orderVos 客诉订单信息
     * @param financeDate 账期
     * @return
     */
    Boolean complainFee(List<RemoteOrderVo> orderVos, Date financeDate);
}
