package com.feidi.xx.cross.finance.controller.driver;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.domain.bo.FinFlowDrvBo;
import com.feidi.xx.cross.finance.domain.bo.FinFlowQueryBo;
import com.feidi.xx.cross.finance.domain.vo.FinFlowVo;
import com.feidi.xx.cross.finance.domain.vo.driver.FinCashFlowDrvVo;
import com.feidi.xx.cross.finance.domain.vo.driver.FinFlowIncomeOrderVo;
import com.feidi.xx.cross.finance.domain.vo.driver.FinTransferFlowDrvVo;
import com.feidi.xx.cross.finance.service.IFinFlowDrvService;
import com.feidi.xx.cross.finance.service.IFinFlowService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 司机 - 资金流水
 * 前端访问路由地址为:/finance/flow
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@SaCheckRole(UserTypeEnum.UserType.DRIVER_USER)
@RequestMapping(WebConstants.DRIVER_ROUTE_PREFIX+"/flow")
public class DrvFlowController extends BaseController {

    private final IFinFlowDrvService FinFlowDrvService;

    private final IFinFlowService FinFlowService;

    /**
     * 查询资金流水列表
     */
    @GetMapping("/list")
    public R<TableDataInfo<FinFlowVo>> list(@Validated FinFlowQueryBo bo, PageQuery pageQuery) {
        bo.setDriverId(LoginHelper.getUserId());
        return R.ok(FinFlowService.queryPageList(bo, pageQuery));
    }

    /**
     * 收入
     */
    @GetMapping("/income")
    public R<TableDataInfo<FinFlowIncomeOrderVo>> income(@Validated FinFlowDrvBo bo, PageQuery pageQuery) {
        return R.ok(FinFlowDrvService.income(bo, pageQuery));
    }

    /**
     * 提现
     */
    @GetMapping("/cash")
    public R<TableDataInfo<FinCashFlowDrvVo>> cash(@Validated FinFlowDrvBo bo, PageQuery pageQuery) {
        return R.ok(FinFlowDrvService.cash(bo, pageQuery));
    }

    /**
     * 转账
     */
    @GetMapping("/transfer")
    public R<TableDataInfo<FinTransferFlowDrvVo>> transfer(@Validated FinFlowDrvBo bo, PageQuery pageQuery) {
        return R.ok(FinFlowDrvService.transfer(bo, pageQuery));
    }

}
