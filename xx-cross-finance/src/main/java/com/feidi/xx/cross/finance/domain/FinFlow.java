package com.feidi.xx.cross.finance.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import java.io.Serial;
import java.util.List;

/**
 * 资金流水对象 fin_flow
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "fin_flow", autoResultMap = true)
public class FinFlow extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 编号
     */
    private String flowNo;

    /**
     * 代理id
     */
    private Long agentId;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 关联表
     */
    private String joinTable;

    /**
     * 关联单号
     */
    private String joinNo;

    /**
     * 关联id
     */
    private Long joinId;

    /**
     * 变动前总金额
     */
    private Long beforeAmount;

    /**
     * 变动金额
     */
    private Long amount;

    /**
     * 变动后总金额
     */
    private Long afterAmount;

    /**
     * 变动前提现金额
     */
    private Long beforeCash;

    /**
     * 变动的提现金额
     */
    private Long cash;

    /**
     * 变动后提现金额
     */
    private Long afterCash;

    /**
     * 进出[DirectionEnum]
     */
    private String direction;

    /**
     * 类型[FlowTypeEnum]
     */
    private String type;

    /**
     * 备注
     */
    private String remark;

    /**
     * 上一条记录id
     */
    private Long preId;

    /**
     * 相关订单id
     */
    @TableField(typeHandler = com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler.class)
    private List<Long> relatedOrder;


}
