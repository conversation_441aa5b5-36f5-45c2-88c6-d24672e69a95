package com.feidi.xx.cross.finance.controller.notify;

import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.PaymentChannelEnum;
import com.feidi.xx.common.core.enums.PaymentTypeEnum;
import com.feidi.xx.common.core.enums.RecordTypeEnum;
import com.feidi.xx.common.core.utils.xx.RandomUtils;
import com.feidi.xx.common.payment.domain.PaymentTenantEntity;
import com.feidi.xx.common.payment.domain.payment.bo.PayBo;
import com.feidi.xx.common.payment.domain.payment.vo.PrePayResultVo;
import com.feidi.xx.common.payment.domain.trans.bo.TransferBo;
import com.feidi.xx.common.payment.domain.trans.vo.TransferVo;
import com.feidi.xx.common.payment.strategy.IPaymentService;
import com.feidi.xx.common.payment.strategy.ITransService;
import com.feidi.xx.common.web.core.BaseController;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 支付测试
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/payment/test")
public class PaymentController extends BaseController {

    private final ITransService transService;

    private final IPaymentService paymentService;

    /**
     * 转账
     */
    @GetMapping()
    public R trans(PaymentTenantEntity entity) {
        TransferBo transferBo = new TransferBo();
        transferBo.setTenantId(entity.getTenantId());
        transferBo.setAgentId(entity.getAgentId());
        transferBo.setChannel(PaymentChannelEnum.ALIPAY);
        transferBo.setPaymentType(PaymentTypeEnum.ALI_PAY.getCode());
        transferBo.setOutBizNo("**********"+ RandomUtils.randomInt(1,1000));
        //transferBo.setOutBizNo("*************");
        transferBo.setName("gbynvm7803");
        transferBo.setAccount("<EMAIL>");
        transferBo.setTransTypeEnum(RecordTypeEnum.DRIVER_CASH);
        transferBo.setTransAmount(RandomUtils.randomInt(0,80).longValue());
        TransferVo transfer = transService.transfer(transferBo);
        return R.ok(transfer);
    }

    /**
     * app支付
     */
    @GetMapping("/prepay")
    public R prepay(PayBo entity) {
        entity.setChannel(PaymentChannelEnum.WX_PAY);
        PrePayResultVo resultVo = paymentService.paymentApp(entity);
        return R.ok(resultVo);
    }
}
