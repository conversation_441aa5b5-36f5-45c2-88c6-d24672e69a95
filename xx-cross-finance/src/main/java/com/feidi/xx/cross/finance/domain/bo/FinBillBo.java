package com.feidi.xx.cross.finance.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.finance.domain.FinBill;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 订单对账业务对象 fin_bill
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinBill.class, reverseConvertGenerate = false)
public class FinBillBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 代理商id
     */
    @NotNull(message = "代理商id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long agentId;

    /**
     * 代理商名称
     */
    @NotBlank(message = "代理商名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String agentName;

    /**
     * 账单月份
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "账单月份不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date month;

    /**
     * 订单量
     */
    @NotNull(message = "订单量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderNumber;

    /**
     * 订单金额
     */
    @NotNull(message = "订单金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderAmount;

    /**
     * 司机收益
     */
    @NotNull(message = "司机收益不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long driverProfit;

    /**
     * 代收款
     */
    @NotNull(message = "代收款不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long proxyAmount;

    /**
     * 应收信息服务费
     */
    @NotNull(message = "应收信息服务费不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long infoServiceFee;

    /**
     * 技术服务费
     */
    @NotNull(message = "技术服务费不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long technicalServiceFee;

    /**
     * 奖励金额
     */
    @NotNull(message = "奖励金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long rewardAmount;

    /**
     * 客诉金额
     */
    @NotNull(message = "客诉金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long complainAmount;

    /**
     * 优惠券使用总金额
     */
    private Long couponQuotaAmount;

    /**
     * 其他金额
     */
    @NotNull(message = "其他金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long otherAmount;

    /**
     * 应结算金额
     */
    @NotNull(message = "应结算金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long rebateAmount;

    /**
     * 已结算金额
     */
    @NotNull(message = "已结算金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long alreadyRebateAmount;

    /**
     * 已结算金额
     */
    @NotNull(message = "已结算金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long notRebateAmount;

    /**
     * 已提现金额
     */
    @NotNull(message = "已提现金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long alreadyCashAmount;

    /**
     * 可提现金额
     */
    @NotNull(message = "可提现金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long notCashAmount;

    /**
     * 对账状态[---Status]
     */
    @NotBlank(message = "对账状态[---Status]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String financeStatus;

    /**
     * 对账状态确认时间
     */
    private Date financeTime;

    /**
     * 对账确认人类型[UserTypeEnum]
     */
    private String financeUserType;

    /**
     * 对账确认人id
     */
    private Long financeUserId;

    /**
     * 相关单号
     */
    private String relatedOrder;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 主键id集合
     */
    private List<Long> ids;
}
