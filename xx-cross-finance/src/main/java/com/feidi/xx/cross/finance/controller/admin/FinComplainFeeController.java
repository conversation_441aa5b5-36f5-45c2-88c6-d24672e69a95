package com.feidi.xx.cross.finance.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.domain.bo.FinComplainFeeBo;
import com.feidi.xx.cross.finance.domain.vo.FinComplainFeeExportVo;
import com.feidi.xx.cross.finance.domain.vo.FinComplainFeeVo;
import com.feidi.xx.cross.finance.service.IFinComplainFeeService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 订单客诉费用
 * 前端访问路由地址为:/finance/complainFee
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/complainFee")
public class FinComplainFeeController extends BaseController {

    private final IFinComplainFeeService finComplainFeeService;

    /**
     * 查询订单客诉费用列表
     */
    @SaCheckPermission("finance:complainFee:list")
    @GetMapping("/list")
    public TableDataInfo<FinComplainFeeVo> list(FinComplainFeeBo bo, PageQuery pageQuery) {
        return finComplainFeeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出订单客诉费用列表
     */
    @SaCheckPermission("finance:complainFee:export")
    @Log(title = "订单客诉费用", businessType = BusinessType.EXPORT)
    @Download(name="客诉金额",module = ModuleConstants.FINANCE, mode="no")
    @PostMapping("/export")
    public Object export(FinComplainFeeBo bo, HttpServletResponse response) {
        List<FinComplainFeeVo> list = finComplainFeeService.queryList(bo);

        List<FinComplainFeeExportVo> exportVos = MapstructUtils.convert(list, FinComplainFeeExportVo.class);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(exportVos, "客诉金额", FinComplainFeeExportVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取订单客诉费用详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("finance:complainFee:query")
    @GetMapping("/{id}")
    public R<FinComplainFeeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(finComplainFeeService.queryById(id));
    }

    /**
     * 新增订单客诉费用
     */
    @SaCheckPermission("finance:complainFee:add")
    @Log(title = "订单客诉费用", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinComplainFeeBo bo) {
        return toAjax(finComplainFeeService.insertByBo(bo));
    }

    /**
     * 修改订单客诉费用
     */
    @SaCheckPermission("finance:complainFee:edit")
    @Log(title = "订单客诉费用", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinComplainFeeBo bo) {
        return toAjax(finComplainFeeService.updateByBo(bo));
    }

    /**
     * 编辑客诉费用
     */
    @SaCheckPermission("finance:complainFee:edit")
    @Log(title = "订单客诉费用", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/batchEdit")
    public R<Void> batchEdit(@Validated(EditGroup.class) @RequestBody List<FinComplainFeeBo> bos) {
        return toAjax(finComplainFeeService.updateByBos(bos));
    }

    /**
     * 删除订单客诉费用
     *
     * @param ids 主键串
     */
    @SaCheckPermission("finance:complainFee:remove")
    @Log(title = "订单客诉费用", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(finComplainFeeService.deleteWithValidByIds(List.of(ids), true));
    }
}
