package com.feidi.xx.cross.finance.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.domain.bo.FinBillBo;
import com.feidi.xx.cross.finance.domain.vo.FinBillExportVo;
import com.feidi.xx.cross.finance.domain.vo.FinBillVo;
import com.feidi.xx.cross.finance.service.IFinBillService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 订单对账
 * 前端访问路由地址为:/finance/bill
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/bill")
public class FinBillController extends BaseController {

    private final IFinBillService finBillService;

    /**
     * 查询订单对账列表
     */
    @SaCheckPermission("finance:bill:list")
    @GetMapping("/list")
    public TableDataInfo<FinBillVo> list(FinBillBo bo, PageQuery pageQuery) {
        return finBillService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出订单对账列表
     */
    @SaCheckPermission("finance:bill:export")
    @Log(title = "订单对账", businessType = BusinessType.EXPORT)
    @Download(name="订单对账",module = ModuleConstants.FINANCE, mode="no")
    @PostMapping("/export")
    public Object export(FinBillBo bo, HttpServletResponse response) {
        List<FinBillVo> list = finBillService.queryList(bo);
        List<FinBillExportVo> exportVos = MapstructUtils.convert(list, FinBillExportVo.class);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(exportVos, "订单对账", FinBillExportVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取订单对账详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("finance:bill:query")
    @GetMapping("/{id}")
    public R<FinBillVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(finBillService.queryById(id));
    }

    /**
     * 新增订单对账
     */
    @SaCheckPermission("finance:bill:add")
    @Log(title = "订单对账", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinBillBo bo) {
        return toAjax(finBillService.insertByBo(bo));
    }

    /**
     * 修改订单对账
     */
    @SaCheckPermission("finance:bill:edit")
    @Log(title = "订单对账", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinBillBo bo) {
        return toAjax(finBillService.updateByBo(bo));
    }

    /**
     * 删除订单对账
     *
     * @param ids 主键串
     */
    @SaCheckPermission("finance:bill:remove")
    @Log(title = "订单对账", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(finBillService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 确认账单
     */
    @SaCheckPermission("finance:bill:edit")
    @GetMapping("/confirm/{id}")
    public R<Void> confirm(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return toAjax(finBillService.confirm(id));
    }

    /**
     * 账单生成
     */
    @SaCheckPermission("finance:bill:add")
    @GetMapping("/order")
    public R<Void> order() {
        finBillService.orderBill();
        return R.ok();
    }
}
