package com.feidi.xx.cross.finance.domain.bo;

import cn.hutool.core.date.DateTime;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.finance.domain.FinTransfer;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 转账业务对象 Fin_transfer
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinTransfer.class, reverseConvertGenerate = false)
public class FinTransferBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 单号
     */
    private String transferNo;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 转出司机id
     */
    private Long outDriverId;

    /**
     * 转出司机姓名
     */
    private String outDriverName;

    /**
     * 转出司机手机号
     */
    private String outDriverPhone;

    /**
     * 转入司机id
     */
    @NotNull(message = "转入司机id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long inDriverId;

    /**
     * 转入司机姓名
     */
    private String inDriverName;

    /**
     * 转入司机手机号
     */
    private String inDriverPhone;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long amount;

    /**
     * 类型
     */
    private String type;

    /**
     * 操作人类型
     */
    private String userType;

    /**
     * 操作人id
     */
    private Long userId;

    /**
     * 操作姓名
     */
    private String userName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 资金密码
     */
    @NotBlank(message = "资金密码不能为空", groups = { AddGroup.class })
    private String capitalPassword;

    /**
     * 开始
     */
    private DateTime startTime;

    /**
     * 结算
     */
    private DateTime endTime;

}
