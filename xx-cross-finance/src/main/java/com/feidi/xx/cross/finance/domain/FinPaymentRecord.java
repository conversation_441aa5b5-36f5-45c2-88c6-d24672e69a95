package com.feidi.xx.cross.finance.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import java.io.Serial;

/**
 * 支付记录对象 fin_payment_record
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("fin_payment_record")
public class FinPaymentRecord extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 支付方式[PaymentTypeEnum]
     */
    private String paymentType;

    /**
     * AppId
     */
    private String appId;

    /**
     * 商户ID|OpenId
     */
    private String mchId;

    /**
     * 关联表
     */
    private String joinTable;

    /**
     * 关联单号
     */
    private String joinNo;

    /**
     * 关联id
     */
    private Long joinId;

    /**
     * 业务单号
     */
    private String outBizNo;

    /**
     * 流水单号
     */
    private String flowNo;

    /**
     * 交易ID
     */
    private String orderId;

    /**
     * 金额
     */
    private String amount;

    /**
     * 交易时间
     */
    private String tradeTime;

    /**
     * 类型[RecordTypeEnum]
     */
    private String type;

    /**
     * 进出[DirectionEnum]
     */
    private String direction;

    /**
     * 状态[PaymentStatusEnum]
     */
    private String status;

    /**
     * 是否退款[IsYesEnum]
     */
    private String isRefund;

    /**
     * 退款单号
     */
    private String refundNo;

    /**
     * 退款金额
     */
    private Long refundAmount;

    /**
     * 交易状态码
     */
    private String code;

    /**
     * 交易提示信息
     */
    private String msg;

    /**
     * 请求参数
     */
    private String paramsJson;

    /**
     * 返回结果集
     */
    private String responseJson;

    /**
     * 是否回调插入
     */
    private String notify;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
