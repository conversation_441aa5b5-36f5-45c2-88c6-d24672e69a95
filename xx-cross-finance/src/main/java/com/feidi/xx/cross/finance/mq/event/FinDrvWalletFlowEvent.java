package com.feidi.xx.cross.finance.mq.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 司机钱包流水事件
 *
 * <AUTHOR>
 * @date 2025/7/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FinDrvWalletFlowEvent implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 流水类型
     */
    private String flowType;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 关联单号
     */
    private String orderNo;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 佣金收益
     */
    private Long orderProfit;

    /**
     * 奖励收益
     */
    private Long rewardProfit;

    /**
     * 订单转卖收益
     */
    private Long resellProfit;

    /**
     * 账户余额(冻结金额+可提现金额)
     */
    private Long accountAmount;

    /**
     * 可提现金额
     */
    private Long balance;

    /**
     * 冻结金额
     */
    private Long freeze;

    /**
     * 累计提现收益
     */
    private Long expend;

    /**
     * 提现中金额
     */
    private Long withdrawalAmount;
}
