package com.feidi.xx.cross.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.enums.UserStatusEnum;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.common.enums.finance.FinanceTypeEnum;
import com.feidi.xx.cross.common.enums.order.CreateModelEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.finance.domain.FinOrderFee;
import com.feidi.xx.cross.finance.domain.bo.FinOrderFeeBo;
import com.feidi.xx.cross.finance.domain.vo.FinOrderFeeVo;
import com.feidi.xx.cross.finance.mapper.FinOrderFeeMapper;
import com.feidi.xx.cross.finance.service.IFinOrderFeeService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.RemoteDriverService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 订单应收服务费Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@RequiredArgsConstructor
@Service
public class FinOrderFeeServiceImpl implements IFinOrderFeeService {

    private final FinOrderFeeMapper baseMapper;
    @DubboReference
    private final RemoteDriverService remoteDriverService;
    @DubboReference
    private final RemoteAgentService remoteAgentService;

    /**
     * 查询订单应收服务费
     *
     * @param id 主键
     * @return 订单应收服务费
     */
    @Override
    public FinOrderFeeVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询订单应收服务费列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单应收服务费分页列表
     */
    @Override
    public TableDataInfo<FinOrderFeeVo> queryPageList(FinOrderFeeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinOrderFee> lqw = buildQueryWrapper(bo);
        Page<FinOrderFeeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        result.getRecords().forEach(item -> {
            item.setFinanceTypeText(FinanceTypeEnum.getInfoByCode(item.getFinanceType()));
            item.setCreateModelText(CreateModelEnum.getInfoByCode(item.getCreateModel()));
            item.setPlatformCodeText(PlatformCodeEnum.getInfoByCode(item.getPlatformCode()));
        });
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的订单应收服务费列表
     *
     * @param bo 查询条件
     * @return 订单应收服务费列表
     */
    @Override
    public List<FinOrderFeeVo> queryList(FinOrderFeeBo bo) {
        LambdaQueryWrapper<FinOrderFee> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinOrderFee> buildQueryWrapper(FinOrderFeeBo bo) {
        LambdaQueryWrapper<FinOrderFee> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getFinanceDate() != null, FinOrderFee::getFinanceDate, bo.getFinanceDate());
        lqw.eq(bo.getDriverId() != null, FinOrderFee::getDriverId, bo.getDriverId());
        lqw.eq(bo.getAgentId() != null, FinOrderFee::getAgentId, bo.getAgentId());
        lqw.eq(bo.getOrderId() != null, FinOrderFee::getOrderId, bo.getOrderId());
        lqw.like(StringUtils.isNotBlank(bo.getDriverName()), FinOrderFee::getDriverName, bo.getDriverName());
        lqw.like(StringUtils.isNotBlank(bo.getOrderNo()), FinOrderFee::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getFinanceType()), FinOrderFee::getFinanceType, bo.getFinanceType());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateModel()), FinOrderFee::getCreateModel, bo.getCreateModel());
        if (StringUtils.isNotBlank(bo.getFinanceStartTime()) && StringUtils.isNotBlank(bo.getFinanceEndTime())) {
            lqw.ge(FinOrderFee::getFinanceDate, bo.getFinanceStartTime());
            lqw.le(FinOrderFee::getFinanceDate, bo.getFinanceEndTime());
        }
        return lqw;
    }

    /**
     * 新增订单应收服务费
     *
     * @param bo 订单应收服务费
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinOrderFeeBo bo) {
        FinOrderFee add = MapstructUtils.convert(bo, FinOrderFee.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改订单应收服务费
     *
     * @param bo 订单应收服务费
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinOrderFeeBo bo) {
        FinOrderFee update = MapstructUtils.convert(bo, FinOrderFee.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinOrderFee entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除订单应收服务费信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public Boolean orderFee(List<RemoteOrderVo> orderVos, List<Long> complainOrderIds, Date financeDate) {
        if (CollUtils.isEmpty(orderVos)) {
            return false;
        }

        if (financeDate == null) {
            financeDate = DateUtils.getBeginDayOfYesterday();
        }

        List<FinOrderFee> finOrderFees = new ArrayList<>();

        // 将代理商信息按照代理商id进行分组
        Map<Long, String> agentId2NameMap = remoteAgentService.getAllAgentInfo()
                .stream().collect(Collectors.toMap(RemoteAgentVo::getId, RemoteAgentVo::getCompanyName, (k1, k2) -> k1));

        // 将司机信息按照司机id进行分组
        List<String> driverStatuses = Stream.of(UserStatusEnum.OK.getCode(), UserStatusEnum.DISABLE.getCode()).toList();
        Map<Long, String> driverId2NameMap = remoteDriverService.queryByDriverStatus(driverStatuses)
                .stream().collect(Collectors.toMap(RemoteDriverVo::getId, RemoteDriverVo::getName, (k1, k2) -> k1));

        for (RemoteOrderVo orderVo : orderVos) {
            FinOrderFee finOrderFee = buildFinOrderFee(orderVo);

            finOrderFee.setFinanceDate(financeDate);
            finOrderFee.setAgentName(agentId2NameMap.get(orderVo.getAgentId()));
            finOrderFee.setDriverName(driverId2NameMap.get(orderVo.getDriverId()));
            // 客诉金额
            if (CollUtils.isNotEmpty(complainOrderIds) && complainOrderIds.contains(orderVo.getId())) {
                finOrderFee.setComplainAmount(orderVo.getOrderPrice());
            }

            finOrderFees.add(finOrderFee);
        }

        if (CollUtils.isNotEmpty(finOrderFees)) {
            baseMapper.insertBatch(finOrderFees);
        }

        return true;
    }

    private FinOrderFee buildFinOrderFee(RemoteOrderVo orderVo) {
        FinOrderFee finOrderFee = new FinOrderFee();

        finOrderFee.setAgentId(orderVo.getAgentId());
        finOrderFee.setDriverId(orderVo.getDriverId());
        finOrderFee.setOrderId(orderVo.getId());
        finOrderFee.setOrderNo(orderVo.getOrderNo());
        finOrderFee.setPlatformCode(orderVo.getPlatformCode());
        finOrderFee.setOrderPrice(orderVo.getOrderPrice());
        finOrderFee.setPayPrice(orderVo.getPayPrice());
        finOrderFee.setFinanceType(FinanceTypeEnum.PROFIT.getCode());
        finOrderFee.setCreateModel(orderVo.getCreateModel());

        // 应收信息服务费比例
        finOrderFee.setInfoServiceRate(new BigDecimal("8.5"));
        // 司机下单：订单金额 * 2.5%
        if (Objects.equals(orderVo.getCreateModel(), CreateModelEnum.DRIVER_ORDER.getCode())) {
            finOrderFee.setInfoServiceRate(new BigDecimal("2.5"));
        }
        finOrderFee.setInfoServiceFee(ArithUtils.profitUseBigDecimal(orderVo.getOrderPrice(), finOrderFee.getInfoServiceRate()));

        // 技术服务费 = (订单费用-作弊订单费用) * 1.5%
        finOrderFee.setTechnicalServiceRate(new BigDecimal("1.5"));
        finOrderFee.setTechnicalServiceFee(ArithUtils.profitUseBigDecimal(orderVo.getOrderPrice(), finOrderFee.getTechnicalServiceRate()));

        // 奖励金额
        BigDecimal rewardAmountRate = Objects.equals(orderVo.getCreateModel(), CreateModelEnum.DRIVER_ORDER.getCode()) ? new BigDecimal("2") : new BigDecimal("5");
        finOrderFee.setRewardAmount(ArithUtils.profitUseBigDecimal(orderVo.getOrderPrice(), rewardAmountRate));

        // 代收款（订单金额扣除信息服务费和技术服务费后的款项）
        finOrderFee.setProxyAmount(orderVo.getOrderPrice() - finOrderFee.getInfoServiceFee() - finOrderFee.getTechnicalServiceFee());
        // 客诉金额
        finOrderFee.setComplainAmount(0L);

        return finOrderFee;
    }
}
