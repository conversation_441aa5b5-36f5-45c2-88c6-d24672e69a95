package com.feidi.xx.cross.finance.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.domain.bo.FinDrvWalletFlowBo;
import com.feidi.xx.cross.finance.domain.vo.FinDrvWalletFlowExportVo;
import com.feidi.xx.cross.finance.domain.vo.FinDrvWalletFlowVo;
import com.feidi.xx.cross.finance.service.IFinDrvWalletFlowService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 司机钱包流水
 * 前端访问路由地址为:/finance/drvWalletFlow
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/drvWalletFlow")
public class FinDrvWalletFlowController extends BaseController {

    private final IFinDrvWalletFlowService finDrvWalletFlowService;

    /**
     * 查询司机钱包流水列表
     */
    @SaCheckPermission("finance:drvWalletFlow:list")
    @GetMapping("/list")
    public TableDataInfo<FinDrvWalletFlowVo> list(FinDrvWalletFlowBo bo, PageQuery pageQuery) {
        return finDrvWalletFlowService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出司机钱包流水列表
     */
    @SaCheckPermission("finance:drvWalletFlow:export")
    @Log(title = "司机钱包流水", businessType = BusinessType.EXPORT)
    @Download(name="司机钱包流水",module = ModuleConstants.FINANCE, mode="no")
    @PostMapping("/export")
    public Object export(FinDrvWalletFlowBo bo, HttpServletResponse response) {
        List<FinDrvWalletFlowVo> list = finDrvWalletFlowService.queryList(bo);
        List<FinDrvWalletFlowExportVo> exportVos = MapstructUtils.convert(list, FinDrvWalletFlowExportVo.class);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(exportVos, "司机钱包流水", FinDrvWalletFlowExportVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取司机钱包流水详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("finance:drvWalletFlow:query")
    @GetMapping("/{id}")
    public R<FinDrvWalletFlowVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(finDrvWalletFlowService.queryById(id));
    }

    /**
     * 新增司机钱包流水
     */
    @SaCheckPermission("finance:drvWalletFlow:add")
    @Log(title = "司机钱包流水", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinDrvWalletFlowBo bo) {
        return toAjax(finDrvWalletFlowService.insertByBo(bo));
    }

    /**
     * 修改司机钱包流水
     */
    @SaCheckPermission("finance:drvWalletFlow:edit")
    @Log(title = "司机钱包流水", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinDrvWalletFlowBo bo) {
        return toAjax(finDrvWalletFlowService.updateByBo(bo));
    }

    /**
     * 删除司机钱包流水
     *
     * @param ids 主键串
     */
    @SaCheckPermission("finance:drvWalletFlow:remove")
    @Log(title = "司机钱包流水", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(finDrvWalletFlowService.deleteWithValidByIds(List.of(ids), true));
    }
}
