package com.feidi.xx.cross.finance.controller.driver;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.domain.bo.FinCashApplyBo;
import com.feidi.xx.cross.finance.domain.bo.FinCashQueryBo;
import com.feidi.xx.cross.finance.domain.vo.FinCashListVo;
import com.feidi.xx.cross.finance.service.IFinCashService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 司机 - 提现
 * 前端访问路由地址为:/finance/cash
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@SaCheckRole(UserTypeEnum.UserType.DRIVER_USER)
@RequestMapping(WebConstants.DRIVER_ROUTE_PREFIX + "/cash")
public class DrvCashController extends BaseController {

    private final IFinCashService FinCashService;

    /**
     * 查询提现列表
     */
    @Enum2TextAspect
    @GetMapping("/list")
    public TableDataInfo<FinCashListVo> list(FinCashQueryBo bo, PageQuery pageQuery) {
        bo.setDriverId(LoginHelper.getUserId());
        return FinCashService.queryPageList(bo, pageQuery);
    }

    /**
     * 新增提现
     */
    @Log(title = "提现", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinCashApplyBo bo) {
        bo.setDriverId(LoginHelper.getUserId());
        bo.setAgentId(LoginHelper.getAgentId());
        bo.setParentId(LoginHelper.getParentId());
        return toAjax(FinCashService.insertByBo(bo));
    }

}
