package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.finance.domain.FinDrvWallet;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinDrvWallet.class, convertGenerate = false)
public class FinWalletVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机id")
    private Long driverId;

    /**
     * 余额 单位：分
     */
    @ExcelProperty(value = "余额 单位：分")
    private Long total;

    /**
     * 可提现余额 单位：分
     */
    @ExcelProperty(value = "可提现余额 单位：分")
    private Long balance;

    /**
     * 冻结金额 单位：分
     */
    @ExcelProperty(value = "冻结金额 单位：分")
    private Long freeze;

}
