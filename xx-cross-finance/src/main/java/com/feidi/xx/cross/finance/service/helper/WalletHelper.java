package com.feidi.xx.cross.finance.service.helper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.feidi.xx.common.core.enums.CacheKeyEnum;
import com.feidi.xx.cross.common.constant.finance.FinanceConstants;
import com.feidi.xx.cross.common.enums.finance.PwdVerifyEnum;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.enums.finance.CashAuditStatusEnum;
import com.feidi.xx.cross.finance.domain.FinDrvWallet;
import com.feidi.xx.cross.finance.domain.FinFlow;
import com.feidi.xx.cross.finance.mapper.FinCashMapper;
import com.feidi.xx.cross.finance.mapper.FinDrvWalletMapper;
import com.feidi.xx.cross.finance.mapper.FinFlowMapper;
import com.feidi.xx.cross.finance.mapper.FinTransferMapper;
import com.feidi.xx.cross.power.api.RemoteDriverAccountService;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverAccountVo;
import com.feidi.xx.system.api.RemoteConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class WalletHelper {

    private final FinFlowMapper flowMapper;

    private final FinCashMapper cashMapper;

    private final FinTransferMapper transferMapper;

    private final FinDrvWalletMapper walletMapper;

    @DubboReference
    private final RemoteConfigService remoteConfigService;

    @DubboReference
    private final RemoteDriverAccountService remoteDriverAccountService;

    /**
     * 是否需要初始化账号
     * @param driverId
     * @return
     */
    public boolean initAccount(long driverId) {
        List<RemoteDriverAccountVo> accounts = remoteDriverAccountService.getDriverAccounts(Arrays.asList(driverId));
        return CollUtil.isEmpty(accounts);
    }

    /**
     * 转账申请是否可用
     * @param driverId
     * @return
     */
    public boolean transferApplicable(long driverId) {
        return transferApplicable(driverId, true);
    }

    /**
     * 转账申请是否可用
     * @param driverId
     * @param ignoreError 是否忽略错误
     * @return
     */
    public boolean transferApplicable(long driverId, boolean ignoreError) {
        // 历史提现
        if (cashMapper.existByStatus(driverId, CashAuditStatusEnum.ING) && !ignoreError) {
            throw new ServiceException(FinanceConstants.CASHING_MSG);
        }
        // 频率校验
        String transferFrequency = remoteConfigService.selectValueByKey(FinanceConstants.TRANSFER_FREQUENCY);
        Assert.notNull(transferFrequency, "请在系统配置设置【单日最大转账频率】");
        int transLimit = Convert.toInt(transferFrequency);
        boolean ret = getTransferTimesToday(driverId) <= transLimit;
        if (!ret && !ignoreError) {
            throw new ServiceException(StrUtil.format(FinanceConstants.TRANSFER_APPLY_EXCEED_MSG, transLimit));
        }
        return ret;
    }

    public int getTransferTimesToday(long driverId) {
        return transferMapper.listToday(driverId).size();
    }

    /**
     * 转账申请是否可用
     * @param driverId
     * @return
     */
    public boolean cashApplicable(long driverId) {
        return cashApplicable(driverId, true);
    }

    /**
     * 钱包状态异常禁止转账或提现
     * @param driverId 司机id
     * @return
     */

    public boolean transferOrCashDisable(long driverId) {
        FinDrvWallet FinDrvWallet = walletMapper.selectByDriverId(driverId);
        if (FinDrvWallet==null || Objects.equals(FinDrvWallet.getStatus(), StatusEnum.DISABLE.getCode())){
            throw new ServiceException(FinanceConstants.WALLET_DISABLE);
        }
        return true;
    }

    /**
     * 提现申请是否可用
     * @param driverId
     * @param ignoreError 是否忽略错误
     * @return
     */
    public boolean cashApplicable(long driverId, boolean ignoreError) {
        // 频率检验
        String cashFrequency = remoteConfigService.selectValueByKey(FinanceConstants.CASH_FREQUENCY);
        Assert.notNull(cashFrequency, "请在系统配置设置【单日最大提现频率】");
        int cashLimit = Convert.toInt(cashFrequency);
        if (cashMapper.existByStatus(driverId, CashAuditStatusEnum.ING) && !ignoreError) {
            throw new ServiceException(FinanceConstants.CASHING_MSG);
        }
        // 非驳回的提现记录
        boolean ret = getCashTimesToday(driverId) <= cashLimit;
        if (!ret && !ignoreError) {
            throw new ServiceException(StrUtil.format(FinanceConstants.CASH_APPLY_EXCEED_MSG, cashLimit));
        }
        return ret;
    }

    public int getCashTimesToday(long driverId) {
        return cashMapper.listToday(driverId).size();
    }

    /**
     * 是否需要修改交易密码
     * @param driverId
     * @return
     */
    public boolean needChangePwd(long driverId) {
        return needChangePwd(driverId, true);
    }

    /**
     * 是否需要修改交易密码
     * @param driverId
     * @param ignoreError 是否忽略错误
     * @return
     */
    public boolean needChangePwd(Long driverId, boolean ignoreError) {
        String reset = CacheKeyEnum.DRIVER_PWD_RESET_KEY.create(driverId);
        boolean resetFlag = RedisUtils.getOrDefault(reset, false);
        if (resetFlag && !ignoreError) {
            throw new ServiceException(FinanceConstants.PASSWORD_LOCK_MSG);
        }
        return resetFlag;
    }

    /**
     * 验证token是否可用
     * @param driverId
     * @param verifyType
     * @return
     */
    public boolean verifyToken(long driverId, PwdVerifyEnum verifyType, String accessToken) {
        return verifyToken(driverId, verifyType, accessToken, true);
    }


    /**
     * 验证token是否可用
     * @param driverId
     * @param verifyType 校验类型
     * @param ignoreError 是否忽略错误
     * @return
     */
    public boolean verifyToken(long driverId, PwdVerifyEnum verifyType, String accessToken, boolean ignoreError) {
        String verify = CacheKeyEnum.DRIVER_PWD_VERIFY_TOKEN_KEY.create(driverId, verifyType);
        String token = RedisUtils.getCacheObject(verify);
        if (!(token != null && token.equals(accessToken))) {
            if (ignoreError) {
                RedisUtils.deleteObject(verify);
                return true;
            }
            throw new ServiceException("请求已失效，请重新验证交易密码");
        }
        return false;
    }

    /**
     *
     * @param driverId
     * @param out
     * @param ignoreError
     * @return
     */
    public boolean verifyAmount(long driverId, long out, boolean ignoreError) {
        FinFlow lastFlow = flowMapper.getLastFlow(driverId);
        Assert.notNull(lastFlow, FinanceConstants.NO_FLOW_MSG);
        // 提现金额、是否可提现
        boolean ret = lastFlow.getAfterCash() >= out;
        if (!ret && !ignoreError) {
            throw new ServiceException(FinanceConstants.CASH_OUT_MSG);
        }
        return ret;
    }

}
