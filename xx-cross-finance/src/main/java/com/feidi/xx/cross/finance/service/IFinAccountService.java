package com.feidi.xx.cross.finance.service;

import com.feidi.xx.cross.finance.domain.vo.FinAccountListVo;
import com.feidi.xx.cross.finance.domain.vo.FinAccountVo;
import com.feidi.xx.cross.finance.domain.bo.FinAccountBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 商家账户Service接口
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface IFinAccountService {

    /**
     * 查询商家账户
     *
     * @param id 主键
     * @return 商家账户
     */
    FinAccountVo queryById(Long id);

    /**
     * 分页查询商家账户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 商家账户分页列表
     */
    TableDataInfo<FinAccountListVo> queryPageList(FinAccountBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的商家账户列表
     *
     * @param bo 查询条件
     * @return 商家账户列表
     */
    List<FinAccountListVo> queryList(FinAccountBo bo);

    /**
     * 新增商家账户
     *
     * @param bo 商家账户
     * @return 是否新增成功
     */
    Boolean insertByBo(FinAccountBo bo);

    /**
     * 修改商家账户
     *
     * @param bo 商家账户
     * @return 是否修改成功
     */
    Boolean updateByBo(FinAccountBo bo);

    /**
     * 禁用/启用
     * @param  id id
     * @param status 状态
     * @return 是否禁用/启用成功
     */
    Boolean updateStatus(Long id, String status);

    /**
     * 校验并批量删除商家账户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

}
