package com.feidi.xx.cross.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.enums.UserStatusEnum;
import com.feidi.xx.common.core.utils.*;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.common.enums.finance.ComplainFeeTypeEnum;
import com.feidi.xx.cross.finance.domain.FinBill;
import com.feidi.xx.cross.finance.domain.FinComplainFee;
import com.feidi.xx.cross.finance.domain.bo.FinComplainFeeBo;
import com.feidi.xx.cross.finance.domain.vo.FinComplainFeeVo;
import com.feidi.xx.cross.finance.mapper.FinBillMapper;
import com.feidi.xx.cross.finance.mapper.FinComplainFeeMapper;
import com.feidi.xx.cross.finance.service.IFinComplainFeeService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.RemoteDriverService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 订单客诉费用Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@RequiredArgsConstructor
@Service
public class FinComplainFeeServiceImpl implements IFinComplainFeeService {

    private final FinComplainFeeMapper baseMapper;
    private final FinBillMapper finBillMapper;
    @DubboReference
    private final RemoteDriverService remoteDriverService;
    @DubboReference
    private final RemoteAgentService remoteAgentService;

    /**
     * 查询订单客诉费用
     *
     * @param id 主键
     * @return 订单客诉费用
     */
    @Override
    public FinComplainFeeVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询订单客诉费用列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单客诉费用分页列表
     */
    @Override
    public TableDataInfo<FinComplainFeeVo> queryPageList(FinComplainFeeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinComplainFee> lqw = buildQueryWrapper(bo);
        Page<FinComplainFeeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        result.getRecords().forEach(item -> item.setFeeTypeText(ComplainFeeTypeEnum.getInfoByCode(item.getFeeType())));
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的订单客诉费用列表
     *
     * @param bo 查询条件
     * @return 订单客诉费用列表
     */
    @Override
    public List<FinComplainFeeVo> queryList(FinComplainFeeBo bo) {
        LambdaQueryWrapper<FinComplainFee> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinComplainFee> buildQueryWrapper(FinComplainFeeBo bo) {
        LambdaQueryWrapper<FinComplainFee> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getFinanceDate() != null, FinComplainFee::getFinanceDate, bo.getFinanceDate());
        lqw.eq(StringUtils.isNotBlank(bo.getFeeType()), FinComplainFee::getFeeType, bo.getFeeType());
        lqw.eq(bo.getDriverId() != null, FinComplainFee::getDriverId, bo.getDriverId());
        lqw.eq(bo.getAgentId() != null, FinComplainFee::getAgentId, bo.getAgentId());
        lqw.like(StringUtils.isNotBlank(bo.getDriverName()), FinComplainFee::getDriverName, bo.getDriverName());
        lqw.like(StringUtils.isNotBlank(bo.getOrderNo()), FinComplainFee::getOrderNo, bo.getOrderNo());
        lqw.in(CollUtils.isNotEmpty(bo.getIds()), FinComplainFee::getId, bo.getIds());
        if (StringUtils.isNotBlank(bo.getFinanceStartTime()) && StringUtils.isNotBlank(bo.getFinanceEndTime())) {
            lqw.ge(FinComplainFee::getFinanceDate, bo.getFinanceStartTime());
            lqw.le(FinComplainFee::getFinanceDate, bo.getFinanceEndTime());
        }
        return lqw;
    }

    /**
     * 新增订单客诉费用
     *
     * @param bo 订单客诉费用
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinComplainFeeBo bo) {
        FinComplainFee add = MapstructUtils.convert(bo, FinComplainFee.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改订单客诉费用
     *
     * @param bo 订单客诉费用
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinComplainFeeBo bo) {
        FinComplainFee update = MapstructUtils.convert(bo, FinComplainFee.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 批量修改
     *
     * @param bos
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBos(List<FinComplainFeeBo> bos) {
        if (CollUtil.isEmpty(bos)) {
            return false;
        }

        LambdaQueryWrapper<FinComplainFee> lqw = Wrappers.lambdaQuery();
        lqw.eq(FinComplainFee::getOrderId, bos.get(0).getOrderId());

        List<FinComplainFee> finComplainFees = baseMapper.selectList(lqw);
        Map<String, FinComplainFee> feeType2Map = finComplainFees.stream().collect(Collectors.toMap(FinComplainFee::getFeeType, Function.identity(), (v1, v2) -> v1));

        for (FinComplainFeeBo bo : bos) {
            FinComplainFee finComplainFee = feeType2Map.get(bo.getFeeType());
            if (ObjectUtils.isNotNull(finComplainFee)) {
                finComplainFee.setAmount(bo.getAmount());
                finComplainFee.setRemark(bo.getRemark());
                finComplainFee.setUpdateTime(DateUtils.getNowDate());
                finComplainFees.add(finComplainFee);
            } else {
                FinComplainFee complainFee = null;
                if (feeType2Map.containsKey(ComplainFeeTypeEnum.COMPLAIN.getCode())) {
                    complainFee = BeanUtils.copyProperties(feeType2Map.get(ComplainFeeTypeEnum.COMPLAIN.getCode()), FinComplainFee.class);
                    complainFee.setFeeType(bo.getFeeType());
                } else {
                    complainFee = BeanUtils.copyProperties(bo, FinComplainFee.class);
                }
                complainFee.setId(null);
                complainFee.setAmount(bo.getAmount());
                complainFee.setRemark(bo.getRemark());
                complainFee.setUpdateTime(DateUtils.getNowDate());
                finComplainFees.add(complainFee);
            }
        }

        if (CollUtils.isNotEmpty(bos)) {
            long complainPrice = 0L;
            long otherPrice = 0L;
            for (FinComplainFeeBo complainFee : bos) {
                if (Objects.equals(complainFee.getFeeType(), ComplainFeeTypeEnum.COMPLAIN.getCode())) {
                    complainPrice = complainFee.getAmount();
                } else {
                    otherPrice += complainFee.getAmount();
                }
            }

            LambdaQueryWrapper<FinBill> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(FinBill::getMonth, finComplainFees.get(0).getFinanceDate())
                    .eq(FinBill::getAgentId, finComplainFees.get(0).getAgentId())
                    .orderByDesc(FinBill::getCreateTime)
                    .last(Constants.LIMIT_ONE);
            FinBill finBill = finBillMapper.selectOne(queryWrapper);

            if (finBill != null) {
                finBill.setComplainAmount(finBill.getComplainAmount() + complainPrice);
                finBill.setOtherAmount(finBill.getOtherAmount() + otherPrice);
                finBill.setRebateAmount(finBill.getRebateAmount() + otherPrice);
                finBill.setNotRebateAmount(finBill.getNotRebateAmount() + otherPrice);
                finBill.setUpdateTime(new Date());
                finBillMapper.updateById(finBill);
            }
        }

        return baseMapper.insertOrUpdateBatch(finComplainFees);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinComplainFee entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除订单客诉费用信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 订单客诉费用
     *
     * @param orderVos 客诉订单信息
     * @param financeDate 账期
     * @return
     */
    @Override
    public Boolean complainFee(List<RemoteOrderVo> orderVos, Date financeDate) {
        if (CollUtils.isEmpty(orderVos)) {
            return false;
        }

        if (financeDate == null) {
            financeDate = DateUtils.getBeginDayOfYesterday();
        }

        List<FinComplainFee> complainFees = new ArrayList<>();

        // 将代理商信息按照代理商id进行分组
        Map<Long, String> agentId2NameMap = remoteAgentService.getAllAgentInfo()
                .stream().collect(Collectors.toMap(RemoteAgentVo::getId, RemoteAgentVo::getCompanyName, (k1, k2) -> k1));

        // 将司机信息按照司机id进行分组
        List<String> driverStatuses = Stream.of(UserStatusEnum.OK.getCode(), UserStatusEnum.DISABLE.getCode()).toList();
        Map<Long, String> driverId2NameMap = remoteDriverService.queryByDriverStatus(driverStatuses)
                .stream().collect(Collectors.toMap(RemoteDriverVo::getId, RemoteDriverVo::getName, (k1, k2) -> k1));

        for (RemoteOrderVo orderVo : orderVos) {
            FinComplainFee complainFee = buildComplainFee(orderVo);

            complainFee.setFinanceDate(financeDate);
            complainFee.setAgentName(agentId2NameMap.get(orderVo.getAgentId()));
            complainFee.setDriverName(driverId2NameMap.get(orderVo.getDriverId()));

            complainFees.add(complainFee);
        }

        if (CollUtils.isNotEmpty(complainFees)) {
            baseMapper.insertBatch(complainFees);
        }

        return true;
    }

    private FinComplainFee buildComplainFee(RemoteOrderVo orderVo) {
        FinComplainFee complainFee = new FinComplainFee();
        complainFee.setAgentId(orderVo.getAgentId());
        complainFee.setDriverId(orderVo.getDriverId());
        complainFee.setOrderId(orderVo.getId());
        complainFee.setOrderNo(orderVo.getOrderNo());
        complainFee.setPlatformCode(orderVo.getPlatformCode());
        complainFee.setOrderPrice(orderVo.getOrderPrice());
        complainFee.setPayPrice(orderVo.getPayPrice());
        complainFee.setFeeType(ComplainFeeTypeEnum.COMPLAIN.getCode());
        complainFee.setAmount(orderVo.getOrderPrice());

        return complainFee;
    }
}
