package com.feidi.xx.cross.finance.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.finance.domain.bo.FinDrvWalletFlowBo;
import com.feidi.xx.cross.finance.domain.vo.FinDrvWalletFlowVo;

import java.util.Collection;
import java.util.List;

/**
 * 司机钱包流水Service接口
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface IFinDrvWalletFlowService {

    /**
     * 查询司机钱包流水
     *
     * @param id 主键
     * @return 司机钱包流水
     */
    FinDrvWalletFlowVo queryById(Long id);

    /**
     * 分页查询司机钱包流水列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机钱包流水分页列表
     */
    TableDataInfo<FinDrvWalletFlowVo> queryPageList(FinDrvWalletFlowBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的司机钱包流水列表
     *
     * @param bo 查询条件
     * @return 司机钱包流水列表
     */
    List<FinDrvWalletFlowVo> queryList(FinDrvWalletFlowBo bo);

    /**
     * 新增司机钱包流水
     *
     * @param bo 司机钱包流水
     * @return 是否新增成功
     */
    Boolean insertByBo(FinDrvWalletFlowBo bo);

    /**
     * 修改司机钱包流水
     *
     * @param bo 司机钱包流水
     * @return 是否修改成功
     */
    Boolean updateByBo(FinDrvWalletFlowBo bo);

    /**
     * 校验并批量删除司机钱包流水信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 添加司机钱包流水
     *
     * @param bo
     */
    void addDrvWalletFlow(FinDrvWalletFlowBo bo);
}
