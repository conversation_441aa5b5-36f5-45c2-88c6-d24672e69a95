package com.feidi.xx.cross.finance.controller.agent;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.domain.bo.FinAccountBo;
import com.feidi.xx.cross.finance.domain.bo.FinCashQueryBo;
import com.feidi.xx.cross.finance.domain.vo.ExportVo;
import com.feidi.xx.cross.finance.domain.vo.FinAccountListVo;
import com.feidi.xx.cross.finance.domain.vo.FinCashListVo;
import com.feidi.xx.cross.finance.domain.vo.FinCashVo;
import com.feidi.xx.cross.finance.service.IFinCashService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 代理商 - 提现
 * 前端访问路由地址为:/finance/cash
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Validated
@RestController
@RequiredArgsConstructor
@SaCheckRole(UserTypeEnum.UserType.AGENT_USER)
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX+"/cash")
public class AgtCashController extends BaseController {

    private final IFinCashService FinCashService;

    /**
     * 查询提现列表
     */
    @Enum2TextAspect
    @PostMapping("/list")
    public TableDataInfo<FinCashListVo> list(@Validated @RequestBody FinCashQueryBo bo) {
        Long agentId = LoginHelper.getAgentId();
        if (bo.getAgentId() != null) {
            bo.setAgentIds(CollUtil.newArrayList(bo.getAgentId()));
        } else {
            bo.setAgentId(agentId);
        }
        bo.setUserType(UserTypeEnum.AGENT_USER.getUserType());
        return FinCashService.queryPageList(bo, bo.buildPageQuery());
    }

    /**
     * 导出提现列表
     */
    @Log(title = "提现", businessType = BusinessType.EXPORT)
    @Download(name="提现",module = ModuleConstants.FINANCE,mode="no")
    @PostMapping("/export")
    public Object export(@Validated @RequestBody FinCashQueryBo bo,HttpServletResponse response) {
        Long agentId = LoginHelper.getAgentId();
        if (bo.getAgentId() != null) {
            bo.setAgentIds(CollUtil.newArrayList(bo.getAgentId()));
        } else {
            bo.setAgentId(agentId);
        }
        bo.setUserType(UserTypeEnum.AGENT_USER.getUserType());
        List<FinCashListVo> list = FinCashService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(list, "提现", FinCashListVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取提现详细信息
     *
     * @param id 主键
     */
    @Enum2TextAspect
    @GetMapping("/{id}")
    public R<FinCashVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(FinCashService.queryById(id));
    }

}
