package com.feidi.xx.cross.finance.timer;

import com.feidi.xx.cross.finance.service.IFinBillDetailService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 订单对账明细定时器
 *
 * <AUTHOR>
 * @date 2025/5/27
 */

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderBillDetailJob {

    private final IFinBillDetailService finBillDetailService;

    /**
     * 订单对账明细
     */
    @XxlJob("orderBillDetailJob")
    public void orderBillDetailJob() {
        if (log.isInfoEnabled()) {
            log.info("============== 订单对账明细-定时器开始执行 ==============");
        }

        finBillDetailService.orderBillDetail();

        if (log.isInfoEnabled()) {
            log.info("============== 订单对账明细-定时器执行结束 ==============");
        }
    }
}
