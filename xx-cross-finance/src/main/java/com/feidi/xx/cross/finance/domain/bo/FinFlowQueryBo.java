package com.feidi.xx.cross.finance.domain.bo;


import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.cross.common.enums.finance.FlowSelectEnum;
import com.feidi.xx.cross.common.enums.finance.FlowShowStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
public class FinFlowQueryBo extends PageQuery {

    /**
     * 综合搜索
     */
    private String unionId;

    /**
     * 所属代理商
     */
    private Long agentId;

    /**
     * 代理商ids
     */
    private List<Long> agentIds;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 资金流向 {@link com.feidi.xx.common.core.enums.DirectionEnum}
     */
    private String direction;

    /**
     * 流水类型
     */
    private FlowSelectEnum type;

    /**
     * 流水状态
     */
    private FlowShowStatusEnum status;

    // 开始结束时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}
