package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.finance.domain.FinTransfer;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.util.Date;

@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinTransfer.class, convertGenerate = false)
public class FinTransferListVo {

    /**
     * 主键
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 流水编号
     */
    @ExcelProperty(value = "转账编号")
    private String transferNo;

    /**
     * 代理商id
     */
    @ExcelProperty(value = "代理商id")
    private Long agentId;

    /**
     * 代理商名称
     */
    @ExcelProperty(value = "代理商名称")
    private String agentName;

    /**
     * 转出司机id
     */
    @ExcelProperty(value = "转出司机id")
    private Long outDriverId;

    /**
     * 转出司机姓名
     */
    @ExcelProperty(value = "转出司机姓名")
    private String outDriverName;

    /**
     * 转出司机手机号
     */
    @ExcelProperty(value = "转出司机手机号")
    private String outDriverPhone;

    /**
     * 转入司机id
     */
    @ExcelProperty(value = "转入司机id")
    private Long inDriverId;

    /**
     * 转入司机姓名
     */
    @ExcelProperty(value = "转入司机姓名")
    private String inDriverName;

    /**
     * 转入司机手机号
     */
    @ExcelProperty(value = "转入司机手机号")
    private String inDriverPhone;

    /**
     * 金额
     */
    @ExcelProperty(value = "金额")
    private Long amount;

    /**
     * 转账时间
     */
    @ExcelProperty(value = "转账时间")
    private Date createTime;

}
