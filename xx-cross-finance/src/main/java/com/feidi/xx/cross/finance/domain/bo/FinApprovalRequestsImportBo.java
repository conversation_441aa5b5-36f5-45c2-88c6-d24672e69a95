package com.feidi.xx.cross.finance.domain.bo;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.converters.date.DateStringConverter;
import com.feidi.xx.cross.common.annotations.EnumValue;
import com.feidi.xx.cross.common.enums.finance.ApprTransactionTypeEnum;
import com.feidi.xx.cross.common.utils.MoneyUtil;
import com.feidi.xx.cross.finance.domain.FinApprovalRequestRelateOrder;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ExcelIgnoreUnannotated
public class FinApprovalRequestsImportBo {
    /**
     * 司机账号（主键）
     */
    @NotNull(message = "司机账号id不能为空")
    @ExcelProperty(value = "司机账号id", index = 0)
    private Long driverId;

    /**
     * 司机姓名
     */
    @ExcelProperty(value = "司机姓名", index = 1)
    private String driverName;

    /**
     * 交易类型（固定为“人工调账”）
     */
    @ExcelProperty(value = "交易类型", index = 2)
    @EnumValue(message = "交易类型错误", value = ApprTransactionTypeEnum.class)
    private String transactionTypeText = "人工调账";

    /**
     * 关联订单（非必填）
     */
    @ExcelProperty(value = "关联订单", index = 3)
    private String relatedOrder;

    /**
     * 交易说明
     */
    @ExcelProperty(value = "交易说明", index = 4)
    private String transactionNote;

    /**
     * 调账金额（可为负数）
     */
    @ExcelProperty(value = "调账金额", index = 5)
    @NotNull(message = "调账金额不能为空")
    private BigDecimal adjustAmount;

    /**
     * 申请原因
     */
    @ExcelProperty(value = "申请原因", index = 6)
    private String exceptionReason;

    /**
     * 调整日期
     */
    @ExcelProperty(value = "调整日期", index = 7, converter = DateStringConverter.class)
    @DateTimeFormat(DatePattern.NORM_DATE_PATTERN)
    private Date adjustmentDate;

    public FinApprovalRequestRelateOrder toRelateOrder() {
        FinApprovalRequestRelateOrder relateOrder = new FinApprovalRequestRelateOrder();
        relateOrder.setOrderNo(relatedOrder);
        relateOrder.setAdjustAmount(MoneyUtil.yuanToFen(adjustAmount));
        relateOrder.setTransactionNote(transactionNote);
        relateOrder.setExceptionReason(exceptionReason);
        relateOrder.setAdjustmentDate(adjustmentDate);
        return relateOrder;
    }
}
