package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.core.enums.DirectionEnum;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.cross.finance.domain.FinFlow;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 资金流水视图对象 Fin_flow
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinFlow.class, convertGenerate = false)
public class FinFlowVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 流水单号
     */
    @ExcelProperty(value = "流水单号")
    private String flowNo;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 代理id
     */
    @ExcelProperty(value = "代理id")
    private Long agentId;

    @ExcelProperty(value = "上级代理商名称")
    private String agentName;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机id")
    private Long driverId;

    /**
     * 司机姓名
     */
    @ExcelProperty(value = "司机姓名")
    private String driverName;

    /**
     * 司机手机号
     */
    @ExcelProperty(value = "司机手机号")
    private String driverPhone;

    /**
     * 关联单
     */
    @ExcelProperty(value = "关联单")
    private String joinTable;

    /**
     * 关联单号
     */
    @ExcelProperty(value = "关联单号")
    private String joinNo;

    /**
     * 关联id
     */
    @ExcelProperty(value = "关联id")
    private String joinId;

    /**
     * 变动前金额
     */
    @ExcelProperty(value = "变动前金额")
    private Long beforeAmount;

    /**
     * 提现金额
     */
    @ExcelProperty(value = "提现金额")
    private Long amount;

    /**
     * 变动后金额
     */
    @ExcelProperty(value = "变动后金额")
    private Long afterAmount;

    /**
     * 变动前可提现金额
     */
    @ExcelProperty(value = "变动前可提现金额")
    private Long beforeCash;

    /**
     * 提现可提现金额
     */
    @ExcelProperty(value = "提现可提现金额")
    private Long cash;

    /**
     * 变动后可提现金额
     */
    @ExcelProperty(value = "变动后可提现金额")
    private Long afterCash;

    /**
     * 进出
     */
    @ExcelProperty(value = "进出", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = DirectionEnum.class)
    private String direction;

    /**
     * 类型
     */
    @ReverseAutoMapping(target = "type", expression = "java(com.feidi.xx.cross.common.enums.finance.FlowSelectEnum.getInfoByType(source.getType()))")
    @ExcelProperty(value = "类型")
    private String type;

    /**
     * 状态
     */
    @ReverseAutoMapping(target = "status", expression = "java(com.feidi.xx.cross.common.enums.finance.FlowStatusEnum.getInfoByType(source.getType()))")
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 乘客名称
     */
    private String passengerName;

}
