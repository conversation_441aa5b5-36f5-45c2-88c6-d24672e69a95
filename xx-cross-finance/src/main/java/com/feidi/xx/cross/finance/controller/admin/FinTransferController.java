package com.feidi.xx.cross.finance.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.domain.bo.FinPaymentRecordBo;
import com.feidi.xx.cross.finance.domain.bo.FinTransBo;
import com.feidi.xx.cross.finance.domain.bo.FinTransferBo;
import com.feidi.xx.cross.finance.domain.bo.FinTransferQueryBo;
import com.feidi.xx.cross.finance.domain.vo.*;
import com.feidi.xx.cross.finance.service.IFinTransferService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 转账
 * 前端访问路由地址为:/finance/transfer
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@SaCheckRole(UserTypeEnum.UserType.SYS_USER)
@RequestMapping("/transfer")
public class FinTransferController extends BaseController {

    private final IFinTransferService finTransferService;

    /**
     * 查询转账列表
     */
    @SaCheckPermission("finance:transfer:list")
    @GetMapping("/list")
    public TableDataInfo<FinTransferListVo> list(FinTransferQueryBo bo, PageQuery pageQuery) {
        return finTransferService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出转账列表
     */
    @SaCheckPermission("finance:transfer:export")
    @Log(title = "转账", businessType = BusinessType.EXPORT)
    @Download(name="转账",module = ModuleConstants.FINANCE,mode="no")
    @PostMapping("/export")
    public Object export(@RequestBody FinTransferQueryBo bo, HttpServletResponse response) {
        List<FinTransferListVo> list = finTransferService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        List<FinTransferExportVo> convert = MapstructUtils.convert(list, FinTransferExportVo.class);
        ExcelUtil.exportExcel(convert, "转账", FinTransferExportVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取转账详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("finance:transfer:query")
    @GetMapping("/{id}")
    public R<FinTransferVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long id) {
        return R.ok(finTransferService.queryById(id));
    }

    /**
     * 新增转账
     */
    @SaCheckPermission("finance:transfer:add")
    @Log(title = "转账", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinTransBo bo) {
        return toAjax(finTransferService.insertByBo(bo));
    }
    /**
     * 修改转账
     */
    @SaCheckPermission("finance:transfer:edit")
    @Log(title = "转账", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinTransferBo bo) {
        return toAjax(finTransferService.updateByBo(bo));
    }

    /**
     * 删除转账
     *
     * @param ids 主键串
     */
    @SaCheckPermission("finance:transfer:remove")
    @Log(title = "转账", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(finTransferService.deleteWithValidByIds(List.of(ids), true));
    }
}
