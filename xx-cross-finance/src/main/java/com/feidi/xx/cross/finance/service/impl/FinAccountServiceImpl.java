package com.feidi.xx.cross.finance.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.PaymentChannelEnum;
import com.feidi.xx.common.core.enums.PaymentDirectionEnum;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.ValidatorUtils;
import com.feidi.xx.common.core.utils.xx.ProfileUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.payment.common.enums.PaymentOperateEnum;
import com.feidi.xx.common.payment.config.AliPayConfig;
import com.feidi.xx.common.payment.config.PayConfig;
import com.feidi.xx.common.payment.config.WxPayConfig;
import com.feidi.xx.common.payment.listener.PaymentConfigManager;
import com.feidi.xx.common.payment.mq.PaymentConfigEvent;
import com.feidi.xx.common.payment.mq.PaymentConfigProducer;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.tenant.helper.TenantHelper;
import com.feidi.xx.cross.finance.domain.FinAccount;
import com.feidi.xx.cross.finance.domain.bo.FinAccountBo;
import com.feidi.xx.cross.finance.domain.vo.FinAccountListVo;
import com.feidi.xx.cross.finance.domain.vo.FinAccountVo;
import com.feidi.xx.cross.finance.mapper.FinAccountMapper;
import com.feidi.xx.cross.finance.service.IFinAccountService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 商家账户Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@RequiredArgsConstructor
@Service
public class FinAccountServiceImpl implements IFinAccountService {

    private static final Logger log = LoggerFactory.getLogger(FinAccountServiceImpl.class);

    private final FinAccountMapper baseMapper;

    /**
     * 查询商家账户
     *
     * @param id 主键
     * @return 商家账户
     */
    @Override
    public FinAccountVo queryById(Long id) {
        FinAccount account = baseMapper.selectById(id);
        String configAppId = null;
        if (PaymentChannelEnum.ALIPAY.getCode().equals(account.getChannel())) {
            AliPayConfig config = JSONUtil.toBean((JSONObject) account.getConfig(), AliPayConfig.class);
            config.setAlipayCertContent(null);
            config.setRootCertContent(null);
            config.setAppCertContent(null);
            config.setPublicKey(null);
            config.setPrivateKey(null);
            account.setConfig(config);
            configAppId = config.getAppId();
        } else if (PaymentChannelEnum.WX_PAY.getCode().equals(account.getChannel())) {
            WxPayConfig config = JSONUtil.toBean((JSONObject) account.getConfig(), WxPayConfig.class);
            config.setPrivateKey(null);
            config.setApiV3Key(null);
            config.setMchKey(null);
            account.setConfig(config);
            configAppId = config.getAppId();
        }
        FinAccountVo vo = MapstructUtils.convert(account, FinAccountVo.class);
        vo.setConfigAppId(configAppId);
        return vo;
    }

    /**
     * 分页查询商家账户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 商家账户分页列表
     */
    @Override
    public TableDataInfo<FinAccountListVo> queryPageList(FinAccountBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinAccount> lqw = buildQueryWrapper(bo);
        Page<FinAccount> result = baseMapper.selectPage(pageQuery.build(), lqw);
        List<FinAccount> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            List<FinAccountListVo> convert = MapstructUtils.convert(records, FinAccountListVo.class);
            for (int i = 0; i < records.size(); i++) {
                FinAccountListVo vo = convert.get(i);
                FinAccount account = records.get(i);
                if (PaymentChannelEnum.ALIPAY.getCode().equals(account.getChannel())) {
                    AliPayConfig config = JSONUtil.toBean((JSONObject) account.getConfig(), AliPayConfig.class);
                    vo.setConfigAppId(config.getAppId());
                } else if (PaymentChannelEnum.WX_PAY.getCode().equals(account.getChannel())) {
                    WxPayConfig config = JSONUtil.toBean((JSONObject) account.getConfig(), WxPayConfig.class);
                    vo.setConfigAppId(config.getAppId());
                }
            }
            return TableDataInfo.build(convert);
        }
        return TableDataInfo.build();
    }

    /**
     * 查询符合条件的商家账户列表
     *
     * @param bo 查询条件
     * @return 商家账户列表
     */
    @Override
    public List<FinAccountListVo> queryList(FinAccountBo bo) {
        LambdaQueryWrapper<FinAccount> lqw = buildQueryWrapper(bo);
        List<FinAccount> records = baseMapper.selectList(lqw);
        if (CollUtil.isNotEmpty(records)) {
            List<FinAccountListVo> convert = MapstructUtils.convert(records, FinAccountListVo.class);
            for (int i = 0; i < records.size(); i++) {
                FinAccountListVo vo = convert.get(i);
                FinAccount account = records.get(i);
                if (PaymentChannelEnum.ALIPAY.getCode().equals(account.getChannel())) {
                    AliPayConfig config = JSONUtil.toBean((JSONObject) account.getConfig(), AliPayConfig.class);
                    vo.setConfigAppId(config.getAppId());
                } else if (PaymentChannelEnum.WX_PAY.getCode().equals(account.getChannel())) {
                    WxPayConfig config = JSONUtil.toBean((JSONObject) account.getConfig(), WxPayConfig.class);
                    vo.setConfigAppId(config.getAppId());
                }
            }
            return convert;
        }
        return Collections.emptyList();
    }

    private LambdaQueryWrapper<FinAccount> buildQueryWrapper(FinAccountBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinAccount> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getAgentId() != null, FinAccount::getAgentId, bo.getAgentId());
        lqw.like(StringUtils.isNotBlank(bo.getAgentName()), FinAccount::getAgentName, bo.getAgentName());
        lqw.eq(StringUtils.isNotBlank(bo.getChannel()), FinAccount::getChannel, bo.getChannel());
        lqw.eq(StringUtils.isNotBlank(bo.getAppId()), FinAccount::getAppId, bo.getAppId());
        lqw.eq(StringUtils.isNotBlank(bo.getMchId()), FinAccount::getMchId, bo.getMchId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirection()), FinAccount::getDirection, bo.getDirection());
        lqw.eq(StringUtils.isNotBlank(bo.getMain()), FinAccount::getMain, bo.getMain());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinAccount::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增商家账户
     *
     * @param bo 商家账户
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinAccountBo bo) {
        FinAccount add = MapstructUtils.convert(bo, FinAccount.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            refreshCache(null, add, PaymentOperateEnum.ADD.getCode());
            sengMessage(add, PaymentOperateEnum.ADD.getCode());
        }
        return flag;
    }

    /**
     * 修改商家账户
     *
     * @param bo 商家账户
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinAccountBo bo) {
        FinAccount update = baseMapper.selectById(bo.getId());
        // 需要原始的对象清楚缓存配置
        FinAccount copied = BeanUtil.copyProperties(update, FinAccount.class);
        // 更新配置
        BeanUtil.copyProperties(bo, update, "config");
        CopyOptions copyOptions = new CopyOptions();
        copyOptions.setIgnoreNullValue(true);
        BeanUtil.copyProperties(bo.getConfig(), update.getConfig(), copyOptions);
        validEntityBeforeSave(update);
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            refreshCache(copied, update, PaymentOperateEnum.UPDATE.getCode());
            sengMessage(update, PaymentOperateEnum.UPDATE.getCode());
        }
        return flag;
    }

    @Override
    public Boolean updateStatus(Long id, String status) {
        FinAccount update = baseMapper.selectById(id);
        update.setStatus(status);
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            refreshCache(update, update, PaymentOperateEnum.UPDATE.getCode());
            sengMessage(update, PaymentOperateEnum.UPDATE.getCode());
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinAccount entity) {
        entity.setTenantId(TenantHelper.getTenantId());
        // 判重
        FinAccount origin = baseMapper.queryByAgentId(entity.getAgentId(), entity.getChannel(), entity.getAppId(), entity.getDirection());
        if (origin != null && !origin.getId().equals(entity.getId())) {
            throw new ServiceException("配置已存在，请勿重复添加");
        }
        // 主账号只能有一个
        if (IsYesEnum.YES.getCode().equals(entity.getMain())) {
            baseMapper.resetMainAccount(entity.getChannel(), entity.getAppId(), entity.getDirection());
        }
        // 校验
        if (PaymentChannelEnum.ALIPAY.getCode().equals(entity.getChannel())) {
            // 支付宝校验
            AliPayConfig config = BeanUtil.copyProperties(entity.getConfig(), AliPayConfig.class);
            ValidatorUtils.validate(config);
            // 查询校验
            if (ProfileUtils.isProd() || ProfileUtils.isTest()) {
                PaymentConfigManager.checkConfig(config);
            }
            // APPID转账的取租户ID，否则取配置的APPID，因为目前是转账需要使用他人的支付宝配置，而进账不需要
            if (PaymentDirectionEnum.OUTGOING.getCode().equals(entity.getDirection())) {
                entity.setAppId(LoginHelper.getTenantId());
            } else {
                entity.setAppId(config.getAppId());
            }
            entity.setConfig(config);
        } else if (PaymentChannelEnum.WX_PAY.getCode().equals(entity.getChannel())) {
//            Assert.isTrue(StrUtil.isNotBlank(entity.getAppId()), "应用ID不能为空");
            WxPayConfig config = BeanUtil.copyProperties(entity.getConfig(), WxPayConfig.class);
            ValidatorUtils.validate(config);
            // 查询校验
            if (ProfileUtils.isProd() || ProfileUtils.isTest()) {
                PaymentConfigManager.checkConfig(config);
            }
            entity.setAppId(config.getAppId());
            entity.setMchId(config.getMchId());
            entity.setConfig(config);
        } else {
            throw new ServiceException("不支持的支付渠道");
        }
    }

    /**
     * 校验并批量删除商家账户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            List<FinAccount> accounts = baseMapper.selectBatchIds(ids);
            accounts.forEach(e -> sengMessage(e, PaymentOperateEnum.DELETE.getCode()));
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    private void sengMessage(FinAccount entity, String operate) {
        PaymentConfigEvent event = MapstructUtils.convert(entity, PaymentConfigEvent.class);
        event.setOperate(operate);
        PaymentConfigProducer.sendMessage(event);
    }

    private void refreshCache(FinAccount oldEntity, FinAccount newEntity, String operate) {
        if (PaymentOperateEnum.DELETE.getCode().equals(operate) || StatusEnum.DISABLE.getCode().equals(newEntity.getStatus())) {
            deleteConfig(newEntity);
        } else if (PaymentOperateEnum.ADD.getCode().equals(operate) || PaymentOperateEnum.UPDATE.getCode().equals(operate)) {
            cacheConfig(oldEntity, newEntity);
        }
    }

    private void deleteConfig(FinAccount entity) {
        PaymentConfigManager.clearConfig(entity.getTenantId(), entity.getChannel(), entity.getAppId(), entity.getDirection(), entity.getAgentId());
        if (IsYesEnum.YES.getCode().equals(entity.getMain())) {
            PaymentConfigManager.clearConfig(entity.getTenantId(), entity.getChannel(), entity.getAppId(), entity.getDirection(), null);
        }
    }

    private void cacheConfig(FinAccount oldEntity, FinAccount newEntity) {
        String configJson = JSONUtil.toJsonStr(newEntity.getConfig());
        PayConfig payConfig = null;
        if (PaymentChannelEnum.ALIPAY.getCode().equals(newEntity.getChannel())) {
            payConfig = JSONUtil.toBean(configJson, AliPayConfig.class);
        } else if (PaymentChannelEnum.WX_PAY.getCode().equals(newEntity.getChannel())) {
            payConfig = JSONUtil.toBean(configJson, WxPayConfig.class);
        }
        Assert.notNull(payConfig, "支付配置错误，配置为空");

        // 清除旧配置
        if (oldEntity != null) {
            deleteConfig(oldEntity);
        }

        // 缓存新配置
        PaymentConfigManager.cacheConfig(newEntity.getTenantId(), newEntity.getChannel(), newEntity.getAppId(), newEntity.getDirection(), newEntity.getAgentId(), payConfig);
        if (IsYesEnum.YES.getCode().equals(newEntity.getMain())) {
            PaymentConfigManager.cacheConfig(newEntity.getTenantId(), newEntity.getChannel(), newEntity.getAppId(), newEntity.getDirection(), null, payConfig);
        }
    }
}
