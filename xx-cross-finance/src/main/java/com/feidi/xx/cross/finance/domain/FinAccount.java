package com.feidi.xx.cross.finance.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.mybatis.handler.EncryptTypeHandler;
import com.feidi.xx.common.payment.mq.PaymentConfigEvent;
import com.feidi.xx.common.tenant.core.TenantEntity;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 商家账户对象 fin_account
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PaymentConfigEvent.class, reverseConvertGenerate = false)
@TableName(value = "fin_account", autoResultMap = true)
public class FinAccount extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 渠道
     */
    private String channel;

    /**
     * appId
     */
    private String appId;

    /**
     * mchId
     */
    private String mchId;

    /**
     * 进账/出账
     */
    private String direction;

    /**
     * 是否主账号
     */
    private String main;

    /**
     * 配置信息
     */
    @TableField(typeHandler = EncryptTypeHandler.class)
    private Object config;

    /**
     * 状态
     */
    private String status;

    /**
     * 逻辑删除 0存在 2删除
     */
    @TableLogic
    private String delFlag;


}
