package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.finance.domain.FinBill;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 订单对账视图对象 fin_bill
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinBill.class)
public class FinBillVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 代理商id
     */
    @ExcelProperty(value = "代理商id")
    private Long agentId;

    /**
     * 代理商名称
     */
    @ExcelProperty(value = "代理商名称")
    private String agentName;

    /**
     * 账单月份
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "账单月份")
    private Date month;

    /**
     * 订单量
     */
    @ExcelProperty(value = "订单量")
    private Long orderNumber;

    /**
     * 订单金额
     */
    @ExcelProperty(value = "订单金额")
    private Long orderAmount;

    /**
     * 司机收益
     */
    @ExcelProperty(value = "司机收益")
    private Long driverProfit;

    /**
     * 代收款
     */
    @ExcelProperty(value = "代收款")
    private Long proxyAmount;

    /**
     * 应收信息服务费
     */
    @ExcelProperty(value = "应收信息服务费")
    private Long infoServiceFee;

    /**
     * 技术服务费
     */
    @ExcelProperty(value = "技术服务费")
    private Long technicalServiceFee;

    /**
     * 奖励金额
     */
    @ExcelProperty(value = "奖励金额")
    private Long rewardAmount;

    /**
     * 客诉金额
     */
    @ExcelProperty(value = "客诉金额")
    private Long complainAmount;

    /**
     * 优惠券使用总金额
     */
    private Long couponQuotaAmount;

    /**
     * 其他金额
     */
    @ExcelProperty(value = "其他金额")
    private Long otherAmount;

    /**
     * 应结算金额
     */
    @ExcelProperty(value = "应结算金额")
    private Long rebateAmount;

    /**
     * 已结算金额
     */
    @ExcelProperty(value = "已结算金额")
    private Long alreadyRebateAmount;

    /**
     * 待结算金额
     */
    @ExcelProperty(value = "待结算金额")
    private Long notRebateAmount;

    /**
     * 已提现金额
     */
    @ExcelProperty(value = "已提现金额")
    private Long alreadyCashAmount;

    /**
     * 可提现金额
     */
    @ExcelProperty(value = "可提现金额")
    private Long notCashAmount;

    /**
     * 对账状态[---Status]
     */
    @ExcelProperty(value = "对账状态[---Status]")
    private String financeStatus;

    /**
     * 对账状态确认时间
     */
    @ExcelProperty(value = "对账状态确认时间")
    private Date financeTime;

    /**
     * 对账确认人类型[UserTypeEnum]
     */
    @ExcelProperty(value = "对账确认人类型[UserTypeEnum]")
    private String financeUserType;

    /**
     * 对账确认人id
     */
    @ExcelProperty(value = "对账确认人id")
    private Long financeUserId;

    /**
     * 相关单号
     */
    @ExcelProperty(value = "相关单号")
    private String relatedOrder;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
