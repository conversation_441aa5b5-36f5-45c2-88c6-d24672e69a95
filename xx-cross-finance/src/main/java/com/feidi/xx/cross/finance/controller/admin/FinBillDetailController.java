package com.feidi.xx.cross.finance.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.domain.bo.FinBillDetailBo;
import com.feidi.xx.cross.finance.domain.vo.FinBillDetailExportVo;
import com.feidi.xx.cross.finance.domain.vo.FinBillDetailVo;
import com.feidi.xx.cross.finance.service.IFinBillDetailService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 流水明细
 * 前端访问路由地址为:/finance/billDetail
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/billDetail")
public class FinBillDetailController extends BaseController {

    private final IFinBillDetailService finBillDetailService;

    /**
     * 查询流水明细列表
     */
    @SaCheckPermission("finance:billDetail:list")
    @GetMapping("/list")
    public TableDataInfo<FinBillDetailVo> list(FinBillDetailBo bo, PageQuery pageQuery) {
        return finBillDetailService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出流水明细列表
     */
    @SaCheckPermission("finance:billDetail:export")
    @Log(title = "流水明细", businessType = BusinessType.EXPORT)
    @Download(name = "流水明细", module = ModuleConstants.FINANCE, mode = "no")
    @PostMapping("/export")
    public Object export(FinBillDetailBo bo, HttpServletResponse response) {
        List<FinBillDetailVo> list = finBillDetailService.queryList(bo);
        List<FinBillDetailExportVo> exportVos = MapstructUtils.convert(list, FinBillDetailExportVo.class);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(exportVos, "流水明细", FinBillDetailExportVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取流水明细详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("finance:billDetail:query")
    @GetMapping("/{id}")
    public R<FinBillDetailVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(finBillDetailService.queryById(id));
    }

    /**
     * 新增流水明细
     */
    @SaCheckPermission("finance:billDetail:add")
    @Log(title = "流水明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinBillDetailBo bo) {
        return toAjax(finBillDetailService.insertByBo(bo));
    }

    /**
     * 修改流水明细
     */
    @SaCheckPermission("finance:billDetail:edit")
    @Log(title = "流水明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinBillDetailBo bo) {
        return toAjax(finBillDetailService.updateByBo(bo));
    }

    /**
     * 删除流水明细
     *
     * @param ids 主键串
     */
    @SaCheckPermission("finance:billDetail:remove")
    @Log(title = "流水明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(finBillDetailService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 订单对账流水明细
     */
    @SaCheckPermission("finance:billDetail:add")
    @GetMapping("/order")
    public R<Void> order() {
        finBillDetailService.orderBillDetail();
        return R.ok();
    }
}
