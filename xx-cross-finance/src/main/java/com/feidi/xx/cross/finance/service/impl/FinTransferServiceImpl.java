package com.feidi.xx.cross.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.enums.DirectionEnum;
import com.feidi.xx.cross.common.enums.finance.PwdVerifyEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.enums.finance.FlowTypeEnum;
import com.feidi.xx.cross.common.enums.order.OrderFlowTypeEnum;
import com.feidi.xx.cross.finance.domain.FinFlow;
import com.feidi.xx.cross.finance.domain.FinTransfer;
import com.feidi.xx.cross.finance.domain.bo.FinTransBo;
import com.feidi.xx.cross.finance.domain.bo.FinTransferBo;
import com.feidi.xx.cross.finance.domain.bo.FinTransferQueryBo;
import com.feidi.xx.cross.finance.domain.factory.FinFlowFactory;
import com.feidi.xx.cross.finance.domain.vo.FinTransferAccountVo;
import com.feidi.xx.cross.finance.domain.vo.FinTransferListVo;
import com.feidi.xx.cross.finance.domain.vo.FinTransferVo;
import com.feidi.xx.cross.finance.mapper.FinDrvWalletMapper;
import com.feidi.xx.cross.finance.mapper.FinFlowMapper;
import com.feidi.xx.cross.finance.mapper.FinTransferMapper;
import com.feidi.xx.cross.finance.service.IFinTransferService;
import com.feidi.xx.cross.finance.service.helper.WalletHelper;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDetailVo;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.RemoteDriverService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 转账Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinTransferServiceImpl implements IFinTransferService {

    private final FinTransferMapper baseMapper;
    private final FinFlowMapper flowMapper;
    private final FinDrvWalletMapper drvWalletMapper;
    private final WalletHelper walletHelper;
    @DubboReference
    private final RemoteDriverService remoteDriverService;
    @DubboReference
    private final RemoteAgentService remoteAgentService;
    @DubboReference
    private final RemoteOrderService remoteOrderService;

    @Override
    public List<FinTransferAccountVo> recent() {
        List<Long> recent = baseMapper.recent(LoginHelper.getUserId(), 1);
        if (CollUtil.isNotEmpty(recent)) {
            List<RemoteDriverVo> driverVos = remoteDriverService.getDriverInfo(recent);
            if (CollUtil.isNotEmpty(driverVos)) {
                List<FinTransferAccountVo> vos = new ArrayList<>(driverVos.size());
                for (RemoteDriverVo vo : driverVos) {
                    FinTransferAccountVo accountVo = new FinTransferAccountVo();
                    accountVo.setDriverId(vo.getId());
                    accountVo.setAvatar(vo.getAvatar());
                    accountVo.setName(vo.getName());
                    accountVo.setPhone(vo.getPhone());
                    vos.add(accountVo);
                }
                return vos;
            }
        }
        return List.of();
    }

    /**
     * 查询转账
     *
     * @param id 主键
     * @return 转账
     */
    @Override
    public FinTransferVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询转账列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 转账分页列表
     */
    @Override
    public TableDataInfo<FinTransferListVo> queryPageList(FinTransferQueryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinTransfer> lqw = buildQueryWrapper(bo);
        Page<FinTransferListVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw, FinTransferListVo.class);
        List<FinTransferListVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            Map<Long, String> agentMap = getAgentMap();
            for (FinTransferListVo record : records) {
                String agentName = agentMap.get(record.getAgentId());
                record.setAgentName(agentName);
            }
        }
        return TableDataInfo.build(records, result.getTotal());
    }

    /**
     * 查询符合条件的转账列表
     *
     * @param bo 查询条件
     * @return 转账列表
     */
    @Override
    public List<FinTransferListVo> queryList(FinTransferQueryBo bo) {
        LambdaQueryWrapper<FinTransfer> lqw = buildQueryWrapper(bo);
        List<FinTransferListVo> records = baseMapper.selectVoList(lqw, FinTransferListVo.class);
        if (CollUtil.isNotEmpty(records)) {
            Map<Long, String> agentMap = getAgentMap();
            for (FinTransferListVo record : records) {
                String agentName = agentMap.get(record.getAgentId());
                record.setAgentName(agentName);
            }
        }
        return records;
    }

    private LambdaQueryWrapper<FinTransfer> buildQueryWrapper(FinTransferQueryBo bo) {
        LambdaQueryWrapper<FinTransfer> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, FinTransfer::getId, bo.getId());
        lqw.eq(bo.getTransferNo() != null, FinTransfer::getTransferNo, bo.getTransferNo());

        if (CollUtil.isNotEmpty(bo.getAgentIds())) {
            lqw.in(bo.getAgentIds() != null, FinTransfer::getAgentId, bo.getAgentIds());
        } else {
            if (UserTypeEnum.AGENT_USER.equals(LoginHelper.getUserType())) {
                lqw.nested(bo.getAgentId() != null, l -> {
                    l.eq(FinTransfer::getAgentId, bo.getAgentId());
                } );
            } else {
                lqw.eq(bo.getAgentId() != null, FinTransfer::getAgentId, bo.getAgentId());
            }
        }

        lqw.nested(StrUtil.isNotBlank(bo.getInDriver()),
                l -> l.like(FinTransfer::getInDriverId, bo.getInDriver()).or()
                        .like(FinTransfer::getInDriverName, bo.getInDriver()).or()
                        .like(FinTransfer::getInDriverPhone, bo.getInDriver())
        );
        lqw.nested(StrUtil.isNotBlank(bo.getOutDriver()),
                l -> l.like(FinTransfer::getOutDriverId, bo.getOutDriver()).or()
                        .like(FinTransfer::getOutDriverName, bo.getOutDriver()).or()
                        .like(FinTransfer::getOutDriverPhone, bo.getOutDriver())
        );
        // 创建时间筛选
        lqw.ge(bo.getStartTime() != null, FinTransfer::getCreateTime, bo.getStartTime());
        lqw.le(bo.getEndTime() != null, FinTransfer::getCreateTime, bo.getEndTime());
        // 倒序
        lqw.orderByDesc(FinTransfer::getUpdateTime);
        return lqw;
    }

    private Map<Long, String> getAgentMap() {
        List<RemoteAgentVo> pxAgents = remoteAgentService.getAllAgentInfo();
        return pxAgents.stream().collect(Collectors.toMap(RemoteAgentVo::getId, RemoteAgentVo::getCompanyName));
    }

    /**
     * 新增转账
     *
     * @param bo 转账
     * @return 是否新增成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(FinTransBo bo) {
        Long outDriverId = bo.getOutDriverId();
        // 重置密码
        walletHelper.needChangePwd(outDriverId, false);
        // 密码校验
        walletHelper.verifyToken(outDriverId, PwdVerifyEnum.TRANSFER, bo.getAccessToken(), false);
        // 转账校验
        walletHelper.transferApplicable(outDriverId, false);
        //钱包状态校验
        walletHelper.transferOrCashDisable(outDriverId);

        FinTransfer transfer = MapstructUtils.convert(bo, FinTransfer.class);
        transfer.setUserId(LoginHelper.getUserId());
        transfer.setUserName(LoginHelper.getUsername());
        transfer.setUserType(LoginHelper.getUserType().getUserType());
        transfer.setTransferNo(FinTransfer.generateTransferNo());

        // 转出司机
        RemoteDriverVo outDriver = remoteDriverService.getDriverInfo(outDriverId);
        // token可能是旧的
        transfer.setAgentId(outDriver.getAgentId());
        transfer.setOutDriverName(outDriver.getName());
        transfer.setOutDriverPhone(outDriver.getPhone());
        // 转入司机
        RemoteDriverVo inDriver = remoteDriverService.getDriverInfo(bo.getInDriverId());
        transfer.setInDriverName(inDriver.getName());
        transfer.setInDriverPhone(inDriver.getPhone());

        // 校验
        Assert.isTrue(!ObjectUtil.equals(bo.getInDriverId(), outDriverId), "不能对自己账户转账");
        Assert.isTrue(ObjectUtil.equals(inDriver.getAgentId(), outDriver.getAgentId()), "不能跨代理转账");

        // 更新订单资金流向状态，需要同步处理
        try {
            List<RemoteOrderDetailVo> orders = remoteOrderService.updateOrderFlowStatus(outDriverId, OrderFlowTypeEnum.TRANSFERRED.getCode(), OrderFlowTypeEnum.NORMAL.getCode());
            List<Long> orderIds = StreamUtils.toList(orders, RemoteOrderDetailVo::getId);
            transfer.setRelatedOrder(orderIds);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("提现订单资金流向状态修改失败，{}， {}", JSONUtil.toJsonStr(bo), e.getMessage());
        }

        boolean flag = baseMapper.insert(transfer) > 0;
        if (flag) {
            FlowTypeEnum outType = FlowTypeEnum.DRIVER_SUB;
            FlowTypeEnum inType = FlowTypeEnum.DRIVER_ADD;
            if (LoginHelper.getUserType() == UserTypeEnum.SYS_USER) {
                outType = FlowTypeEnum.ADMIN_TRANSFER_SUB;
                inType = FlowTypeEnum.ADMIN_TRANSFER_ADD;
            }

            // 转出司机扣钱
            FinFlow outLastFlow = flowMapper.getLastFlow(outDriverId);
            Assert.notNull(outLastFlow, "转出司机资金不足，无法转出");
            long sub = ArithUtils.sub(outLastFlow.getAfterAmount(), bo.getAmount());
            Assert.isTrue(sub >= 0, "转出司机资金不足，无法转出");
            FinFlow flowOut = FinFlowFactory.createFlow(outLastFlow, FinFlowFactory.FlowCreateType.TRANSFER, DirectionEnum.OUT, outType, transfer);
            flag &= flowMapper.insert(flowOut) > 0;
            flag &= drvWalletMapper.updateByFlowAndAddExpend(flowOut, bo.getAmount()) > 0;
            Assert.isTrue(flag, "转账转出方扣款失败");

            // 转入司机加钱
            FinFlow inLastFlow = flowMapper.getLastFlow(bo.getInDriverId());
            FinFlow flowIn = FinFlowFactory.createFlow(inLastFlow, FinFlowFactory.FlowCreateType.TRANSFER, DirectionEnum.IN, inType, transfer);
            flag &= flowMapper.insert(flowIn) > 0;
            flag &= drvWalletMapper.updateByFlow(flowIn) > 0;
            Assert.isTrue(flag, "转账接收方入账失败");
        }
        return flag;
    }
    /**
     * 修改转账
     *
     * @param bo 转账
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinTransferBo bo) {
        FinTransfer update = MapstructUtils.convert(bo, FinTransfer.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinTransfer entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除转账信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
