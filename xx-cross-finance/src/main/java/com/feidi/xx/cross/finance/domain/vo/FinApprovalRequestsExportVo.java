package com.feidi.xx.cross.finance.domain.vo;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.converters.date.DateStringConverter;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.cross.common.enums.finance.ApprTransactionTypeEnum;
import com.feidi.xx.cross.common.utils.MoneyUtil;
import com.feidi.xx.cross.finance.domain.FinApprovalRequestRelateOrder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 导出对象
 *
 * <AUTHOR>
 */
@Data
public class FinApprovalRequestsExportVo {
    /**
     * 审批ID
     */
    @ExcelProperty(value = "审批ID")
    private Long id;

    /**
     * 审批名称
     */
    @ExcelProperty(value = "审批名称")
    private String name;
    /**
     * 司机账号（主键）
     */
    @ExcelProperty(value = "司机账号id", index = 0)
    private Long driverId;

    /**
     * 司机姓名
     */
    @ExcelProperty(value = "司机姓名", index = 1)
    private String driverName;

    /**
     * 交易类型（固定为“人工调账”）
     */
    @ExcelProperty(value = "交易类型", index = 2, converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = ApprTransactionTypeEnum.class)
    private Integer transactionType;

    /**
     * 关联订单（非必填）
     */
    @ExcelProperty(value = "关联订单", index = 3)
    private String relatedOrder;

    /**
     * 交易说明
     */
    @ExcelProperty(value = "交易说明", index = 4)
    private String transactionNote;

    /**
     * 调账金额（可为负数）
     */
    @ExcelProperty(value = "调账金额", index = 5)
    private BigDecimal adjustAmount;

    /**
     * 申请原因
     */
    @ExcelProperty(value = "申请原因", index = 6)
    private String exceptionReason;

    /**
     * 调整日期
     */
    @ExcelProperty(value = "调整日期", index = 7, converter = DateStringConverter.class)
    @DateTimeFormat(DatePattern.NORM_DATE_PATTERN)
    private Date adjustmentDate;

    public static FinApprovalRequestsExportVo create(FinApprovalRequestsVo farv, FinApprovalRequestDetailsVo detail, FinApprovalRequestRelateOrder relateOrder) {
        var exportVo = new FinApprovalRequestsExportVo();
        exportVo.setId(farv.getId());
        exportVo.setName(farv.getName());
        exportVo.setDriverId(detail.getDriverId());
        exportVo.setDriverName(detail.getDriverName());
        exportVo.setTransactionType(detail.getTransactionType());
        exportVo.setRelatedOrder(relateOrder.getOrderNo());
        exportVo.setTransactionNote(relateOrder.getTransactionNote());
        exportVo.setAdjustAmount(MoneyUtil.fenToYuan(relateOrder.getAdjustAmount()));
        exportVo.setExceptionReason(relateOrder.getExceptionReason());
        exportVo.setAdjustmentDate(relateOrder.getAdjustmentDate());
        return exportVo;
    }
}
