package com.feidi.xx.cross.finance.domain.vo.driver;

import com.feidi.xx.common.core.domain.SimpleAddress;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * 订单视图对象 cx_order
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FinFlowIncomeOrderVo extends FinFlowDrvBase {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 起点
     */
    private SimpleAddress start;

    /**
     * 终点
     */
    private SimpleAddress end;

}
