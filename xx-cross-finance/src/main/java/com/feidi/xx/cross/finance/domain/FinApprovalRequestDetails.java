package com.feidi.xx.cross.finance.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * 账单审批关联订单对象 fin_approval_request_details
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "fin_approval_request_details", autoResultMap = true)
public class FinApprovalRequestDetails extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 账单审批记录ID
     */
    private Long approvalRequestId;
    /**
     * 司机id
     */
    private Long driverId;
    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 司机姓名
     */
    private String driverName;
    /**
     * 关联订单
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<FinApprovalRequestRelateOrder> relateOrders;

    /**
     * 交易类型，1:人工调账
     */
    private Integer transactionType;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 账户余额(冻结金额+可提现金额)
     */
    private Long accountAmount;

    /**
     * 可提现金额
     */
    private Long balance;

    /**
     * 冻结金额
     */
    private Long freeze;
    /**
     * 代理商id
     */
    private Long agentId;

}
