package com.feidi.xx.cross.finance;


import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 财务模块
 *
 * <AUTHOR>
 * @date 2024/12/5
 */
@EnableAsync
@EnableDubbo
@MapperScan("com.feidi.xx.**.mapper")
@SpringBootApplication(scanBasePackages = {"com.feidi.xx.**"})
public class XXCrossFinanceApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(XXCrossFinanceApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  财务模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
