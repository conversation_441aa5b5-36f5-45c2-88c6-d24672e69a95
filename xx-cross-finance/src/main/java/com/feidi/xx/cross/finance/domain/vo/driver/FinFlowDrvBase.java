package com.feidi.xx.cross.finance.domain.vo.driver;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.feidi.xx.cross.common.enums.finance.FlowTypeEnum;
import com.feidi.xx.cross.finance.domain.FinFlow;
import lombok.Data;
import org.springframework.util.Assert;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class FinFlowDrvBase implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 编号
     */
    private String no;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 金额
     */
    private Long amount;

    /**
     * 流水状态
     */
    private String status;

    /**
     * 类型
     */
    private String type;

    /**
     * 是否冻结
     */
    private Boolean isFreeze;

    /**
     * 资金流向 2转出 1转入 {@link com.feidi.xx.common.core.enums.DirectionEnum}
     */
    private String direction;

    /**
     * 备注
     */
    private String remark;

    /**
     * 代理商名称
     */
    private String agentName;

    public static <T extends FinFlowDrvBase> T create(FinFlow flow, Class<T> clazz) {
        T base = ReflectUtil.newInstance(clazz);
        base.setId(flow.getId());
        base.setNo(flow.getFlowNo());
        base.setCreateTime(flow.getCreateTime());
        base.setAmount(flow.getAmount());
        base.setType(flow.getType());
        FlowTypeEnum flowType = FlowTypeEnum.getByCode(flow.getType());
        Assert.notNull(flowType, StrUtil.format("{}对应的流水类型为空", flow.getType()));
        base.setStatus(flowType.getInfo());
        base.setIsFreeze(FlowTypeEnum.isFreezeType(flowType));
        base.setDirection(flow.getDirection());
        base.setRemark(flow.getRemark());
        return base;
    }

}
