package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.finance.domain.FinBillDetail;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 流水明细视图对象 fin_bill_detail
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinBillDetail.class)
public class FinBillDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 代理商id
     */
    @ExcelProperty(value = "代理商id")
    private Long agentId;

    /**
     * 代理商名称
     */
    @ExcelProperty(value = "代理商名称")
    private String agentName;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机id")
    private Long driverId;

    /**
     * 司机姓名
     */
    @ExcelProperty(value = "司机姓名")
    private String driverName;

    /**
     * 司机电话
     */
    @ExcelProperty(value = "司机电话")
    private String driverPhone;

    /**
     * 账单周期(精确到日)
     */
    @ExcelProperty(value = "账单周期(精确到日)")
    private Date financeDate;

    /**
     * 订单渠道
     */
    @ExcelProperty(value = "订单渠道")
    private String platformCode;

    /**
     * 订单渠道
     */
    private String platformCodeText;

    /**
     * 账单对账类型[FinanceTypeEnum]
     */
    @ExcelProperty(value = "账单对账类型")
    private String financeType;

    /**
     *  账单对账类型[FinanceTypeEnum]
     */
    private String financeTypeText;

    /**
     * 总单量
     */
    @ExcelProperty(value = "总单量")
    private Long orderNumber;

    /**
     * 订单总金额
     */
    @ExcelProperty(value = "订单总金额")
    private Long orderAmount;

    /**
     * 总收益
     */
    @ExcelProperty(value = "总收益")
    private Long orderProfit;

    /**
     * 其他收益
     */
    @ExcelProperty(value = "其他收益")
    private Long otherProfit;

    /**
     * 钱包余额
     */
    @ExcelProperty(value = "钱包余额")
    private Long walletBalance;

    /**
     * 客诉金额
     */
    @ExcelProperty(value = "客诉金额")
    private Long complainAmount;

    /**
     * 优惠券使用总金额
     */
    private Long couponQuotaAmount;

    /**
     * 相关单号
     */
    private String relatedOrder;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
