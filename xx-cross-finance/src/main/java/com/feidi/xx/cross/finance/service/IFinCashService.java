package com.feidi.xx.cross.finance.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.finance.domain.bo.FinCashApplyBo;
import com.feidi.xx.cross.finance.domain.bo.FinCashQueryBo;
import com.feidi.xx.cross.finance.domain.bo.FinCashReviewBo;
import com.feidi.xx.cross.finance.domain.vo.FinCashListVo;
import com.feidi.xx.cross.finance.domain.vo.FinCashVo;

import java.util.Collection;
import java.util.List;

/**
 * 提现Service接口
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
public interface IFinCashService {

    /**
     * 查询提现
     *
     * @param id 主键
     * @return 提现
     */
    FinCashVo queryById(Long id);

    /**
     * 分页查询提现列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 提现分页列表
     */
    TableDataInfo<FinCashListVo> queryPageList(FinCashQueryBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的提现列表
     *
     * @param bo 查询条件
     * @return 提现列表
     */
    List<FinCashListVo> queryList(FinCashQueryBo bo);

    /**
     * 新增提现
     *
     * @param bo 提现
     * @return 是否新增成功
     */
    Boolean insertByBo(FinCashApplyBo bo);

    /**
     * 修改提现
     *
     * @param bo 提现
     * @return 是否修改成功
     */
    Boolean review(FinCashReviewBo bo);

    /**
     * 自动打款
     * @param id 提现单id
     */
    void autoPayment(Long id);

    /**
     * 批量申请提现
     * @param agentIds
     */
    void batchWithdraw(List<Long> agentIds);


    /**
     * 校验并批量删除提现信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
