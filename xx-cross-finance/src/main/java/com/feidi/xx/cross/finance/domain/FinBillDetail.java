package com.feidi.xx.cross.finance.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 流水明细对象 fin_bill_detail
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("fin_bill_detail")
public class FinBillDetail extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 司机电话
     */
    private String driverPhone;

    /**
     * 账单周期(精确到日)
     */
    private Date financeDate;

    /**
     * 订单渠道
     */
    private String platformCode;

    /**
     * 账单对账类型[----Type]
     */
    private String financeType;

    /**
     * 总单量
     */
    private Long orderNumber;

    /**
     * 订单总金额
     */
    private Long orderAmount;

    /**
     * 总收益
     */
    private Long orderProfit;

    /**
     * 其他收益
     */
    private Long otherProfit;

    /**
     * 钱包余额
     */
    private Long walletBalance;

    /**
     * 客诉金额
     */
    private Long complainAmount;

    /**
     * 优惠券使用总金额
     */
    private Long couponQuotaAmount;

    /**
     * 相关单号
     */
    private String relatedOrder;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
