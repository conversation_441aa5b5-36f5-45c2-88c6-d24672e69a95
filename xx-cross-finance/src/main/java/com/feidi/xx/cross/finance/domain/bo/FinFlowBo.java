package com.feidi.xx.cross.finance.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 资金流水业务对象 Fin_flow
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FinFlowBo extends BaseEntity {

    /**
     * 代理id
     */
    private Long agentId;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 进出
     */
    @NotBlank(message = "进出不能为空", groups = { AddGroup.class, EditGroup.class })
    private String direction;

    /**
     * 类型
     */
    @NotBlank(message = "类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;


}
