package com.feidi.xx.cross.finance.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.cross.finance.domain.FinRemit;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 打款记录视图对象 fin_remit
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinRemit.class)
public class FinRemitVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 代理商名称
     */
    @ExcelProperty(value = "代理商名称")
    private String agentName;

    /**
     * 打款账号
     */
    @ExcelProperty(value = "打款账号")
    private String remitAccount;

    /**
     * 打款账户姓名
     */
    @ExcelProperty(value = "打款账户姓名")
    private String remitAccountName;

    /**
     * 打款账号
     */
    @ExcelProperty(value = "打款账号")
    private String receiveAccount;

    /**
     * 打款账户姓名
     */
    @ExcelProperty(value = "打款账户姓名")
    private String receiveAccountName;

    /**
     * 打款类型[AccountTypeEnum]
     */
    @ExcelProperty(value = "打款类型[AccountTypeEnum]")
    private String accountType;

    /**
     * 账单金额
     */
    @ExcelProperty(value = "账单金额")
    private String financeAmount;

    /**
     * 打款金额
     */
    @ExcelProperty(value = "打款金额")
    private String remitAmount;

    /**
     * 打款流水号
     */
    @ExcelProperty(value = "打款流水号")
    private String flowNo;

    /**
     * 交易单号
     */
    @ExcelProperty(value = "交易单号")
    private String transNo;

    /**
     * 交易状态
     */
    @ExcelProperty(value = "交易状态")
    private String transStatus;

    /**
     * 打款时间
     */
    @ExcelProperty(value = "打款时间")
    private Date remitTime;

    /**
     * 打款人姓名
     */
    @ExcelProperty(value = "打款人姓名")
    private String remitUserName;

    /**
     * 打款类型[RemitTypeEnum]
     */
    @ExcelProperty(value = "打款类型[RemitTypeEnum]")
    private String remitType;

    /**
     * 账单日期
     */
    @ExcelProperty(value = "账单日期")
    private Date financeDate;


}
