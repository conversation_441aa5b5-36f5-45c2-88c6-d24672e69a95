package com.feidi.xx.cross.finance.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.cross.finance.domain.FinCash;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 提现业务对象 Fin_cash
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@AutoMapper(target = FinCash.class, reverseConvertGenerate = false)
public class FinCashApplyBo {

    private String tenantId;

    private Long driverId;

    private Long agentId;

    private Long parentId;

    /**
     * 账户id
     */
    @NotNull(message = "账户id不能为空", groups = { AddGroup.class })
    private Long accountId;

    /**
     * 提现金额
     */
    @NotNull(message = "提现金额不能为空", groups = { AddGroup.class })
    private Long amount;

    /**
     * token
     */
    @NotBlank(message = "token不能为空", groups = { AddGroup.class })
    private String accessToken;

}
