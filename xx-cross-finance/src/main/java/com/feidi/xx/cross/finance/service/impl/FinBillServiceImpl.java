package com.feidi.xx.cross.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.finance.enums.FinCacheKeyEnum;
import com.feidi.xx.cross.common.enums.finance.FinanceStatusEnum;
import com.feidi.xx.cross.common.enums.finance.FlowTypeEnum;
import com.feidi.xx.cross.common.enums.order.CreateModelEnum;
import com.feidi.xx.cross.common.enums.order.RebateStatusEnum;
import com.feidi.xx.cross.common.utils.ExceptionUtil;
import com.feidi.xx.cross.finance.domain.FinBill;
import com.feidi.xx.cross.finance.domain.FinDrvWallet;
import com.feidi.xx.cross.finance.domain.FinFlow;
import com.feidi.xx.cross.finance.domain.bo.FinBillBo;
import com.feidi.xx.cross.finance.domain.vo.FinBillVo;
import com.feidi.xx.cross.finance.mapper.FinBillMapper;
import com.feidi.xx.cross.finance.service.*;
import com.feidi.xx.cross.market.api.RemoteInviteRecordService;
import com.feidi.xx.cross.market.api.domain.RemoteInviteRecordVo;
import com.feidi.xx.cross.order.api.RemoteOrderInfoService;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderInfoVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 订单对账Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinBillServiceImpl implements IFinBillService {

    private final IFinFlowService finFlowService;
    private final IFinDrvWalletService finDrvWalletService;
    private final IFinOrderFeeService finOrderFeeService;
    private final IFinComplainFeeService finComplainFeeService;
    private final FinBillMapper baseMapper;
    private final ScheduledExecutorService scheduledExecutorService;
    @DubboReference
    private final RemoteAgentService remoteAgentService;
    @DubboReference
    private final RemoteOrderService remoteOrderService;
    @DubboReference
    private final RemoteOrderInfoService remoteOrderInfoService;
    @DubboReference
    private final RemoteInviteRecordService remoteInviteRecordService;

    /**
     * 查询订单对账
     *
     * @param id 主键
     * @return 订单对账
     */
    @Override
    public FinBillVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询订单对账列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单对账分页列表
     */
    @Override
    public TableDataInfo<FinBillVo> queryPageList(FinBillBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinBill> lqw = buildQueryWrapper(bo);
        Page<FinBillVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的订单对账列表
     *
     * @param bo 查询条件
     * @return 订单对账列表
     */
    @Override
    public List<FinBillVo> queryList(FinBillBo bo) {
        LambdaQueryWrapper<FinBill> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinBill> buildQueryWrapper(FinBillBo bo) {
        LambdaQueryWrapper<FinBill> lqw = Wrappers.lambdaQuery();
        lqw.in(CollUtils.isNotEmpty(bo.getIds()), FinBill::getId, bo.getIds());
        lqw.eq(bo.getAgentId() != null, FinBill::getAgentId, bo.getAgentId());
        lqw.like(StringUtils.isNotBlank(bo.getAgentName()), FinBill::getAgentName, bo.getAgentName());
        //lqw.eq(bo.getMonth() != null, FinBill::getMonth, bo.getMonth());
        lqw.eq(StringUtils.isNotBlank(bo.getFinanceStatus()), FinBill::getFinanceStatus, bo.getFinanceStatus());
        lqw.eq(bo.getFinanceTime() != null, FinBill::getFinanceTime, bo.getFinanceTime());
        lqw.eq(StringUtils.isNotBlank(bo.getFinanceUserType()), FinBill::getFinanceUserType, bo.getFinanceUserType());
        lqw.eq(bo.getFinanceUserId() != null, FinBill::getFinanceUserId, bo.getFinanceUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getRelatedOrder()), FinBill::getRelatedOrder, bo.getRelatedOrder());
        if (StringUtils.isNotBlank(bo.getStartTime()) && StringUtils.isNotBlank(bo.getEndTime())) {
            lqw.ge(FinBill::getMonth, bo.getStartTime());
            lqw.le(FinBill::getMonth, bo.getEndTime());
        }

        lqw.orderByDesc(FinBill::getMonth).orderByDesc(FinBill::getAgentId).orderByAsc(FinBill::getFinanceStatus);

        return lqw;
    }

    /**
     * 新增订单对账
     *
     * @param bo 订单对账
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinBillBo bo) {
        FinBill add = MapstructUtils.convert(bo, FinBill.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改订单对账
     *
     * @param bo 订单对账
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinBillBo bo) {
        FinBill update = MapstructUtils.convert(bo, FinBill.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinBill entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除订单对账信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据对账状态查询对账记录
     *
     * @param financeStatus 对账状态
     * @return 对账记录
     */
    @Override
    public List<FinBill> queryByFinanceStatus(String financeStatus) {
        LambdaQueryWrapper<FinBill> lqw = Wrappers.lambdaQuery();
        lqw.eq(FinBill::getFinanceStatus, financeStatus);

        return baseMapper.selectList(lqw);
    }

    /**
     * 确认对账
     *
     * @param id 主键
     * @return 是否确认成功
     */
    @Override
    public Boolean confirm(Long id) {
        FinBill bill = baseMapper.selectById(id);
        if (Objects.equals(bill.getFinanceStatus(), FinanceStatusEnum.CONFIRMED.getCode())) {
            log.info("订单对账-【{}】已确认，无需重复确认！", bill.getId());
            throw new ServiceException("订单已确认，无需重复确认！");
        }

        LambdaUpdateWrapper<FinBill> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(FinBill::getFinanceStatus, FinanceStatusEnum.CONFIRMED.getCode())
                .set(FinBill::getFinanceTime, DateUtils.getNowDate())
                .set(FinBill::getFinanceUserId, LoginHelper.getUserId())
                .set(FinBill::getFinanceUserType, LoginHelper.getUserType())
                .set(FinBill::getUpdateTime, DateUtils.getNowDate())
                .eq(FinBill::getId, id);

        return baseMapper.update(updateWrapper) > 0;
    }

    @Override
    public void orderBill() {
        // 查询所有的代理商
        List<RemoteAgentVo> allAgents = remoteAgentService.getAllAgentInfo();
        if (CollUtils.isEmpty(allAgents)) {
            log.info("订单对账-代理商集合为空，对账结束！");
            return;
        }

        // 构建订单对账数据
        List<FinBill> bills = builderOrderBill(allAgents);

        // 获取可以返利的订单
        String startTime = DateUtils.date2String(DateUtils.getDayBegin());
        String endTime = DateUtils.date2String(DateUtils.getDayEnd());
        if (log.isInfoEnabled()) {
            log.info("订单对账 - : 结算时间：【{} - {}】", startTime, endTime);
        }

        // 查询当天已结算的所有订单
        List<String> rebateStatus = Stream.of(RebateStatusEnum.FINISH.getCode(), RebateStatusEnum.COMPLAIN.getCode()).collect(Collectors.toList());
        List<RemoteOrderVo> orderVos = remoteOrderService.queryByRebateStatusAndTime(rebateStatus, startTime, endTime);

        // 将当天已结算的所有订单数据缓存，便于后期生成账单明细
        String cacheKey = FinCacheKeyEnum.FIN_ORDER_BILL_KEY.create(startTime);
        RedisUtils.setCacheList(cacheKey, orderVos, FinCacheKeyEnum.FIN_ORDER_BILL_KEY.getDuration());

        // 查询当天已结算且发生客诉的订单
        Map<Long, Long> agentId2ComplainPriceMap = new HashMap<>();
        List<RemoteOrderVo> complainOrderVos = remoteOrderService.queryByRebateStatusAndTimeWithComplaint(RebateStatusEnum.COMPLAIN.getCode(), startTime, endTime);
        if (CollUtils.isNotEmpty(complainOrderVos)) {
            Set<Long> driverIds = complainOrderVos.stream().map(RemoteOrderVo::getDriverId).collect(Collectors.toSet());
            // 过滤出司机钱包余额小于0的司机id
            List<Long> balanceDriverIds = finDrvWalletService.queryByDriverIds(new ArrayList<>(driverIds))
                    .stream().filter(e -> e.getBalance() < 0)
                    .map(FinDrvWallet::getDriverId).collect(Collectors.toList());

            // 司机钱包余额小于0的订单
            if (CollUtils.isNotEmpty(balanceDriverIds)) {
                complainOrderVos = complainOrderVos.stream().filter(e -> balanceDriverIds.contains(e.getDriverId())).toList();
                agentId2ComplainPriceMap = complainOrderVos.stream().collect(Collectors.groupingBy(RemoteOrderVo::getAgentId, Collectors.summingLong(RemoteOrderVo::getOrderPrice)));
            }
        }

        // 已经确认的订单对账数据
        Map<Long, Long> agentId2RebateAmountMap = this.queryByFinanceStatus(FinanceStatusEnum.CONFIRMED.getCode())
                .stream().collect(Collectors.groupingBy(FinBill::getAgentId, Collectors.summingLong(FinBill::getRebateAmount)));

        if (CollUtils.isNotEmpty(orderVos)) {

            Map<Long, List<RemoteOrderVo>> agentId2OrderMap = orderVos.stream().collect(Collectors.groupingBy(RemoteOrderVo::getAgentId));

            // 当天已结算流水（订单结算和订单转卖结算）
            List<String> flowTypes = Stream.of(FlowTypeEnum.ORDER_REBATE.getCode(), FlowTypeEnum.RESELL_ORDER_REBATE.getCode()).toList();
            List<FinFlow> flows = finFlowService.queryTodayFlowByTypes(flowTypes);
            Map<Long, Long> agentId2CashMap = new HashMap<>();
            if (CollUtils.isNotEmpty(flows)) {
                agentId2CashMap = flows.stream().collect(Collectors.groupingBy(FinFlow::getAgentId, Collectors.summingLong(FinFlow::getCash)));
            }

            // 代理商拉新奖励收益
            Map<Long, Long> agentId2RewardMap = new HashMap<>();
            List<RemoteOrderVo> inviteOrders = orderVos.stream().filter(e -> e.getInviteAgentId() != null && e.getInviteAgentId() > 0).collect(Collectors.toList());
            if (CollUtils.isNotEmpty(inviteOrders)) {
                // 邀请订单id
                List<Long> inviteOrderIds = inviteOrders.stream().map(RemoteOrderVo::getId).collect(Collectors.toList());
                List<RemoteInviteRecordVo> inviteRecordVos = remoteInviteRecordService.queryByOrderIds(inviteOrderIds);
                agentId2RewardMap = inviteRecordVos.stream().collect(Collectors.groupingBy(RemoteInviteRecordVo::getAgentId, Collectors.summingLong(RemoteInviteRecordVo::getRewardPrice)));
            }

            // 司机邀请有奖订单收益
            Map<Long, Long> agentId2DriverInviteMap = new HashMap<>();
            inviteOrders = inviteOrders.stream().filter(e -> e.getInviteDriverId() != null && e.getInviteDriverId() > 0).collect(Collectors.toList());
            if (CollUtils.isNotEmpty(inviteOrders)) {
                List<Long> driverInviteOrderIds = inviteOrders.stream().map(RemoteOrderVo::getId).collect(Collectors.toList());
                List<FinFlow> driverInviteFlows = finFlowService.queryByOrderIdsAndType(driverInviteOrderIds, FlowTypeEnum.INVITE_ORDER_REWARD.getCode());
                agentId2DriverInviteMap = driverInviteFlows.stream().collect(Collectors.groupingBy(FinFlow::getAgentId, Collectors.summingLong(FinFlow::getCash)));
            }

            for (FinBill bill : bills) {
                if (agentId2OrderMap.containsKey(bill.getAgentId())) {
                    List<RemoteOrderVo> orderList = agentId2OrderMap.get(bill.getAgentId());
                    // 订单金额
                    Long orderAmount = orderList.stream().mapToLong(RemoteOrderVo::getOrderPrice).sum();
                    // 司机收益
                    Long driverProfit = 0L;
                    // 订单结算和订单转卖结算收益
                    driverProfit += agentId2CashMap.getOrDefault(bill.getAgentId(), 0L);
                    // 代理商拉新奖励收益
                    driverProfit += agentId2RewardMap.getOrDefault(bill.getAgentId(), 0L);
                    // 司机邀请有奖订单收益
                    driverProfit += agentId2DriverInviteMap.getOrDefault(bill.getAgentId(), 0L);

                    // 过滤出当天使用通过优惠券的订单
                    List<Long> useCouponOrderIds = orderList.stream().filter(e -> e.getOrderPrice() > e.getPayPrice())
                            .map(RemoteOrderVo::getId).toList();
                    if (CollUtils.isNotEmpty(useCouponOrderIds)) {
                        long couponQuotaAmount = remoteOrderInfoService.queryByOrderIds(useCouponOrderIds)
                                .stream().mapToLong(RemoteOrderInfoVo::getCouponGrantQuota).sum();
                        bill.setCouponQuotaAmount(couponQuotaAmount);
                    }

                    // 当天没有已计算订单，跳过后续的计算
                    if (CollUtils.isEmpty(orderList)) {
                        continue;
                    }

                    bill.setOrderNumber((long) orderList.size());
                    bill.setOrderAmount(orderAmount);
                    bill.setDriverProfit(driverProfit);

                    // 应收信息服务费 
                    long infoServiceFee = calculateInfoServiceFee(orderList);
                    bill.setInfoServiceFee(infoServiceFee);

                    // 技术服务费 = (订单费用-作弊订单费用) * 1.5%
                    long technicalServiceFee = 0L;
                    for (RemoteOrderVo orderVo : orderList) {
                        technicalServiceFee += ArithUtils.profitUseBigDecimal(orderVo.getOrderPrice(), new BigDecimal("1.5"));
                    }
                    bill.setTechnicalServiceFee(technicalServiceFee);

                    // 代收款 = 订单金额扣除信息服务费和技术服务费后的款项
                    long proxyAmount = orderAmount - infoServiceFee - technicalServiceFee;
                    bill.setProxyAmount(proxyAmount);

                    // 奖励金额
                    long rewardAmount = calculateRewardAmount(orderList);
                    //// 渠道标记3的为鹤山引流订单，是没有奖励的，最终应结算时需要扣除
                    //List<RemoteOrderVo> subRewardOrders = orderList.stream().filter(e -> Objects.equals(e.getChannel(), "3")).collect(Collectors.toList());
                    //if (CollUtils.isNotEmpty(subRewardOrders)) {
                    //    rewardAmount -= calculateRewardAmount(subRewardOrders);
                    //}
                    bill.setRewardAmount(rewardAmount);

                    // 应结算金额 = 代收款 + 奖励金额 - 其他
                    bill.setRebateAmount(proxyAmount + rewardAmount);

                    // 客诉金额
                    bill.setComplainAmount(agentId2ComplainPriceMap.getOrDefault(bill.getAgentId(), 0L));

                    // 已经算金额
                    bill.setAlreadyRebateAmount(agentId2RebateAmountMap.getOrDefault(bill.getAgentId(), 0L));

                    // 可提现金额
                    //bill.setNotCashAmount(agentId2RebateAmountMap.getOrDefault(bill.getAgentId(), 0L));
                    bill.setNotCashAmount(0L);

                    // 渠道标记3的为鹤山引流订单，是没有奖励的，需要在奖励金额中体现，最终应结算时需要扣除
                    List<RemoteOrderVo> channelOrderList = orderList.stream().filter(e -> Objects.equals(e.getChannel(), "3")).collect(Collectors.toList());
                    if (CollUtils.isNotEmpty(channelOrderList)) {
                        Long channelRewardAmount = calculateRewardAmount(channelOrderList);
                        // 在应结算金额中扣除
                        bill.setRebateAmount(bill.getRebateAmount() - channelRewardAmount);
                        // 将应扣除的奖励金额记录在“其他金额”中
                        //bill.setOtherAmount(bill.getOtherAmount() + channelRewardAmount);
                    }

                    // 待结算金额
                    bill.setNotRebateAmount(bill.getRebateAmount());

                    // 订单编号
                    bill.setRelatedOrder(orderList.stream().map(RemoteOrderVo::getOrderNo).collect(Collectors.joining(",")));
                }
            }
        }

        // 过滤掉订单没有结算的账单
        bills = bills.stream().filter(bill -> bill.getOrderNumber() != null && bill.getOrderNumber() > 0).collect(Collectors.toList());

        if (CollUtils.isNotEmpty(bills)) {
            baseMapper.insertBatch(bills);
        }

        // 订单应收服务费
        orderFee(orderVos, complainOrderVos.stream().map(RemoteOrderVo::getId).collect(Collectors.toList()));

        // 订单客诉费用
        complainFee(complainOrderVos);
    }

    /**
     * 订单应收服务费
     *
     * @param orderVos 订单信息
     */
    public void orderFee(List<RemoteOrderVo> orderVos, List<Long> complainOrderIds) {
        if (CollUtils.isEmpty(orderVos)) {
            return;
        }

        ExceptionUtil.ignoreEx(() -> {
            scheduledExecutorService.schedule(() -> {
                finOrderFeeService.orderFee(orderVos, complainOrderIds, DateUtils.getBeginDayOfYesterday());
            }, 5, TimeUnit.SECONDS);

        });
    }

    public void complainFee(List<RemoteOrderVo> orderVos) {
        if (CollUtils.isEmpty(orderVos)) {
            return;
        }

        ExceptionUtil.ignoreEx(() -> {
            scheduledExecutorService.schedule(() -> {
                finComplainFeeService.complainFee(orderVos, DateUtils.getBeginDayOfYesterday());
            }, 10, TimeUnit.SECONDS);

        });
    }

    /**
     * 构建订单对账数据
     *
     * @param allAgents 代理商信息
     * @return
     */
    private List<FinBill> builderOrderBill(List<RemoteAgentVo> allAgents) {
        if (CollUtils.isEmpty(allAgents)) {
            return Collections.emptyList();
        }
        Date nowDate = DateUtils.getBeginDayOfYesterday();
        List<FinBill> bills = new ArrayList<>();

        allAgents.forEach(agent -> {
            FinBill bill = new FinBill();
            bill.setAgentId(agent.getId());
            bill.setAgentName(agent.getCompanyName());
            bill.setMonth(nowDate);
            bill.setOrderNumber(0L);
            bill.setOrderAmount(0L);
            bill.setDriverProfit(0L);
            bill.setProxyAmount(0L);
            bill.setInfoServiceFee(0L);
            bill.setTechnicalServiceFee(0L);
            bill.setRewardAmount(0L);
            bill.setComplainAmount(0L);
            bill.setOtherAmount(0L);
            bill.setRebateAmount(0L);
            bill.setAlreadyRebateAmount(0L);
            bill.setNotRebateAmount(0L);
            bill.setAlreadyCashAmount(0L);
            bill.setNotCashAmount(0L);
            bill.setFinanceStatus(FinanceStatusEnum.NOT_CONFIRM.getCode());

            bills.add(bill);
        });

        return bills;
    }

    /**
     * 计算信息服务费
     *
     * 1.司机下单：订单金额 * 2.5%
     * 2.运力下单：订单金额 * 8.5%
     * 3.司机二维码下单：订单金额 * 8.5%
     * 4.运力二维码下单：订单金额 * 8.5%
     * 5.乘客下单：订单金额 * 8.5%
     * 6.订单转让：订单金额 * 8.5%
     *
     * @param orders 订单集合
     * @return 信息服务费
     */
    private Long calculateInfoServiceFee(List<RemoteOrderVo> orders) {
        long infoServiceFee = 0L;
        if (CollUtils.isEmpty(orders)) {
            return infoServiceFee;
        }

        // 司机代下单订单
        List<RemoteOrderVo> driverOrders = orders.stream().filter(e -> Objects.equals(e.getCreateModel(), CreateModelEnum.DRIVER_ORDER.getCode())).collect(Collectors.toList());
        if (CollUtils.isNotEmpty(driverOrders)) {
            for (RemoteOrderVo driverOrder : driverOrders) {
                infoServiceFee += ArithUtils.profitUseBigDecimal(driverOrder.getOrderPrice(), new BigDecimal("2.5"));
            }
        }

        // 其他方式下单
        List<RemoteOrderVo> otherOrders = orders.stream().filter(e -> !Objects.equals(e.getCreateModel(), CreateModelEnum.DRIVER_ORDER.getCode())).collect(Collectors.toList());
        if (CollUtils.isNotEmpty(otherOrders)) {
            for (RemoteOrderVo otherOrder : otherOrders) {
                infoServiceFee += ArithUtils.profitUseBigDecimal(otherOrder.getOrderPrice(), new BigDecimal("8.5"));
            }
        }

        return infoServiceFee;
    }

    /**
     * 计算奖励金额
     *
     * 1.司机下单：订单应付金额×2%
     * 2.运力下单：订单应付金额×5%
     * 3.司机二维码下单：订单应付金额×5%
     * 4.运力二维码下单：订单应付金额×5%
     * 5.乘客下单：订单应付金额×5%
     * 6.订单转让：订单应付金额×5%
     *
     * 注意：渠道标记3的为鹤山引流订单，是没有奖励的，需要在此列体现，最终应结算时需要扣除
     * @param orders
     * @return
     */
    private Long calculateRewardAmount(List<RemoteOrderVo> orders) {
        long rewardAmount = 0L;
        if (CollUtils.isEmpty(orders)) {
            return rewardAmount;
        }

        // 司机代下单订单
        List<RemoteOrderVo> driverOrders = orders.stream().filter(e -> Objects.equals(e.getCreateModel(), CreateModelEnum.DRIVER_ORDER.getCode())).collect(Collectors.toList());
        if (CollUtils.isNotEmpty(driverOrders)) {
            for (RemoteOrderVo driverOrder : driverOrders) {
                rewardAmount += ArithUtils.profitUseBigDecimal(driverOrder.getOrderPrice(), new BigDecimal("2"));
            }
        }

        // 其他方式下单
        List<RemoteOrderVo> otherOrders = orders.stream().filter(e -> !Objects.equals(e.getCreateModel(), CreateModelEnum.DRIVER_ORDER.getCode())).collect(Collectors.toList());
        if (CollUtils.isNotEmpty(otherOrders)) {
            for (RemoteOrderVo otherOrder : otherOrders) {
                rewardAmount += ArithUtils.profitUseBigDecimal(otherOrder.getOrderPrice(), new BigDecimal("5"));
            }
        }

        return rewardAmount;
    }

}
