package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.finance.domain.FinDrvWallet;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinDrvWallet.class, convertGenerate = false)
public class FinWalletListVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    //@ExcelProperty(value = "主键")
    @ExcelIgnore
    private Long id;

    /**
     * 城市
     */
    //@ExcelProperty(value = "城市")
    @ExcelIgnore
    private String city;

    /**
     * 代理id
     */
    //@ExcelProperty(value = "代理id")
    @ExcelIgnore
    private Long agentId;

    /**
     * 代理名称
     */
    @ExcelProperty(value = "代理名称")
    private String agentName;

    /**
     * 父代理id
     */
    //@ExcelProperty(value = "父代理id")
    @ExcelIgnore
    private Long parentId;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机id")
    private Long driverId;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机名称")
    private String driverName;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机名称")
    private String driverPhone;

    /**
     * 累计收益
     */
    @ExcelProperty(value = "累计收益")
    private Long reduceProfit;

    /**
     * 佣金收益
     */
    private Long orderAmount;

    /**
     * 奖励收益
     */
    private Long rewardAmount;


    /**
     * 订单转卖收益
     */
    private Long resellAmount;

    /**
     * 账户调整金额
     */
    private Long adjustAmount;

    /**
     * 累计提现
     */
    @ExcelProperty(value = "累计提现收益")
    private Long reduceCash;

    /**
     * 总金额
     */
    @ExcelProperty(value = "账户余额")
    private Long total;

    /**
     * 冻结金额
     */
    @ExcelProperty(value = "冻结金额")
    private Long freeze;

    /**
     * 余额
     */
    @ExcelProperty(value = "可提现金额")
    private Long balance;

    /**
     * 提现中金额
     */
    @ExcelProperty(value = "提现中金额")
    private Long cashing;

    /**
     * 司机账户状态
     */
    //@ExcelProperty(value = "司机账户状态", converter = ExcelEnumConvert.class)
    //@ExcelEnumFormat(enumClass = UserStatusEnum.class)
    @ExcelIgnore
    private String status;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

}
