package com.feidi.xx.cross.finance.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 账单审批操作记录对象 fin_approval_log
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("fin_approval_log")
public class FinApprovalLog implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 关联账单审批ID
     */
    private Long approvalId;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作类型，如提交、审批通过、驳回等
     */
    private String operation;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 操作备注
     */
    private String remark;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 创建部门
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createDept;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    public static FinApprovalLog create(Long approvalId, String operation) {
        return FinApprovalLog.builder()
                .approvalId(approvalId)
                .operatorId(LoginHelper.getUserId())
                .operation(operation)
                .operateTime(new Date())
                .build();
    }
}
