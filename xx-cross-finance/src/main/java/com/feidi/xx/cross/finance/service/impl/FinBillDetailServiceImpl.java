package com.feidi.xx.cross.finance.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.UserStatusEnum;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.common.constant.order.OrderConstants;
import com.feidi.xx.cross.common.enums.finance.FinanceTypeEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.common.enums.order.RateTypeEnum;
import com.feidi.xx.cross.finance.domain.FinBillDetail;
import com.feidi.xx.cross.finance.domain.FinDrvWallet;
import com.feidi.xx.cross.finance.domain.bo.FinBillDetailBo;
import com.feidi.xx.cross.finance.domain.vo.FinBillDetailVo;
import com.feidi.xx.cross.finance.mapper.FinBillDetailMapper;
import com.feidi.xx.cross.finance.service.IFinBillDetailService;
import com.feidi.xx.cross.finance.service.IFinDrvWalletService;
import com.feidi.xx.cross.order.api.RemoteOrderRateService;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderRateVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.RemoteDriverService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.system.api.RemoteConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 流水明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinBillDetailServiceImpl implements IFinBillDetailService {


    private final IFinDrvWalletService finDrvWalletService;
    private final FinBillDetailMapper baseMapper;
    @DubboReference
    private final RemoteOrderService remoteOrderService;
    @DubboReference
    private final RemoteOrderRateService remoteOrderRateService;
    @DubboReference
    private final RemoteDriverService  remoteDriverService;
    @DubboReference
    private final RemoteAgentService remoteAgentService;
    @DubboReference
    private final RemoteConfigService remoteConfigService;

    /**
     * 查询流水明细
     *
     * @param id 主键
     * @return 流水明细
     */
    @Override
    public FinBillDetailVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询流水明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 流水明细分页列表
     */
    @Override
    public TableDataInfo<FinBillDetailVo> queryPageList(FinBillDetailBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinBillDetail> lqw = buildQueryWrapper(bo);
        Page<FinBillDetailVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        result.getRecords().forEach(e -> {
            e.setPlatformCodeText(PlatformCodeEnum.getInfoByCode(e.getPlatformCode()));
            e.setFinanceTypeText(FinanceTypeEnum.getInfoByCode(e.getFinanceType()));
        });
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的流水明细列表
     *
     * @param bo 查询条件
     * @return 流水明细列表
     */
    @Override
    public List<FinBillDetailVo> queryList(FinBillDetailBo bo) {
        LambdaQueryWrapper<FinBillDetail> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinBillDetail> buildQueryWrapper(FinBillDetailBo bo) {
        LambdaQueryWrapper<FinBillDetail> lqw = Wrappers.lambdaQuery();
        lqw.in(CollUtils.isNotEmpty(bo.getIds()), FinBillDetail::getId, bo.getIds());
        lqw.eq(bo.getAgentId() != null && bo.getAgentId() > 0, FinBillDetail::getAgentId, bo.getAgentId());
        lqw.eq(bo.getDriverId() != null && bo.getDriverId() > 0, FinBillDetail::getDriverId, bo.getDriverId());
        lqw.like(StringUtils.isNotBlank(bo.getDriverPhone()), FinBillDetail::getDriverPhone, bo.getDriverPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getPlatformCode()), FinBillDetail::getPlatformCode, bo.getPlatformCode());
        lqw.eq(StringUtils.isNotBlank(bo.getFinanceType()), FinBillDetail::getFinanceType, bo.getFinanceType());
        if (StringUtils.isNotBlank(bo.getFinanceStartTime()) && StringUtils.isNotBlank(bo.getFinanceEndTime())) {
            lqw.ge(FinBillDetail::getFinanceDate, bo.getFinanceStartTime());
            lqw.le(FinBillDetail::getFinanceDate, bo.getFinanceEndTime());
        }
        if (StringUtils.isNotBlank(bo.getCreateStartTime()) && StringUtils.isNotBlank(bo.getCreateEndTime())) {
            lqw.ge(FinBillDetail::getCreateTime, bo.getCreateStartTime());
            lqw.le(FinBillDetail::getCreateTime, bo.getCreateEndTime());
        }

        lqw.orderByDesc(FinBillDetail::getFinanceDate).orderByDesc(FinBillDetail::getAgentId).orderByDesc(FinBillDetail::getDriverId);

        return lqw;
    }

    /**
     * 新增流水明细
     *
     * @param bo 流水明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinBillDetailBo bo) {
        FinBillDetail add = MapstructUtils.convert(bo, FinBillDetail.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改流水明细
     *
     * @param bo 流水明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinBillDetailBo bo) {
        FinBillDetail update = MapstructUtils.convert(bo, FinBillDetail.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinBillDetail entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除流水明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 订单对账明细
     */
    @Override
    public void orderBillDetail() {
        // 查询完成的所有订单
        List<RemoteOrderVo> orderVos = getOrders();

        // 创建默认的账单详情
        List<FinBillDetail> finBillDetails = builderBillDetail();
        // 按照司机id+订单渠道+账单对账类型进行分组
        Map<String, FinBillDetail> driverId2DetailMap = finBillDetails.stream().collect(Collectors.toMap(e -> e.getDriverId() + e.getPlatformCode() + e.getFinanceType(), Function.identity(), (v1, v2) -> v2));

        if (CollUtils.isNotEmpty(orderVos)) {
            Set<Long> driverIds = orderVos.stream().filter(e -> e.getDriverId() != null && e.getDriverId() > 0)
                    .map(RemoteOrderVo::getDriverId).collect(Collectors.toSet());
            // 查询司机的钱包，并按照司机id进行分组
            Map<Long, FinDrvWallet> driverId2WalletMap = finDrvWalletService.queryByDriverIds(new ArrayList<>(driverIds))
                    .stream().collect(Collectors.toMap(FinDrvWallet::getDriverId, Function.identity(), (v1, v2) -> v2));

            // 过滤出客诉的订单，并按照司机id进行分组
            Map<Long, Long> driverId2ComplainPriceMap = orderVos.stream().filter(e -> Objects.equals(e.getComplain(), IsYesEnum.YES.getCode()))
                    .collect(Collectors.groupingBy(RemoteOrderVo::getDriverId, Collectors.summingLong(RemoteOrderVo::getOrderPrice)));

            List<Long> orderIds = orderVos.stream().map(RemoteOrderVo::getId).collect(Collectors.toList());
            // 司机收益
            List<RemoteOrderRateVo> orderRateVos = remoteOrderRateService.queryByOrderIdsAndRateType(orderIds, RateTypeEnum.DRIVER.getCode());
            // 将订单按照司ID进行分组
            Map<Long, List<RemoteOrderVo>> driverId2OrderMap = orderVos.stream().collect(Collectors.groupingBy(RemoteOrderVo::getDriverId));
            for (Map.Entry<Long, List<RemoteOrderVo>> entry : driverId2OrderMap.entrySet()) {
                Long driverId = entry.getKey();
                List<RemoteOrderVo> orderVoList = entry.getValue();
                if (CollUtils.isNotEmpty(orderVoList)) {
                    // 再将订单按平台进行分组
                    Map<String, List<RemoteOrderVo>> platformCode2OrderMap = orderVoList.stream().collect(Collectors.groupingBy(RemoteOrderVo::getPlatformCode));
                    platformCode2OrderMap.forEach((platformCode, orderList) -> {
                        if (CollUtils.isNotEmpty(orderList)) {
                            FinBillDetail billDetail = driverId2DetailMap.get(driverId + platformCode + FinanceTypeEnum.PROFIT.getCode());
                            if (billDetail != null) {
                                billDetail.setOrderNumber((long) orderList.size());
                                billDetail.setOrderAmount(orderList.stream().mapToLong(RemoteOrderVo::getOrderPrice).sum());

                                List<Long> platformOrderIds = orderList.stream().map(RemoteOrderVo::getId).toList();
                                // 司机收益
                                long orderProfit = orderRateVos.stream().filter(e -> platformOrderIds.contains(e.getOrderId()))
                                        .mapToLong(RemoteOrderRateVo::getAmount).sum();
                                billDetail.setOrderProfit(orderProfit);

                                // 钱包余额
                                if (driverId2WalletMap.containsKey(driverId)) {
                                    billDetail.setWalletBalance(driverId2WalletMap.get(driverId).getBalance());
                                }

                                billDetail.setComplainAmount(driverId2ComplainPriceMap.getOrDefault(driverId, 0L));

                                // 优惠券使用总金额
                                Long couponQuotaAmount = orderList.stream().mapToLong(e -> (e.getOrderPrice() - e.getPayPrice())).sum();
                                billDetail.setCouponQuotaAmount(couponQuotaAmount);
                                // 订单编号
                                billDetail.setRelatedOrder(orderList.stream().map(RemoteOrderVo::getOrderNo).collect(Collectors.joining(",")));
                            }

                            // 佣金奖励
                            if (Objects.equals(platformCode, PlatformCodeEnum.SELF.getCode())) {
                                List<Long> inviteOrderIds = orderList.stream().filter(e -> e.getInviteDriverId() != null && e.getInviteDriverId() > 0)
                                        .map(RemoteOrderVo::getId).collect(Collectors.toList());
                                if (CollUtils.isNotEmpty(inviteOrderIds)) {
                                    // 司机邀请有奖收益
                                    long inviteOrderProfit = remoteOrderRateService.queryByOrderIdsAndRateType(inviteOrderIds, RateTypeEnum.INVITE_DRIVER.getCode())
                                            .stream().mapToLong(RemoteOrderRateVo::getAmount).sum();

                                    FinBillDetail rewardBillDetail = driverId2DetailMap.get(driverId + platformCode + FinanceTypeEnum.REWARD.getCode());
                                    if (rewardBillDetail != null) {
                                        rewardBillDetail.setOrderNumber((long) orderList.size());
                                        rewardBillDetail.setOrderAmount(orderList.stream().mapToLong(RemoteOrderVo::getOrderPrice).sum());
                                        rewardBillDetail.setOrderProfit(inviteOrderProfit);

                                        // 订单编号
                                        String relatedOrder = orderList.stream().filter(e -> e.getInviteDriverId() != null && e.getInviteDriverId() > 0)
                                                .map(RemoteOrderVo::getOrderNo).collect(Collectors.joining(","));
                                        rewardBillDetail.setRelatedOrder(relatedOrder);
                                    }
                                }
                            }
                        }
                    });
                }
            }
        }

        // 过滤掉账单明细
        finBillDetails = finBillDetails.stream().filter(e -> e.getOrderNumber() != null && e.getOrderNumber() > 0).collect(Collectors.toList());

        if (CollUtils.isNotEmpty(finBillDetails)) {
            baseMapper.insertBatch(finBillDetails);
        }
    }

    private List<RemoteOrderVo> getOrders() {
        // 获取自营平台返利周期时间（单位天）
        String period = Optional.ofNullable(remoteConfigService.selectValueByKey(OrderConstants.CX_REBATE_SELF_PERIOD)).orElse(OrderConstants.CX_REBATE_SELF_PERIOD_DEFAULT);
        if (log.isInfoEnabled()) {
            log.info("订单对账明细-订单返利周期时间：【{}】", period);
        }

        String startTime = DateUtils.date2String(DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -Integer.parseInt(period))));
        String endTime = DateUtils.date2String(DateUtil.endOfDay(DateUtil.offsetDay(new Date(), -Integer.parseInt(period))));
        if (log.isInfoEnabled()) {
            log.info("订单对账明细 - : 自营平台完单时间：【{} - {}】", startTime, endTime);
        }

        // 查询当天完成的所有订单
        List<String> platformCodes = Stream.of(PlatformCodeEnum.SELF.getCode()).collect(Collectors.toList());
        List<RemoteOrderVo> orderVos = remoteOrderService.queryByFinishTimeAndPlatformCodes(startTime, endTime, platformCodes);

        // 获取第三方平台返利周期时间（单位天）
        String platformPeriod = Optional.ofNullable(remoteConfigService.selectValueByKey(OrderConstants.CX_REBATE_PLATFORM_PERIOD)).orElse(OrderConstants.CX_REBATE_PLATFORM_PERIOD_DEFAULT);
        if (log.isInfoEnabled()) {
            log.info("订单对账明细-第三方平台订单返利周期时间：【{}】", platformPeriod);
        }

        startTime = DateUtils.date2String(DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -Integer.parseInt(platformPeriod))));
        endTime = DateUtils.date2String(DateUtil.endOfDay(DateUtil.offsetDay(new Date(), -Integer.parseInt(platformPeriod))));
        if (log.isInfoEnabled()) {
            log.info("订单对账明细 - : 第三方平台完单时间：【{} - {}】", startTime, endTime);
        }

        // 查询当天完成的所有订单
        platformCodes.clear();
        platformCodes = Stream.of(PlatformCodeEnum.MT.getCode(), PlatformCodeEnum.HBK.getCode()).collect(Collectors.toList());
        log.info("订单对账明细 - : platformCodes：【{}】", platformCodes);
        orderVos.addAll(remoteOrderService.queryByFinishTimeAndPlatformCodes(startTime, endTime, platformCodes));

        return orderVos;
    }

    /**
     * 构建默认的账单明细
     *
     * @return 账单明细
     */
    private List<FinBillDetail> builderBillDetail() {
        Date financeDate = DateUtils.getBeginDayOfYesterday();
        List<FinBillDetail> billDetails = new ArrayList<>();

        Map<Long, String> agentId2NameMap = remoteAgentService.getAllAgentInfo()
                .stream().collect(Collectors.toMap(RemoteAgentVo::getId, RemoteAgentVo::getCompanyName, (v1, v2) -> v1));

        List<String> driverStatuses = Stream.of(UserStatusEnum.OK.getCode(), UserStatusEnum.DISABLE.getCode()).toList();
        List<RemoteDriverVo> driverVos = remoteDriverService.queryByDriverStatus(driverStatuses);
        for (RemoteDriverVo driverVo : driverVos) {
            for (PlatformCodeEnum platformCode : PlatformCodeEnum.values()) {
                if (Objects.equals(platformCode.getIsShow(), IsYesEnum.NO.getCode())) {
                    continue;
                }

                FinBillDetail billDetail = builderDefaultBillDetail(platformCode.getCode(), driverVo);
                billDetail.setFinanceDate(financeDate);
                billDetail.setAgentName(agentId2NameMap.get(driverVo.getAgentId()));
                billDetails.add(billDetail);

                // 自营平台多了一个“佣金奖励”类型的账单明细数据
                if (Objects.equals(platformCode.getCode(), PlatformCodeEnum.SELF.getCode())) {
                    FinBillDetail rewardBillDetail = builderDefaultBillDetail(platformCode.getCode(), driverVo);
                    rewardBillDetail.setFinanceType(FinanceTypeEnum.REWARD.getCode());
                    rewardBillDetail.setFinanceDate(financeDate);
                    rewardBillDetail.setAgentName(agentId2NameMap.get(driverVo.getAgentId()));
                    billDetails.add(rewardBillDetail);
                }
            }
        }

        return billDetails;
    }

    /**
     * 构建默认的订单对账明细
     *
     * @param platformCode 平台编码
     * @param driverVo 司机信息
     * @return 默认的订单对账明细
     */
    private FinBillDetail builderDefaultBillDetail(String platformCode, RemoteDriverVo driverVo) {
        FinBillDetail billDetail = new FinBillDetail();
        billDetail.setAgentId(driverVo.getAgentId());
        billDetail.setDriverId(driverVo.getId());
        billDetail.setDriverName(driverVo.getName());
        billDetail.setDriverPhone(driverVo.getPhone());
        billDetail.setPlatformCode(platformCode);
        billDetail.setFinanceType(FinanceTypeEnum.PROFIT.getCode());
        billDetail.setOrderNumber(0L);
        billDetail.setOrderAmount(0L);
        billDetail.setOrderProfit(0L);
        billDetail.setOtherProfit(0L);
        billDetail.setWalletBalance(0L);
        billDetail.setComplainAmount(0L);
        billDetail.setCouponQuotaAmount(0L);

        return billDetail;
    }
}
