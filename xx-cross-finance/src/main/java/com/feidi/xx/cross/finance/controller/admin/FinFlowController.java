package com.feidi.xx.cross.finance.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.domain.bo.FinFlowBo;
import com.feidi.xx.cross.finance.domain.bo.FinFlowQueryBo;
import com.feidi.xx.cross.finance.domain.vo.FinFlowExportVo;
import com.feidi.xx.cross.finance.domain.vo.FinFlowVo;
import com.feidi.xx.cross.finance.service.IFinFlowService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 资金流水
 * 前端访问路由地址为:/finance/flow
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/flow")
public class FinFlowController extends BaseController {

    private final IFinFlowService finFlowService;

    /**
     * 查询资金流水列表
     */
    @SaCheckPermission("finance:flow:list")
    @GetMapping("/list")
    public TableDataInfo<FinFlowVo> list(FinFlowQueryBo bo, PageQuery pageQuery) {
        return finFlowService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出资金流水列表
     */
    @SaCheckPermission("finance:flow:export")
    @Log(title = "资金流水", businessType = BusinessType.EXPORT)
    @Download(name="资金流水",module = ModuleConstants.FINANCE,mode="no")
    @PostMapping("/export")
    public Object export(@RequestBody FinFlowQueryBo bo, HttpServletResponse response) {
        List<FinFlowExportVo> list = finFlowService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(list, "资金流水", FinFlowExportVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取资金流水详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("finance:flow:query")
    @GetMapping("/{id}")
    public R<FinFlowVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(finFlowService.queryById(id));
    }

    /**
     * 新增资金流水
     */
    @SaCheckPermission("finance:flow:add")
    @Log(title = "资金流水", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinFlowBo bo) {
        return toAjax(finFlowService.insertByBo(bo));
    }

    /**
     * 修改资金流水
     */
    @SaCheckPermission("finance:flow:edit")
    @Log(title = "资金流水", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinFlowBo bo) {
        return toAjax(finFlowService.updateByBo(bo));
    }

    /**
     * 删除资金流水
     *
     * @param ids 主键串
     */
    @SaCheckPermission("finance:flow:remove")
    @Log(title = "资金流水", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(finFlowService.deleteWithValidByIds(List.of(ids), true));
    }

}
