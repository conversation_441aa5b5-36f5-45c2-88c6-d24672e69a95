package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.core.enums.DirectionEnum;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.cross.finance.domain.FinFlow;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinFlow.class, convertGenerate = false)
public class FinFlowExportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 流水单号
     */
    @ExcelProperty(value = "流水单号")
    private String flowNo;

    /**
     * 代理商id
     */
    @ExcelProperty(value = "代理商id")
    private Long agentId;

    /**
     * 代理商名称
     */
    @ExcelProperty(value = "代理商名称")
    private String agentName;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机id")
    private Long driverId;

    /**
     * 司机姓名
     */
    @ExcelProperty(value = "司机姓名")
    private String driverName;

    /**
     * 司机手机号
     */
    @ExcelProperty(value = "司机手机号")
    private String driverPhone;

    /**
     * 变动前金额
     */
    @ReverseAutoMapping(target = "beforeAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanBigDecimal(source.getBeforeAmount()))")
    @ExcelProperty(value = "变动前金额")
    private BigDecimal beforeAmount;

    /**
     * 变动金额
     */
    @ReverseAutoMapping(target = "amount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanBigDecimal(source.getAmount()))")
    @ExcelProperty(value = "变动金额")
    private BigDecimal amount;

    /**
     * 变动后金额
     */
    @ReverseAutoMapping(target = "afterAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanBigDecimal(source.getAfterAmount()))")
    @ExcelProperty(value = "变动后金额")
    private BigDecimal afterAmount;

    /**
     * 资金流向
     */
//    @ReverseAutoMapping(target = "direction", expression = "java(com.feidi.xx.common.core.enums.DirectionEnum.getInfoByCode(source.getDirection()))")
    @ExcelProperty(value = "资金流向", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = DirectionEnum.class)
    private String direction;

    /**
     * 流水类型
     */
    @ReverseAutoMapping(target = "type", expression = "java(com.feidi.xx.cross.common.enums.finance.FlowSelectEnum.getInfoByType(source.getType()))")
    @ExcelProperty(value = "流水类型")
    private String type;

    /**
     * 状态
     */
    @ReverseAutoMapping(target = "status", expression = "java(com.feidi.xx.cross.common.enums.finance.FlowStatusEnum.getInfoByType(source.getType()))")
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 发生时间
     */
    @ExcelProperty(value = "发生时间")
    private Date createTime;

    /**
     * 变动时间
     */
    @ExcelProperty(value = "变动时间")
    private Date updateTime;

    /**
     * 关联单号
     */
    // FIXME: 2024/11/5 暂时用一个字段
    @ExcelProperty(value = "关联单号")
    private String joinId;

    /**
     * 关联单号
     */
//    @ExcelProperty(value = "关联单号")
    private String joinNo;

    /**
     * 关联表
     */
    private String joinTable;

    /**
     * 订单金额
     */
    @ExcelProperty(value = "订单金额")
//    @ReverseAutoMapping(target = "orderPrice", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanBigDecimal(source.getOrderPrice()))")
    private BigDecimal orderPrice;

    /**
     * 代理商佣金比例
     */
    @ExcelProperty(value = "代理商佣金比例(%)")
//    @ReverseAutoMapping(target = "agentRate", expression = "java(BigDecimal.valueOf(source.getAgentRate()).toString() + \"%\")")
    private BigDecimal agentRate;

    /**
     * 代理商收益
     */
    @ExcelProperty(value = "代理商收益")
//    @ReverseAutoMapping(target = "agentProfit", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanBigDecimal(source.getAgentProfit()))")
    private BigDecimal agentProfit;

    /**
     * 司机佣金比例
     */
    @ExcelProperty(value = "司机佣金比例(%)")
//    @ReverseAutoMapping(target = "driverRate", expression = "java(BigDecimal.valueOf(source.getDriverRate()).toString() + \"%\")")
    private BigDecimal driverRate;

    /**
     * 司机收益
     */
    @ExcelProperty(value = "司机收益")
//    @ReverseAutoMapping(target = "driverProfit", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanBigDecimal(source.getDriverProfit()))")
    private BigDecimal driverProfit;

    @ExcelProperty(value = "转账对象司机ID")
    private Long targetDriverId;

    @ExcelProperty(value = "转账对象司机姓名")
    private String targetDriverName;

    @ExcelProperty(value = "转账对象司机手机号")
    private String targetDriverPhone;

    @ExcelProperty(value = "打款凭证号")
    private String tradeNo;
}
