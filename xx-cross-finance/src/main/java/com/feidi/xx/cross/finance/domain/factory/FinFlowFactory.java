package com.feidi.xx.cross.finance.domain.factory;

import com.feidi.xx.common.core.enums.DirectionEnum;
import com.feidi.xx.common.core.enums.JoinEnum;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.xx.RandomUtils;
import com.feidi.xx.cross.common.enums.finance.FlowTypeEnum;
import com.feidi.xx.cross.common.enums.order.RebateStatusEnum;
import com.feidi.xx.cross.finance.api.domain.bo.RemoteRebateBo;
import com.feidi.xx.cross.finance.domain.*;
import com.feidi.xx.cross.finance.domain.bo.FinApprovalRequestDetailsBo;
import com.feidi.xx.cross.finance.domain.bo.FinApprovalRequestsBo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 流水创建工厂
 */
public class FinFlowFactory {

    /**
     * 流水创建类型
     */
    @Getter
    @AllArgsConstructor
    public enum FlowCreateType {

        /**
         * 转账
         */
        TRANSFER(JoinEnum.TRANSFER, List.of(FinTransfer.class)),

        /**
         * 订单返利-资金冻结
         */
        FREEZE_ADD(JoinEnum.ORDER, List.of(RemoteRebateBo.class)),

        /**
         * 订单返利-邀请有奖-资金冻结
         */
        INVITE_FREEZE_ADD(JoinEnum.ORDER, List.of(RemoteRebateBo.class)),

        /**
         * 订单返利-订单转卖-资金冻结
         */
        RESELL_FREEZE_ADD(JoinEnum.ORDER, List.of(RemoteRebateBo.class)),

        /**
         * 订单-结算
         */
        REBATE(JoinEnum.ORDER, List.of(RemoteRebateBo.class)),

        /**
         * 订单-邀请有奖
         */
        REBATE_ADD(JoinEnum.ORDER, List.of(RemoteRebateBo.class)),

        /**
         * 客诉
         */
        COMPLAIN(JoinEnum.ORDER, List.of(RemoteRebateBo.class)),

        /**
         * 返利客诉
         */
        INVITE_COMPLAIN(JoinEnum.ORDER, List.of(RemoteRebateBo.class)),

        /**
         * 订单转卖客诉
         */
        RESELL_COMPLAIN(JoinEnum.ORDER, List.of(RemoteRebateBo.class)),

        /**
         * 订单转卖结算
         */
        RESELL_REBATE(JoinEnum.ORDER, List.of(RemoteRebateBo.class)),

        /**
         * 手动调整
         */
        MANUAL(JoinEnum.WALLET, List.of(FinDrvWallet.class, RemoteDriverVo.class, Long.class)),

        /**
         * 提现
         */
        CASH(JoinEnum.CASH, List.of(FinCash.class, RemoteDriverVo.class)),

        /**
         * 人工调账
         */
        ARTIFICIAL_ADJUSTMENT(JoinEnum.ORDER, List.of(FinApprovalRequestsBo.class, FinApprovalRequestDetailsBo.class, FinApprovalRequestRelateOrder.class)),
        ;

        /**
         * 关联表
         */
        private final JoinEnum joinTable;

        /**
         * 入参类型列表
         */
        private final List<Class<?>> classes;
    }

    private FinFlowFactory() {
    }

    /**
     * 创建流水
     *
     * @param preFlow    前一条流水，允许为空，空则创建空流水
     * @param createType 流水创建类型
     * @param direction  流水方向
     * @param flowType   流水类型
     * @param objects    入参
     * @return
     */
    public static FinFlow createFlow(FinFlow preFlow, FlowCreateType createType, DirectionEnum direction, FlowTypeEnum flowType, Object... objects) {
        // 校验参数
        verify(createType, objects);
        // 初始化上一条流水
        if (preFlow == null) {
            preFlow = initFlow();
        }
        FinFlow flow = null;
        switch (createType) {
            case TRANSFER -> {
                flow = createFlowByTransfer(preFlow, direction, (FinTransfer) objects[0]);
            }
            case FREEZE_ADD -> {
                flow = createFlowByFreeze(preFlow, (RemoteRebateBo) objects[0]);
            }
            case INVITE_FREEZE_ADD -> {
                flow = createFlowByInviteFreeze(preFlow, (RemoteRebateBo) objects[0]);
            }
            case RESELL_FREEZE_ADD -> {
                flow = createFlowByResellFreeze(preFlow, (RemoteRebateBo) objects[0]);
            }
            case REBATE -> {
                flow = createFlowByRebate(preFlow, (RemoteRebateBo) objects[0]);
            }
            case REBATE_ADD -> {
                flow = createFlowByRebateAdd(preFlow, (RemoteRebateBo) objects[0]);
            }
            case COMPLAIN -> {
                flow = createFlowByComplain(preFlow, (RemoteRebateBo) objects[0]);
            }
            case INVITE_COMPLAIN -> {
                flow = createFlowByInviteComplain(preFlow, (RemoteRebateBo) objects[0]);
            }
            case RESELL_COMPLAIN -> {
                flow = createFlowByResellComplain(preFlow, (RemoteRebateBo) objects[0]);
            }
            case RESELL_REBATE -> {
                flow = createFlowByResellRebate(preFlow, (RemoteRebateBo) objects[0]);
            }
            case MANUAL -> {
                flow = createFlowByManual(preFlow, direction, (FinDrvWallet) objects[0], (RemoteDriverVo) objects[1], (Long) objects[2]);
            }
            case CASH -> {
                flow = createFlowByCash(preFlow, direction, (FinCash) objects[0], (RemoteDriverVo) objects[1]);
            }
            case ARTIFICIAL_ADJUSTMENT -> {
                flow = createFlowByArtificialAdjustment(preFlow, (FinApprovalRequestsBo) objects[0], (FinApprovalRequestDetailsBo) objects[1], (FinApprovalRequestRelateOrder) objects[2]);
            }
        }
        Assert.notNull(flow, "你还没写流水创建逻辑呢，创建了个寂寞");
        // 基础信息
        flow.setFlowNo(generateNo());
        flow.setPreId(preFlow.getId());
        flow.setDirection(direction.getCode());
        flow.setType(flowType.getCode());
        flow.setJoinTable(createType.getJoinTable().getCode());
        return flow;
    }

    /**
     * 人工调账
     *
     * @param preFlow
     * @param far
     * @param fard
     * @param order
     * @return
     */
    private static FinFlow createFlowByArtificialAdjustment(FinFlow preFlow, FinApprovalRequestsBo far,
                                                            FinApprovalRequestDetailsBo fard, FinApprovalRequestRelateOrder order) {
        FinFlow flow = new FinFlow();
        // 关联信息
        flow.setJoinId(order.getOrderId());
        flow.setJoinNo(order.getOrderNo());

        // 流水处理对象
        flow.setAgentId(fard.getAgentId());
        flow.setDriverId(fard.getDriverId());
        flow.setDriverName(fard.getDriverName());
        flow.setDriverPhone(fard.getDriverPhone());

        // 总金额处理
        flow.setBeforeAmount(preFlow.getAfterAmount());
        flow.setAmount(order.getAdjustAmount());
        flow.setAfterAmount(preFlow.getAfterAmount() + (order.getAdjustAmount()));
        // 可提现金额处理
        flow.setBeforeCash(preFlow.getAfterCash());
        flow.setCash(order.getAdjustAmount());
        flow.setAfterCash(preFlow.getAfterCash() + (order.getAdjustAmount()));

        return flow;
    }

    /**
     * 创建流水号
     */
    public static String generateNo() {
        return "flow" + DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSS) + RandomUtils.randomInt(10, 99);
    }


    private static void verify(FlowCreateType createType, Object... objects) {
        List<Class<?>> classes = createType.getClasses();
        List<Object> params = Arrays.stream(objects).toList();
        Assert.isTrue(classes.size() == objects.length, "传入参数个数需与创建类型定义的类型个数保持一致");
        for (int i = 0; i < classes.size(); i++) {
            Class<?> clazz = classes.get(i);
            Object object = params.get(i);
            Assert.isTrue(clazz == object.getClass(), "传入参数类型需与创建类型定义的类型保持一致");
        }
    }

    /**
     * 订单-资金结算
     *
     * @param preFlow
     * @param remoteRebateBo
     * @return
     */
    private static FinFlow createFlowByRebate(FinFlow preFlow, RemoteRebateBo remoteRebateBo) {
        FinFlow flow = new FinFlow();
        // 关联信息
        flow.setJoinId(remoteRebateBo.getOrderId());
        flow.setJoinNo(remoteRebateBo.getOrderNo());

        // 流水处理对象
        flow.setAgentId(remoteRebateBo.getAgentId());
        flow.setDriverId(remoteRebateBo.getDriverId());
        flow.setDriverName(remoteRebateBo.getDriverName());
        flow.setDriverPhone(remoteRebateBo.getDriverPhone());

        // 总金额处理（总金额保持不变）
        flow.setBeforeAmount(preFlow.getAfterAmount());
        flow.setAmount(0L);
        flow.setAfterAmount(preFlow.getAfterAmount());
        // 可提现金额处理
        flow.setBeforeCash(preFlow.getAfterCash());
        flow.setCash(remoteRebateBo.getDriverProfit());
        flow.setAfterCash(preFlow.getAfterCash() + (remoteRebateBo.getDriverProfit()));
        return flow;
    }

    private static FinFlow createFlowByResellRebate(FinFlow preFlow, RemoteRebateBo remoteRebateBo) {
        FinFlow flow = new FinFlow();
        // 关联信息
        flow.setJoinId(remoteRebateBo.getOrderId());
        flow.setJoinNo(remoteRebateBo.getOrderNo());

        // 流水处理对象
        flow.setAgentId(remoteRebateBo.getResellAgentId());
        flow.setDriverId(remoteRebateBo.getResellDriverId());
        flow.setDriverName(remoteRebateBo.getResellDriverName());
        flow.setDriverPhone(remoteRebateBo.getResellDriverPhone());


        // 总金额处理（总金额保持不变）
        flow.setBeforeAmount(preFlow.getAfterAmount());
        flow.setAmount(0L);
        flow.setAfterAmount(preFlow.getAfterAmount());
        // 可提现金额处理
        flow.setBeforeCash(preFlow.getAfterCash());
        flow.setCash(remoteRebateBo.getResellDriverProfit());
        flow.setAfterCash(preFlow.getAfterCash() + (remoteRebateBo.getResellDriverProfit()));
        return flow;
    }

    /**
     * 订单返利-资金结算
     *
     * @param preFlow
     * @param remoteRebateBo
     * @return
     */
    private static FinFlow createFlowByRebateAdd(FinFlow preFlow, RemoteRebateBo remoteRebateBo) {
        FinFlow flow = new FinFlow();
        // 关联信息
        flow.setJoinId(remoteRebateBo.getOrderId());
        flow.setJoinNo(remoteRebateBo.getOrderNo());

        // 流水处理对象
        flow.setAgentId(remoteRebateBo.getAgentId());
        flow.setDriverId(remoteRebateBo.getDriverId());
        flow.setDriverName(remoteRebateBo.getDriverName());
        flow.setDriverPhone(remoteRebateBo.getDriverPhone());

        // 总金额处理（总金额保持不变）
        flow.setBeforeAmount(preFlow.getAfterAmount());
        flow.setAmount(0L);
        flow.setAfterAmount(preFlow.getAfterAmount());
        // 可提现金额处理
        flow.setBeforeCash(preFlow.getAfterCash());
        flow.setCash(remoteRebateBo.getInviteProfit());
        flow.setAfterCash(preFlow.getAfterCash() + (remoteRebateBo.getDriverProfit()));
        return flow;
    }

    /**
     * 订单返利-资金冻结
     *
     * @param preFlow
     * @param remoteRebateBo
     * @return
     */
    private static FinFlow createFlowByFreeze(FinFlow preFlow, RemoteRebateBo remoteRebateBo) {
        FinFlow flow = new FinFlow();
        // 关联信息
        flow.setJoinId(remoteRebateBo.getOrderId());
        flow.setJoinNo(remoteRebateBo.getOrderNo());

        // 流水处理对象
        flow.setAgentId(remoteRebateBo.getAgentId());
        flow.setDriverId(remoteRebateBo.getDriverId());
        flow.setDriverName(remoteRebateBo.getDriverName());
        flow.setDriverPhone(remoteRebateBo.getDriverPhone());

        // 总金额处理
        flow.setBeforeAmount(preFlow.getAfterAmount());
        flow.setAmount(remoteRebateBo.getDriverProfit());
        flow.setAfterAmount(preFlow.getAfterAmount() + (remoteRebateBo.getDriverProfit()));
        // 可提现金额不变
        keepCashPre(flow, preFlow);
        return flow;
    }

    /**
     * 邀请有奖返利-资金冻结
     *
     * @param preFlow        上一个流水
     * @param remoteRebateBo 返利参数
     * @return 流水
     */
    private static FinFlow createFlowByInviteFreeze(FinFlow preFlow, RemoteRebateBo remoteRebateBo) {
        FinFlow flow = new FinFlow();
        // 关联信息
        flow.setJoinId(remoteRebateBo.getOrderId());
        flow.setJoinNo(remoteRebateBo.getOrderNo());

        // 流水处理对象
        flow.setAgentId(remoteRebateBo.getInviteAgentId());
        flow.setDriverId(remoteRebateBo.getInviteDriverId());
        flow.setDriverName(remoteRebateBo.getDriverName());
        flow.setDriverPhone(remoteRebateBo.getDriverPhone());

        // 总金额处理
        flow.setBeforeAmount(preFlow.getAfterAmount());
        flow.setAmount(remoteRebateBo.getInviteProfit());
        flow.setAfterAmount(preFlow.getAfterAmount() + (remoteRebateBo.getInviteProfit()));
        // 对提现金额进行处理
        flow.setBeforeCash(preFlow.getAfterCash());
        flow.setCash(remoteRebateBo.getInviteProfit());
        flow.setAfterCash(preFlow.getAfterCash() + remoteRebateBo.getInviteProfit());
        return flow;
    }

    /**
     * 订单转卖返利-资金冻结
     *
     * @param preFlow        上一个流水
     * @param remoteRebateBo 返利参数
     * @return 流水
     */
    private static FinFlow createFlowByResellFreeze(FinFlow preFlow, RemoteRebateBo remoteRebateBo) {
        FinFlow flow = new FinFlow();
        // 关联信息
        flow.setJoinId(remoteRebateBo.getOrderId());
        flow.setJoinNo(remoteRebateBo.getOrderNo());

        // 流水处理对象
        flow.setAgentId(remoteRebateBo.getResellAgentId());
        flow.setDriverId(remoteRebateBo.getResellDriverId());
        flow.setDriverName(remoteRebateBo.getResellDriverName());
        flow.setDriverPhone(remoteRebateBo.getResellDriverPhone());

        // 总金额处理
        flow.setBeforeAmount(preFlow.getAfterAmount());
        flow.setAmount(remoteRebateBo.getResellDriverProfit());
        flow.setAfterAmount(preFlow.getAfterAmount() + (remoteRebateBo.getResellDriverProfit()));
        // 可提现金额不变
        keepCashPre(flow, preFlow);
        return flow;
    }

    /**
     * 客诉 - 创建资金流水
     *
     * @param preFlow
     * @param remoteRebateBo
     * @return
     */
    private static FinFlow createFlowByInviteComplain(FinFlow preFlow, RemoteRebateBo remoteRebateBo) {
        FinFlow flow = new FinFlow();
        // 关联信息
        flow.setJoinId(remoteRebateBo.getOrderId());
        flow.setJoinNo(remoteRebateBo.getOrderNo());

        // 流水处理对象
        flow.setAgentId(remoteRebateBo.getInviteAgentId());
        flow.setDriverId(remoteRebateBo.getInviteDriverId());
        flow.setDriverName(remoteRebateBo.getDriverName());
        flow.setDriverPhone(remoteRebateBo.getDriverPhone());

        // 返利中 仅对总金额进行处理
        flow.setBeforeAmount(preFlow.getAfterAmount());
        flow.setAmount(remoteRebateBo.getInviteComplainPrice());
        flow.setAfterAmount(preFlow.getAfterAmount() - remoteRebateBo.getInviteComplainPrice());
        // 对提现金额进行处理
        flow.setBeforeCash(preFlow.getAfterCash());
        flow.setCash(remoteRebateBo.getInviteComplainPrice());
        flow.setAfterCash(preFlow.getAfterCash() - remoteRebateBo.getInviteComplainPrice());
        return flow;
    }

    /**
     * 订单转卖客诉
     *
     * @param preFlow
     * @param remoteRebateBo
     * @return
     */
    private static FinFlow createFlowByResellComplain(FinFlow preFlow, RemoteRebateBo remoteRebateBo) {
        FinFlow flow = new FinFlow();
        // 关联信息
        flow.setJoinId(remoteRebateBo.getOrderId());
        flow.setJoinNo(remoteRebateBo.getOrderNo());

        // 流水处理对象
        flow.setAgentId(remoteRebateBo.getResellAgentId());
        flow.setDriverId(remoteRebateBo.getResellDriverId());
        flow.setDriverName(remoteRebateBo.getResellDriverName());
        flow.setDriverPhone(remoteRebateBo.getResellDriverPhone());

        // 返利中 仅对总金额进行处理
        flow.setBeforeAmount(preFlow.getAfterAmount());
        flow.setAmount(remoteRebateBo.getResellDriverProfit());
        flow.setAfterAmount(preFlow.getAfterAmount() - remoteRebateBo.getResellDriverProfit());
        // 对提现金额进行处理
        if (Objects.equals(remoteRebateBo.getRebateStatus(), RebateStatusEnum.FINISH.getCode())) {
            // 已结算 需要额外对提现金额进行处理
            flow.setBeforeCash(preFlow.getAfterCash());
            flow.setCash(remoteRebateBo.getResellDriverProfit());
            flow.setAfterCash(preFlow.getAfterCash() - remoteRebateBo.getResellDriverProfit());
        } else {
            keepCashPre(flow, preFlow);
        }
        return flow;
    }

    /**
     * 返利 客诉 - 创建资金流水
     *
     * @param preFlow
     * @param remoteRebateBo
     * @return
     */
    private static FinFlow createFlowByComplain(FinFlow preFlow, RemoteRebateBo remoteRebateBo) {
        FinFlow flow = new FinFlow();
        // 关联信息
        flow.setJoinId(remoteRebateBo.getOrderId());
        flow.setJoinNo(remoteRebateBo.getOrderNo());

        // 流水处理对象
        flow.setAgentId(remoteRebateBo.getAgentId());
        flow.setDriverId(remoteRebateBo.getDriverId());
        flow.setDriverName(remoteRebateBo.getDriverName());
        flow.setDriverPhone(remoteRebateBo.getDriverPhone());

        // 返利中 仅对总金额进行处理
        flow.setBeforeAmount(preFlow.getAfterAmount());
        flow.setAmount(remoteRebateBo.getDriverComplainPrice());
        flow.setAfterAmount(preFlow.getAfterAmount() - remoteRebateBo.getDriverComplainPrice());
        if (Objects.equals(remoteRebateBo.getRebateStatus(), RebateStatusEnum.FINISH.getCode())) {
            // 已结算 需要额外对提现金额进行处理
            flow.setBeforeCash(preFlow.getAfterCash());
            flow.setCash(remoteRebateBo.getDriverComplainPrice());
            flow.setAfterCash(preFlow.getAfterCash() - remoteRebateBo.getDriverComplainPrice());
        } else {
            keepCashPre(flow, preFlow);
        }
        return flow;
    }

    private static FinFlow createFlowByManual(FinFlow preFlow, DirectionEnum direction, FinDrvWallet wallet, RemoteDriverVo driverInfo, Long adjustAmount) {
        FinFlow flow = new FinFlow();
        // 关联信息
        flow.setJoinId(wallet.getId());

        // 代理商id
        flow.setAgentId(driverInfo.getAgentId());

        // 司机信息
        flow.setDriverId(driverInfo.getId());
        flow.setDriverName(driverInfo.getName());
        flow.setDriverPhone(driverInfo.getPhone());

        // 金额处理
        changeTogether(flow, preFlow, adjustAmount, direction);
        return flow;
    }

    private static FinFlow createFlowByCash(FinFlow preFlow, DirectionEnum direction, FinCash cash, RemoteDriverVo driverInfo) {
        FinFlow flow = new FinFlow();
        // 关联信息
        flow.setJoinId(cash.getId());
        flow.setJoinNo(cash.getCashNo());
        flow.setRelatedOrder(cash.getRelatedOrder());

        // 代理商id取提现单的
        flow.setAgentId(cash.getAgentId());

        // 司机信息
        flow.setDriverId(driverInfo.getId());
        flow.setDriverName(driverInfo.getName());
        flow.setDriverPhone(driverInfo.getPhone());

        // 金额处理
        changeTogether(flow, preFlow, cash.getAmount(), direction);
        return flow;
    }

    private static FinFlow createFlowByTransfer(FinFlow preFlow, DirectionEnum direction, FinTransfer transfer) {
        FinFlow flow = new FinFlow();
        // 关联信息
        flow.setJoinId(transfer.getId());
        flow.setJoinNo(transfer.getTransferNo());
        flow.setRelatedOrder(transfer.getRelatedOrder());

        // 代理商id
        flow.setAgentId(transfer.getAgentId());

        // 流水处理对象
        if (DirectionEnum.OUT.equals(direction)) {
            flow.setDriverId(transfer.getOutDriverId());
            flow.setDriverName(transfer.getOutDriverName());
            flow.setDriverPhone(transfer.getOutDriverPhone());
        } else if (DirectionEnum.IN.equals(direction)) {
            flow.setDriverId(transfer.getInDriverId());
            flow.setDriverName(transfer.getInDriverName());
            flow.setDriverPhone(transfer.getInDriverPhone());
        }
        // 金额处理
        changeTogether(flow, preFlow, transfer.getAmount(), direction);
        return flow;
    }

    /**
     * 初始化上一条记录
     */
    private static FinFlow initFlow() {
        FinFlow flow = new FinFlow();
        flow.setBeforeAmount(0L);
        flow.setAmount(0L);
        flow.setAfterAmount(0L);
        flow.setBeforeCash(0L);
        flow.setCash(0L);
        flow.setAfterCash(0L);
        return flow;
    }

    /**
     * 保持提现金额跟前一条流水一致
     *
     * @param flow    新流水
     * @param preFlow 前一条流水
     */
    private static void keepCashPre(FinFlow flow, FinFlow preFlow) {
        flow.setCash(0L);
        flow.setBeforeCash(preFlow.getAfterCash());
        flow.setAfterCash(preFlow.getAfterCash());
    }

    /**
     * 金额和提现金额同时变更
     *
     * @param flow
     * @param preFlow
     * @param amount
     * @param direction
     */
    private static void changeTogether(FinFlow flow, FinFlow preFlow, Long amount, DirectionEnum direction) {
        flow.setBeforeCash(preFlow.getAfterCash());
        flow.setBeforeAmount(preFlow.getAfterAmount());
        flow.setAmount(amount);
        flow.setCash(amount);
        if (DirectionEnum.OUT.equals(direction)) {
            flow.setAfterAmount(preFlow.getAfterAmount() - amount);
            flow.setAfterCash(preFlow.getAfterCash() - amount);
        } else if (DirectionEnum.IN.equals(direction)) {
            flow.setAfterAmount(preFlow.getAfterAmount() + amount);
            flow.setAfterCash(preFlow.getAfterCash() + amount);
        }
    }

}
