package com.feidi.xx.cross.finance.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.finance.domain.bo.FinPaymentRecordBo;
import com.feidi.xx.cross.finance.domain.vo.FinPaymentRecordVo;

import java.util.List;

/**
 * 支付记录Service接口
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
public interface IFinPaymentRecordService {

    /**
     * 查询支付记录
     *
     * @param id 主键
     * @return 支付记录
     */
    FinPaymentRecordVo queryById(Long id);

    /**
     * 分页查询支付记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 支付记录分页列表
     */
    TableDataInfo<FinPaymentRecordVo> queryPageList(FinPaymentRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的支付记录列表
     *
     * @param bo 查询条件
     * @return 支付记录列表
     */
    List<FinPaymentRecordVo> queryList(FinPaymentRecordBo bo);

    /**
     * 新增支付记录
     *
     * @param bo 支付记录
     * @return 是否新增成功
     */
    Boolean insertByBo(FinPaymentRecordBo bo);

}
