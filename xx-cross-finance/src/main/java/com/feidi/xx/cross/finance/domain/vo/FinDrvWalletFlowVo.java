package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.finance.domain.FinDrvWalletFlow;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 司机钱包流水视图对象 fin_drv_wallet_flow
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinDrvWalletFlow.class)
public class FinDrvWalletFlowVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 关联单号
     */
    @ExcelProperty(value = "关联单号")
    private String orderNo;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机id")
    private Long driverId;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 佣金收益
     */
    @ExcelProperty(value = "佣金收益")
    private Long orderProfit;

    /**
     * 奖励收益
     */
    @ExcelProperty(value = "奖励收益")
    private Long rewardProfit;

    /**
     * 订单转卖收益
     */
    @ExcelProperty(value = "订单转卖收益")
    private Long resellProfit;

    /**
     * 账户余额(冻结金额+可提现金额)
     */
    @ExcelProperty(value = "账户余额(冻结金额+可提现金额)")
    private Long accountAmount;

    /**
     * 可提现金额
     */
    @ExcelProperty(value = "可提现金额")
    private Long balance;

    /**
     * 冻结金额
     */
    @ExcelProperty(value = "冻结金额")
    private Long freeze;

    /**
     * 累计提现收益
     */
    @ExcelProperty(value = "累计提现收益")
    private Long expend;

    /**
     * 提现中金额
     */
    @ExcelProperty(value = "提现中金额")
    private Long withdrawalAmount;

    /**
     * 创建时间
     */
    private Date createTime;
}
