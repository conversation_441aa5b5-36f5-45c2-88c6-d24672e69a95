package com.feidi.xx.cross.finance.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.finance.domain.FinBillDetail;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 流水明细业务对象 fin_bill_detail
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinBillDetail.class, reverseConvertGenerate = false)
public class FinBillDetailBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 代理商id
     */
    @NotNull(message = "代理商id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long agentId;

    /**
     * 代理商名称
     */
    @NotBlank(message = "代理商名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String agentName;

    /**
     * 司机id
     */
    @NotNull(message = "司机id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long driverId;

    /**
     * 司机姓名
     */
    @NotBlank(message = "司机姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String driverName;

    /**
     * 司机电话
     */
    @NotBlank(message = "司机电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String driverPhone;

    /**
     * 账单周期(精确到日)
     */
    @NotNull(message = "账单周期(精确到日)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date financeDate;

    /**
     * 订单渠道
     */
    @NotBlank(message = "订单渠道不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platformCode;

    /**
     * 账单对账类型[----Type]
     */
    @NotBlank(message = "账单对账类型[----Type]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String financeType;

    /**
     * 总单量
     */
    @NotNull(message = "总单量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderNumber;

    /**
     * 订单总金额
     */
    @NotNull(message = "订单总金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderAmount;

    /**
     * 总收益
     */
    @NotNull(message = "总收益不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderProfit;

    /**
     * 其他收益
     */
    @NotNull(message = "其他收益不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long otherProfit;

    /**
     * 钱包余额
     */
    @NotNull(message = "钱包余额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long walletBalance;

    /**
     * 客诉金额
     */
    @NotNull(message = "客诉金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long complainAmount;

    /**
     * 优惠券使用总金额
     */
    private Long couponQuotaAmount;

    /**
     * 账单开始日期
     */
    private String financeStartTime;

    /**
     * 账单结束日期
     */
    private String financeEndTime;

    /**
     *  创建开始时间
     */
    private String createStartTime;

     /**
     *  创建结束时间
     */
    private String createEndTime;

    /**
     * 相关单号
     */
    private String relatedOrder;

    /**
     * 主键id集合
     */
    private List<Long> ids;
}
