package com.feidi.xx.cross.finance.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 打款记录对象 fin_remit
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("fin_remit")
public class FinRemit extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 订单对账id
     */
    private Long billId;

    /**
     * 打款单号
     */
    private String remitNo;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 打款账户id
     */
    private Long remitAccountId;

    /**
     * 打款账号
     */
    private String remitAccount;

    /**
     * 打款账户姓名
     */
    private String remitAccountName;

    /**
     * 打款账户id
     */
    private Long receiveAccountId;

    /**
     * 打款账号
     */
    private String receiveAccount;

    /**
     * 打款账户姓名
     */
    private String receiveAccountName;

    /**
     * 打款类型[AccountTypeEnum]
     */
    private String accountType;

    /**
     * 账单金额
     */
    private String financeAmount;

    /**
     * 打款金额
     */
    private String remitAmount;

    /**
     * 打款流水号
     */
    private String flowNo;

    /**
     * 交易单号
     */
    private String transNo;

    /**
     * 交易状态
     */
    private String transStatus;

    /**
     * 打款时间
     */
    private Date remitTime;

    /**
     * 打款人id
     */
    private Long remitUserId;

    /**
     * 打款人姓名
     */
    private String remitUserName;

    /**
     * 打款类型[RemitTypeEnum]
     */
    private String remitType;

    /**
     * 审批人id
     */
    private Long auditUserId;

    /**
     * 审批人姓名
     */
    private String auditUserName;

    /**
     * 审批时间
     */
    private Date auditTime;

    /**
     * 账单日期
     */
    private Date financeDate;

    /**
     * 交易时间
     */
    private Date transDate;

    /**
     * 交易信息
     */
    private String transInfo;

    /**
     * 凭证 自动打款保存的是支付订单号，手动打款保存的是打款凭证照片的ossId
     */
    private String voucher;

    /**
     * AppId
     */
    private String appId;

    /**
     * 商户ID|OpenId
     */
    private String mchId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
