package com.feidi.xx.cross.finance.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.common.validate.ReviewGroup;
import com.feidi.xx.cross.finance.domain.bo.FinAccountBo;
import com.feidi.xx.cross.finance.domain.bo.FinCashApplyBo;
import com.feidi.xx.cross.finance.domain.bo.FinCashQueryBo;
import com.feidi.xx.cross.finance.domain.bo.FinCashReviewBo;
import com.feidi.xx.cross.finance.domain.vo.*;
import com.feidi.xx.cross.finance.service.IFinCashService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 提现
 * 前端访问路由地址为:/finance/cash
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@SaCheckRole(UserTypeEnum.UserType.SYS_USER)
@RequestMapping("/cash")
public class FinCashController extends BaseController {

    private final IFinCashService finCashService;

    /**
     * 查询提现列表
     */
    @Enum2TextAspect
    @SaCheckPermission("finance:cash:list")
    @PostMapping("/list")
    public TableDataInfo<FinCashListVo> list(@Validated @RequestBody FinCashQueryBo bo) {
        bo.setUserType(UserTypeEnum.SYS_USER.getUserType());
        return finCashService.queryPageList(bo, bo.buildPageQuery());
    }

    /**
     * 导出提现列表
     */
    @SaCheckPermission("finance:cash:export")
    @Log(title = "提现", businessType = BusinessType.EXPORT)
    @Download(name="提现",module = ModuleConstants.FINANCE,mode="no")
    @PostMapping("/export")
    public Object export(@Validated @RequestBody FinCashQueryBo bo,HttpServletResponse response) {
        bo.setUserType(UserTypeEnum.SYS_USER.getUserType());
        List<FinCashListVo> list = finCashService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        List<FinCashExportVo> convert = MapstructUtils.convert(list, FinCashExportVo.class);
        ExcelUtil.exportExcel(convert, "提现", FinCashExportVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取提现详细信息
     *
     * @param id 主键
     */
    @Enum2TextAspect
    @SaCheckPermission("finance:cash:query")
    @GetMapping("/{id}")
    public R<FinCashVo> getInfo(@NotNull(message = "主键不能为空")
                                @PathVariable Long id) {
        return R.ok(finCashService.queryById(id));
    }

    /**
     * 新增提现
     */
    @SaCheckPermission("finance:cash:add")
    @Log(title = "提现", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinCashApplyBo bo) {
        return toAjax(finCashService.insertByBo(bo));
    }

    /**
     * 提现审核
     */
    @SaCheckPermission("finance:cash:edit")
    @Log(title = "提现", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> review(@Validated(ReviewGroup.class) @RequestBody FinCashReviewBo bo) {
        return toAjax(finCashService.review(bo));
    }

    /**
     * 删除提现
     *
     * @param ids 主键串
     */
    @SaCheckPermission("finance:cash:remove")
    @Log(title = "提现", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(finCashService.deleteWithValidByIds(List.of(ids), true));
    }
}
