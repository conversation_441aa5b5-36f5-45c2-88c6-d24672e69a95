package com.feidi.xx.cross.finance.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.finance.domain.FinBill;
import com.feidi.xx.cross.finance.domain.bo.FinBillBo;
import com.feidi.xx.cross.finance.domain.vo.FinBillVo;

import java.util.Collection;
import java.util.List;

/**
 * 订单对账Service接口
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface IFinBillService {

    /**
     * 查询订单对账
     *
     * @param id 主键
     * @return 订单对账
     */
    FinBillVo queryById(Long id);

    /**
     * 分页查询订单对账列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单对账分页列表
     */
    TableDataInfo<FinBillVo> queryPageList(FinBillBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的订单对账列表
     *
     * @param bo 查询条件
     * @return 订单对账列表
     */
    List<FinBillVo> queryList(FinBillBo bo);

    /**
     * 新增订单对账
     *
     * @param bo 订单对账
     * @return 是否新增成功
     */
    Boolean insertByBo(FinBillBo bo);

    /**
     * 修改订单对账
     *
     * @param bo 订单对账
     * @return 是否修改成功
     */
    Boolean updateByBo(FinBillBo bo);

    /**
     * 校验并批量删除订单对账信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据对账状态查询对账记录
     *
     * @param financeStatus 对账状态
     * @return 对账记录
     */
    List<FinBill> queryByFinanceStatus(String financeStatus);

    /**
     * 确认对账
     *
     * @param id 主键
     * @return 是否确认成功
     */
    Boolean confirm(Long id);

    void orderBill();

}
