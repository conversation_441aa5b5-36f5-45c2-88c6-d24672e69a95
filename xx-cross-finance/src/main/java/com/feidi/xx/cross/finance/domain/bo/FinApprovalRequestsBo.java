package com.feidi.xx.cross.finance.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.finance.domain.FinApprovalRequests;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 账单审批记录业务对象 fin_approval_requests
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinApprovalRequests.class, reverseConvertGenerate = false)
public class FinApprovalRequestsBo extends BaseEntity {

    /**
     * 审批ID
     */
    @NotNull(message = "审批ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 审批名称
     */
    @NotBlank(message = "审批名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 审批状态：0待审批，1已通过，2已驳回，3已撤销
     */
    private Integer status;

    /**
     * 调账金额，可为负，单位分
     */
    private Long amount;

    /**
     * 审核时间，审核通过时间
     */
    private Date approvalTime;

    /**
     * 审批人ID
     */
    private Long approverId;
    /**
     * 审核人姓名
     */
    private String approverName;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 审批按司机分组列表
     */
    @NotEmpty(message = "审批按司机分组列表不能为空", groups = {AddGroup.class, EditGroup.class})
    @Valid
    private List<FinApprovalRequestDetailsBo> details = new ArrayList<>();


    /**
     * 开始创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startCreateTime;
    /**
     * 结束创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endCreateTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startUpdateTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endUpdateTime;

    /**
     * 开始审批时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startApprovalTime;
    /**
     * 结束审批时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endApprovalTime;

    /**
     * 审核备注
     */
    private String reviewNotes;
}
