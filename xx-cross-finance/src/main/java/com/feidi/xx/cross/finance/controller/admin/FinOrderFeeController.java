package com.feidi.xx.cross.finance.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.domain.bo.FinOrderFeeBo;
import com.feidi.xx.cross.finance.domain.vo.FinOrderFeeExportVo;
import com.feidi.xx.cross.finance.domain.vo.FinOrderFeeVo;
import com.feidi.xx.cross.finance.service.IFinOrderFeeService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 订单应收服务费
 * 前端访问路由地址为:/finance/orderFee
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/orderFee")
public class FinOrderFeeController extends BaseController {

    private final IFinOrderFeeService finOrderFeeService;

    /**
     * 查询订单应收服务费列表
     */
    @SaCheckPermission("finance:orderFee:list")
    @GetMapping("/list")
    public TableDataInfo<FinOrderFeeVo> list(FinOrderFeeBo bo, PageQuery pageQuery) {
        return finOrderFeeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出订单应收服务费列表
     */
    @SaCheckPermission("finance:orderFee:export")
    @Log(title = "订单应收服务费", businessType = BusinessType.EXPORT)
    @Download(name="应收服务费",module = ModuleConstants.FINANCE, mode="no")
    @PostMapping("/export")
    public Object export(FinOrderFeeBo bo, HttpServletResponse response) {
        List<FinOrderFeeVo> list = finOrderFeeService.queryList(bo);

        List<FinOrderFeeExportVo> exportVos = MapstructUtils.convert(list, FinOrderFeeExportVo.class);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(exportVos, "应收服务费", FinOrderFeeExportVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取订单应收服务费详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("finance:orderFee:query")
    @GetMapping("/{id}")
    public R<FinOrderFeeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(finOrderFeeService.queryById(id));
    }

    /**
     * 新增订单应收服务费
     */
    @SaCheckPermission("finance:orderFee:add")
    @Log(title = "订单应收服务费", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FinOrderFeeBo bo) {
        return toAjax(finOrderFeeService.insertByBo(bo));
    }

    /**
     * 修改订单应收服务费
     */
    @SaCheckPermission("finance:orderFee:edit")
    @Log(title = "订单应收服务费", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinOrderFeeBo bo) {
        return toAjax(finOrderFeeService.updateByBo(bo));
    }

    /**
     * 删除订单应收服务费
     *
     * @param ids 主键串
     */
    @SaCheckPermission("finance:orderFee:remove")
    @Log(title = "订单应收服务费", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(finOrderFeeService.deleteWithValidByIds(List.of(ids), true));
    }
}
