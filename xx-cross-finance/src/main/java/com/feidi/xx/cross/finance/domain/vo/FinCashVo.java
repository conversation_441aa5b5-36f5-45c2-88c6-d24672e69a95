package com.feidi.xx.cross.finance.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.cross.common.enums.finance.AccountTypeEnum;
import com.feidi.xx.cross.common.enums.finance.CashAuditStatusEnum;
import com.feidi.xx.cross.finance.domain.FinCash;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 提现视图对象 Fin_cash
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinCash.class, convertGenerate = false)
public class FinCashVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 提现单号
     */
    private String cashNo;

    /**
     * 代理商id
     */
    private Long agentId;
    private String agentName;

    /**
     * 司机id
     */
    private Long driverId;
    private String driverName;
    private String driverPhone;

    /**
     * 账户姓名
     */
    private String name;

    /**
     * 账户手机号
     */
    private String phone;

    /**
     * 账号
     */
    private String account;

    /**
     * 类型
     */
    @Enum2Text(enumClass = AccountTypeEnum.class)
    private String accountType;

    /**
     * 是否自动
     */
    @Enum2Text(enumClass = IsYesEnum.class)
    private String isAuto;

    /**
     * 状态
     */
    @Enum2Text(enumClass = CashAuditStatusEnum.class)
    private String status;

    /**
     * 打款流水号
     */
    private String flowNo;

    /**
     * 提现金额
     */
    private Long amount;

    /**
     * 打款金额
     */
    private Long actualAmount;

    /**
     * 凭证ossId
     */
    private String voucher;

    /**
     * 钱包信息
     */
    private FinWalletVo wallet;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 司机类型
     */
    private String driverTypeText;

    /**
     * 司机组类型
     */
    private String groupTypeText;

    /**
     * 支出方式
     */
    private String payAccountTypeText;

    /**
     * 支出商户主体
     */
    private String payAgentName;

    /**
     * 支出商户id
     */
    private String payAppid;
}
