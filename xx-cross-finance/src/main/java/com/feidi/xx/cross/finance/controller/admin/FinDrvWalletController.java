package com.feidi.xx.cross.finance.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.finance.domain.bo.FinDrvWalletBo;
import com.feidi.xx.cross.finance.domain.bo.FinWalletQueryBo;
import com.feidi.xx.cross.finance.domain.vo.FinDrvWalletVo;
import com.feidi.xx.cross.finance.domain.vo.FinWalletExportVo;
import com.feidi.xx.cross.finance.domain.vo.FinWalletListVo;
import com.feidi.xx.cross.finance.service.IFinDrvWalletService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 司机钱包
 * 前端访问路由地址为:/finance/drvWallet
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@SaCheckRole(UserTypeEnum.UserType.SYS_USER)
@RequestMapping("/drvWallet")
public class FinDrvWalletController extends BaseController {

    private final IFinDrvWalletService FinDrvWalletService;

    /**
     * 查询司机钱包列表
     */
    @SaCheckPermission("finance:drvWallet:list")
    @GetMapping("/list")
    public TableDataInfo<FinWalletListVo> list(FinWalletQueryBo bo, PageQuery pageQuery) {
        bo.setUserType(UserTypeEnum.SYS_USER.getUserType());
        return FinDrvWalletService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出司机钱包列表
     */
    @SaCheckPermission("finance:drvWallet:export")
    @Log(title = "司机钱包", businessType = BusinessType.EXPORT)
    @Download(name="司机钱包",module = ModuleConstants.FINANCE,mode="no")
    @PostMapping("/export")
    public Object export(@RequestBody FinWalletQueryBo bo, HttpServletResponse response) {
        bo.setUserType(UserTypeEnum.SYS_USER.getUserType());
        List<FinWalletListVo> list = FinDrvWalletService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        List<FinWalletExportVo> convert = MapstructUtils.convert(list, FinWalletExportVo.class);
        ExcelUtil.exportExcel(convert, "司机钱包", FinWalletExportVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取司机钱包详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("finance:drvWallet:query")
    @GetMapping("/{id}")
    public R<FinDrvWalletVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(FinDrvWalletService.queryById(id));
    }

    /**
     * 调账
     */
    @Log(title = "司机钱包", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FinDrvWalletBo bo) {
        return toAjax(FinDrvWalletService.updateByBo(bo));
    }

    /**
     * 司机钱包禁用/启用
     */
    @RepeatSubmit()
    @GetMapping("/{id}/{status}")
    public R<Void> disableWallet(@PathVariable Long id, @PathVariable String status){
        return toAjax(FinDrvWalletService.disableWallet(id,status));
    }
}
