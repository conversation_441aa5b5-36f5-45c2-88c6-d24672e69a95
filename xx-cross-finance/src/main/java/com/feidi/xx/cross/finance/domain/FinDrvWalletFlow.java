package com.feidi.xx.cross.finance.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 司机钱包流水对象 fin_drv_wallet_flow
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("fin_drv_wallet_flow")
public class FinDrvWalletFlow extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 关联单号
     */
    private String orderNo;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 佣金收益
     */
    private Long orderProfit;

    /**
     * 奖励收益
     */
    private Long rewardProfit;

    /**
     * 订单转卖收益
     */
    private Long resellProfit;

    /**
     * 账户余额(冻结金额+可提现金额)
     */
    private Long accountAmount;

    /**
     * 可提现金额
     */
    private Long balance;

    /**
     * 冻结金额
     */
    private Long freeze;

    /**
     * 累计提现收益
     */
    private Long expend;

    /**
     * 提现中金额
     */
    private Long withdrawalAmount;

    /**
     * 状态[WalletStatusEnum]
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
