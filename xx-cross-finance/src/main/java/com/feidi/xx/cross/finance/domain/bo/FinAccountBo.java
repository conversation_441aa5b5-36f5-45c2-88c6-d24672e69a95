package com.feidi.xx.cross.finance.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.finance.domain.FinAccount;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 商家账户业务对象 fin_account
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinAccount.class, reverseConvertGenerate = false)
public class FinAccountBo extends TenantEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 代理商ID
     */
    @NotNull(message = "代理商ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long agentId;

    /**
     * 代理商名称
     */
    @NotBlank(message = "代理商名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String agentName;

    /**
     * 渠道
     */
    @NotBlank(message = "渠道不能为空", groups = { AddGroup.class, EditGroup.class })
    private String channel;

    /**
     * appId
     */
//    @NotBlank(message = "应用ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String appId;

    /**
     * mchId
     */
    private String mchId;

    /**
     * 进账/出账
     */
    @NotBlank(message = "进账/出账不能为空", groups = { AddGroup.class, EditGroup.class })
    private String direction;

    /**
     * 是否主账号
     */
    @NotBlank(message = "是否主账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String main;

    /**
     * 配置信息
     */
    @NotNull(message = "配置信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private Object config;

    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

}
