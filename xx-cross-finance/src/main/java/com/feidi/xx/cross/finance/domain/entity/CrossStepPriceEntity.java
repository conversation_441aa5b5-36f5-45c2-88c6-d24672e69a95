package com.feidi.xx.cross.finance.domain.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/***
 * 顺风车阶梯价
 * @description <TODO description class purpose>
 * <AUTHOR>
 * @version 1.0.0
 * @create 2024/9/2 10:55
 **/
@Data
public class CrossStepPriceEntity extends StartEndEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 人数对应的价格
     */
    private List<NumPriceEntity> numPrice;

}
