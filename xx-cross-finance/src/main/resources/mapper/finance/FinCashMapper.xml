<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.feidi.xx.cross.finance.mapper.FinCashMapper">
    <select id="listByQuery" resultType="com.feidi.xx.cross.finance.domain.vo.FinCashListVo">
        SELECT c.id, c.cash_no, c.agent_id, c.driver_id,
        c.account_id, c.name, c.phone, c.account, c.account_type, c.amount, c.service_fee, c.is_auto, c.status,
        c.user_id, c.user_name,
        c.flow_no, c.voucher, c.app_id, c.mch_id, c.actual_amount, c.review_time,
        c.trans_date,c.trans_status, c.trans_info, c.remark, c.create_time
        FROM fin_cash c
        <where>
            <if test="bo.unionId != null and bo.unionId != ''">
                AND (
                    c.id = #{bo.unionId} OR c.cash_no = #{bo.unionId} OR c.driver_id = #{bo.unionId}
                )
            </if>
            <choose>
                <when test="bo.agentIds != null and bo.agentIds.size() > 0">
                    AND c.agent_id in
                    <foreach collection="bo.agentIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    <if test="bo.agentId != null and bo.agentId != ''">
                        AND c.agent_id = #{bo.agentId}
                    </if>
                </otherwise>
            </choose>
            <if test="bo.driverId != null">
                AND c.driver_id = #{bo.driverId}
            </if>
            <if test="bo.isAuto != null and bo.isAuto != ''">
                AND c.is_auto = #{bo.isAuto}
            </if>
            <if test="bo.status != null and bo.status != ''">
                AND c.status = #{bo.status}
            </if>
            <if test="bo.transStatus != null and bo.transStatus != ''">
                AND c.trans_status = #{bo.transStatus}
            </if>
            <if test="bo.create != null">
                <if test="bo.create.startTime != null">
                    AND c.create_time &gt;= #{bo.create.startTime}
                </if>
                <if test="bo.create.endTime != null">
                    AND c.create_time &lt;= #{bo.create.endTime}
                </if>
            </if>
            <if test="bo.review != null">
                <if test="bo.review.startTime != null">
                    AND c.review_time &gt;= #{bo.review.startTime}
                </if>
                <if test="bo.review.endTime != null">
                    AND c.review_time &lt;= #{bo.review.endTime}
                </if>
            </if>
        </where>
        ORDER BY c.review_time IS NULL DESC, c.review_time desc, c.create_time ASC
    </select>
</mapper>
