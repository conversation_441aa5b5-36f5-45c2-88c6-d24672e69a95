<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.feidi.xx.cross.finance.mapper.FinApprovalRequestsMapper">

    <select id="selectCountByOrderNoAndStatus" resultType="java.lang.Integer">
        SELECT count(*) FROM fin_approval_requests t1
                          LEFT JOIN fin_approval_request_details t2 on (t1.id=t2.approval_request_id)
        WHERE t1.`status` = #{status}
          and JSON_CONTAINS(t2.relate_orders->'$[*].orderNo', JSON_ARRAY(#{orderNo}))
          and t1.del_flag = '0' and t2.del_flag = '0'
          <if test="excludeId != null">
            and t1.id != #{excludeId}
          </if>
    </select>
</mapper>
