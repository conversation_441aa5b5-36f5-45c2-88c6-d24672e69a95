package com.feidi.xx.cross.common.enums.market;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 拉新类型
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum InviteTypeEnum {

    DRIVER_INVITE("1", "司机拉新"),      // 司机拉新
    AGENT_INVITE("2", "代理商拉新"),
    PASSENGER_INVITE("3", "乘客拉新"),
    ;

    /**
     * 编码
     */
    @EnumValue
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (InviteTypeEnum itemEnum : InviteTypeEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

