package com.feidi.xx.cross.message.dubbo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.common.tenant.helper.TenantHelper;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.utils.order.OrderUtils;
import com.feidi.xx.cross.message.api.RemoteImService;
import com.feidi.xx.cross.message.api.domain.RemoteImMsgInfo;
import com.feidi.xx.cross.message.domain.ImMsg;
import com.feidi.xx.cross.message.domain.ImMsgInfo;
import com.feidi.xx.cross.message.domain.bo.ImMsgInfoBo;
import com.feidi.xx.cross.message.domain.vo.ImMsgVo;
import com.feidi.xx.cross.message.mapper.ImDeviceMapper;
import com.feidi.xx.cross.message.mapper.ImMsgMapper;
import com.feidi.xx.cross.message.service.impl.ImMsgInfoServiceImpl;
import com.feidi.xx.cross.message.service.impl.ImMsgServiceImpl;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDriverVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import com.feidi.xx.push.common.constants.VoiceConstant;
import com.feidi.xx.push.common.enums.PushTypeEnum;
import com.feidi.xx.push.mq.PushEvent;
import com.feidi.xx.push.mq.PushMsgProducer;
import com.feidi.xx.system.api.RemoteDictService;
import com.feidi.xx.system.api.domain.vo.RemoteDictDataVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;

@Slf4j
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteIMServiceImpl implements RemoteImService {

    private final ImDeviceMapper deviceMapper;

    private final ImMsgServiceImpl imMsgService;

    private final ImMsgInfoServiceImpl imMsgInfoService;

    @DubboReference
    private final RemoteOrderService remoteOrderService;

    @DubboReference
    private final RemoteDictService remoteDictService;

    @Override
    public boolean sendMsg2Driver(RemoteImMsgInfo msg) {
        ImMsgInfoBo msgEntity = BeanUtil.copyProperties(msg, ImMsgInfoBo.class);

        RemoteOrderVo detail = null;
        if (msg.getPlatformNo() != null) {
            detail = remoteOrderService.queryByPlatformCodeAndPlatformNo(msg.getPlatformCode(), msg.getPlatformNo());
        } else if (msg.getOrderNo() != null) {
            detail = remoteOrderService.queryByOrderNo(msg.getOrderNo());
        }
        if (detail != null) {
            TenantHelper.setDynamic(detail.getTenantId());
            try {
                msgEntity.setTenantId(detail.getTenantId());
                msgEntity.setOrderId(detail.getId());
                imMsgInfoService.insertByBo(msgEntity);
                // 推送消息
                pushMsg(msgEntity);
            } finally {
                TenantHelper.clearDynamic();
            }
        }
        return true;
    }

    private void pushMsg(ImMsgInfoBo msg) {
        ImMsgVo imMsgVo = imMsgService.queryByOrderId(msg.getOrderId());

        PushTypeEnum pushType = PushTypeEnum.PASSENGER_MESSAGE;
        PushEvent event = new PushEvent(pushType);
        event.setTitle(imMsgVo.getPassengerName());
        ImMsgInfo convert = MapstructUtils.convert(msg, ImMsgInfo.class);
        event.setBody(convert.showContent());
        event.setUrl(pushType.formatUrl(imMsgVo.getOrderId(), imMsgVo.getPassengerName()));
        // 接收人
        event.getReceiverParam().setUserType(UserTypeEnum.DRIVER_USER.getUserType());
        event.getReceiverParam().setUserId(imMsgVo.getDriverId());
        // 语音
        RemoteDictDataVo dataVo = remoteDictService.selectDictDataByTypeAndLabel(VoiceConstant.SYSTEM_VOICE_BROADCAST, pushType.getVoice());
        if (dataVo != null) {
            PushEvent.GtParam gtParam = event.getGtParam();
            gtParam.setPayload(dataVo.getDictValue());
            event.setGtParam(gtParam);
        }
        // 推送
        List<String> cids = getCid(UserTypeEnum.DRIVER_USER.getUserType(), imMsgVo.getDriverId());
        event.setCids(cids);
        PushMsgProducer.sendMessage(event);
    }

    @Override
    public List<String> getCid(String userType, Long... userId) {
        List<String> cid = deviceMapper.listCidByUserIds(userType, userId);
        if (cid.isEmpty()) {
            return Collections.emptyList();
        }
        return cid;
    }

    @Override
    public boolean dispatch(Long orderId, Long driverId) {
        return imMsgService.dispatch(orderId, driverId);
    }

    @Override
    public Integer getUnreadNum(Long orderId) {
        ImMsgVo msgVo = imMsgService.queryByOrderId(orderId);
        if (msgVo != null) {
            if (UserTypeEnum.DRIVER_USER.equals(LoginHelper.getUserType())) {
                return msgVo.getDriverUnreadNum();
            } else if (UserTypeEnum.PASSENGER_USER.equals(LoginHelper.getUserType())) {
                return msgVo.getPassengerUnreadNum();
            }
        }
        return 0;
    }

    @Override
    public Boolean sengMessage(RemoteImMsgInfo msg) {
        Assert.notNull(msg.getOrderId(), "orderId不能为空");
        ImMsgInfoBo msgEntity = BeanUtil.copyProperties(msg, ImMsgInfoBo.class);
        return imMsgInfoService.insertByBo(msgEntity);
    }
}
