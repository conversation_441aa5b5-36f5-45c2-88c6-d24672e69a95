package com.feidi.xx.cross.message.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.message.domain.bo.ImDeviceBo;
import com.feidi.xx.cross.message.domain.vo.ImDeviceVo;

import java.util.Collection;
import java.util.List;

/**
 * IM设备Service接口
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
public interface IImDeviceService {

    /**
     * 查询IM设备
     *
     * @param id 主键
     * @return IM设备
     */
    ImDeviceVo queryById(Long id);

    /**
     * 分页查询IM设备列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return IM设备分页列表
     */
    TableDataInfo<ImDeviceVo> queryPageList(ImDeviceBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的IM设备列表
     *
     * @param bo 查询条件
     * @return IM设备列表
     */
    List<ImDeviceVo> queryList(ImDeviceBo bo);

    /**
     * 新增IM设备
     *
     * @param bo IM设备
     * @return 是否新增成功
     */
    Boolean insertByBo(ImDeviceBo bo);

    /**
     * 校验并批量删除IM设备信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
