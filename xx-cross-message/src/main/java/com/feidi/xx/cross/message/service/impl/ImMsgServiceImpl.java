package com.feidi.xx.cross.message.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.enums.StartEndEnum;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.utils.order.OrderUtils;
import com.feidi.xx.cross.message.domain.ImMsg;
import com.feidi.xx.cross.message.domain.ImMsgInfo;
import com.feidi.xx.cross.message.domain.bo.ImMsgBo;
import com.feidi.xx.cross.message.domain.pojo.imMsg.ImMsgInfoForm;
import com.feidi.xx.cross.message.domain.vo.ImMsgVo;
import com.feidi.xx.cross.message.domain.vo.ImOrderInfo;
import com.feidi.xx.cross.message.mapper.ImMsgInfoMapper;
import com.feidi.xx.cross.message.mapper.ImMsgMapper;
import com.feidi.xx.cross.message.service.IImMsgService;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.RemotePositionService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDetailVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import com.feidi.xx.cross.order.api.domain.vo.RemotePositionVo;
import com.feidi.xx.cross.power.api.RemoteDriverService;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * IM 消息 主Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@RequiredArgsConstructor
@Service
public class ImMsgServiceImpl implements IImMsgService {

    private final ImMsgMapper baseMapper;

    private final ImMsgInfoMapper imMsgInfoMapper;

    @DubboReference
    private final RemoteOrderService remoteOrderService;
    @DubboReference
    private final RemoteDriverService remoteDriverService;
    @DubboReference
    private final RemotePositionService remotePositionService;

    /**
     * 汇总用户未读消息总数
     * @param userTypeEnum
     * @param userId
     * @return
     */
    @Override
    public Integer unreadTotal(UserTypeEnum userTypeEnum, Long userId) {

        if (userTypeEnum.equals(UserTypeEnum.DRIVER_USER)) {
            return baseMapper.driverUnreadTotal(userId);
        } else if (userTypeEnum.equals(UserTypeEnum.PASSENGER_USER)) {
            return baseMapper.passengerUnreadTotal(userId);
        }

        return 0;
    }

    /**
     * 查询IM消息信息
     *
     * @param orderId 订单id
     * @return IM消息信息
     */
    @Override
    public ImOrderInfo queryById(Long orderId) {
        ImOrderInfo orderInfo = new ImOrderInfo();
        RemoteOrderDetailVo detail = remoteOrderService.queryById(orderId);
        if (detail != null) {
            orderInfo.setOrderId(detail.getId());
            orderInfo.setOrderNo(detail.getOrderNo());
            orderInfo.setEarliestTime(detail.getEarliestTime());
            orderInfo.setLatestTime(detail.getLatestTime());
            orderInfo.setStatus(detail.getStatus());
            orderInfo.setVirtualPhone(getVirtualPhone(detail));
            orderInfo.setPassengerRemark(orderInfo.getPassengerRemark());
            // 位置信息
            List<RemotePositionVo> positionVos = remotePositionService.queryByOrderId(orderId);
            if (CollUtil.isNotEmpty(positionVos)) {
                Map<String, RemotePositionVo> map = positionVos.stream().collect(Collectors.toMap(RemotePositionVo::getType, Function.identity()));
                orderInfo.setStartPos(map.get(StartEndEnum.START.getCode()));
                orderInfo.setEndPos(map.get(StartEndEnum.END.getCode()));
            }

            // 更新会话状态
            ImMsgVo msgVo = queryByOrderId(orderId);
            if (msgVo != null && StatusEnum.ENABLE.getCode().equals(msgVo.getStatus())) {
                if (OrderStatusEnum.getConversationOverCodes().contains(detail.getStatus())) {
                    msgEnd(msgVo.getId());
                }
            }

            if (UserTypeEnum.PASSENGER_USER.equals(LoginHelper.getUserType())) {
                orderInfo.setStatusText(OrderStatusEnum.getShowTextPsgByCode(detail.getStatus()));
            } else if (UserTypeEnum.DRIVER_USER.equals(LoginHelper.getUserType())) {
                orderInfo.setStatusText(OrderStatusEnum.getImTextDrvByCode(detail.getStatus()));
            }
        }
        return orderInfo;
    }

    /**
     * 查询 订单 IM 消息 主
     *
     * @param orderId
     * @return IM 消息 主
     */
    @Override
    public ImMsgVo queryByOrderId(Long orderId){

        LambdaQueryWrapper<ImMsg> imMsgLambdaQueryWrapper = new LambdaQueryWrapper<>();
        imMsgLambdaQueryWrapper.eq(ImMsg::getOrderId, orderId);

        return baseMapper.selectVoOne(imMsgLambdaQueryWrapper, false);
    }

    /**
     * 分页查询IM 消息 主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return IM 消息 主分页列表
     */
    @Override
    public TableDataInfo<ImMsgVo> queryPageList(ImMsgBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImMsg> lqw = buildQueryWrapper(bo);
        Page<ImMsgVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<ImMsgVo> vos = result.getRecords();
        //乘客手机号脱敏
        List<Long> orderIds = vos.stream().map(ImMsgVo::getOrderId).toList();
        Map<Long, RemoteOrderVo> orderMap = remoteOrderService.listByIds(orderIds).stream()
                .collect(Collectors.toMap(RemoteOrderVo::getId, Function.identity()));

        List<String> phones = StreamUtils.toList(vos, ImMsgVo::getDriverPhone);
        Map<String, RemoteDriverVo> driverMap = remoteDriverService.listDriverByPhones(phones).stream()
                .collect(Collectors.toMap(RemoteDriverVo::getPhone, Function.identity()));
        vos.forEach(vo -> {
            Long orderId = vo.getOrderId();
            if (UserTypeEnum.PASSENGER_USER.equals(LoginHelper.getUserType())) {
                RemoteDriverVo driverVo = driverMap.get(vo.getDriverPhone());
                if (ObjectUtil.isNotNull(driverVo)) {
                    vo.setDriverName(StrUtil.sub(driverVo.getName(),0 ,1) + "师傅");
                }
            } else if (UserTypeEnum.DRIVER_USER.equals(LoginHelper.getUserType())) {
                RemoteOrderVo orderVo = orderMap.get(orderId);
                if (ObjectUtil.isNotNull(orderVo)) {
                    vo.setPassengerName(OrderUtils.getPassengerName(orderVo.getPassengerPhone()));
                }
            }

            //最后一次会话时间
            ImMsgInfo lastMsg = imMsgInfoMapper.getLastMsg(orderId);
            if (ObjectUtil.isNotNull(lastMsg)) {
                vo.setLastTime(lastMsg.getCreateTime());
                //会话内容
                vo.setLastMsg(lastMsg.showContent());
            }
        });
        //总未读数量
        Integer unreadNum = 0;
        if (UserTypeEnum.PASSENGER_USER.equals(LoginHelper.getUserType())) {
            unreadNum = unreadTotal(UserTypeEnum.PASSENGER_USER, LoginHelper.getUserId());
        } else if (UserTypeEnum.DRIVER_USER.equals(LoginHelper.getUserType())) {
            unreadNum = unreadTotal(UserTypeEnum.DRIVER_USER, LoginHelper.getUserId());
        }
        TableDataInfo<ImMsgVo> tableDataInfo = TableDataInfo.build(vos, result.getTotal());
        tableDataInfo.setExtra(Collections.singletonMap("unreadNum", unreadNum));
        return tableDataInfo;
    }

    /**
     * 查询符合条件的IM 消息 主列表
     *
     * @param bo 查询条件
     * @return IM 消息 主列表
     */
    @Override
    public List<ImMsgVo> queryList(ImMsgBo bo) {
        LambdaQueryWrapper<ImMsg> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImMsg> buildQueryWrapper(ImMsgBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImMsg> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPlatformCode()), ImMsg::getPlatformCode, bo.getPlatformCode());
        lqw.eq(StringUtils.isNotBlank(bo.getPlatformNo()), ImMsg::getPlatformNo, bo.getPlatformNo());
        lqw.eq(StringUtils.isNotBlank(bo.getThirdMsgId()), ImMsg::getThirdMsgId, bo.getThirdMsgId());
        lqw.eq(bo.getAgentId() != null, ImMsg::getAgentId, bo.getAgentId());
        lqw.eq(bo.getDriverId() != null, ImMsg::getDriverId, bo.getDriverId());
        lqw.eq(StringUtils.isNotBlank(bo.getDriverPhone()), ImMsg::getDriverPhone, bo.getDriverPhone());
        lqw.eq(bo.getPassengerId() != null, ImMsg::getPassengerId, bo.getPassengerId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ImMsg::getStatus, bo.getStatus());

        lqw.orderByDesc(ImMsg::getCreateTime);
        return lqw;
    }

    /**
     * 新增IM 消息 主
     *
     * @param bo IM 消息 主
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImMsgInfoForm bo) {

        /// TODO: 1、该订单是否有过消息


//        ImMsg add = MapstructUtils.convert(bo, ImMsg.class);

        /// TODO: 1、订单消息是否存在
        /**
         *  不存在则新增
         *  存在则更新 - 未读数量累加 1
         */
        /// TODO: 2、获取主表ID、图库子表消息体

//        validEntityBeforeSave(add);
//        boolean flag = baseMapper.insert(add) > 0;
//        if (flag) {
////            bo.setId(add.getId());
//        }
        return null;
    }

    /**
     * 修改IM 消息 主
     *
     * @param bo IM 消息 主
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ImMsgBo bo) {
        ImMsg update = MapstructUtils.convert(bo, ImMsg.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ImMsg entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除IM 消息 主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 处理订单消息
     * @param orderId
     * @return
     */
    @Override
    public ImMsgVo handleOrderMsg(Long orderId) {
        return handleOrderMsg(orderId, null);
    }

    /**
     * 处理订单消息
     * @param orderId
     * @param thirdMsgId
     * @return
     */
    @Override
    public ImMsgVo handleOrderMsg(Long orderId, String thirdMsgId) {
        RemoteOrderDetailVo remoteOrderDetailVo = remoteOrderService.queryById(orderId);
        RemoteDriverVo driver = remoteOrderDetailVo != null && ArithUtils.isNotNull(remoteOrderDetailVo.getDriverId()) ?
                remoteDriverService.getDriverByDriverId(remoteOrderDetailVo.getDriverId()) : null;

        ImMsgVo imMsgVo = queryByOrderId(orderId);
        if (imMsgVo == null) {
            ImMsg imMsg = new ImMsg();
            imMsg.setOrderId(orderId);
            imMsg.setThirdMsgId(thirdMsgId);

            if (remoteOrderDetailVo != null) {
                imMsg.setPlatformCode(remoteOrderDetailVo.getPlatformCode());
                imMsg.setPlatformNo(remoteOrderDetailVo.getPlatformNo());
                imMsg.setAgentId(remoteOrderDetailVo.getAgentId());
                imMsg.setDriverId(remoteOrderDetailVo.getDriverId());
                imMsg.setPassengerId(remoteOrderDetailVo.getPassengerId());
                imMsg.setStatus(StatusEnum.ENABLE.getCode());
                if (OrderStatusEnum.getConversationOverCodes().contains(remoteOrderDetailVo.getStatus())) {
                    imMsg.setStatus(StatusEnum.DISABLE.getCode());
                }

                // 司机信息
                if (driver != null) {
                    imMsg.setDriverPhone(driver.getPhone());
                }
            }

            baseMapper.insert(imMsg);
            imMsgVo = MapstructUtils.convert(imMsg, ImMsgVo.class);
        }

        // 更新为禁用
        if (StatusEnum.ENABLE.getCode().equals(imMsgVo.getStatus()) && OrderStatusEnum.getConversationOverCodes().contains(remoteOrderDetailVo.getStatus())) {
            imMsgVo.setStatus(StatusEnum.DISABLE.getCode());
            baseMapper.update(null, Wrappers.<ImMsg>lambdaUpdate()
                    .set(ImMsg::getStatus, StatusEnum.DISABLE.getCode())
                    .eq(ImMsg::getId, imMsgVo.getId()));

        }

        // 名称显示
        if (remoteOrderDetailVo != null) {
            imMsgVo.setPassengerName(OrderUtils.getPassengerName(remoteOrderDetailVo.getPassengerPhone()));
            if (driver != null) {
                imMsgVo.setDriverName(driver.getName());
            }
        }
        return imMsgVo;
    }


    // 虚拟号判断
    private String getVirtualPhone(RemoteOrderDetailVo remoteOrderDetailVo) {
        if (UserTypeEnum.DRIVER_USER.equals(LoginHelper.getUserType())) {
            return StrUtil.isNotBlank(remoteOrderDetailVo.getVirtualPhone()) ? remoteOrderDetailVo.getVirtualPhone() : remoteOrderDetailVo.getPassengerPhone();
        } else if (UserTypeEnum.PASSENGER_USER.equals(LoginHelper.getUserType())) {
            RemoteDriverVo driverInfo = remoteDriverService.getDriverInfo(remoteOrderDetailVo.getDriverId());
            if (driverInfo != null) {
                return StrUtil.isNotBlank(remoteOrderDetailVo.getVirtualPhone()) ? remoteOrderDetailVo.getVirtualPhone() : driverInfo.getPhone();
            }
            return remoteOrderDetailVo.getVirtualPhone();
        }
        throw new ServiceException("错误的用户类型");
    }

    /**
     * 更新未读消息数量
     * @param msgId
     * @param userType 处理谁的未读消息
     * @param unreadNum [0.全部已读；1.未读加1]
     */
    @Override
    public void handleUnread(Long msgId, String userType, int unreadNum) {

        LambdaUpdateWrapper<ImMsg> luw = new LambdaUpdateWrapper<>();
        luw.eq(ImMsg::getId, msgId);

        if (unreadNum == 1) {
            ImMsg imMsg = baseMapper.selectById(msgId);

            if ( userType.equals(UserTypeEnum.DRIVER_USER.getUserType()) ) {
                // 处理司机未读消息数
                unreadNum = imMsg.getDriverUnreadNum() + 1;

            } else if ( userType.equals(UserTypeEnum.PASSENGER_USER.getUserType()) ) {
                // 处理乘客未读消息数
                unreadNum = imMsg.getPassengerUnreadNum() + 1;
            }

        }

        // 更新未读消息数
        if ( userType.equals(UserTypeEnum.DRIVER_USER.getUserType()) ) {
            // 处理司机未读消息数
            luw.set(ImMsg::getDriverUnreadNum, unreadNum);

        } else if ( userType.equals(UserTypeEnum.PASSENGER_USER.getUserType()) ) {
            // 处理乘客未读消息数
            luw.set(ImMsg::getPassengerUnreadNum, unreadNum);
        }
        luw.set(ImMsg::getUpdateTime, DateUtils.getNowDate());
        luw.set(ImMsg::getUpdateBy, LoginHelper.getUserId());
        baseMapper.update(luw);
    }

    /**
     * 结束消息
     * @param msgId
     */
    @Override
    public void msgEnd(Long msgId) {
        LambdaUpdateWrapper<ImMsg> luw = new LambdaUpdateWrapper<>();
        luw.eq(ImMsg::getId, msgId);
        luw.set(ImMsg::getStatus, StatusEnum.DISABLE.getCode());

        baseMapper.update(luw);
    }

    /**
     * 删除im消息信息
     * @param orderId 订单id
     * @return
     */
    @Override
    public Boolean deleteImMsg(Long orderId) {
        boolean flag = baseMapper.delete(Wrappers.<ImMsg>lambdaQuery().eq(ImMsg::getOrderId, orderId)) > 0;
        if (flag){
            return imMsgInfoMapper.delete(Wrappers.<ImMsgInfo>lambdaQuery().eq(ImMsgInfo::getOrderId, orderId)) > 0;
        }
        return false;
    }

    @Override
    public boolean dispatch(Long orderId, Long driverId) {
        ImMsgVo msgVo = queryByOrderId(orderId);
        if (msgVo != null) {
            baseMapper.update(null, Wrappers.<ImMsg>lambdaUpdate()
                    .set(ImMsg::getDriverId, driverId)
                    .set(ImMsg::getUpdateTime, DateUtils.getNowDate())
                    .set(ImMsg::getUpdateBy, LoginHelper.getUserId())
                    .eq(ImMsg::getId, msgVo.getId())
            );
        }
        return true;
    }

}
