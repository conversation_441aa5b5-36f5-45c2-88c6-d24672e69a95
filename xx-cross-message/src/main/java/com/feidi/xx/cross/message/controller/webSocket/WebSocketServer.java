package com.feidi.xx.cross.message.controller.webSocket;


import cn.dev33.satoken.exception.SaTokenException;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.utils.SpringUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.cross.common.enums.message.WebSocketTypeEnum;
import com.feidi.xx.cross.message.api.domain.dto.WebSocketMessageWrapper;
import com.feidi.xx.cross.message.strategy.WebSocketMessageStrategyFactory;
import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName: 开启WebSocket支持
 */
@ServerEndpoint(WebConstants.WEBSOCKET + "/{token}")
@Component
public class WebSocketServer {
    static Log log = LogFactory.get(WebSocketServer.class);

    /**
     * concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象。
     */
    private static ConcurrentHashMap<String, WebSocketServer> webSocketMap = new ConcurrentHashMap<>();
    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;
    private String token = "";

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("token") String token) {
        extracted(session, token);
    }

    private void extracted(Session session, String token) {
        validateWebSocketToken(session, token);
        this.session = session;
        this.token = token;
        if (webSocketMap.containsKey(token)) {
            webSocketMap.remove(token);
            webSocketMap.put(token, this);
            //加入set中
        } else {
            webSocketMap.put(token, this);
            //加入set中
        }
        log.info("用户连接:" + StpUtil.getLoginIdByToken(token) + ",当前在线人数为:" + getOnlineCount());

        try {
            sendMessage("连接成功");
        } catch (IOException e) {
            log.error("用户:" + StpUtil.getLoginIdByToken(token) + ",网络异常!!!!!!");
        }
    }

    private  void validateWebSocketToken(Session session, String token) {
        if (StrUtil.isBlank(token) || token.length() < 10) {
            log.error("请求未携带token， token: {}", token);
        }
        // 校验 Token 是否合法
        StpUtil.setTokenValue(token);
        if (!StpUtil.isLogin()) {
            log.info("请求未携带token， token: {}", token);
            // 未登录，关闭连接
            try {
                session.close();
                throw new SaTokenException("连接失败，无效Token：" + token);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        if (webSocketMap.containsKey(token)) {
            //从set中删除
            webSocketMap.remove(token);
        }
        log.info("用户退出:" + StpUtil.getLoginIdByToken(token) + ",当前在线人数为:" + getOnlineCount());
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        validateWebSocketToken(session, token);
        log.info("用户消息:" + StpUtil.getLoginIdByToken(token) + ",报文:" + message);
        //可以群发消息
        if (!StringUtils.isEmpty(message)) {
            try {
                WebSocketMessageWrapper<?> webSocketMessage = new ObjectMapper().readValue(message, WebSocketMessageWrapper.class);
                if (webSocketMessage.getType().equals(WebSocketTypeEnum.HEARTBEAT.getCode())) {
                    if (!webSocketMap.containsKey(token)) {
                        webSocketMap.put(token, this);
                    }
                }
                WebSocketMessageStrategyFactory strategyFactory = SpringUtils.getBean(WebSocketMessageStrategyFactory.class);
                strategyFactory.getStrategy(webSocketMessage.getType()).handleMessage(webSocketMessage, this);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * @param session
     * @param error
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("用户错误:" + this.token + ",原因:" + error.getMessage());
        error.printStackTrace();
    }

    /**
     * 实现服务器主动推送
     */
    public void sendMessage(String message) throws IOException {
        this.session.getBasicRemote().sendText(message);
    }

    /**
     * 实现服务器主动推送
     */
    public static void sendAllMessage(String message) throws IOException {
        ConcurrentHashMap.KeySetView<String, WebSocketServer> tokens = webSocketMap.keySet();
        for (String token : tokens) {
            WebSocketServer webSocketServer = webSocketMap.get(token);
            webSocketServer.session.getBasicRemote().sendText(message);
            System.out.println("webSocket实现服务器主动推送成功tokens====" + tokens);
        }
    }

    /**
     * 关闭连接
     */
    public static void closeLink(String token) {
        if (webSocketMap.containsKey(token)) {
            webSocketMap.remove(token);
            //从set中删除
        }
        log.info("用户退出:" + token + ",当前在线人数为:" + getOnlineCount());
    }

    /**
     * 发送自定义 WebSocket 消息
     */
    public static <T> void sendInfo(WebSocketMessageWrapper<T> messageWrapper) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String message = objectMapper.writeValueAsString(messageWrapper);
            String userId = messageWrapper.getReceiverId();
            List<String> tokens = StpUtil.getTokenValueListByLoginId(userId);
            for (String token : tokens) {
                if (!StringUtils.isEmpty(message) && webSocketMap.containsKey(token)) {
                    log.info("发送消息到:" + userId + "，报文:" + message);
                    webSocketMap.get(token).sendMessage(message);
                } else {
                    log.error("用户:" + userId + ",不在线！");
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    public static synchronized int getOnlineCount() {

        return webSocketMap.size();
    }
}
