package com.feidi.xx.cross.message.domain.vo;

import com.feidi.xx.cross.message.domain.ImMsgInfo;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * IM消息信息视图对象 im_msg_info
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImMsgInfo.class)
public class ImMsgInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 消息ID
     */
    @ExcelProperty(value = "消息ID")
    private Long msgId;

    /**
     * 第三方消息
     */
    @ExcelProperty(value = "第三方消息")
    private String thirdMsgId;

    /**
     * 消息类型
     */
    @ExcelProperty(value = "消息类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "MsgTypeEnum")
    private String msgType;

    /**
     * 消息内容
     */
    @ExcelProperty(value = "消息内容")
    private String msgContent;

    /**
     * 媒体
     */
    @ExcelProperty(value = "媒体")
    private String mediaId;

    /**
     * 媒体链接
     */
    @ExcelProperty(value = "媒体链接")
    private String mediaUrl;

    /**
     * 坐标
     */
    @ExcelProperty(value = "坐标")
    private String coordinate;

    /**
     * 定位名称
     */
    @ExcelProperty(value = "定位名称")
    private String locateName;

    /**
     * 消息地址
     */
    @ExcelProperty(value = "消息地址")
    private String address;

    /**
     * 发送人
     */
    @ExcelProperty(value = "发送人")
    private Long sendUserId;

    /**
     * 发送人类型
     */
    @ExcelProperty(value = "发送人类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "UserTypeEnum")
    private String sendUserType;

    /**
     * 接收人
     */
    @ExcelProperty(value = "接收人")
    private Long receiveUserId;

    /**
     * 接收人类型
     */
    @ExcelProperty(value = "接收人类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "UserTypeEnum")
    private String receiveUserType;

    /**
     * 是否已读
     */
    @ExcelProperty(value = "是否已读")
    private String unread;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "StatusEnum")
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 是否展示时间
     */
    private Boolean isShowTime = true;

}
