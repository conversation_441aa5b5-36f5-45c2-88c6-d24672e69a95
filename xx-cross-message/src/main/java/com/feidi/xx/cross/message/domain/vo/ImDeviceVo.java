package com.feidi.xx.cross.message.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.message.domain.ImDevice;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * IM设备视图对象 im_device
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImDevice.class)
public class ImDeviceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private String userId;

    /**
     * 用户类型
     */
    @ExcelProperty(value = "用户类型")
    private String userType;

    /**
     * 设备id
     */
    @ExcelProperty(value = "设备id")
    private String deviceId;

    /**
     * 设备类型
     */
    @ExcelProperty(value = "设备类型")
    private String deviceType;

    /**
     * 推送cid
     */
    @ExcelProperty(value = "推送cid")
    private String cid;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String status;


}
