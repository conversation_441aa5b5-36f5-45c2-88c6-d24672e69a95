package com.feidi.xx.cross.message.service;

import com.feidi.xx.cross.message.domain.bo.ImMsgBo;
import com.feidi.xx.cross.message.domain.vo.ImMsgInfoVo;
import com.feidi.xx.cross.message.domain.bo.ImMsgInfoBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * IM消息信息Service接口
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface IImMsgInfoService {

    /**
     * 查询IM消息信息
     *
     * @param id 主键
     * @return IM消息信息
     */
    ImMsgInfoVo queryById(Long id);

    /**
     * 分页查询IM消息信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return IM消息信息分页列表
     */
    TableDataInfo<ImMsgInfoVo> queryPageList(ImMsgInfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的IM消息信息列表
     *
     * @param bo 查询条件
     * @return IM消息信息列表
     */
    List<ImMsgInfoVo> queryList(ImMsgInfoBo bo);

    /**
     * 新增IM消息信息
     *
     * @param bo IM消息信息
     * @return 是否新增成功
     */
    Boolean insertByBo(ImMsgInfoBo bo);

    /**
     * 修改IM消息信息
     * @param bo
     * @return
     */
    Boolean updateByBo(ImMsgInfoBo bo);

    /**
     * 校验并批量删除IM消息信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 异步发送socket消息
     * @param msgId
     * @param receiverUserType
     * @param receiverId
     * @param content
     */
    void asyncSendMessage(Long msgId, String receiverUserType, Long receiverId, String content);
}
