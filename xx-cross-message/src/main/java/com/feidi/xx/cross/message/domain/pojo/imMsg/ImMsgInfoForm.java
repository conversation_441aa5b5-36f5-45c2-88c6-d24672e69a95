package com.feidi.xx.cross.message.domain.pojo.imMsg;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.message.domain.bo.ImMsgInfoBo;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * IM 消息 主对象 im_msg
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Data
@AutoMapper(target = ImMsgInfoBo.class,reverseConvertGenerate = false)
public class ImMsgInfoForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 消息ID
     */
//    @NotNull(message = "消息ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long msgId;

    /**
     * 第三方消息
     */
//    @NotBlank(message = "第三方消息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String thirdMsgId;

    /**
     * 消息类型
     */
    @NotBlank(message = "消息类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String msgType;

    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String msgContent;

    /**
     * 媒体
     */
//    @NotBlank(message = "媒体不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mediaId;

    /**
     * 媒体链接
     */
//    @NotBlank(message = "媒体链接不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mediaUrl;

    /**
     * 坐标
     */
    private String coordinate;

    /**
     * 定位名称
     */
    private String locateName;

    /**
     * 消息地址
     */
    private String address;

    /**
     * 接收人
     */
    private Long receiveUserId;

}
