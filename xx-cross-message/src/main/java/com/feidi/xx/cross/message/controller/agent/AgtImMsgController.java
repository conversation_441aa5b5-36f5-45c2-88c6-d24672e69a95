package com.feidi.xx.cross.message.controller.agent;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.message.domain.bo.ImMsgBo;
import com.feidi.xx.cross.message.domain.bo.ImMsgInfoBo;
import com.feidi.xx.cross.message.domain.pojo.imMsg.ImMsgInfoForm;
import com.feidi.xx.cross.message.domain.vo.ImMsgInfoVo;
import com.feidi.xx.cross.message.domain.vo.ImMsgVo;
import com.feidi.xx.cross.message.service.IImMsgInfoService;
import com.feidi.xx.cross.message.service.IImMsgService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 代理商 - IM消息
 * 前端访问路由地址为:/message/im/msg/info
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/im/msg")
public class AgtImMsgController extends BaseController {

    private final IImMsgService imMsgService;
    private final IImMsgInfoService imMsgInfoService;

    /**
     * 查询IM 消息 主列表
     */
    @GetMapping("/list")
    public TableDataInfo<ImMsgVo> list(ImMsgBo bo, PageQuery pageQuery) {
        bo.setAgentId(LoginHelper.getUserId());
        return imMsgService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询IM消息信息列表
     */
    @GetMapping("/info/{orderId}")
    public TableDataInfo<ImMsgInfoVo> getInfoList(@NotNull(message = "订单ID不能为空")
                                                  @PathVariable Long orderId, PageQuery pageQuery) {

        ImMsgInfoBo bo = new ImMsgInfoBo();
        bo.setOrderId(orderId);
        bo.setUserType(UserTypeEnum.AGENT_USER.getUserType());
        return imMsgInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 司机发送消息 - 敏感词未过滤
     */
    @PostMapping("/sendMsg")
    public R<Void> sendMsg(@Validated(AddGroup.class) @RequestBody ImMsgInfoForm imMsgInfoForm) {

        // 转换BO
        ImMsgInfoBo imMsgInfoBo = MapstructUtils.convert(imMsgInfoForm, ImMsgInfoBo.class);

        // 设置发送人信息
        imMsgInfoBo.setSendUserId(LoginHelper.getUserId());
        imMsgInfoBo.setSendUserType(UserTypeEnum.AGENT_USER.getUserType());

        // 设置接收人信息
        imMsgInfoBo.setReceiveUserType(UserTypeEnum.PASSENGER_USER.getUserType());

        // 插入消息
        imMsgInfoService.insertByBo(imMsgInfoBo);

        return R.ok();
    }
}
