package com.feidi.xx.cross.message.strategy;

import com.feidi.xx.cross.message.controller.webSocket.WebSocketServer;
import com.feidi.xx.cross.message.api.domain.dto.WebSocketMessageWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Slf4j
@RequiredArgsConstructor
public class AnnouncementMessageStrategy implements WebSocketMessageStrategy {
    @Override
    public void handleMessage(WebSocketMessageWrapper<?> message, WebSocketServer server) throws IOException {
        log.info("处理公告消息: " + message);
        WebSocketServer.sendAllMessage("公告：" + message.getData());
    }
}
