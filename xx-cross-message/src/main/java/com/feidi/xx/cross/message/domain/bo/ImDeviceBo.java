package com.feidi.xx.cross.message.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.cross.message.domain.ImDevice;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * IM设备业务对象 im_device
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImDevice.class, reverseConvertGenerate = false)
public class ImDeviceBo extends PageQuery {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = { EditGroup.class })
    private Long userId;

    /**
     * 用户类型 {@link com.feidi.xx.common.core.enums.UserTypeEnum}
     */
    @NotBlank(message = "用户类型不能为空", groups = { EditGroup.class })
    private String userType;

    /**
     * 设备id
     */
    @NotBlank(message = "设备id不能为空", groups = { EditGroup.class })
    private String deviceId;

    /**
     * 设备类型 {@link com.feidi.xx.common.core.enums.DeviceType}
     */
    @NotBlank(message = "设备类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deviceType;

    /**
     * 推送cid
     */
    @NotBlank(message = "推送cid不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cid;

    /**
     * 状态
     */
    private String status;

}
