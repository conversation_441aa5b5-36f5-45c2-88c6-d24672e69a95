package com.feidi.xx.cross.message.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.cross.common.enums.message.MsgTypeEnum;
import com.feidi.xx.cross.message.domain.ImMsg;
import com.feidi.xx.cross.message.domain.ImMsgInfo;
import com.feidi.xx.cross.message.domain.vo.ImMsgInfoVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * IM消息信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface ImMsgInfoMapper extends BaseMapperPlus<ImMsgInfo, ImMsgInfoVo> {

    default ImMsgInfo getLastMsg(Long id) {
        return selectOne(Wrappers.<ImMsgInfo>lambdaQuery()
                .eq(ImMsgInfo::getOrderId, id)
                .ne(ImMsgInfo::getMsgType, MsgTypeEnum.DIY.getCode())
                .orderByDesc(ImMsgInfo::getCreateTime)
                .last("limit 1")
        );
    }

    default List<ImMsgInfo> queryByOrderId(Long orderId) {
        return selectList(Wrappers.<ImMsgInfo>lambdaQuery()
                .eq(ImMsgInfo::getOrderId, orderId)
        );
    }

    /**
     * 获取乘客发的未读的消息
     * @param msgId
     * @return
     */
    default List<ImMsgInfo> listPassengerUnreadMsg(Long msgId) {
        return selectList(Wrappers.<ImMsgInfo>lambdaQuery()
                .eq(ImMsgInfo::getMsgId, msgId)
                .eq(ImMsgInfo::getSendUserType, UserTypeEnum.PASSENGER_USER.getUserType())
                .eq(ImMsgInfo::getUnread, IsYesEnum.NO.getCode())
        );
    }

    /**
     * 是否有未读消息
     * @param orderId
     * @return
     */
    default boolean hasUnread(Long orderId) {
        return exists(Wrappers.<ImMsgInfo>lambdaQuery()
                .eq(ImMsgInfo::getOrderId, orderId)
                .eq(ImMsgInfo::getUnread, IsYesEnum.NO.getCode()));
    }
}
