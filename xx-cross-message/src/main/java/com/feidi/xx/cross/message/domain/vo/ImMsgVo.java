package com.feidi.xx.cross.message.domain.vo;

import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.message.domain.ImMsg;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDetailVo;
import com.feidi.xx.cross.order.api.domain.vo.RemotePositionVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * IM 消息 主视图对象 im_msg
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ImMsg.class)
public class ImMsgVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 平台编码
     */
    @ExcelProperty(value = "平台编码")
    private String platformCode;

    /**
     * 平台单号
     */
    @ExcelProperty(value = "平台单号")
    private String platformNo;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 第三方消息
     */
    @ExcelProperty(value = "第三方消息")
    private String thirdMsgId;

    /**
     * 代理商
     */
    @ExcelProperty(value = "代理商")
    private Long agentId;

    /**
     * 司机
     */
    @ExcelProperty(value = "司机")
    private Long driverId;

    /**
     * 建立会话司机手机号(针对哈啰)
     */
    @ExcelProperty(value = "建立会话司机手机号(针对哈啰)")
    private String driverPhone;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 乘客
     */
    @ExcelProperty(value = "乘客")
    private Long passengerId;

    /**
     * 乘客姓名
     */
    private String passengerName;

    /**
     * AXB虚拟号
     */
    private String virtualPhone;

    /**
     * 司机未读数量
     */
    @ExcelProperty(value = "司机未读数量")
    private Integer driverUnreadNum;

    /**
     * 乘客未读数量
     */
    @ExcelProperty(value = "乘客未读数量")
    private Integer passengerUnreadNum;

    /**
     * 最后一条息
     */
    private String lastMsg;

    /**
     * 最后一次会话时间
     */
    private Date lastTime;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "StatusEnum")
    private String status;

    /**
     * 地址信息
     */
    private AddressInfo addressInfo;

    @Data
    public static class AddressInfo {

        /**
         * 地市
         */
        private String startCity;

        /**
         * 区
         */
        private String startDistrict;

        /**
         * 详细地址
         */
        private String startAddr;

        /**
         * 地市
         */
        private String endCity;

        /**
         * 区
         */
        private String endDistrict;

        /**
         * 详细地址
         */
        private String endAddr;

        public void setStartPos(RemotePositionVo vo) {
            if (vo != null) {
                this.startCity = vo.getCity();
                this.startDistrict = vo.getDistrict();
                this.startAddr = vo.getShortAddr();
            }
        }

        public void setEndPos(RemotePositionVo vo) {
            if (vo != null) {
                this.endCity = vo.getCity();
                this.endDistrict = vo.getDistrict();
                this.endAddr = vo.getShortAddr();
            }
        }

    }
}
