package com.feidi.xx.cross.message.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.message.domain.ImMsg;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * IM 消息 主业务对象 im_msg
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImMsg.class, reverseConvertGenerate = false)
public class ImMsgBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 平台编码
     */
    @NotBlank(message = "平台编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platformCode;

    /**
     * 平台单号
     */
    @NotBlank(message = "平台单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platformNo;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 第三方消息
     */
    @NotBlank(message = "第三方消息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String thirdMsgId;

    /**
     * 代理商
     */
    @NotNull(message = "代理商不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long agentId;

    /**
     * 司机
     */
    @NotNull(message = "司机不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long driverId;

    /**
     * 建立会话司机手机号(针对哈啰)
     */
    @NotBlank(message = "建立会话司机手机号(针对哈啰)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String driverPhone;

    /**
     * 乘客
     */
    @NotNull(message = "乘客不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long passengerId;

    /**
     * 司机未读数量
     */
    private String driverUnreadNum;

    /**
     * 乘客未读数量
     */
    private String passengerUnreadNum;

    /**
     * 状态
     */
    private String status;


}
