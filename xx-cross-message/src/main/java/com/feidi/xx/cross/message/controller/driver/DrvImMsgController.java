package com.feidi.xx.cross.message.controller.driver;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.message.domain.bo.ImMsgBo;
import com.feidi.xx.cross.message.domain.bo.ImMsgInfoBo;
import com.feidi.xx.cross.message.domain.pojo.imMsg.ImMsgInfoForm;
import com.feidi.xx.cross.message.domain.vo.ImMsgInfoVo;
import com.feidi.xx.cross.message.domain.vo.ImMsgVo;
import com.feidi.xx.cross.message.domain.vo.ImOrderInfo;
import com.feidi.xx.cross.message.service.IImMsgInfoService;
import com.feidi.xx.cross.message.service.IImMsgService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 司机 - IM消息
 * 前端访问路由地址为:/message/drv/im/msg
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.DRIVER_ROUTE_PREFIX + "/im/msg")
public class DrvImMsgController extends BaseController {

    private final IImMsgService imMsgService;
    private final IImMsgInfoService imMsgInfoService;

    /**
     * 统计未读数量
     */
    @GetMapping("/unreadTotal")
    public R<Integer> unreadTotal() {
        return R.ok(imMsgService.unreadTotal(UserTypeEnum.DRIVER_USER, LoginHelper.getUserId()));
    }

    /**
     * 查询IM 消息 主列表
     */
    @GetMapping("/list")
    public TableDataInfo<ImMsgVo> list(ImMsgBo bo, PageQuery pageQuery) {
        bo.setDriverId(LoginHelper.getUserId());
        return imMsgService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询IM消息面板信息
     */
    @GetMapping("/info/{orderId}")
    public R<ImOrderInfo> getInfo(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return R.ok(imMsgService.queryById(orderId));
    }

    /**
     * 查询IM消息信息列表
     */
    @GetMapping("/detail/{orderId}")
    public TableDataInfo<ImMsgInfoVo> getDetail(@NotNull(message = "订单ID不能为空")
                                                @PathVariable Long orderId, PageQuery pageQuery) {
        ImMsgInfoBo bo = new ImMsgInfoBo();
        bo.setOrderId(orderId);
        bo.setUserType(UserTypeEnum.DRIVER_USER.getUserType());
        return imMsgInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 司机发送消息 - 敏感词未过滤
     */
    @Log(title = "IM消息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/sendMsg")
    public R<Void> sendMsg(@Validated(AddGroup.class) @RequestBody ImMsgInfoForm imMsgInfoForm) {

        // 转换BO
        ImMsgInfoBo imMsgInfoBo = MapstructUtils.convert(imMsgInfoForm, ImMsgInfoBo.class);

        // 设置发送人信息
        imMsgInfoBo.setSendUserId(LoginHelper.getUserId());
        imMsgInfoBo.setSendUserType(UserTypeEnum.DRIVER_USER.getUserType());

        // 设置接收人信息
        imMsgInfoBo.setReceiveUserType(UserTypeEnum.PASSENGER_USER.getUserType());

        // 插入消息
        imMsgInfoService.insertByBo(imMsgInfoBo);

        return R.ok();
    }

    /**
     * 删除IM 会话消息
     *
     * @param orderId 主键
     */
    @DeleteMapping("/{orderId}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                                                 @PathVariable Long orderId) {
        return toAjax(imMsgService.deleteImMsg(orderId));
    }
}
