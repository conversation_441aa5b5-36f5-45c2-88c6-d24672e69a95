package com.feidi.xx.cross.message.domain.vo;

import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.order.api.domain.vo.RemotePositionVo;
import lombok.Data;

import java.util.Date;

@Data
public class ImOrderInfo {

    /**
     * 主键
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 预计出发时间
     */
    private Date earliestTime;

    /**
     * 预计最晚出发时间
     */
    private Date latestTime;

    /**
     * 订单状态
     */
    private String status;
    private String statusText;

    /**
     * 乘客备注
     */
    private String passengerRemark;

    /**
     * AXB虚拟号
     */
    private String virtualPhone;

    /**
     * 地市
     */
    private String startCity;

    /**
     * 区
     */
    private String startDistrict;

    /**
     * 详细地址
     */
    private String startAddr;

    /**
     * 地市
     */
    private String endCity;

    /**
     * 区
     */
    private String endDistrict;

    /**
     * 详细地址
     */
    private String endAddr;

    public void setStartPos(RemotePositionVo vo) {
        if (vo != null) {
            this.startCity = vo.getCity();
            this.startDistrict = vo.getDistrict();
            this.startAddr = vo.getShortAddr();
        }
    }

    public void setEndPos(RemotePositionVo vo) {
        if (vo != null) {
            this.endCity = vo.getCity();
            this.endDistrict = vo.getDistrict();
            this.endAddr = vo.getShortAddr();
        }
    }


}
