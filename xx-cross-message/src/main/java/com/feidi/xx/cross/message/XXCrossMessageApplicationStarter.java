package com.feidi.xx.cross.message;

import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.cache.order.vo.OrdOrderTrackLocationCacheVo;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.List;

/**
 * IM模块
 */
@EnableAsync
@EnableDubbo
@MapperScan("com.feidi.xx.**.mapper")
@SpringBootApplication(scanBasePackages = {"com.feidi.xx.**"})
public class XXCrossMessageApplicationStarter {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(XXCrossMessageApplicationStarter.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  消息模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }

}
