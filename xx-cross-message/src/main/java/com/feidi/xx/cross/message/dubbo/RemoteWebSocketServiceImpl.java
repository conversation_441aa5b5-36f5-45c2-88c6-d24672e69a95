package com.feidi.xx.cross.message.dubbo;

import com.feidi.xx.cross.message.api.RemoteWebSocketService;
import com.feidi.xx.cross.message.api.domain.dto.WebSocketMessageWrapper;
import com.feidi.xx.cross.message.strategy.WebSocketServiceProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * 乘客优惠券服务实现
 *
 * <AUTHOR>
 * @date 2025/3/14
 */
@Slf4j
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteWebSocketServiceImpl implements RemoteWebSocketService {

    /**
     * 发送消息 订单
     * @param messageWrapper
     */
    @Override
    public void sendMessage(WebSocketMessageWrapper<?> messageWrapper) {
        WebSocketServiceProvider.sendMessage(messageWrapper);
    }

}
