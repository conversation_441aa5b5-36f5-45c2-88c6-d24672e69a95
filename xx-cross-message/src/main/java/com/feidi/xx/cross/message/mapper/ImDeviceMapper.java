package com.feidi.xx.cross.message.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.message.domain.ImDevice;
import com.feidi.xx.cross.message.domain.vo.ImDeviceVo;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * IM设备Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
public interface ImDeviceMapper extends BaseMapperPlus<ImDevice, ImDeviceVo> {

    /**
     * 三个联合查应该是唯一的
     * @param userId
     * @param deviceId
     * @param cid
     * @return
     */
    default List<ImDevice> listByQuery(Long userId, String deviceId, String cid) {
        return selectList(Wrappers.<ImDevice>lambdaQuery()
                .eq(userId != null ,ImDevice::getUserId, userId)
                .eq(deviceId != null ,ImDevice::getDeviceId, deviceId)
                .eq(cid != null ,ImDevice::getCid, cid)
        );
    }

    /**
     * 获取所有cid
     * @param userId
     * @return
     */
    default List<String> listCidByUserId(String userType, Long userId) {
        return selectObjs(Wrappers.<ImDevice>lambdaQuery()
                .select(ImDevice::getCid)
                .eq(userId != null ,ImDevice::getUserId, userId)
        );
    }

    /**
     * 获取所有cid
     * @param userId
     * @param userType
     * @return
     */
    default List<String> listCidByUserIds(String userType, Long... userId) {
        if (userId == null) return Collections.emptyList();
        return selectObjs(Wrappers.<ImDevice>lambdaQuery()
                .select(ImDevice::getCid)
                .in(userId != null, ImDevice::getUserId, Arrays.asList(userId))
                .eq(userType != null, ImDevice::getUserType, userType)
        );
    }

}
