package com.feidi.xx.cross.message.strategy;

import com.feidi.xx.cross.message.api.domain.dto.WebSocketMessageWrapper;
import com.feidi.xx.cross.message.controller.webSocket.WebSocketServer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Component
@Slf4j
@RequiredArgsConstructor
public class CouponMessageStrategy implements WebSocketMessageStrategy {
    @Override
    public void handleMessage(WebSocketMessageWrapper<?> message, WebSocketServer server) {
        log.info("处理优惠券领取成功消息: " + message);
        WebSocketServer.sendInfo(message);
    }
}
