package com.feidi.xx.cross.message.strategy;

import com.feidi.xx.cross.message.controller.webSocket.WebSocketServer;
import com.feidi.xx.cross.message.domain.ImDevice;
import com.feidi.xx.cross.message.api.domain.dto.WebSocketMessageWrapper;
import com.feidi.xx.cross.message.domain.socket.DeviceMessage;
import com.feidi.xx.cross.message.mapper.ImDeviceMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class DeviceMessageStrategy implements WebSocketMessageStrategy {

    private final ImDeviceMapper deviceMapper;

    @Override
    public void handleMessage(WebSocketMessageWrapper<?> message, WebSocketServer server) throws IOException {
        log.info("处理设备消息: " + message);
        DeviceMessage deviceMessage = (DeviceMessage) message.getData();
        List<String> cid = deviceMapper.listCidByUserId(deviceMessage.getSenderUserType(), deviceMessage.getUserId());
        if (cid == null) {
            ImDevice device = new ImDevice();
            device.setCid(deviceMessage.getCid());
            device.setUserType(deviceMessage.getSenderUserType());
            device.setUserId(deviceMessage.getUserId());
            deviceMapper.insert(device);
        }
    }
}
