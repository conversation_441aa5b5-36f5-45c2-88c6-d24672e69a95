package com.feidi.xx.cross.message.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.message.domain.ImDevice;
import com.feidi.xx.cross.message.domain.bo.ImDeviceBo;
import com.feidi.xx.cross.message.domain.vo.ImDeviceVo;
import com.feidi.xx.cross.message.mapper.ImDeviceMapper;
import com.feidi.xx.cross.message.service.IImDeviceService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.feidi.xx.common.core.utils.StringUtils;

import java.util.List;
import java.util.Collection;

/**
 * IM设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@RequiredArgsConstructor
@Service
public class ImDeviceServiceImpl implements IImDeviceService {

    private final ImDeviceMapper baseMapper;

    /**
     * 查询IM设备
     *
     * @param id 主键
     * @return IM设备
     */
    @Override
    public ImDeviceVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询IM设备列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return IM设备分页列表
     */
    @Override
    public TableDataInfo<ImDeviceVo> queryPageList(ImDeviceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ImDevice> lqw = buildQueryWrapper(bo);
        IPage<ImDeviceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的IM设备列表
     *
     * @param bo 查询条件
     * @return IM设备列表
     */
    @Override
    public List<ImDeviceVo> queryList(ImDeviceBo bo) {
        LambdaQueryWrapper<ImDevice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImDevice> buildQueryWrapper(ImDeviceBo bo) {
        LambdaQueryWrapper<ImDevice> lqw = Wrappers.lambdaQuery();
        lqw.eq(ObjectUtil.isNotNull(bo.getUserId()), ImDevice::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getUserType()), ImDevice::getUserType, bo.getUserType());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceId()), ImDevice::getDeviceId, bo.getDeviceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceType()), ImDevice::getDeviceType, bo.getDeviceType());
        lqw.eq(StringUtils.isNotBlank(bo.getCid()), ImDevice::getCid, bo.getCid());
        return lqw;
    }

    /**
     * 新增IM设备
     *
     * @param bo IM设备
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImDeviceBo bo) {
        ImDevice add = MapstructUtils.convert(bo, ImDevice.class);
        boolean validate = validEntityBeforeSave(add);
        if (validate) {
            boolean flag = baseMapper.insert(add) > 0;
            if (flag) {
                bo.setId(add.getId());
            }
            return flag;
        }
        return true;
    }

    /**
     * 保存前的数据校验
     */
    private boolean validEntityBeforeSave(ImDevice entity){
        List<ImDevice> devices = baseMapper.listByQuery(entity.getUserId(), entity.getDeviceId(), entity.getCid());
        if (CollUtil.isNotEmpty(devices)) {
            return false;
        }
        return true;
    }

    /**
     * 校验并批量删除IM设备信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
