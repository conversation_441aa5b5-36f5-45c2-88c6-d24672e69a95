package com.feidi.xx.cross.message.strategy;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.cache.order.vo.OrdOrderTrackLocationCacheVo;
import com.feidi.xx.cross.common.enums.message.WebSocketTypeEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.message.api.domain.dto.WebSocketMessageWrapper;
import com.feidi.xx.cross.message.controller.webSocket.WebSocketServer;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class HeartbeatMessageStrategy implements WebSocketMessageStrategy {

    private final OrdCacheManager ordCacheManager;

    private final RemoteOrderService remoteOrderService;

    @Override
    public void handleMessage(WebSocketMessageWrapper<?> message, WebSocketServer server) {
        log.info("处理心跳消息: " + message);
        if (LoginHelper.getUserType().getUserType().equals(UserTypeEnum.DRIVER_USER.getUserType())) {
            if (ObjectUtils.isNotEmpty(message.getData())) {
                OrdOrderTrackLocationCacheVo orderTrackLocationCacheVo = JSON.parseObject(JSON.toJSONString(message.getData()), OrdOrderTrackLocationCacheVo.class);
                Long userId = LoginHelper.getUserId();
                List<RemoteOrderVo> remoteOrderVos = remoteOrderService.getOrderByDriverId(userId);
                if (ObjectUtils.isNotEmpty(remoteOrderVos)) {
                    for (RemoteOrderVo remoteOrderVo : remoteOrderVos) {
                        orderTrackLocationCacheVo.setOrderId(remoteOrderVo.getId());
                        ordCacheManager.saveOrderTrackLocation(orderTrackLocationCacheVo);
                        if (remoteOrderVo.getPlatformCode().equals(PlatformCodeEnum.SELF.getCode())) {
                            if (ObjectUtils.isNotEmpty(remoteOrderVo.getPassengerId())) {
                                WebSocketMessageWrapper<OrdOrderTrackLocationCacheVo> messageWrapper = new WebSocketMessageWrapper<>();
                                messageWrapper.setType(WebSocketTypeEnum.HEARTBEAT.getCode());
                                messageWrapper.setSenderUserType(UserTypeEnum.DRIVER_USER.getUserType());
                                messageWrapper.setSenderId(String.valueOf(userId));
                                messageWrapper.setReceiverUserType(UserTypeEnum.PASSENGER_USER.getUserType());
                                messageWrapper.setReceiverId(String.valueOf(remoteOrderVo.getPassengerId()));
                                messageWrapper.setData(orderTrackLocationCacheVo);
                                WebSocketServer.sendInfo(messageWrapper);
                            }
                        }
                    }
                }
            }
        }
    }
}
