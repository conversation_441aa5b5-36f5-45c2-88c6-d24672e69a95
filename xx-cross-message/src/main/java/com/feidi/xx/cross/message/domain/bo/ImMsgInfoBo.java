package com.feidi.xx.cross.message.domain.bo;

import cn.hutool.json.JSONObject;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.common.enums.message.MsgTypeEnum;
import com.feidi.xx.cross.message.domain.ImMsgInfo;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.platform.api.mt.domian.bo.RemoteMtMsgSendBo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * IM消息信息业务对象 im_msg_info
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ImMsgInfo.class, reverseConvertGenerate = false)
public class ImMsgInfoBo extends BaseEntity {

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 消息ID
     */
    @NotNull(message = "消息ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long msgId;

    /**
     * 第三方消息
     */
    @NotBlank(message = "第三方消息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String thirdMsgId;

    /**
     * 消息类型
     */
    @NotBlank(message = "消息类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String msgType;

    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String msgContent;

    /**
     * 媒体
     */
    @NotBlank(message = "媒体不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mediaId;

    /**
     * 媒体链接
     */
    @NotBlank(message = "媒体链接不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mediaUrl;

    /**
     * 坐标
     */
    private String coordinate;

    /**
     * 定位名称
     */
    private String locateName;

    /**
     * 消息地址
     */
    private String address;

    /**
     * 发送人
     */
    private Long sendUserId;

    /**
     * 发送人类型
     */
    private String sendUserType;

    /**
     * 接收人
     */
    private Long receiveUserId;

    /**
     * 接收人类型
     */
    private String receiveUserType;

    /**
     * 是否已读
     */
    private String unread;

    /**
     * 状态
     */
    private String status;

    /**
     * 当前用户类型
     */
    private String userType;

}
