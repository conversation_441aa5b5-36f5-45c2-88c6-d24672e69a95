package com.feidi.xx.cross.message.strategy;

import com.feidi.xx.cross.message.api.domain.dto.WebSocketMessageWrapper;
import com.feidi.xx.cross.message.controller.webSocket.WebSocketServer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * websocket服务提供者
 */
@Slf4j
@Service
@AllArgsConstructor
public class WebSocketServiceProvider implements ApplicationContextAware {

    private static WebSocketMessageStrategyFactory wsMessageStrategy;
    private static WebSocketServer webSocketServer;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        webSocketServer = applicationContext.getBean(WebSocketServer.class);
        wsMessageStrategy = applicationContext.getBean(WebSocketMessageStrategyFactory.class);
    }

    /**
     * 发送消息 订单
     * @param messageWrapper
     */
    public static void sendMessage(WebSocketMessageWrapper<?> messageWrapper) {
        try {
            wsMessageStrategy.getStrategy(messageWrapper.getType()).handleMessage(messageWrapper, webSocketServer);
        } catch (IOException e) {
            log.error("发送消息失败：" + e.getMessage());
        }
    }

}
