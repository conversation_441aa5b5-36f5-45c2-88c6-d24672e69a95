package com.feidi.xx.cross.message.strategy;

import com.feidi.xx.cross.message.controller.webSocket.WebSocketServer;
import com.feidi.xx.cross.message.api.domain.dto.WebSocketMessageWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Component
@Slf4j
@RequiredArgsConstructor
public class OrderMessageStrategy implements WebSocketMessageStrategy {
    @Override
    public void handleMessage(WebSocketMessageWrapper<?> message, WebSocketServer server) {
        log.info("处理订单消息: " + message);
        WebSocketServer.sendInfo(message);
    }
}
