package com.feidi.xx.cross.message.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * IM设备对象 im_device
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_device")
public class ImDevice extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 逻辑删除 0存在 2删除
     */
    @TableLogic
    private String delFlag;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户类型 {@link com.feidi.xx.common.core.enums.UserTypeEnum}
     */
    private String userType;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 设备类型 {@link com.feidi.xx.common.core.enums.DeviceType}
     */
    private String deviceType;

    /**
     * 推送cid
     */
    private String cid;

    /**
     * 状态
     */
    private String status;

}
