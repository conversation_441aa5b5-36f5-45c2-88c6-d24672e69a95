package com.feidi.xx.cross.message.domain.socket;

import lombok.*;

import java.io.Serial;
import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatMessage implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 消息类型 99：新消息通知
     */
    private String messageType;

    /**
     * 消息id
     */
    private String msgId;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 当前消息/订单未读数
     */
    private Integer unreadNum;

    /**
     * 消息内容
     */
    private Content content;

    @Getter
    @AllArgsConstructor
    public enum MessageType {

        NEW_MESSAGE_NOTIFY("99", "新消息通知"),

        READ_NOTIFY("11", "已读提醒");

        private final String code;

        private final String value;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Content {

        /**
         * 接收人名称
         */
        private String name;

        /**
         * 消息内容
         */
        private String content;

    }



}
