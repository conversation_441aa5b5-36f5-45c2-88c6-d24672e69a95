package com.feidi.xx.cross.message.domain;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.common.enums.message.MsgTypeEnum;
import com.feidi.xx.cross.platform.api.mt.domian.bo.RemoteMtMsgSendBo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import java.io.Serial;

/**
 * IM消息信息对象 im_msg_info
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("im_msg_info")
public class ImMsgInfo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 消息ID
     */
    private Long msgId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 第三方消息
     */
    private String thirdMsgId;

    /**
     * 消息类型
     */
    private String msgType;

    /**
     * 消息内容
     */
    private String msgContent;

    /**
     * 媒体
     */
    private String mediaId;

    /**
     * 媒体链接
     */
    private String mediaUrl;

    /**
     * 坐标 经度，纬度
     */
    private String coordinate;

    /**
     * 定位名称
     */
    private String locateName;

    /**
     * 消息地址
     */
    private String address;

    /**
     * 发送人
     */
    private Long sendUserId;

    /**
     * 发送人类型
     */
    private String sendUserType;

    /**
     * 接收人
     */
    private Long receiveUserId;

    /**
     * 接收人类型
     */
    private String receiveUserType;

    /**
     * 是否已读
     */
    private String unread;

    /**
     * 状态
     */
    private String status;

    /**
     * 逻辑删除 0存在 2删除
     */
    @TableLogic
    private String delFlag;

    public String showContent() {
        if (msgType != null) {
            switch (MsgTypeEnum.getByCode(msgType)) {
                case TEXT -> {
                    return msgContent;
                }
                case PIC -> {
                    return "[图片]";
                }
                case AUDIO -> {
                    return StrUtil.format("[语音] {}\"", this.getMsgContent());
                }
                case VIDEO -> {
                    return "[视频]";
                }
                case POSITION -> {
                    return StrUtil.format("[位置] {}", this.getLocateName());
                }
                case FILE -> {
                    return StrUtil.format("[文件] {}", this.getMsgContent());
                }
                case DIY -> {
                    return "[系统消息]";
                }
                default -> {
                    return "请点击查看";
                }
            }
        }
        return "请点击查看";
    }


    public RemoteMtMsgSendBo toMtMsg() {
        RemoteMtMsgSendBo bo = new RemoteMtMsgSendBo();
        if (!MsgTypeEnum.TEXT.getCode().equals(this.msgType)) {
            throw new ServiceException("当前对话仅支持文本消息");
        }
        bo.setMsgType(1);
        JSONObject content = new JSONObject();
        content.set("text", this.getMsgContent());
        bo.setMsgContent(content.toString());
        bo.setCreateTime(System.currentTimeMillis());
        // 使用最先接单的司机id
//        bo.setDriverId(sender);
        return bo;
    }

}
