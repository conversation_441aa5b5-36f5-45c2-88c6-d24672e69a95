package com.feidi.xx.cross.message.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.message.domain.ImMsg;
import com.feidi.xx.cross.message.domain.vo.ImMsgVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Select;

/**
 * IM 消息 主Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface ImMsgMapper extends BaseMapperPlus<ImMsg, ImMsgVo> {

    /**
     * 司机未读数量汇总
     * @param driverId
     * @return
     */
    @Select("SELECT sum(`driver_unread_num`) FROM im_msg WHERE driver_id = #{driverId} and del_flag = 0")
    Integer driverUnreadTotal(Long driverId);

    /**
     * 乘客未读数量汇总
     * @param passengerId
     * @return
     */
    @Select("SELECT sum(`passenger_unread_num`) FROM im_msg WHERE passenger_id = #{passengerId} and del_flag = 0")
    Integer passengerUnreadTotal(Long passengerId);

    /**
     * 根据订单ID查询
     * @param orderId
     * @return
     */
    default  ImMsg queryByOrderId(Long orderId){
        return selectOne(Wrappers.<ImMsg>lambdaQuery()
                .eq(ImMsg::getOrderId, orderId));
    }
}
