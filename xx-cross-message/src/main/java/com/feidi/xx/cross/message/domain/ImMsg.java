package com.feidi.xx.cross.message.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import lombok.NoArgsConstructor;

import java.io.Serial;

/**
 * IM 消息 主对象 im_msg
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("im_msg")
public class ImMsg extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 平台单号
     */
    private String platformNo;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 第三方消息
     */
    private String thirdMsgId;

    /**
     * 代理商
     */
    private Long agentId;

    /**
     * 司机
     */
    private Long driverId;

    /**
     * 建立会话司机手机号(针对哈啰)
     */
    private String driverPhone;

    /**
     * 乘客
     */
    private Long passengerId;

    /**
     * 司机未读数量
     */
    private Integer driverUnreadNum;

    /**
     * 乘客未读数量
     */
    private Integer passengerUnreadNum;

    /**
     * 状态
     */
    private String status;

    /**
     * 逻辑删除 0存在 2删除
     */
    @TableLogic
    private String delFlag;

    public ImMsg(RemoteOrderVo detail) {
        this.setTenantId(detail.getTenantId());
        this.orderId = detail.getId();

        this.platformCode = detail.getPlatformCode();
        this.platformNo = detail.getPlatformNo();

        this.agentId = detail.getAgentId();
        this.driverId = detail.getDriverId();
        this.passengerId = detail.getPassengerId();

        if (OrderStatusEnum.getConversationOverCodes().contains(detail.getStatus())) {
            this.status = StatusEnum.DISABLE.getCode();
        } else {
            this.status = StatusEnum.ENABLE.getCode();
        }
    }


}
