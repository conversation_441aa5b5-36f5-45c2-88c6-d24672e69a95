package com.feidi.xx.cross.message.mq.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.feidi.xx.common.rocketmq.base.MessageWrapper;
import com.feidi.xx.common.rocketmq.constants.MQTopicConstants;
import com.feidi.xx.push.mq.PushEvent;
import com.feidi.xx.push.service.PushApiProxy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = MQTopicConstants.TOPIC_PUSH_MSG,
        consumerGroup = MQTopicConstants.TOPIC_PUSH_MSG_CG
)
public class PushMsgConsumer implements RocketMQListener<MessageWrapper<PushEvent>> {

    private final PushApiProxy pushApiProxy;

    @Override
    public void onMessage(MessageWrapper<PushEvent> messageWrapper) {
        log.info("[消费者] 消息推送 - 执行消费逻辑，消息体：{}", JSON.toJSONString(messageWrapper));
        PushEvent message = messageWrapper.getMessage();
        if (CollUtil.isNotEmpty(message.getCids())) {
            pushApiProxy.pushListByCid(message);
        }
        if (StrUtil.isNotBlank(message.getCid())) {
            pushApiProxy.pushToSingleByCid(message);
        }
    }

}