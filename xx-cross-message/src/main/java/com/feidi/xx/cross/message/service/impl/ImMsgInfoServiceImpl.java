package com.feidi.xx.cross.message.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.enums.SuccessFailEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.log.enums.PlatformEnum;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.sensitive.utils.SensitiveUtils;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.enums.message.MsgTypeEnum;
import com.feidi.xx.cross.common.enums.message.WebSocketTypeEnum;
import com.feidi.xx.cross.common.utils.ExceptionUtil;
import com.feidi.xx.cross.common.utils.order.OrderUtils;
import com.feidi.xx.cross.message.api.domain.dto.WebSocketMessageWrapper;
import com.feidi.xx.cross.message.domain.ImMsg;
import com.feidi.xx.cross.message.domain.socket.ChatMessage;
import com.feidi.xx.cross.message.domain.vo.ImMsgVo;
import com.feidi.xx.cross.message.mapper.ImMsgMapper;
import com.feidi.xx.cross.message.service.IImMsgService;
import com.feidi.xx.cross.message.strategy.WebSocketServiceProvider;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDetailVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDriverVo;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerVo;
import com.feidi.xx.cross.platform.api.hbk.domain.hbk.vo.RemotePlatformApiResponseVo;
import com.feidi.xx.cross.platform.api.mt.RemoteMtImService;
import com.feidi.xx.cross.platform.api.mt.domian.bo.RemoteMtMsgReadBo;
import com.feidi.xx.cross.platform.api.mt.domian.bo.RemoteMtMsgSendBo;
import com.feidi.xx.cross.power.api.RemoteDriverService;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import com.feidi.xx.cross.message.domain.bo.ImMsgInfoBo;
import com.feidi.xx.cross.message.domain.vo.ImMsgInfoVo;
import com.feidi.xx.cross.message.domain.ImMsgInfo;
import com.feidi.xx.cross.message.mapper.ImMsgInfoMapper;
import com.feidi.xx.cross.message.service.IImMsgInfoService;
import com.feidi.xx.common.core.utils.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * IM消息信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImMsgInfoServiceImpl implements IImMsgInfoService {

    private final ImMsgMapper msgMapper;

    private final ImMsgInfoMapper baseMapper;

    private final IImMsgService imMsgService;

    private final ScheduledExecutorService scheduledExecutorService;

    private final OrdCacheManager ordCacheManager;

    @DubboReference
    private final RemoteOrderService remoteOrderService;

    @DubboReference
    private final RemoteMtImService remoteMtImService;

    @DubboReference
    private final RemoteDriverService remoteDriverService;

    @DubboReference
    private final RemotePassengerService remotePassengerService;




    /**
     * 查询IM消息信息
     *
     * @param id 主键
     * @return IM消息信息
     */
    @Override
    public ImMsgInfoVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询IM消息信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return IM消息信息分页列表
     */
    @Override
    public TableDataInfo<ImMsgInfoVo> queryPageList(ImMsgInfoBo bo, PageQuery pageQuery) {
        //未读数量清空
        if (bo.getOrderId() != null){
            resetUnread(bo.getOrderId(), bo.getUserType());
            asyncSendReadMessage(bo.getOrderId());
        }
        LambdaQueryWrapper<ImMsgInfo> lqw = buildQueryWrapper(bo);
        Page<ImMsgInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }


    /**
     * 未读数量清空
     * @param orderId
     */
    private void resetUnread(Long orderId, String userType) {
        if (UserTypeEnum.DRIVER_USER.getUserType().equals(userType)) {
            msgMapper.update(new LambdaUpdateWrapper<ImMsg>()
                    .eq(ImMsg::getOrderId, orderId)
                    .set(ImMsg::getDriverUnreadNum, 0));
        } else if (UserTypeEnum.PASSENGER_USER.getUserType().equals(userType)) {
            msgMapper.update(new LambdaUpdateWrapper<ImMsg>()
                    .eq(ImMsg::getOrderId, orderId)
                    .set(ImMsg::getPassengerUnreadNum, 0));
        }
    }

    /**
     * 已读未读处理
     * @param orderId
     */
    private void handlerUnread(Long orderId, String userType) {
        if (UserTypeEnum.DRIVER_USER.getUserType().equals(userType)) {
            // 已读第三方消息
            readPlatformMsg(orderId);
            // 再更新为已读
            baseMapper.update(new LambdaUpdateWrapper<ImMsgInfo>()
                    .eq(ImMsgInfo::getOrderId, orderId)
                    .nested(l -> l.ne(ImMsgInfo::getSendUserType, userType).or().isNull(ImMsgInfo::getSendUserType))
                    .eq(ImMsgInfo::getUnread, IsYesEnum.NO.getCode())
                    .set(ImMsgInfo::getUnread, IsYesEnum.YES.getCode()));
        } else if (UserTypeEnum.PASSENGER_USER.getUserType().equals(userType)) {
            baseMapper.update(new LambdaUpdateWrapper<ImMsgInfo>()
                    .eq(ImMsgInfo::getOrderId, orderId)
                    .nested(l -> l.ne(ImMsgInfo::getSendUserType, userType).or().isNull(ImMsgInfo::getSendUserType))
                    .eq(ImMsgInfo::getUnread, IsYesEnum.NO.getCode())
                    .set(ImMsgInfo::getUnread, IsYesEnum.YES.getCode()));
        }
    }

    /**
     * 查询符合条件的IM消息信息列表
     *
     * @param bo 查询条件
     * @return IM消息信息列表
     */
    @Override
    public List<ImMsgInfoVo> queryList(ImMsgInfoBo bo) {
        LambdaQueryWrapper<ImMsgInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ImMsgInfo> buildQueryWrapper(ImMsgInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ImMsgInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getMsgId() != null, ImMsgInfo::getMsgId, bo.getMsgId());
        lqw.eq(bo.getOrderId() != null, ImMsgInfo::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getThirdMsgId()), ImMsgInfo::getThirdMsgId, bo.getThirdMsgId());
        lqw.eq(StringUtils.isNotBlank(bo.getMsgType()), ImMsgInfo::getMsgType, bo.getMsgType());
        lqw.eq(StringUtils.isNotBlank(bo.getCoordinate()), ImMsgInfo::getCoordinate, bo.getCoordinate());
        lqw.eq(bo.getSendUserId() != null, ImMsgInfo::getSendUserId, bo.getSendUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getSendUserType()), ImMsgInfo::getSendUserType, bo.getSendUserType());
        lqw.eq(bo.getReceiveUserId() != null, ImMsgInfo::getReceiveUserId, bo.getReceiveUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getReceiveUserType()), ImMsgInfo::getReceiveUserType, bo.getReceiveUserType());
        lqw.eq(StringUtils.isNotBlank(bo.getUnread()), ImMsgInfo::getUnread, bo.getUnread());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ImMsgInfo::getStatus, bo.getStatus());
        lqw.orderByAsc(ImMsgInfo::getCreateTime);
        return lqw;
    }

    /**
     * 新增IM消息信息
     *
     * @param bo IM消息信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ImMsgInfoBo bo) {
        boolean isThirdMsg = ObjectUtil.isNotNull(bo.getThirdMsgId());
        if (!isThirdMsg) {
            validateBeforeSend(bo);
        }

        // 消息体
        ImMsgVo imMsgVo = imMsgService.handleOrderMsg(bo.getOrderId(), bo.getThirdMsgId());

        if (!isThirdMsg && imMsgVo.getStatus().equals(StatusEnum.DISABLE.getCode())) {
            throw new ServiceException("订单对话已关闭");
        }

        if (UserTypeEnum.DRIVER_USER.getUserType().equals(bo.getSendUserType())) {
            if (!imMsgVo.getDriverId().equals(LoginHelper.getUserId())) {
                throw new ServiceException("发送失败，会话已结束");
            }
        }

        // 消息内容保存
        ImMsgInfo add = MapstructUtils.convert(bo, ImMsgInfo.class);

        add.setMsgId(imMsgVo.getId());
        add.setUnread(IsYesEnum.NO.getCode());
        add.setStatus(StatusEnum.ENABLE.getCode());

        if (ObjectUtil.isEmpty(add.getReceiveUserId())) {
            if (UserTypeEnum.PASSENGER_USER.getUserType().equals(bo.getSendUserType())) {
                add.setReceiveUserId(imMsgVo.getDriverId());
            } else if (UserTypeEnum.DRIVER_USER.getUserType().equals(bo.getSendUserType())) {
                add.setReceiveUserId(imMsgVo.getPassengerId());
            }
        }

        // 发送给第三方 期间更新发送状态
        if (UserTypeEnum.DRIVER_USER.getUserType().equals(bo.getSendUserType())) {
            sendToPlatform(add, imMsgVo);
        }

        boolean flag = baseMapper.insert(add) > 0;

        if (flag) {
            /// 未读消息累加
            imMsgService.handleUnread(imMsgVo.getId(), add.getReceiveUserType(), 1);

            /// TODO 发送 推送 消息 【司机、乘客】
            asyncSendMessage(add.getMsgId(), add.getReceiveUserType(), add.getReceiveUserId(), add.showContent());
            // 已读
            asyncSendReadMessage(bo.getOrderId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(ImMsgInfoBo bo) {
        ImMsgInfo update = MapstructUtils.convert(bo, ImMsgInfo.class);
        return baseMapper.updateById(update) > 0;
    }


    /**
     * 校验并批量删除IM消息信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    public void asyncSendReadMessage(Long orderId) {
        if (!baseMapper.hasUnread(orderId)) {
            return;
        }

        UserTypeEnum userType = LoginHelper.getUserType();
        handlerUnread(orderId, userType.getUserType());

        // 延迟一下防止数据库还没更新完消息已经被消费了
        scheduledExecutorService.schedule(() -> {
            log.info("发送已读消息: {}", orderId);
            //发送websocket，通知司机订单状态变更
            WebSocketMessageWrapper<ChatMessage> messageWrapper = new WebSocketMessageWrapper<>();

            ChatMessage message = new ChatMessage();
            messageWrapper.setType(WebSocketTypeEnum.CHAT.getCode());
            message.setMessageType(ChatMessage.MessageType.READ_NOTIFY.getCode());
            message.setOrderId(String.valueOf(orderId));
            ImMsgVo msg = imMsgService.queryByOrderId(orderId);
            if (msg != null) {
                message.setMsgId(String.valueOf(msg.getId()));
                if (UserTypeEnum.DRIVER_USER.equals(userType)) {
                    messageWrapper.setReceiverUserType(UserTypeEnum.PASSENGER_USER.getUserType());
                    messageWrapper.setReceiverId(String.valueOf(msg.getPassengerId()));
                } else if (UserTypeEnum.PASSENGER_USER.equals(userType)) {
                    messageWrapper.setReceiverUserType(UserTypeEnum.DRIVER_USER.getUserType());
                    messageWrapper.setReceiverId(String.valueOf(msg.getDriverId()));
                }
            }
            messageWrapper.setData(message);
            WebSocketServiceProvider.sendMessage(messageWrapper);
        }, 150, TimeUnit.MILLISECONDS);
    }


    /**
     * 发送websocket消息，通知司机订单状态变更
     *
     * @param msgId 会话id
     * @param receiverUserType 接收者用户类型
     * @param receiverId 接收者id
     */
    @Override
    public void asyncSendMessage(Long msgId, String receiverUserType, Long receiverId, String content) {
        scheduledExecutorService.schedule(() -> {
            log.info("发送消息: msgId: {}, 用户：{}-{}, 内容：{}", msgId, receiverUserType, receiverId, content);
            //发送websocket，通知司机订单状态变更
            WebSocketMessageWrapper<ChatMessage> messageWrapper = new WebSocketMessageWrapper<>();

            ChatMessage message = new ChatMessage();
            message.setMessageType(ChatMessage.MessageType.NEW_MESSAGE_NOTIFY.getCode());
            message.setMsgId(String.valueOf(msgId));
            ImMsg msg = msgMapper.selectById(msgId);
            ChatMessage.Content cont = new ChatMessage.Content();
            cont.setContent(content);
            if (msg != null) {
                message.setOrderId(String.valueOf(msg.getOrderId()));
                if (UserTypeEnum.DRIVER_USER.getUserType().equals(receiverUserType)) {
                    message.setUnreadNum(msg.getDriverUnreadNum());
                    cont.setName("乘客");
                    ExceptionUtil.ignoreEx(() -> {
                        RemoteOrderDetailVo remoteOrderDetailVo = remoteOrderService.queryById(msg.getOrderId());
                        cont.setName(OrderUtils.getPassengerName(remoteOrderDetailVo.getPassengerPhone()));
                    });
                } else if (UserTypeEnum.PASSENGER_USER.getUserType().equals(receiverUserType)) {
                    message.setUnreadNum(msg.getPassengerUnreadNum());
                    cont.setName("师傅");
                    ExceptionUtil.ignoreEx(() -> {
                        RemoteOrderDriverVo dispatchDriver = ordCacheManager.getOrderDispatchDriverInfoByOrderId(msg.getOrderId());
                        if (dispatchDriver != null) {
                            cont.setName(StrUtil.sub(dispatchDriver.getDriverName(), 0, 1) + "师傅");
                        }
                    });
                }
            }
            message.setContent(cont);
            messageWrapper.setType(WebSocketTypeEnum.CHAT.getCode());
            messageWrapper.setReceiverUserType(receiverUserType);
            messageWrapper.setReceiverId(String.valueOf(receiverId));
            messageWrapper.setData(message);
            WebSocketServiceProvider.sendMessage(messageWrapper);
        }, 100, TimeUnit.MILLISECONDS);
    }

    private static void validateBeforeSend(ImMsgInfoBo bo) {
        switch (MsgTypeEnum.getByCode(bo.getMsgType())) {
            case TEXT -> {
                Assert.isTrue(StrUtil.isNotBlank(bo.getMsgContent()), "不支持发送空消息");
                Assert.isTrue(bo.getMsgContent().length() <= 255, "最多发送255个字");
                // 敏感词校验
                Assert.isFalse(SensitiveUtils.isSensitive(bo.getMsgContent()), "消息包含违规内容，发送失败");
            }
            case POSITION -> {
                Assert.isTrue(bo.getAddress() != null, "请重新选择地图位置");
                Assert.isTrue(bo.getCoordinate() != null, "请重新选择地图位置");
                Assert.isTrue(bo.getLocateName() != null, "请重新选择地图位置");
            }
            case PIC, AUDIO, VIDEO, FILE -> {
                Assert.isTrue(bo.getMediaUrl() != null, "发送失败，请重试");
            }
            case DIY -> {
                Assert.isTrue(StrUtil.isNotBlank(bo.getMsgContent()), "消息内容不能为空");
            }
            default -> {
                throw new ServiceException("不支持的消息类型");
            }
        }
    }

    private void sendToPlatform(ImMsgInfo bo, ImMsgVo imMsg) {
        if (PlatformEnum.SELF.getCode().equals(imMsg.getPlatformCode())) {
            return;
        }
        try {
            // 默认发送失败
            bo.setStatus(StatusEnum.DISABLE.getCode());
            if (PlatformEnum.MT.getCode().equals(imMsg.getPlatformCode())) {
                RemoteMtMsgSendBo mtMsg = bo.toMtMsg();
                mtMsg.setMsgId(RandomUtil.randomNumbers(22));
                mtMsg.setPlatformNo(imMsg.getPlatformNo());
                // 使用接单的司机id
                RemoteOrderDriverVo disDriver = ordCacheManager.getOrderDispatchDriverInfoByOrderId(bo.getOrderId());
                if (disDriver != null) {
                    mtMsg.setDriverId(String.valueOf(disDriver.getId()));
                } else {
                    log.error("订单【{}】未找到调度司机信息", bo.getOrderId());
                    throw new ServiceException("订单【" + bo.getOrderId() + "】未找到调度司机信息");
                }

                log.info("发送美团消息：mtMsg：【{}】", JSONUtil.toJsonStr(mtMsg));

                RemotePlatformApiResponseVo mtResp = remoteMtImService.sendMsg(mtMsg);
                if (mtResp.getStatus().equals(SuccessFailEnum.SUCCESS.getCode())) {
                    // 是否发送成功
                    bo.setStatus(StatusEnum.ENABLE.getCode());
                }
            } else {
                // 其他逻辑
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("消息推送至第三方时出现错误：" + e.getMessage());
        }
    }

    /**
     * 已读第三方消息
     * @param orderId
     * @return
     */
    private boolean readPlatformMsg(long orderId) {
        // 已读第三方消息
        if (!UserTypeEnum.DRIVER_USER.equals(LoginHelper.getUserType())) {
            return true;
        }
        try {
            ImMsg imMsg = msgMapper.queryByOrderId(orderId);
            if (imMsg != null) {
                // 已读美团消息，哈啰是发消息的时候已读
                if (PlatformEnum.MT.getCode().equals(imMsg.getPlatformCode())) {
                    List<ImMsgInfo> unreadMsg = baseMapper.listPassengerUnreadMsg(imMsg.getId());
                    if (CollUtil.isNotEmpty(unreadMsg)) {
                        RemoteMtMsgReadBo readBo = new RemoteMtMsgReadBo();
                        // 使用接单的司机id
                        RemoteOrderDriverVo disDriver = ordCacheManager.getOrderDispatchDriverInfoByOrderId(orderId);
                        if (disDriver != null) {
                            readBo.setDriverId(String.valueOf(disDriver.getId()));
                        } else {
                            log.error("订单【{}】未找到调度司机信息", orderId);
                            throw new ServiceException("订单【" + orderId + "】未找到调度司机信息");
                        }
                        readBo.setPlatformNo(imMsg.getPlatformNo());
                        readBo.setOriginMsgIds(StreamUtils.toList(unreadMsg, ImMsgInfo::getThirdMsgId));
                        if (CollUtil.isNotEmpty(readBo.getOriginMsgIds())) {
                            RemotePlatformApiResponseVo mtResp = remoteMtImService.readMsg(readBo);
                            if (!mtResp.getStatus().equals(SuccessFailEnum.SUCCESS.getCode())) {
                                log.error("已读美团消息失败， {}", JSONUtil.toJsonStr(readBo));
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("已读美团消息失败， {}", e.getMessage());
        }
        return true;
    }
}
