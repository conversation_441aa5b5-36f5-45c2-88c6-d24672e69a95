package com.feidi.xx.cross.message.service;

import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.message.domain.bo.ImMsgBo;
import com.feidi.xx.cross.message.domain.pojo.imMsg.ImMsgInfoForm;
import com.feidi.xx.cross.message.domain.vo.ImMsgVo;
import com.feidi.xx.cross.message.domain.vo.ImOrderInfo;

import java.util.Collection;
import java.util.List;

/**
 * IM 消息 主Service接口
 *
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface IImMsgService {

    /**
     * 汇总用户未读消息总数
     * @param userTypeEnum
     * @param userId
     * @return
     */
    Integer unreadTotal (UserTypeEnum userTypeEnum, Long userId);

    /**
     * 查询IM 消息 主
     *
     * @param id 主键
     * @return IM 消息 主
     */
    ImOrderInfo queryById(Long id);

    /**
     * 查询 订单 IM 消息 主
     *
     * @param orderId
     * @return IM 消息 主
     */
    ImMsgVo queryByOrderId(Long orderId);

    /**
     * 分页查询IM 消息 主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return IM 消息 主分页列表
     */
    TableDataInfo<ImMsgVo> queryPageList(ImMsgBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的IM 消息 主列表
     *
     * @param bo 查询条件
     * @return IM 消息 主列表
     */
    List<ImMsgVo> queryList(ImMsgBo bo);

    /**
     * 新增IM 消息 主
     *
     * @param bo IM 消息 主
     * @return 是否新增成功
     */
    Boolean insertByBo(ImMsgInfoForm bo);

    /**
     * 修改IM 消息 主
     *
     * @param bo IM 消息 主
     * @return 是否修改成功
     */
    Boolean updateByBo(ImMsgBo bo);

    /**
     * 校验并批量删除IM 消息 主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 处理订单消息体
     *
     * @param orderId
     * @return 是否新增成功
     */
    ImMsgVo handleOrderMsg(Long orderId);

    /**
     * 处理订单消息体
     *
     * @param orderId
     * @param thirdMsgId
     * @return 是否新增成功
     */
    ImMsgVo handleOrderMsg(Long orderId, String thirdMsgId);

    /**
     * 消息体 - 未读消息处理
     *
     * @param msgId
     * @param userType 处理谁的未读消息
     * @param unreadNum [0.全部已读；1.未读加1]
     * @return 是否新增成功
     */
    void handleUnread(Long msgId, String userType, int unreadNum);

    /**
     * 结束消息
     * @param msgId
     */
    void msgEnd(Long msgId);

    /**
     * 删除IM消息信息
     * @param orderId 订单id
     * @return
     */
    Boolean deleteImMsg(Long orderId);

    /**
     * 司机改派
     * @param orderId 订单id
     * @param driverId 司机id
     * @return
     */
    boolean dispatch(Long orderId, Long driverId);

}
