package com.feidi.xx.cross.message.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.message.domain.bo.ImDeviceBo;
import com.feidi.xx.cross.message.domain.vo.ImDeviceVo;
import com.feidi.xx.cross.message.service.IImDeviceService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - IM设备
 * 前端访问路由地址为:/im/device
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/device")
public class ImDeviceController extends BaseController {

    private final IImDeviceService imDeviceService;

    /**
     * 查询IM设备列表
     */
    @SaCheckPermission("im:device:list")
    @GetMapping("/list")
    public TableDataInfo<ImDeviceVo> list(ImDeviceBo bo, PageQuery pageQuery) {
        return imDeviceService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出IM设备列表
     */
    @SaCheckPermission("im:device:export")
    @Log(title = "IM设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ImDeviceBo bo,HttpServletResponse response) {
        List<ImDeviceVo> list = imDeviceService.queryList(bo);
        ExcelUtil.exportExcel(list, "IM设备", ImDeviceVo.class, response);
    }

    /**
     * 获取IM设备详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("im:device:query")
    @GetMapping("/{id}")
    public R<ImDeviceVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable Long id) {
        return R.ok(imDeviceService.queryById(id));
    }

    /**
     * 新增IM设备
     */
    @SaCheckPermission("im:device:add")
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ImDeviceBo bo) {
        return toAjax(imDeviceService.insertByBo(bo));
    }

    /**
     * 删除IM设备
     *
     * @param ids 主键串
     */
    @SaCheckPermission("im:device:remove")
    @Log(title = "IM设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(imDeviceService.deleteWithValidByIds(List.of(ids), true));
    }
}
