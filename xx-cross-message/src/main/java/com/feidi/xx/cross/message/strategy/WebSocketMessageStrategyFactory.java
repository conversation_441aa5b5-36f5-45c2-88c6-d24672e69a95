package com.feidi.xx.cross.message.strategy;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class WebSocketMessageStrategyFactory {
    private final Map<String, WebSocketMessageStrategy> strategies;

    @Autowired
    public WebSocketMessageStrategyFactory(List<WebSocketMessageStrategy> strategyList) {
        strategies = new HashMap<>();
        for (WebSocketMessageStrategy strategy : strategyList) {
            strategies.put(strategy.getClass().getSimpleName().replace("MessageStrategy", "").toUpperCase(), strategy);
        }
    }

    public WebSocketMessageStrategy getStrategy(String type) {
        return strategies.getOrDefault(type.toUpperCase(), (message, server) -> log.warn("未找到合适的消息处理策略: " + message.getType()));
    }
}
