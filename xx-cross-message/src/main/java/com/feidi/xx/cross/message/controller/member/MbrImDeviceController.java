package com.feidi.xx.cross.message.controller.member;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.message.domain.bo.ImDeviceBo;
import com.feidi.xx.cross.message.domain.vo.ImDeviceVo;
import com.feidi.xx.cross.message.service.IImDeviceService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 乘客 - IM设备
 * 前端访问路由地址为:/im/drv/device
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.MEMBER_ROUTE_PREFIX + "/device")
public class MbrImDeviceController extends BaseController {

    private final IImDeviceService imDeviceService;

    /**
     * 乘客-查询IM设备列表
     */
    @PostMapping("/list")
    public TableDataInfo<ImDeviceVo> list(@RequestBody ImDeviceBo bo) {
        bo.setUserId(LoginHelper.getUserId());
        bo.setUserType(LoginHelper.getUserType().getUserType());
        return imDeviceService.queryPageList(bo, bo.buildPageQuery());
    }

    /**
     * 乘客-获取IM设备详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<ImDeviceVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable Long id) {
        return R.ok(imDeviceService.queryById(id));
    }

    /**
     * 乘客-新增IM设备
     */
    @Log(title = "IM设备", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ImDeviceBo bo) {
        bo.setUserId(LoginHelper.getUserId());
        bo.setUserType(LoginHelper.getUserType().getUserType());
        return toAjax(imDeviceService.insertByBo(bo));
    }

    /**
     * 乘客-删除IM设备
     *
     * @param ids 主键串
     */
    @Log(title = "IM设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(imDeviceService.deleteWithValidByIds(List.of(ids), true));
    }
}
