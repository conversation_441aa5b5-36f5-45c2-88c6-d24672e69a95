package com.feidi.xx.cross.market;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.market.domain.MktCouponGrant;
import com.feidi.xx.cross.market.mapper.MktCouponGrantMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class MktCouponGrantMapperTest {
    @Autowired
    MktCouponGrantMapper mktCouponGrantMapper;

    @Test
    public void testUpd() {
//        var w = Wrappers.<MktCouponGrant>lambdaUpdate()
//                .eq(MktCouponGrant::getId, 1935959172976005121L)
//                .set(MktCouponGrant::getOrderId, null)
//                .set(MktCouponGrant::getOrderNo, null);
//        int i = mktCouponGrantMapper.update(w);
//        System.out.println(i);
//        Assertions.assertEquals(1, i);
    }
}
