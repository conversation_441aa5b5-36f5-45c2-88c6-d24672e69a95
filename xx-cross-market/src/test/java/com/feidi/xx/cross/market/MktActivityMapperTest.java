package com.feidi.xx.cross.market;

import com.feidi.xx.cross.common.enums.market.ActivityStatusEnum;
import com.feidi.xx.cross.market.mapper.MktActivityMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class MktActivityMapperTest {
    @Autowired
    private MktActivityMapper mktActivityMapper;

    @Test
    public void selectCountByCouponId() {
//        mktActivityMapper.selectCountByCouponId(1L);
//        mktActivityMapper.selectCountByCouponId(1L, ActivityStatusEnum.STARTING);
    }
}
