package com.feidi.xx.cross.market.helper;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.*;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CampaignInviteCodeHelperTest {

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RMap<String, String> codeMap;

    @Mock
    private RMap<String, String> userMap;

    @Mock
    private RSet<String> codeSet;

    @Mock
    private RTransaction transaction;

    @Mock
    private RKeys keys;

    private CampaignInviteCodeHelper campaignInviteCodeHelper;

    private static final Long ACTIVITY_ID = 1001L;
    private static final Long USER_ID = 2001L;
    private static final String INVITE_CODE = "ABC12345";

    @BeforeEach
    void setUp() {
        campaignInviteCodeHelper = new CampaignInviteCodeHelper(redissonClient);
    }

    @Test
    void testCreateAndSaveInviteCode_NewUser_Success() {
        // Given
        when(redissonClient.getMap(anyString())).thenReturn(userMap);
        when(userMap.get(USER_ID.toString())).thenReturn(null); // 用户没有邀请码
        when(redissonClient.getSet(anyString())).thenReturn(codeSet);
        when(codeSet.contains(anyString())).thenReturn(false); // 邀请码不存在
        when(redissonClient.createTransaction(any())).thenReturn(transaction);
        when(transaction.getMap(anyString())).thenReturn(codeMap, userMap);
        when(transaction.getSet(anyString())).thenReturn(codeSet);

        // When
        String result = campaignInviteCodeHelper.createAndSaveInviteCode(ACTIVITY_ID, USER_ID);

        // Then
        assertNotNull(result);
        assertEquals(8, result.length()); // 默认长度为8
        verify(transaction).commit();
    }

    @Test
    void testCreateAndSaveInviteCode_ExistingUser_ReturnExistingCode() {
        // Given
        when(redissonClient.getMap(anyString())).thenReturn(userMap);
        when(userMap.get(USER_ID.toString())).thenReturn(INVITE_CODE);

        // When
        String result = campaignInviteCodeHelper.createAndSaveInviteCode(ACTIVITY_ID, USER_ID);

        // Then
        assertEquals(INVITE_CODE, result);
        verify(transaction, never()).commit();
    }

    @Test
    void testGetUserIdByInviteCode_ValidCode_ReturnUserId() {
        // Given
        when(redissonClient.getMap(anyString())).thenReturn(codeMap);
        when(codeMap.get(INVITE_CODE)).thenReturn(USER_ID.toString());

        // When
        Long result = campaignInviteCodeHelper.getUserIdByInviteCode(ACTIVITY_ID, INVITE_CODE);

        // Then
        assertEquals(USER_ID, result);
    }

    @Test
    void testGetUserIdByInviteCode_InvalidCode_ReturnNull() {
        // Given
        when(redissonClient.getMap(anyString())).thenReturn(codeMap);
        when(codeMap.get(INVITE_CODE)).thenReturn(null);

        // When
        Long result = campaignInviteCodeHelper.getUserIdByInviteCode(ACTIVITY_ID, INVITE_CODE);

        // Then
        assertNull(result);
    }

    @Test
    void testGetInviteCodeByUser_ValidUser_ReturnCode() {
        // Given
        when(redissonClient.getMap(anyString())).thenReturn(userMap);
        when(userMap.get(USER_ID.toString())).thenReturn(INVITE_CODE);

        // When
        String result = campaignInviteCodeHelper.getInviteCodeByUser(ACTIVITY_ID, USER_ID);

        // Then
        assertEquals(INVITE_CODE, result);
    }

    @Test
    void testGetInviteCodeByUser_InvalidUser_ReturnNull() {
        // Given
        when(redissonClient.getMap(anyString())).thenReturn(userMap);
        when(userMap.get(USER_ID.toString())).thenReturn(null);

        // When
        String result = campaignInviteCodeHelper.getInviteCodeByUser(ACTIVITY_ID, USER_ID);

        // Then
        assertNull(result);
    }

    @Test
    void testDeleteInviteCode_ValidCode_Success() {
        // Given
        when(redissonClient.getMap(anyString())).thenReturn(codeMap);
        when(codeMap.get(INVITE_CODE)).thenReturn(USER_ID.toString());
        when(redissonClient.createTransaction(any())).thenReturn(transaction);
        when(transaction.getMap(anyString())).thenReturn(codeMap, userMap);
        when(transaction.getSet(anyString())).thenReturn(codeSet);

        // When
        campaignInviteCodeHelper.deleteInviteCode(ACTIVITY_ID, INVITE_CODE);

        // Then
        verify(codeMap).remove(INVITE_CODE);
        verify(userMap).remove(USER_ID.toString());
        verify(codeSet).remove(INVITE_CODE);
        verify(transaction).commit();
    }

    @Test
    void testDeleteInviteCode_InvalidCode_NoOperation() {
        // Given
        when(redissonClient.getMap(anyString())).thenReturn(codeMap);
        when(codeMap.get(INVITE_CODE)).thenReturn(null);

        // When
        campaignInviteCodeHelper.deleteInviteCode(ACTIVITY_ID, INVITE_CODE);

        // Then
        verify(transaction, never()).commit();
    }

    @Test
    void testDeleteUserInviteCode_ValidUser_Success() {
        // Given
        when(redissonClient.getMap(anyString())).thenReturn(userMap, codeMap);
        when(userMap.get(USER_ID.toString())).thenReturn(INVITE_CODE);
        when(redissonClient.createTransaction(any())).thenReturn(transaction);
        when(transaction.getMap(anyString())).thenReturn(userMap, codeMap);
        when(transaction.getSet(anyString())).thenReturn(codeSet);

        // When
        campaignInviteCodeHelper.deleteUserInviteCode(ACTIVITY_ID, USER_ID);

        // Then
        verify(userMap).remove(USER_ID.toString());
        verify(codeMap).remove(INVITE_CODE);
        verify(codeSet).remove(INVITE_CODE);
        verify(transaction).commit();
    }

    @Test
    void testDeleteAllInviteCodesByActivity_Success() {
        // Given
        when(redissonClient.getKeys()).thenReturn(keys);
        when(keys.delete(anyString())).thenReturn(1L);

        // When
        int result = campaignInviteCodeHelper.deleteAllInviteCodesByActivity(ACTIVITY_ID);

        // Then
        assertEquals(3, result); // 删除3个key
        verify(keys, times(3)).delete(anyString());
    }

    @Test
    void testDeleteAllInviteCodesByActivities_Success() {
        // Given
        List<Long> activityIds = Arrays.asList(1001L, 1002L, 1003L);
        when(redissonClient.getKeys()).thenReturn(keys);
        when(keys.delete(anyString())).thenReturn(1L);

        // When
        int result = campaignInviteCodeHelper.deleteAllInviteCodesByActivities(activityIds);

        // Then
        assertEquals(9, result); // 3个活动 * 3个key = 9
        verify(keys, times(9)).delete(anyString());
    }

    @Test
    void testExistsInviteCode_CodeExists_ReturnTrue() {
        // Given
        when(redissonClient.getSet(anyString())).thenReturn(codeSet);
        when(codeSet.contains(INVITE_CODE)).thenReturn(true);

        // When
        boolean result = campaignInviteCodeHelper.existsInviteCode(ACTIVITY_ID, INVITE_CODE);

        // Then
        assertTrue(result);
    }

    @Test
    void testExistsInviteCode_CodeNotExists_ReturnFalse() {
        // Given
        when(redissonClient.getSet(anyString())).thenReturn(codeSet);
        when(codeSet.contains(INVITE_CODE)).thenReturn(false);

        // When
        boolean result = campaignInviteCodeHelper.existsInviteCode(ACTIVITY_ID, INVITE_CODE);

        // Then
        assertFalse(result);
    }

    @Test
    void testExistsUserInviteCode_UserExists_ReturnTrue() {
        // Given
        when(redissonClient.getMap(anyString())).thenReturn(userMap);
        when(userMap.containsKey(USER_ID.toString())).thenReturn(true);

        // When
        boolean result = campaignInviteCodeHelper.existsUserInviteCode(ACTIVITY_ID, USER_ID);

        // Then
        assertTrue(result);
    }

    @Test
    void testExistsUserInviteCode_UserNotExists_ReturnFalse() {
        // Given
        when(redissonClient.getMap(anyString())).thenReturn(userMap);
        when(userMap.containsKey(USER_ID.toString())).thenReturn(false);

        // When
        boolean result = campaignInviteCodeHelper.existsUserInviteCode(ACTIVITY_ID, USER_ID);

        // Then
        assertFalse(result);
    }

    @Test
    void testGetInviteCodeCount_ReturnCorrectCount() {
        // Given
        when(redissonClient.getSet(anyString())).thenReturn(codeSet);
        when(codeSet.size()).thenReturn(100);

        // When
        long result = campaignInviteCodeHelper.getInviteCodeCount(ACTIVITY_ID);

        // Then
        assertEquals(100, result);
    }

    @Test
    void testTransactionRollback_OnException() {
        // Given
        when(redissonClient.getMap(anyString())).thenReturn(userMap);
        when(userMap.get(USER_ID.toString())).thenReturn(null);
        when(redissonClient.getSet(anyString())).thenReturn(codeSet);
        when(codeSet.contains(anyString())).thenReturn(false);
        when(redissonClient.createTransaction(any())).thenReturn(transaction);
        when(transaction.getMap(anyString())).thenReturn(codeMap, userMap);
        when(transaction.getSet(anyString())).thenReturn(codeSet);
        doThrow(new RuntimeException("Redis error")).when(transaction).commit();

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            campaignInviteCodeHelper.createAndSaveInviteCode(ACTIVITY_ID, USER_ID);
        });

        verify(transaction).rollback();
    }
}
