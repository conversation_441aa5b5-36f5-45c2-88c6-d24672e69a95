package com.feidi.xx.cross.market.mapper;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.common.enums.market.CouponSourceEnum;
import com.feidi.xx.cross.common.enums.market.CouponStatusEnum;
import com.feidi.xx.cross.common.enums.market.UserCouponSourceEnum;
import com.feidi.xx.cross.market.domain.MktCouponGrant;
import com.feidi.xx.cross.market.domain.vo.MktCouponGrantVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 优惠券发放Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-22
 */
public interface MktCouponGrantMapper extends BaseMapperPlus<MktCouponGrant, MktCouponGrantVo> {

    default void revoke(UserCouponSourceEnum userCouponSourceEnum, Long id, Long couponId) {
        var updateWrapper = Wrappers.<MktCouponGrant>lambdaUpdate()
                .eq(MktCouponGrant::getCouponId, couponId)
                .eq(MktCouponGrant::getSourceType, userCouponSourceEnum.getCode())
                .eq(MktCouponGrant::getSourceId, id)
                .eq(MktCouponGrant::getUsingStatus, CouponStatusEnum.NOT_USED.getCode())
                .set(MktCouponGrant::getUsingStatus, CouponStatusEnum.INVALID.getCode());
        this.update(updateWrapper);
    }
}
