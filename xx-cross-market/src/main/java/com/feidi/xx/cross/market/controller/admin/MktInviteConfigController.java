package com.feidi.xx.cross.market.controller.admin;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.cross.market.domain.vo.ExportVo;
import com.feidi.xx.cross.market.domain.bo.MktInviteConfigBo;
import com.feidi.xx.cross.market.domain.vo.MktInviteConfigVo;
import com.feidi.xx.cross.market.service.IMktInviteConfigService;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 邀请有奖配置
 * 前端访问路由地址为:/system/inviteConfig
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/inviteConfig")
public class MktInviteConfigController extends BaseController {

    private final IMktInviteConfigService mktInviteConfigService;

    /**
     * 查询邀请有奖配置列表
     */
    @SaCheckPermission("market:inviteConfig:list")
    @GetMapping("/list")
    public TableDataInfo<MktInviteConfigVo> list(MktInviteConfigBo bo, PageQuery pageQuery) {
        return mktInviteConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出邀请有奖配置列表
     */
    @SaCheckPermission("market:inviteConfig:export")
    @Log(title = "邀请有奖配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MktInviteConfigBo bo,HttpServletResponse response) {
        List<MktInviteConfigVo> list = mktInviteConfigService.queryList(bo);
        ExcelUtil.exportExcel(list, "邀请有奖配置", MktInviteConfigVo.class, response);
    }

    /**
     * 获取邀请有奖配置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("market:inviteConfig:query")
    @GetMapping("/{id}")
    public R<MktInviteConfigVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mktInviteConfigService.queryById(id));
    }

    /**
     * 新增邀请有奖配置
     */
    @SaCheckPermission("market:inviteConfig:add")
    @Log(title = "邀请有奖配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MktInviteConfigBo bo) {
        return toAjax(mktInviteConfigService.insertByBo(bo));
    }

    /**
     * 修改邀请有奖配置
     */
    @SaCheckPermission("market:inviteConfig:edit")
    @Log(title = "邀请有奖配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MktInviteConfigBo bo) {
        return toAjax(mktInviteConfigService.updateByBo(bo));
    }

    /**
     * 删除邀请有奖配置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("market:inviteConfig:remove")
    @Log(title = "邀请有奖配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mktInviteConfigService.deleteWithValidByIds(List.of(ids), true));
    }


}
