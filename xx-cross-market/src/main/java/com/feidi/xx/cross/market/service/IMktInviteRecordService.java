package com.feidi.xx.cross.market.service;


import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.cross.market.domain.bo.MktInviteRecordBo;
import com.feidi.xx.cross.market.domain.vo.DriverInviteCountVo;
import com.feidi.xx.cross.market.domain.vo.MktInviteRecordVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;

import java.util.Collection;
import java.util.List;

/**
 * 拉新记录Service接口
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
public interface IMktInviteRecordService {

    /**
     * 查询拉新记录
     *
     * @param id 主键
     * @return 拉新记录
     */
    MktInviteRecordVo queryById(Long id);

    /**
     * 分页查询拉新记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 拉新记录分页列表
     */
    TableDataInfo<MktInviteRecordVo> queryPageList(MktInviteRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的拉新记录列表
     *
     * @param bo 查询条件
     * @return 拉新记录列表
     */
    List<MktInviteRecordVo> queryList(MktInviteRecordBo bo);


    /**
     * 修改拉新记录
     *
     * @param bo 拉新记录
     * @return 是否修改成功
     */
    Boolean updateByBo(MktInviteRecordBo bo);

    /**
     * 校验并批量删除拉新记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    DriverInviteCountVo inviteRecordCount(Long userId );

    List<RemoteDriverVo> query();


}
