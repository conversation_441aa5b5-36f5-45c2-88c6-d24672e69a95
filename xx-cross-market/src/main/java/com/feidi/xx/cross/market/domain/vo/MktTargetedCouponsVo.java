package com.feidi.xx.cross.market.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.cross.common.enums.market.CouponTargetEnum;
import com.feidi.xx.cross.common.enums.market.DiscountTypeEnum;
import com.feidi.xx.cross.common.enums.market.TargetedCouponsStatusEnum;
import com.feidi.xx.cross.market.domain.MktTargetedCoupons;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 定向放券表
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MktTargetedCoupons.class)
public class MktTargetedCouponsVo {

    private Long id;

    /**
     * 放券标题
     */
    private String title;

    /**
     * 每人限领张数
     */
    private Integer perUserLimit;
    /**
     * 发放的用户手机号
     */
    private List<String> phoneNumbers;
    /**
     * 优惠卷
     */
    private Long couponId;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 优惠类型[DiscountTypeEnum],0:立减券;1:满减券
     */
    @Enum2Text(enumClass = DiscountTypeEnum.class)
    private String couponType;

    /**
     * TargetedCouponsStatusEnum 状态，1：已发放，2：已撤销
     */
    @Enum2Text(enumClass = TargetedCouponsStatusEnum.class)
    private String status;

    /**
     * 优惠对象[CouponTargetEnum],0:线路专属；1:全平台通用；2:城市专属
     */
    @Enum2Text(enumClass = CouponTargetEnum.class)
    private String target;

    /**
     * 面值，单位：分
     */
    private Long quota;

    /**
     * 过期时间（单位：秒）
     */
    private Long expireTime;

    private Date startTime;

    private Date endTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;


    /**
     * 使用数量
     */
    private Long quantityUsed;

    /**
     * 待使用数
     */
    private Long quantityAvailable;

    /**
     * 总发行量
     */
    private Long total;


    private MktCouponVo mktCouponVo;
}
