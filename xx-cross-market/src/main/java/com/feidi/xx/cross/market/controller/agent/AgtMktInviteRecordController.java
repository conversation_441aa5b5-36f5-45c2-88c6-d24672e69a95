package com.feidi.xx.cross.market.controller.agent;

import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.market.domain.bo.MktInviteRecordBo;
import com.feidi.xx.cross.market.domain.vo.MktInviteRecordVo;
import com.feidi.xx.cross.market.service.IMktInviteRecordService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 拉新记录
 * 前端访问路由地址为:/system/inviteRecord
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX+ "/inviteRecord")
public class AgtMktInviteRecordController extends BaseController {

    private final IMktInviteRecordService mktInviteRecordService;

    /**
     * 查询拉新记录列表
     */
    @GetMapping("/list")
    @Enum2TextAspect
    public TableDataInfo<MktInviteRecordVo> list(MktInviteRecordBo bo, PageQuery pageQuery) {
        bo.setAgentId(LoginHelper.getAgentId());
        return mktInviteRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出拉新记录列表
     */
    @Log(title = "拉新记录", businessType = BusinessType.EXPORT)
    @Download(name = "拉新记录",module = ModuleConstants.MARKET,mode = "no")
    @PostMapping("/export")
    public Object export(MktInviteRecordBo bo,HttpServletResponse response) {
        List<MktInviteRecordVo> list = mktInviteRecordService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(list, "拉新记录", MktInviteRecordVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取拉新记录详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<MktInviteRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mktInviteRecordService.queryById(id));
    }
    /**
     * 修改拉新记录
     */
    @Log(title = "拉新记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MktInviteRecordBo bo) {
        return toAjax(mktInviteRecordService.updateByBo(bo));
    }

    /**
     * 删除拉新记录
     *
     * @param ids 主键串
     */
    @Log(title = "拉新记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mktInviteRecordService.deleteWithValidByIds(List.of(ids), true));
    }
}
