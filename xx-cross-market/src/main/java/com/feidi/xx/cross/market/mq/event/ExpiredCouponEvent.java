

package com.feidi.xx.cross.market.mq.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 邀请有奖队列
 * <p>
 * 作者：zyt
 * 开发时间：2024-09-10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExpiredCouponEvent implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 优惠卷id
     */
    private Long couponGrantId;
    /**
     * 请求流水号
     */
    private String requestNo;

    /**
     * 过期时间
     */
    private Long expireTime;
}
