
package com.feidi.xx.cross.market.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.feidi.xx.common.idempotent.annotation.NoMQDuplicateConsume;
import com.feidi.xx.common.rocketmq.base.MessageWrapper;
import com.feidi.xx.cross.common.constant.market.MarketRocketMQConstant;
import com.feidi.xx.cross.common.constant.order.OrderRocketMQConstant;
import com.feidi.xx.cross.common.enums.market.CouponStatusEnum;
import com.feidi.xx.cross.market.domain.bo.MktCouponGrantBo;
import com.feidi.xx.cross.market.mq.event.ExpiredCouponEvent;
import com.feidi.xx.cross.market.service.IMktCouponGrantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 数据上报消息消费者
 * <p>
 * 开发时间：2024-09-10
 */
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = MarketRocketMQConstant.XX_MARKET_INVITE_EXPIRED_COUPON_TOPIC,
        consumerGroup = MarketRocketMQConstant.XX_MARKET_INVITE_EXPIRED_COUPON_CG
)
@Slf4j(topic = "ExpiredCouponConsumer")
public class ExpiredCouponConsumer implements RocketMQListener<MessageWrapper<ExpiredCouponEvent>> {

    private final IMktCouponGrantService mktCouponGrantService;
    @NoMQDuplicateConsume(
            keyPrefix = "global:xx-cross-market:",
            key = "#messageWrapper.keys",
            keyTimeout = 600
    )
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void onMessage(MessageWrapper<ExpiredCouponEvent> messageWrapper) {
        ExpiredCouponEvent message = messageWrapper.getMessage();
        //根据id更新是否展示
        Long couponGrantId = message.getCouponGrantId();
        MktCouponGrantBo mktCouponGrantBo = new MktCouponGrantBo();
        mktCouponGrantBo.setId(couponGrantId);
        mktCouponGrantBo.setUsingStatus(CouponStatusEnum.EXPIRED.getCode());
        mktCouponGrantService.updateStatus(mktCouponGrantBo);
        // 开头打印日志，平常可 Debug 看任务参数，线上可报平安（比如消息是否消费，重新投递时获取参数等）
        log.info("[消费者] 消费卷过期 - 执行消费逻辑，消息体：{}", JSON.toJSONString(messageWrapper));
    }
}
