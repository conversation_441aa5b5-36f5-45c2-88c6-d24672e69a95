package com.feidi.xx.cross.market.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * 活动对象 mkt_activity
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "mkt_activity", autoResultMap = true)
public class MktActivity extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 代理id
     */
    private Long agentId;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动描述
     */
    private String refer;

    /**
     * 内容
     */
    private String content;

    /**
     * 广告图
     */
    private String adImage;

    /**
     * 活动方式
     */
    private String manner;

    /**
     * 范围
     * ActivityScopeEnum
     */
    private String scope;

    /**
     * 活动对象
     */
    private String colony;

    /**
     * 活动类型{@link com.feidi.xx.cross.common.enums.market.ActivityTypeEnum}
     */
    private String type;

    /**
     * 投放渠道
     */
    private String issuingChannel;

    /**
     * 优惠券发放范围{@link com.feidi.xx.cross.common.enums.market.GrantScopeEnum}
     */
    private String grantScope;
    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 状态
     * {@link com.feidi.xx.cross.common.enums.market.ActivityStatusEnum}
     */
    private String status;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 上下架状态：0=下架，1=上架
     */
    private String shelvesStatus;

    /**
     * 活动关联的优惠券ids
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> couponIds;

    /**
     * 活动关联的线路ids
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> lineIds;
}
