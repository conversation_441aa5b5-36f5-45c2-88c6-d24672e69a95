package com.feidi.xx.cross.market.service;

import com.feidi.xx.cross.market.domain.bo.MktActivityLineForm;
import com.feidi.xx.cross.market.domain.vo.MktActivityLineVo;
import com.feidi.xx.cross.market.domain.bo.MktActivityLineBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 活动线路Service接口
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
public interface IMktActivityLineService {

    /**
     * 查询活动线路
     *
     * @param id 主键
     * @return 活动线路
     */
    MktActivityLineVo queryById(Long id);

    /**
     * 分页查询活动线路列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动线路分页列表
     */
    TableDataInfo<MktActivityLineVo> queryPageList(MktActivityLineBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的活动线路列表
     *
     * @param bo 查询条件
     * @return 活动线路列表
     */
    List<MktActivityLineVo> queryList(MktActivityLineBo bo);

    /**
     * 新增活动线路
     *
     * @param bo 活动线路
     * @return 是否新增成功
     */
    Boolean insertByBo(MktActivityLineBo bo);

    /**
     * 修改活动线路
     *
     * @param bo 活动线路
     * @return 是否修改成功
     */
    Boolean updateByBo(MktActivityLineBo bo);

    /**
     * 校验并批量删除活动线路信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 绑定活动线路
     * @param activityId
     * @param bo
     * @return
     */
    Boolean bindActivityLine(Long activityId, List<MktActivityLineForm> bo);
}
