package com.feidi.xx.cross.market.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.market.domain.MktInviteConfig;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 邀请有奖配置业务对象 mkt_invite_config
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MktInviteConfig.class, reverseConvertGenerate = false)
public class MktInviteConfigBo extends TenantEntity {

    /**
     * 注解id
     */
    private Long id;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 主体名称
     */
    private String companyName;

    /**
     * 拉新返奖比例
     */
    private BigDecimal recruitRewardRatio;

    /**
     * 奖励司机抽成
     */
    private BigDecimal driverBonusRatio;

    /**
     * 奖励代理商抽成
     */
    private BigDecimal agentBonusRatio;

    /**
     * 状态
     */
    private String status;

    /**
     * 开始创建时间
     */
    private String startCreateTime;
    /**
     * 结束创建时间
     */
    private String endCreateTime;

}
