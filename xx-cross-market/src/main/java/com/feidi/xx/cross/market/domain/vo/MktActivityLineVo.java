package com.feidi.xx.cross.market.domain.vo;

import com.feidi.xx.cross.market.domain.MktActivityLine;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 活动线路视图对象 mkt_activity_line
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MktActivityLine.class)
public class MktActivityLineVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 活动
     */
    @ExcelProperty(value = "活动")
    private Long activityId;

    private String activityName;

    /**
     * 线路
     */
    @ExcelProperty(value = "线路")
    private Long lineId;
    private String lineName;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "StatusEnum")
    private String status;

}
