//package com.feidi.xx.cross.market.controller.admin;
//
//import cn.dev33.satoken.stp.StpUtil;
//import cn.hutool.extra.mail.MailUtil;
//import cn.hutool.http.useragent.UserAgent;
//import cn.hutool.http.useragent.UserAgentUtil;
//import com.feidi.xx.common.core.cache.system.SysDistrictCacheVo;
//import com.feidi.xx.common.core.enums.SystemCacheKeyEnum;
//import com.feidi.xx.common.json.utils.JsonUtils;
//import com.feidi.xx.common.redis.utils.RedisUtils;
//import com.feidi.xx.common.satoken.utils.LoginHelper;
//import com.feidi.xx.cross.market.mapper.MktCouponMapper;
//import jakarta.servlet.http.HttpServletRequest;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.Map;
//import java.util.Set;
//
//@Slf4j
//@RestController
//@RequestMapping("/test")
//public class TestController {
//    @Autowired
//    MktCouponMapper mktCouponMapper;
//    @Autowired
//
//    @GetMapping("/test")
//    public String test(HttpServletRequest request) {
//        log.info("test");
//        log.debug("debug");
//        mktCouponMapper.selectList();
//        Map<String, SysDistrictCacheVo> multiCacheMapValue = RedisUtils.getMultiCacheMapValue(SystemCacheKeyEnum.SYS_CITY_CODE_KEY.create(), Set.of("0379", "0763"));
//        log.info("multiCacheMapValue:{}", multiCacheMapValue);
////        if (StpUtil.isLogin()) {
////            var tenantId = LoginHelper.getTenantId();
////            var userId = LoginHelper.getUserId();
////            log.warn("t {}", StpUtil.getTokenValue());
////            log.warn("in {}", JsonUtils.toJsonString(LoginHelper.getLoginUser()));
////        }
//        UserAgent userAgent = UserAgentUtil.parse(request.getHeader("User-Agent"));
//        //分行打印所有字段
//        System.out.println(userAgent.getOsVersion());
//        System.out.println(userAgent.getOs());
//        System.out.println(userAgent.getBrowser());
//        return "test";
//    }
//}
