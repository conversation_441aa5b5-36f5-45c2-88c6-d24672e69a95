package com.feidi.xx.cross.market.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.market.domain.bo.MktCouponGrantBo;
import com.feidi.xx.cross.market.domain.vo.MktCouponGrantVo;
import com.feidi.xx.cross.market.service.IMktCouponGrantService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 后台 - 优惠券发放
 * 前端访问路由地址为:/market/couponGrant
 *
 * <AUTHOR>
 * @date 2025-03-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/coupon/grant")
public class MktCouponGrantController extends BaseController {

    private final IMktCouponGrantService mktCouponGrantService;

    /**
     * 查询优惠券发放列表
     */
    @SaCheckPermission("market:couponGrant:list")
    @Enum2TextAspect
    @GetMapping("/list")
    public TableDataInfo<MktCouponGrantVo> list(MktCouponGrantBo bo, PageQuery pageQuery) {
        return mktCouponGrantService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取优惠券发放详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("market:couponGrant:query")
    @GetMapping("/{id}")
    public R<MktCouponGrantVo> getInfo(@NotNull(message = "主键不能为空")
                                       @PathVariable Long id) {
        return R.ok(mktCouponGrantService.queryById(id));
    }
}
