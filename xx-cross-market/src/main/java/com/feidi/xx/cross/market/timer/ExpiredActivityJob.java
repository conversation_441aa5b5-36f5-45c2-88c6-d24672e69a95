package com.feidi.xx.cross.market.timer;

import cn.hutool.core.date.StopWatch;
import com.feidi.xx.cross.market.service.IMktActivityService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 活动开始结束定时器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ExpiredActivityJob {

    @Autowired
    private IMktActivityService mktActivityService;

    @XxlJob("expiredActivityJob")
    public void expiredActivityJob() {
        log.info("执行定时任务：过期活动");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        //活动状态定时任务，参照优惠券过期
        mktActivityService.updateStatusTask();
        stopWatch.stop();
        log.info("结束定时任务：过期活动, 耗时 {}", stopWatch.getTotalTimeMillis());
    }
}
