package com.feidi.xx.cross.market.controller.agent;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.market.domain.bo.MktInviteConfigBo;
import com.feidi.xx.cross.market.domain.vo.MktInviteConfigVo;
import com.feidi.xx.cross.market.service.IMktInviteConfigService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 后台 - 邀请有奖配置
 * 前端访问路由地址为:/system/inviteConfig
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/inviteConfig")
public class AgtMktInviteConfigController extends BaseController {

    private final IMktInviteConfigService mktInviteConfigService;

    /**
     * 查询邀请有奖配置列表
     */
    @GetMapping("/list")
    public TableDataInfo<MktInviteConfigVo> list(MktInviteConfigBo bo, PageQuery pageQuery) {
        return mktInviteConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出邀请有奖配置列表
     */
    @Log(title = "邀请有奖配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MktInviteConfigBo bo,HttpServletResponse response) {
        List<MktInviteConfigVo> list = mktInviteConfigService.queryList(bo);
        ExcelUtil.exportExcel(list, "邀请有奖配置", MktInviteConfigVo.class, response);
    }

    /**
     * 获取邀请有奖配置详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<MktInviteConfigVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mktInviteConfigService.queryById(id));
    }

    /**
     * 新增邀请有奖配置
     */
    @Log(title = "邀请有奖配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MktInviteConfigBo bo) {
        return toAjax(mktInviteConfigService.insertByBo(bo));
    }

    /**
     * 修改邀请有奖配置
     */
    @Log(title = "邀请有奖配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MktInviteConfigBo bo) {
        return toAjax(mktInviteConfigService.updateByBo(bo));
    }

    /**
     * 删除邀请有奖配置
     *
     * @param ids 主键串
     */
    @Log(title = "邀请有奖配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mktInviteConfigService.deleteWithValidByIds(List.of(ids), true));
    }

}
