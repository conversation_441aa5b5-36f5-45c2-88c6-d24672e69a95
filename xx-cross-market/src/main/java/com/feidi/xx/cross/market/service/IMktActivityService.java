package com.feidi.xx.cross.market.service;

import com.feidi.xx.cross.market.domain.MktActivity;
import com.feidi.xx.cross.market.domain.vo.MktActivityVo;
import com.feidi.xx.cross.market.domain.bo.MktActivityBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 活动Service接口
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
public interface IMktActivityService {

    /**
     * 查询活动
     *
     * @param id 主键
     * @return 活动
     */
    MktActivityVo queryById(Long id);

    /**
     * 分页查询活动列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动分页列表
     */
    TableDataInfo<MktActivityVo> queryPageList(MktActivityBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的活动列表
     *
     * @param bo 查询条件
     * @return 活动列表
     */
    List<MktActivityVo> queryList(MktActivityBo bo);

    /**
     * 新增活动
     *
     * @param bo 活动
     * @return 是否新增成功
     */
    Boolean insertByBo(MktActivityBo bo);

    /**
     * 修改活动
     *
     * @param bo 活动
     * @return 是否修改成功
     */
    Boolean updateByBo(MktActivityBo bo);

    /**
     * 校验并批量删除活动信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 定时任务更新活动状态
     */
    void updateStatusTask();
}
