package com.feidi.xx.cross.market.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.cross.common.enums.market.InviteTypeEnum;
import com.feidi.xx.cross.common.enums.market.RewardTypeEnum;
import com.feidi.xx.cross.market.domain.MktInviteRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 拉新记录视图对象 mkt_invite_record
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MktInviteRecord.class)
public class MktInviteRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 流水编号
     */
    @ExcelProperty(value = "流水编号")
    private String flowNo;

    /**
     * 拉新类型[InviteTypeEnum]
     */
    @ExcelProperty(value = "拉新类型")
    @Enum2Text(enumClass = InviteTypeEnum.class)
    @ExcelEnumFormat(enumClass = InviteTypeEnum.class)
    private String inviteType;
    /**
     * 奖励比例
     */
    @ExcelProperty(value = "奖励比例")
    //@ReverseAutoMapping(target = "rewardRate", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanBigDecimal(source.getRewardRate())) + \"%\")")
    private BigDecimal rewardRate;
    /**
     * 奖励金额
     */
    @ExcelProperty(value = "奖励金额")
    //@ReverseAutoMapping(target = "rewardPrice", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getRewardPrice()))")
    private Long rewardPrice;

    /**
     * 奖励类型[RewardTypeEnum]
     */
    @ExcelProperty(value = "奖励类型")
    @Enum2Text(enumClass = RewardTypeEnum.class)
    @ExcelEnumFormat(enumClass = RewardTypeEnum.class)
    private String rewardType;

    /**
     * 订单id
     */
    @ExcelProperty(value = "订单id")
    private Long orderId;

    /**
     * 代理商id
     */
    @ExcelProperty(value = "代理商id")
    private Long agentId;

    /**
     * 代理商名称
     */
    @ExcelProperty(value = "代理商名称")
    private String companyName;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机id")
    private Long driverId;

    /**
     * 订单价格
     */
    @ExcelProperty(value = "订单价格（单位：元）")
    //@ReverseAutoMapping(target = "orderPrice", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getOrderPrice()))")
    private Long orderPrice;

    /**
     * 订单完单时间
     */
    @ExcelProperty(value = "订单完单时间")
    private Date orderFinishTime;


    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;



    /**
     * 乘客id
     */
    @ExcelProperty(value = "乘客id")
    private Long passengerId;



}
