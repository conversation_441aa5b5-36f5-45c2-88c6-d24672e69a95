package com.feidi.xx.cross.market.domain.bo;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.market.domain.MktCoupon;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.Set;

/**
 * 优惠券业务对象 mkt_coupon
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MktCoupon.class, reverseConvertGenerate = false)
public class MktCouponBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 活动ID
     */
    private Long activityId;


    /**
     * 线路id
     */
    private String lineId;

    /**
     * 城市编码
     */
    private String cityCode;
    /**
     * 代理id
     */
    private Long agentId;

    /**
     * 优惠卷编码
     */
    private String couponCode;

    /**
     * 优惠卷名称
     */
    @NotBlank(message = "优惠卷名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 优惠类型[DiscountTypeEnum]
     */
    @NotBlank(message = "折扣方式不能为空", groups = {AddGroup.class, EditGroup.class})
    private String discountType;

    /**
     * 额度
     */
    @NotNull(message = "额度不能为空", groups = {AddGroup.class, EditGroup.class})
    @Min(value = 0, message = "额度不能小于0", groups = {AddGroup.class, EditGroup.class})
    private Long quota;

    /**
     * 总数
     */
    @NotNull(message = "总数不能为空", groups = {AddGroup.class, EditGroup.class})
    @Min(value = 0, message = "总数不能小于0", groups = {AddGroup.class, EditGroup.class})
    private Integer total;

    /**
     * 余量
     */
    private Integer margin;

    /**
     * 优惠规则
     */
    @NotNull(message = "优惠规则不能为空", groups = {AddGroup.class, EditGroup.class})
    private String rule;

    /**
     * 过期时间
     */
//    @NotNull(message = "过期时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long expireTime;

    /**
     * 领取方式
     */
//    @NotBlank(message = "领取方式不能为空", groups = { AddGroup.class, EditGroup.class })
    private String paidType;

    /**
     * 最大领取数量
     */
//    @NotNull(message = "最大领取数量不能为空", groups = {AddGroup.class, EditGroup.class})
//    @PositiveOrZero
    private Integer maxNum = 0;

    /**
     * 使用条件 满 x 元可用 满减卷
     */
    private String termsOfUse;

    /**
     * 状态[CouponTemplateStatusEnum]
     */
    private String status;

    /**
     * 优惠对象[CouponTargetEnum]
     */
    @NotNull(message = "优惠对象不能为空", groups = {AddGroup.class, EditGroup.class})
    private String target;

    /**
     * 优惠券来源 [CouponSourceEnum]
     */
    private String source;

    /**
     * 增加库存
     * @deprecated 新版本直接修改库存
     */
    @Deprecated
    private Integer addNumber;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Future(message = "结束时间不能早于当前时间", groups = {AddGroup.class, EditGroup.class})
    private Date endTime;

    /**
     * 上下架状态：0=下架，1=上架
     */
    private String shelvesStatus;

    /**
     * 产品范围：0=全部，1=独享，2=拼车
     */
    private String productScope;

    /**
     * 创建时间范围 查询条件
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startCreateTime;
    /**
     * 创建时间范围 查询条件
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endCreateTime;

    /**
     * 是否只显示有效的优惠券
     */
    private Boolean onlyValid = false;

    public void validate() {
        boolean hasStartEnd = startTime != null && endTime != null;
        boolean hasExpire = expireTime != null;

        // 时间范围和 expireTime 只能存在一个
        if (hasStartEnd && hasExpire) {
            throw new IllegalArgumentException("过期时间选择错误");
        }
        if (hasStartEnd) {
            if (!endTime.after(new Date())) {
                throw new IllegalArgumentException("endTime 必须大于当前时间");
            }
        }
    }

    /**
     * 卡券ids
     */
    private Set<Long> couponIds;
}
