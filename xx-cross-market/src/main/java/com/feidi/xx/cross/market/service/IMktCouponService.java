package com.feidi.xx.cross.market.service;

import com.feidi.xx.cross.market.domain.MktCoupon;
import com.feidi.xx.cross.market.domain.vo.MktCouponVo;
import com.feidi.xx.cross.market.domain.bo.MktCouponBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 优惠券Service接口
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
public interface IMktCouponService {

    /**
     * 查询优惠券
     *
     * @param id 主键
     * @return 优惠券
     */
    MktCouponVo queryById(Long id);

    /**
     * 查询优惠券
     *
     * @param bo 条件
     * @return 优惠券
     */
    List<MktCouponVo> queryCoupon(MktCouponBo bo);

    /**
     * 分页查询优惠券列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 优惠券分页列表
     */
    TableDataInfo<MktCouponVo> queryPageList(MktCouponBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的优惠券列表
     *
     * @param bo 查询条件
     * @return 优惠券列表
     */
    List<MktCouponVo> queryList(MktCouponBo bo);

    /**
     * 新增优惠券
     *
     * @param bo 优惠券
     * @return 是否新增成功
     */
    Boolean insertByBo(MktCouponBo bo);

    /**
     * 修改优惠券
     *
     * @param bo 优惠券
     * @return 是否修改成功
     */
    Boolean updateByBo(MktCouponBo bo);

    /**
     * 校验并批量删除优惠券信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 验证优惠券
     *
     * @param couponId 优惠券id
     * @return MktCouponVo
     */
    MktCouponVo verifyCoupon(Long couponId);

    /**
     * 根据id集合查询优惠券
     *
     * @param longs id集合
     * @return 优惠券
     */
    Map<Long, MktCouponVo> queryMapByIds(Set<Long> longs);

    /**
     * 根据id集合查询优惠券
     *
     * @param couponIds id集合
     * @return 优惠券
     */
    List<MktCouponVo> queryListByIds(Set<Long> couponIds);
}
