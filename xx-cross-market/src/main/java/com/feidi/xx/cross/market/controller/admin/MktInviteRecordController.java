package com.feidi.xx.cross.market.controller.admin;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.cross.market.domain.vo.ExportVo;
import com.feidi.xx.cross.market.domain.bo.MktInviteRecordBo;
import com.feidi.xx.cross.market.domain.vo.MktInviteRecordVo;
import com.feidi.xx.cross.market.service.IMktInviteRecordService;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 拉新记录
 * 前端访问路由地址为:/system/inviteRecord
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/inviteRecord")
public class MktInviteRecordController extends BaseController {

    private final IMktInviteRecordService mktInviteRecordService;

    /**
     * 查询拉新记录列表
     */
    @SaCheckPermission("market:inviteRecord:list")
    @GetMapping("/list")
    @Enum2TextAspect
    public TableDataInfo<MktInviteRecordVo> list(MktInviteRecordBo bo, PageQuery pageQuery) {
        return mktInviteRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出拉新记录列表
     */
    @SaCheckPermission("market:inviteRecord:export")
    @Log(title = "拉新记录", businessType = BusinessType.EXPORT)
    @Download(name="拉新记录",module = ModuleConstants.MARKET,mode="no")
    @PostMapping("/export")
    public Object export(MktInviteRecordBo bo,HttpServletResponse response) {
        List<MktInviteRecordVo> list = mktInviteRecordService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(list, "拉新记录", MktInviteRecordVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取拉新记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("market:inviteRecord:query")
    @GetMapping("/{id}")
    public R<MktInviteRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mktInviteRecordService.queryById(id));
    }
    /**
     * 修改拉新记录
     */
    @SaCheckPermission("market:inviteRecord:edit")
    @Log(title = "拉新记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MktInviteRecordBo bo) {
        return toAjax(mktInviteRecordService.updateByBo(bo));
    }

    /**
     * 删除拉新记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("market:inviteRecord:remove")
    @Log(title = "拉新记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mktInviteRecordService.deleteWithValidByIds(List.of(ids), true));
    }
}
