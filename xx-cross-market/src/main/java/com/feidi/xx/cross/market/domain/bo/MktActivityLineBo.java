package com.feidi.xx.cross.market.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.market.domain.MktActivityLine;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.List;

/**
 * 活动线路业务对象 mkt_activity_line
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MktActivityLine.class, reverseConvertGenerate = false)
public class MktActivityLineBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 活动
     */
    @NotNull(message = "活动不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long activityId;

    /**
     * 线路
     */
    @NotNull(message = "活动不能为空", groups = { AddGroup.class, EditGroup.class })
    private List<Long> lineIds;

    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
