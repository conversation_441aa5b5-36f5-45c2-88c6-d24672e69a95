

package com.feidi.xx.cross.market.mq.producer;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.feidi.xx.common.rocketmq.base.BaseSendExtendDTO;
import com.feidi.xx.common.rocketmq.enums.MqMessageTypeEnum;
import com.feidi.xx.common.rocketmq.producer.AbstractProducer;
import com.feidi.xx.common.rocketmq.util.MQMessageUtil;
import com.feidi.xx.cross.common.constant.market.MarketRocketMQConstant;
import com.feidi.xx.cross.market.mq.event.ExpiredCouponEvent;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.stereotype.Component;

import java.util.Date;


/**
 * 用户兑换优惠券消息生产者
 * <p>
 * 开发时间：2024-09-10
 */
@Component
@RequiredArgsConstructor
public class ExpiredCouponProducer extends AbstractProducer<ExpiredCouponEvent> {

    private BaseSendExtendDTO buildBaseSendExtendParam(ExpiredCouponEvent inviteEvent) {

        Date now = new Date();
        DateTime validEndTime = DateUtil.offsetMillisecond(now, Math.toIntExact(inviteEvent.getExpireTime()*1000));
        return BaseSendExtendDTO.builder()
                .eventName("优惠卷过期延迟队列")
                .keys(inviteEvent.getRequestNo())
                .messageType(MqMessageTypeEnum.SYNC)
                .topic(MarketRocketMQConstant.XX_MARKET_INVITE_EXPIRED_COUPON_TOPIC)
                .sentTimeout(2000L)
                .delayTime(validEndTime.getTime())
                .build();
    }
    @Override
    public SendResult sendMessage(ExpiredCouponEvent expiredCouponEvent) {
        return MQMessageUtil.sendMessage(expiredCouponEvent, this::buildBaseSendExtendParam);
    }
}

