package com.feidi.xx.cross.market.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.market.domain.MktInviteRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 拉新记录业务对象 mkt_invite_record
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MktInviteRecord.class, reverseConvertGenerate = false)
public class MktInviteRecordBo extends BaseEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 流水编号
     */
    private String flowNo;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 拉新类型[InviteTypeEnum]
     */
    private String inviteType;

    /**
     * 奖励类型[RewardTypeEnum]
     */
    private String rewardType;

    /**
     * 奖励比例
     */
    private BigDecimal rewardRate;

    /**
     * 开始创建时间
     */
    private String startCreateTime;
    /**
     * 结束创建时间
     */
    private String endCreateTime;
}
