package com.feidi.xx.cross.market.domain.vo;

import java.math.BigDecimal;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import com.feidi.xx.cross.market.domain.MktInviteConfig;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 邀请有奖配置视图对象 mkt_invite_config
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MktInviteConfig.class)
public class MktInviteConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 注解id
     */
    @ExcelProperty(value = "注解id")
    private Long id;

    /**
     * 代理商id
     */
    @ExcelProperty(value = "代理商id")
    private Long agentId;

    /**
     * 主体名称
     */
    @ExcelProperty(value = "主体名称")
    private String companyName;

    /**
     * 拉新返奖比例
     */
    @ExcelProperty(value = "拉新返奖比例")
    private BigDecimal recruitRewardRatio;

    /**
     * 奖励司机抽成
     */
    @ExcelProperty(value = "奖励司机抽成")
    private BigDecimal driverBonusRatio;

    /**
     * 奖励代理商抽成
     */
    @ExcelProperty(value = "奖励代理商抽成")
    private BigDecimal agentBonusRatio;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

}
