package com.feidi.xx.cross.market.controller.driver;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.market.domain.vo.DriverInviteCountVo;
import com.feidi.xx.cross.market.service.IMktInviteRecordService;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 司机端 - 拉新记录
 * 前端访问路由地址为:/system/inviteRecord
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.DRIVER_ROUTE_PREFIX + "/inviteRecord")
public class DrvDrvInviteRecordController extends BaseController {

    private final IMktInviteRecordService mktInviteRecordService;


    /**
     * 邀请有奖奖励
     */
    @GetMapping("/inviteRecordCount")
    public R<DriverInviteCountVo> inviteRecordCount() {
        Long userId = LoginHelper.getUserId();
        return R.ok(mktInviteRecordService.inviteRecordCount(userId));
    }

    /**
     * 假数据展示
     */
    @GetMapping("/info")
    public R<List<RemoteDriverVo>> list() {
        return R.ok(mktInviteRecordService.query());
    }
}
