package com.feidi.xx.cross.market.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.io.Serial;

/**
 * 邀请有奖配置对象 mkt_invite_config
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mkt_invite_config")
public class MktInviteConfig extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 注解id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 主体名称
     */
    private String companyName;

    /**
     * 拉新返奖比例
     */
    private BigDecimal recruitRewardRatio;

    /**
     * 奖励司机抽成
     */
    private BigDecimal driverBonusRatio;

    /**
     * 奖励代理商抽成
     */
    private BigDecimal agentBonusRatio;

    /**
     * 状态
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
