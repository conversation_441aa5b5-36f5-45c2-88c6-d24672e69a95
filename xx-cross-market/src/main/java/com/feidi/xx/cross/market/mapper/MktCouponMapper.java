package com.feidi.xx.cross.market.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.market.domain.MktCoupon;
import com.feidi.xx.cross.market.domain.vo.MktCouponVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 优惠券Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
public interface MktCouponMapper extends BaseMapperPlus<MktCoupon, MktCouponVo> {

    default boolean nameSame(Long id, String name) {
        var w = Wrappers.<MktCoupon>lambdaQuery()
                .eq(MktCoupon::getName, name)
                .ne(id != null, MktCoupon::getId, id);
        return selectCount(w) > 0;
    }
}
