package com.feidi.xx.cross.market.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.common.enums.market.CouponStatusEnum;
import com.feidi.xx.cross.common.enums.market.ProductScopeEnum;
import com.feidi.xx.cross.common.enums.market.TargetedCouponsStatusEnum;
import com.feidi.xx.cross.common.enums.market.UserCouponSourceEnum;
import com.feidi.xx.cross.market.api.RemoteCouponGrantService;
import com.feidi.xx.cross.market.api.domain.couponGrant.RemoteCouponGrantBo;
import com.feidi.xx.cross.market.api.domain.couponGrant.RemoteCouponGrantVo;
import com.feidi.xx.cross.market.domain.MktCouponGrant;
import com.feidi.xx.cross.market.domain.bo.MktCouponGrantBo;
import com.feidi.xx.cross.market.domain.vo.MktCouponGrantVo;
import com.feidi.xx.cross.market.domain.vo.MktTargetedCouponsVo;
import com.feidi.xx.cross.market.mapper.MktCouponGrantMapper;
import com.feidi.xx.cross.market.mapper.MktTargetedCouponsMapper;
import com.feidi.xx.cross.market.service.IMktCouponGrantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 优惠券领取服务接口实现类
 *
 * <AUTHOR>
 * @date 2024/9/7
 */
@Slf4j
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteCouponGrantServiceImpl implements RemoteCouponGrantService {

    private final IMktCouponGrantService mktCouponGrantService;
    private final MktCouponGrantMapper mktCouponGrantMapper;
    private final MktTargetedCouponsMapper targetedCouponsMapper;

    @Override
    public List<RemoteCouponGrantVo> getPassengerUsableCoupon(Long passengerId) {

        MktCouponGrantBo bo = new MktCouponGrantBo();
        bo.setPassengerId(passengerId);
        bo.setUsingStatus(CouponStatusEnum.NOT_USED.getCode());
        List<MktCouponGrantVo> mktCouponGrantVos = mktCouponGrantService.queryList(bo);

        return MapstructUtils.convert(mktCouponGrantVos, RemoteCouponGrantVo.class);

    }


    @Override
    public void updateStatus(RemoteCouponGrantBo bo) {
        log.info("更新优惠券状态：{}", bo);
        MktCouponGrantVo mktCouponGrantVo = mktCouponGrantMapper.selectVoById(bo.getId());
        LambdaUpdateWrapper<MktCouponGrant> lambdaUpdateWrapper = new LambdaUpdateWrapper<MktCouponGrant>()
                .eq(MktCouponGrant::getId, bo.getId());
        if (bo.getUsingStatus().equals(CouponStatusEnum.EXPIRED.getCode())) {
            if (!CouponStatusEnum.NOT_USED.getCode().equals(mktCouponGrantVo.getUsingStatus())) {
                return;
            }
        } else if (CouponStatusEnum.NOT_USED.getCode().equals(bo.getUsingStatus())) {
            //比较是否过期
            if (mktCouponGrantVo.getEndTime().getTime() < System.currentTimeMillis()) {
                bo.setUsingStatus(CouponStatusEnum.EXPIRED.getCode());
            }
            //如果优惠券来源以及撤回，则优惠券作废、例如定向发券
            if (UserCouponSourceEnum.DIRECT.getCode().equals(mktCouponGrantVo.getSourceType())) {
                MktTargetedCouponsVo mktTargetedCouponsVo = targetedCouponsMapper.selectVoById(mktCouponGrantVo.getSourceId());
                if (mktTargetedCouponsVo != null && TargetedCouponsStatusEnum.REVOKE.getCode().equals(mktTargetedCouponsVo.getStatus())) {
                    bo.setUsingStatus(CouponStatusEnum.INVALID.getCode());
                }
            }
            lambdaUpdateWrapper.set(MktCouponGrant::getWipedTime, null);
            lambdaUpdateWrapper.set(MktCouponGrant::getOrderId, null);
            lambdaUpdateWrapper.set(MktCouponGrant::getOrderNo, null);
        } else if (bo.getUsingStatus().equals(CouponStatusEnum.USED.getCode())) {
            lambdaUpdateWrapper.set(MktCouponGrant::getWipedTime, new Date());
            lambdaUpdateWrapper.set(MktCouponGrant::getOrderId, bo.getOrderId());
            lambdaUpdateWrapper.set(MktCouponGrant::getOrderNo, bo.getOrderNo());
        }
        lambdaUpdateWrapper.set(MktCouponGrant::getUsingStatus, bo.getUsingStatus());
        mktCouponGrantMapper.update(lambdaUpdateWrapper);
    }

    /**
     * 获取可用优惠卷
     *
     * @param remoteCouponGrantBo
     * @return
     */
    @Override
    public List<RemoteCouponGrantVo> getCouponGrant(RemoteCouponGrantBo remoteCouponGrantBo) {
        log.info(" 获取可用优惠券参数 {}", remoteCouponGrantBo);
        LambdaQueryWrapper<MktCouponGrant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MktCouponGrant::getPassengerId, remoteCouponGrantBo.getPassengerId())
                .eq(MktCouponGrant::getUsingStatus, CouponStatusEnum.NOT_USED.getCode())
                .le(MktCouponGrant::getStartTime, new Date())
                .ge(MktCouponGrant::getEndTime, new Date());
        List<MktCouponGrantVo> mktCouponGrants = mktCouponGrantMapper.selectVoList(wrapper);
        // 第一阶段：按范围过滤
        List<MktCouponGrantVo> filteredByScope = mktCouponGrants.stream()
                .filter(coupon -> {
                    ProductScopeEnum productScopeEnum = ProductScopeEnum.fromProductCode(coupon.getProductScope());
                    if (ObjectUtils.isEmpty(productScopeEnum) || productScopeEnum.equals(ProductScopeEnum.ALL)) {
                        return true;
                    } else {
                        return productScopeEnum.getProductCode().getCode().equals(remoteCouponGrantBo.getProductCode());
                    }
                })
                .filter(coupon -> {
                    // 按线路过滤
                    if (ObjectUtils.isNotEmpty(coupon.getLineId()) && Arrays.stream(coupon.getLineId().split(","))
                            .anyMatch(lineId -> remoteCouponGrantBo.getLineId().equals(lineId))) {
                        log.debug("线路匹配 {}", remoteCouponGrantBo.getLineId());
                        return true;
                    }
                    // 按城市过滤
                    return ObjectUtils.isNotEmpty(coupon.getCityCode()) && Arrays.stream(coupon.getCityCode().split(","))
                            .anyMatch(cityCode -> remoteCouponGrantBo.getCityCode().equals(cityCode));
                })
                .toList();

        log.debug("一阶段过滤后的优惠券 {}", filteredByScope);

        // 第二阶段：按价格匹配
        List<MktCouponGrantVo> lessOrEqual = filteredByScope.stream()
                // 满足使用门槛
                .filter(coupon -> remoteCouponGrantBo.getOrderPrice() <= coupon.getQuota())
                // 优惠额度小的优先
                .sorted(Comparator.comparing(MktCouponGrantVo::getQuota))
                .collect(Collectors.toList());

        if (!lessOrEqual.isEmpty()) {
            // 优先返回最大可用优惠
            lessOrEqual.get(0).setDefaulted(true);
            List<RemoteCouponGrantVo> remoteCouponGrantVos = BeanUtils.copyToList(lessOrEqual, RemoteCouponGrantVo.class);
            log.debug("01，获取可用优惠券结果 {}", remoteCouponGrantVos);
            return remoteCouponGrantVos;
        }

        // 若没有匹配到，再找额度 > 价格的最小优惠
        List<MktCouponGrantVo> greater = filteredByScope.stream()
                .filter(coupon -> remoteCouponGrantBo.getOrderPrice() > coupon.getQuota())
                // 额度大的优先
                .sorted(Comparator.comparing(MktCouponGrantVo::getQuota).reversed())
                .collect(Collectors.toList());
        if (!greater.isEmpty()) {
            greater.get(0).setDefaulted(true);
            List<RemoteCouponGrantVo> remoteCouponGrantVos = BeanUtils.copyToList(greater, RemoteCouponGrantVo.class);
            log.debug("02，获取可用优惠券结果 {}", remoteCouponGrantVos);
            return remoteCouponGrantVos;
        }
        return List.of();
    }

    /**
     * 批量获取优惠券发放信息
     *
     * @param ids
     * @return
     */
    @Override
    public List<RemoteCouponGrantVo> getCouponGrantByIds(List<Long> ids) {
        List<MktCouponGrantVo> mktCouponGrantVoList = mktCouponGrantService.queryByIds(ids);
        ArrayList<RemoteCouponGrantVo> remoteCouponGrantVos = new ArrayList<>();
        for (MktCouponGrantVo mktCouponGrantVo : mktCouponGrantVoList) {
            RemoteCouponGrantVo remoteCouponGrantVo = BeanUtils.copyProperties(mktCouponGrantVo, RemoteCouponGrantVo.class);
            remoteCouponGrantVo.setCouponName(mktCouponGrantVo.getCouponName());
            remoteCouponGrantVos.add(remoteCouponGrantVo);
        }
        return remoteCouponGrantVos;
    }
}

