package com.feidi.xx.cross.market.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.common.enums.market.TargetedCouponsStatusEnum;
import com.feidi.xx.cross.market.domain.bo.CouponIssuanceParam;
import com.feidi.xx.cross.market.domain.vo.MktCouponVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 定向放券表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "mkt_targeted_coupons", autoResultMap = true)
public class MktTargetedCoupons extends TenantEntity {

    @TableId(value = "id")
    private Long id;

    private String title;
    /**
     * 每人限领张数
     */
    private Integer perUserLimit;
    /**
     * 发放的用户手机号
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> phoneNumbers;


    /**
     * 优惠卷
     */
    private Long couponId;

    private String couponName;

    /**
     * 优惠类型[DiscountTypeEnum],0:立减券;1:满减券
     */
    private String couponType;

    /**
     * 状态，1：已发放，2：已撤销
     */
    private String status;

    /**
     * 优惠对象[CouponTargetEnum],0:线路专属；1:全平台通用；2:城市专属
     */
    private String target;

    /**
     * 面值，单位：分
     */
    private Long quota;

    /**
     * 过期时间（单位：秒）
     */
    private Long expireTime;

    private Date startTime;

    private Date endTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    public static MktTargetedCoupons create(MktCouponVo mktCouponVo, CouponIssuanceParam p) {
        MktTargetedCoupons targetedCoupons = new MktTargetedCoupons();
        targetedCoupons.setTitle(p.getTitle());
        targetedCoupons.setPerUserLimit(p.getCouponIds().get(mktCouponVo.getId()));
        targetedCoupons.setPhoneNumbers(p.getPassengerPhones().stream().toList());
        targetedCoupons.setStatus(TargetedCouponsStatusEnum.ISSUED.getCode());
        targetedCoupons.setCouponId(mktCouponVo.getId());
        targetedCoupons.setCouponName(mktCouponVo.getName());
        targetedCoupons.setCouponType(mktCouponVo.getDiscountType());
        targetedCoupons.setTarget(mktCouponVo.getTarget());
        targetedCoupons.setQuota(mktCouponVo.getQuota());
        targetedCoupons.setExpireTime(mktCouponVo.getExpireTime());
        targetedCoupons.setStartTime(mktCouponVo.getStartTime());
        targetedCoupons.setEndTime(mktCouponVo.getEndTime());
        return targetedCoupons;
    }
}
