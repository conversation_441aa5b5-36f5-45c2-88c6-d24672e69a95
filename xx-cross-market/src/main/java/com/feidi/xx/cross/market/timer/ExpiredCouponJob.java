package com.feidi.xx.cross.market.timer;

import com.feidi.xx.cross.market.service.IMktCouponGrantService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 优惠券过期定时器
 *
 * <AUTHOR>
 * @date 2025/4/29
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ExpiredCouponJob {

    private final IMktCouponGrantService mktCouponGrantService;

    /**
     *
     */
    @XxlJob("expiredCouponJob")
    public void expiredCouponJob() {
        if (log.isInfoEnabled()) {
            log.info("============== 优惠券过期定时器开始执行 ==============");
        }

        mktCouponGrantService.expiredCoupon();

        if (log.isInfoEnabled()) {
            log.info("============== 优惠券过期定时器执行结束 ==============");
        }
    }
}
