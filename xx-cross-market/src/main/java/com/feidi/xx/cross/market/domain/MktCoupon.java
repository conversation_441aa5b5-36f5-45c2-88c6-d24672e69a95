package com.feidi.xx.cross.market.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 优惠券对象 mkt_coupon
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mkt_coupon")
public class MktCoupon extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 代理id
     */
    private Long agentId;

    /**
     * 线路id
     */
    private String lineId;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 优惠卷编码
     */
    private String couponCode;

    /**
     * 优惠卷活动名称
     */
    private String activityName;

    /**
     * 优惠卷名称
     */
    private String name;

    /**
     * 优惠类型[DiscountTypeEnum]
     */
    private String discountType;

    /**
     * 额度
     */
    private Long quota;

    /**
     * 总数
     */
    private Integer total;

    /**
     * 余量
     */
    private Integer margin;

    /**
     * 优惠规则
     */
    private String rule;

    /**
     * 过期时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long expireTime;

    /**
     * 最大领取数量
     */
    private Integer maxNum;

    /**
     * 使用条件 满 x 元可用 满减卷
     */
    private String termsOfUse;

    /**
     * 领取方式[PaidTypeEnum]
     */
    private String paidType;

    /**
     * 优惠券状态 [CouponTemplateStatusEnum]
     */
    private String status;

    /**
     * 优惠对象[CouponTargetEnum]
     */
    private String target;

    /**
     * 优惠券来源 [CouponSourceEnum]
     */
    private String source;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 生效开始时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date startTime;

    /**
     * 生效结束时间
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date endTime;

    /**
     * 上下架状态：0=下架，1=上架
     */
    private String shelvesStatus;

    /**
     * 产品范围：0=全部，1=独享，2=拼车
     */
    private String productScope;
}
