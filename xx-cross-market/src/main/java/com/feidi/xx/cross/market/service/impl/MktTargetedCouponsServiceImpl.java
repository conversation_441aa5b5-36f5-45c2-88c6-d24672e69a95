package com.feidi.xx.cross.market.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.common.cache.market.manage.MktCacheManager;
import com.feidi.xx.cross.common.enums.market.CouponStatusEnum;
import com.feidi.xx.cross.common.enums.market.PaidTypeEnum;
import com.feidi.xx.cross.common.enums.market.TargetedCouponsStatusEnum;
import com.feidi.xx.cross.common.enums.market.UserCouponSourceEnum;
import com.feidi.xx.cross.market.domain.MktCouponGrant;
import com.feidi.xx.cross.market.domain.MktTargetedCoupons;
import com.feidi.xx.cross.market.domain.bo.CouponIssuanceParam;
import com.feidi.xx.cross.market.domain.bo.ImportPhoneNumbersBo;
import com.feidi.xx.cross.market.domain.bo.MktCouponGrantBo;
import com.feidi.xx.cross.market.domain.bo.MktTargetedCouponsBo;
import com.feidi.xx.cross.market.domain.vo.MktCouponVo;
import com.feidi.xx.cross.market.domain.vo.MktTargetedCouponsVo;
import com.feidi.xx.cross.market.domain.vo.UploadExcelFileVo;
import com.feidi.xx.cross.market.helper.NameSetterUtil;
import com.feidi.xx.cross.market.mapper.MktCouponGrantMapper;
import com.feidi.xx.cross.market.mapper.MktTargetedCouponsMapper;
import com.feidi.xx.cross.market.service.IMktCouponGrantService;
import com.feidi.xx.cross.market.service.IMktCouponService;
import com.feidi.xx.cross.market.service.IMktTargetedCouponsService;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 定向放券
 */
@RequiredArgsConstructor
@Service
public class MktTargetedCouponsServiceImpl implements IMktTargetedCouponsService {
    @Autowired
    private MktTargetedCouponsMapper baseMapper;
    @Autowired
    private MktCacheManager mktCacheManager;

    @Autowired
    private IMktCouponGrantService mktCouponGrantService;
    @Autowired
    private RemotePassengerService remotePassengerService;
    @Autowired
    private IMktCouponService mktCouponService;
    @Autowired
    private MktCouponGrantMapper mktCouponGrantMapper;

    @Override
    public TableDataInfo<MktTargetedCouponsVo> queryPageList(MktTargetedCouponsBo bo, PageQuery pageQuery) {
        //初始化
        if (ObjectUtils.isEmpty(pageQuery.getIsAsc())) {
            pageQuery.setIsAsc("desc");
        }
        if (ObjectUtils.isEmpty(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("create_time");
        }
        var lqw = Wrappers.<MktTargetedCoupons>lambdaQuery()
                .eq(ObjUtil.isNotNull(bo.getCouponId()), MktTargetedCoupons::getCouponId, bo.getCouponId())
                .eq(ObjUtil.isNotNull(bo.getTarget()), MktTargetedCoupons::getTarget, bo.getTarget())
                .eq(ObjUtil.isNotNull(bo.getStatus()), MktTargetedCoupons::getStatus, bo.getStatus())
                .likeLeft(StrUtil.isNotBlank(bo.getTitle()), MktTargetedCoupons::getTitle, bo.getTitle())
                .likeLeft(StrUtil.isNotBlank(bo.getCouponName()), MktTargetedCoupons::getCouponName, bo.getCouponName());
        var page = baseMapper.selectVoPage(pageQuery.build(), lqw);

        var couponVoMap = mktCouponService.queryMapByIds(page.getRecords().stream().map(MktTargetedCouponsVo::getCouponId).collect(Collectors.toSet()));
        NameSetterUtil.cityNameSetterByCode(couponVoMap.values(), MktCouponVo::getCityCodeList, MktCouponVo::setCityNames);
        NameSetterUtil.lineNameSetter(couponVoMap.values(), MktCouponVo::getLineIdList, MktCouponVo::setLineNames);
        page.getRecords().forEach(item -> {
            Optional.ofNullable(couponVoMap.get(item.getCouponId())).ifPresent(item::setMktCouponVo);
        });
        return TableDataInfo.build(page);
    }

    @Override
    public UploadExcelFileVo parserExcelData(List<ImportPhoneNumbersBo> list) {
        if (list.stream().anyMatch(item -> !PhoneUtil.isMobile(item.getPhoneNumber()))) {
            throw new RuntimeException("手机号格式错误");
        }
        String key = mktCacheManager.storeFileData(StreamUtils.toList(list, ImportPhoneNumbersBo::getPhoneNumber));
        return new UploadExcelFileVo(key);
    }

    @Override
    public void targetedCoupons(CouponIssuanceParam p) {
        //TODO R 考虑限制 单次可选的优惠券数量和限制发放用户数，性能优化
        //校验，并获取优惠券信息
        Map<Long, MktCouponVo> couponMap = preCheck(p);
        //存储发放表
        List<MktTargetedCoupons> mktTargetedCoupons = p.getCouponIds().keySet().stream().map(integer -> {
            MktCouponVo mktCouponVo = couponMap.get(integer);
            return MktTargetedCoupons.create(mktCouponVo, p);
        }).toList();
        baseMapper.insertBatch(mktTargetedCoupons);
        //key 优惠券id, value 优惠券发放表id
        Map<Long, Long> couponIdTcIdMap = StreamUtils.toMap(mktTargetedCoupons, MktTargetedCoupons::getCouponId, MktTargetedCoupons::getId);

        //老的发放逻辑
        Map<Long, Integer> couponIds = p.getCouponIds();
        couponIds.forEach((key, value) -> {
            for (Integer i = value; i > 0; i--) {
                ArrayList<Long> longs = new ArrayList<>();
                longs.add(key);
                MktCouponGrantBo bo = new MktCouponGrantBo();
                bo.setPaidType(PaidTypeEnum.ARTIFICIAL.getCode());
                bo.setPaidUserType(UserTypeEnum.SYS_USER.getUserType());
                bo.setPassengerPhones(p.getPassengerPhones().stream().toList());
                bo.setCouponIds(longs);
                bo.setSourceType(UserCouponSourceEnum.DIRECT.getCode());
                bo.setSourceId(couponIdTcIdMap.get(key));
                mktCouponGrantService.grantCoupon(bo);
            }
        });
    }

    private @NotNull Map<Long, MktCouponVo> preCheck(CouponIssuanceParam p) {
//       boolean b =  baseMapper.nameSame(p.getTitle());
//       if (b) {
//           throw new ServiceException("标题已存在!");
//       }
        if (p.getDataKey() != null) {
            List<String> fileData = mktCacheManager.getFileData(p.getDataKey());
            p.getPassengerPhones().addAll(fileData);
        }
        Set<String> phoneNumbers = p.getPassengerPhones().stream().filter(e -> !StrUtil.isBlankIfStr(e)).collect(Collectors.toSet());
        p.setPassengerPhones(phoneNumbers);
        if (CollUtil.isEmpty(p.getPassengerPhones())) {
            throw new ServiceException("手机号码为空");
        }
        //校验用户是否存在
        for (String passengerPhone : phoneNumbers) {
            RemotePassengerVo remotePassengerVo = remotePassengerService.queryByPhone(passengerPhone);
            if (ObjectUtils.isEmpty(remotePassengerVo)) {
//                throw new ServiceException("手机号为: " + passengerPhone + " 用户不存在");
                throw new ServiceException("文件内含有未在平台内注册的用户，无法导入，请检查后重试");
            }
        }
        Map<Long, MktCouponVo> couponMap = mktCouponService.queryMapByIds(p.getCouponIds().keySet());
        if (ObjectUtils.isEmpty(couponMap)) {
            throw new ServiceException("优惠券不存在");
        }
        couponMap.forEach((key, value) -> {
            Integer perUserLimit = p.getCouponIds().get(key);
            if (value.getMaxNum() != null && value.getMaxNum() > 0 && perUserLimit > value.getMaxNum()) {
                throw new ServiceException("优惠券 " + value.getName() + " 超出最大领取数量");
            }
            if (value.getMargin() < phoneNumbers.size() * perUserLimit) {
                throw new ServiceException("优惠券 " + value.getName() + " 余量不足");
            }
        });
        return couponMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revoke(MktTargetedCouponsBo p) {
        MktTargetedCoupons targetedCoupons = Optional.ofNullable(baseMapper.selectById(p.getId())).orElseThrow(() -> new ServiceException("记录不存在！"));
        //发放表修改状态
        var updateWrapper = Wrappers.<MktTargetedCoupons>lambdaUpdate()
                .eq(MktTargetedCoupons::getId, targetedCoupons.getId())
                .set(MktTargetedCoupons::getStatus, TargetedCouponsStatusEnum.REVOKE.getCode());
        baseMapper.update(updateWrapper);
        //用户券表设置为已作废
        mktCouponGrantMapper.revoke(UserCouponSourceEnum.DIRECT, targetedCoupons.getId(), targetedCoupons.getCouponId());

        //TODO R 优惠券表，库存增加
    }

    @Override
    public MktTargetedCouponsVo queryById(Long id) {
        MktTargetedCouponsVo mktTargetedCouponsVo = baseMapper.selectVoById(id);
        if  (mktTargetedCouponsVo == null) {
            return null;
        }
        var w1 = Wrappers.<MktCouponGrant>lambdaQuery()
                .select(MktCouponGrant::getId)
                .eq(MktCouponGrant::getCouponId, mktTargetedCouponsVo.getCouponId())
                .eq(MktCouponGrant::getUsingStatus, CouponStatusEnum.USED.getCode());
        mktTargetedCouponsVo.setQuantityUsed(mktCouponGrantMapper.selectCount(w1));

        var w2 = Wrappers.<MktCouponGrant>lambdaQuery()
                .select(MktCouponGrant::getId)
                .eq(MktCouponGrant::getCouponId, mktTargetedCouponsVo.getCouponId())
                .eq(MktCouponGrant::getUsingStatus, CouponStatusEnum.NOT_USED.getCode());
        mktTargetedCouponsVo.setQuantityAvailable(mktCouponGrantMapper.selectCount(w2));

        MktCouponVo mktCouponVo = mktCouponService.queryById(mktTargetedCouponsVo.getCouponId());
        mktTargetedCouponsVo.setMktCouponVo(mktCouponVo);
        mktTargetedCouponsVo.setTotal(Long.valueOf(mktCouponVo.getTotal()));
        return mktTargetedCouponsVo;
    }
}
