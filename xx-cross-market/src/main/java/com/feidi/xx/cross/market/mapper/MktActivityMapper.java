package com.feidi.xx.cross.market.mapper;

import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.common.enums.market.ActivityStatusEnum;
import com.feidi.xx.cross.market.domain.MktActivity;
import com.feidi.xx.cross.market.domain.vo.MktActivityVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;
import java.util.stream.Stream;

/**
 * 活动Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
public interface MktActivityMapper extends BaseMapperPlus<MktActivity, MktActivityVo> {

    default Long selectCountByCouponId(Long id, ActivityStatusEnum... activityStatusEnum) {
        var w = Wrappers.<MktActivity>lambdaQuery()
                .in(ArrayUtil.isNotEmpty(activityStatusEnum), MktActivity::getStatus, Stream.of(activityStatusEnum).map(ActivityStatusEnum::getCode).toList())
                .apply("JSON_CONTAINS(coupon_ids, JSON_ARRAY({0}))", id);
        return selectCount(w);
    }

    default boolean nameSame(Long id, String name) {
        var w = Wrappers.<MktActivity>lambdaQuery()
                .ne(id != null, MktActivity::getId, id)
                .eq(MktActivity::getName, name);
        return selectCount(w) > 0;
    }
}
