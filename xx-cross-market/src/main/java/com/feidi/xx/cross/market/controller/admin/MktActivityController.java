package com.feidi.xx.cross.market.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.market.domain.bo.MktActivityBo;
import com.feidi.xx.cross.market.domain.vo.MktActivityVo;
import com.feidi.xx.cross.market.service.IMktActivityService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 活动
 * 前端访问路由地址为:/market/activity
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/activity")
public class MktActivityController extends BaseController {

    private final IMktActivityService mktActivityService;

    /**
     * 查询活动列表
     */
    @Enum2TextAspect
    @SaCheckPermission("market:activity:list")
    @GetMapping("/list")
    public TableDataInfo<MktActivityVo> list(MktActivityBo bo, PageQuery pageQuery) {
        return mktActivityService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询活动列表 - 不分页
     */
    @SaCheckPermission("market:activity:list")
    @GetMapping("/list/all")
    public List<MktActivityVo> listAll(MktActivityBo bo) {
        return mktActivityService.queryList(bo);
    }

    /**
     * 导出活动列表
     */
    @SaCheckPermission("market:activity:export")
    @Log(title = "活动", businessType = BusinessType.EXPORT)
    @Download(name="活动",module = ModuleConstants.MARKET,mode="no")
    @PostMapping("/export")
    public Object export(MktActivityBo bo, HttpServletResponse response) {
        List<MktActivityVo> list = mktActivityService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(list, "活动", MktActivityVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取活动详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("market:activity:query")
    @GetMapping("/{id}")
    public R<MktActivityVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long id) {
        return R.ok(mktActivityService.queryById(id));
    }

    /**
     * 新增活动
     */
    @SaCheckPermission("market:activity:add")
    @Log(title = "活动", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MktActivityBo bo) {
        return toAjax(mktActivityService.insertByBo(bo));
    }

    /**
     * 修改活动
     */
    @SaCheckPermission("market:activity:edit")
    @Log(title = "活动", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MktActivityBo bo) {
        return toAjax(mktActivityService.updateByBo(bo));
    }

    /**
     * 删除活动
     *
     * @param ids 主键串`
     */
    @SaCheckPermission("market:activity:remove")
    @Log(title = "活动", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mktActivityService.deleteWithValidByIds(List.of(ids), true));
    }
}
