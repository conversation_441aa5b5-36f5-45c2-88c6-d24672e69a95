package com.feidi.xx.cross.market.controller.agent;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.market.domain.bo.MktCouponBo;
import com.feidi.xx.cross.market.domain.vo.MktCouponVo;
import com.feidi.xx.cross.market.service.IMktCouponService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 代理商 - 优惠券
 * 前端访问路由地址为:/market/agt/coupon
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/coupon")
public class AgtCouponController extends BaseController {

    private final IMktCouponService mktCouponService;

    /**
     * 查询优惠券列表
     */
    @GetMapping("/list")
    public TableDataInfo<MktCouponVo> list(MktCouponBo bo, PageQuery pageQuery) {
        bo.setAgentId(LoginHelper.getUserId());
        return mktCouponService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出优惠券列表
     */
    @Log(title = "优惠券", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MktCouponBo bo,HttpServletResponse response) {
        List<MktCouponVo> list = mktCouponService.queryList(bo);
        ExcelUtil.exportExcel(list, "优惠券", MktCouponVo.class, response);
    }

    /**
     * 获取优惠券详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<MktCouponVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mktCouponService.queryById(id));
    }

    /**
     * 新增优惠券
     */
    @Log(title = "优惠券", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MktCouponBo bo) {
        bo.setAgentId(LoginHelper.getUserId());
        return toAjax(mktCouponService.insertByBo(bo));
    }

    /**
     * 修改优惠券
     */
    @Log(title = "优惠券", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MktCouponBo bo) {
        return toAjax(mktCouponService.updateByBo(bo));
    }

    /**
     * 删除优惠券
     *
     * @param ids 主键串
     */
    @Log(title = "优惠券", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mktCouponService.deleteWithValidByIds(List.of(ids), true));
    }
}
