package com.feidi.xx.cross.market.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.io.Serial;

/**
 * 拉新记录对象 mkt_invite_record
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mkt_invite_record")
public class MktInviteRecord extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 流水编号
     */
    private String flowNo;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 拉新类型[InviteTypeEnum]
     */
    private String inviteType;

    /**
     * 奖励类型[RewardTypeEnum]
     */
    private String rewardType;

    /**
     * 奖励比例
     */
    private BigDecimal rewardRate;

    /**
     * 奖励金额
     */
    private Long rewardPrice;


    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
