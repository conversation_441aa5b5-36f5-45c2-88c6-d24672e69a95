package com.feidi.xx.cross.market.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharPool;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.feidi.xx.common.core.cache.system.SysDistrictCacheVo;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.enums.SystemCacheKeyEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.market.manage.MktCacheManager;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import com.feidi.xx.cross.common.enums.market.*;
import com.feidi.xx.cross.market.domain.MktCoupon;
import com.feidi.xx.cross.market.domain.bo.MktCouponBo;
import com.feidi.xx.cross.market.domain.bo.MktCouponGrantBo;
import com.feidi.xx.cross.market.domain.vo.MktCouponVo;
import com.feidi.xx.cross.market.service.IMktCouponGrantService;
import com.feidi.xx.cross.market.service.IMktCouponService;
import com.feidi.xx.cross.operate.api.domain.city.vo.RemoteCityVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import com.feidi.xx.cross.market.domain.bo.MktActivityBo;
import com.feidi.xx.cross.market.domain.vo.MktActivityVo;
import com.feidi.xx.cross.market.domain.MktActivity;
import com.feidi.xx.cross.market.mapper.MktActivityMapper;
import com.feidi.xx.cross.market.service.IMktActivityService;
import com.feidi.xx.common.core.utils.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MktActivityServiceImpl implements IMktActivityService {

    private final MktActivityMapper baseMapper;
    private final IMktCouponService mktCouponService;
    private final MktCacheManager mktCacheManager;

    /**
     * 查询活动
     *
     * @param id 主键
     * @return 活动
     */
    @Override
    public MktActivityVo queryById(Long id) {
        MktActivityVo activityVo = baseMapper.selectVoById(id);
        if (ObjectUtils.isNotEmpty(activityVo) && CollUtil.isNotEmpty(activityVo.getCouponIds())) {
            activityVo.setCoupons(mktCouponService.queryListByIds(new HashSet<>(activityVo.getCouponIds())));
        }
        return activityVo;
    }

    /**
     * 分页查询活动列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动分页列表
     */
    @Override
    public TableDataInfo<MktActivityVo> queryPageList(MktActivityBo bo, PageQuery pageQuery) {
        //初始化
        if (ObjectUtils.isEmpty(pageQuery.getIsAsc())) {
            pageQuery.setIsAsc("desc");
        }
        if (ObjectUtils.isEmpty(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("create_time");
        }

        LambdaQueryWrapper<MktActivity> lqw = buildQueryWrapper(bo);
        Page<MktActivityVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的活动列表
     *
     * @param bo 查询条件
     * @return 活动列表
     */
    @Override
    public List<MktActivityVo> queryList(MktActivityBo bo) {
        LambdaQueryWrapper<MktActivity> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MktActivity> buildQueryWrapper(MktActivityBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MktActivity> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getAgentId() != null, MktActivity::getAgentId, bo.getAgentId());
        lqw.like(StringUtils.isNotBlank(bo.getName()), MktActivity::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getAdImage()), MktActivity::getAdImage, bo.getAdImage());
        lqw.eq(StringUtils.isNotBlank(bo.getManner()), MktActivity::getManner, bo.getManner());
        lqw.eq(StringUtils.isNotBlank(bo.getScope()), MktActivity::getScope, bo.getScope());
        lqw.eq(StringUtils.isNotBlank(bo.getColony()), MktActivity::getColony, bo.getColony());
        lqw.eq(StringUtils.isNotBlank(bo.getIssuingChannel()), MktActivity::getIssuingChannel, bo.getIssuingChannel());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), MktActivity::getStatus, bo.getStatus());
        lqw.ge(bo.getStartTime() != null, MktActivity::getStartTime, bo.getStartTime());
        lqw.le(bo.getEndTime() != null, MktActivity::getEndTime, bo.getEndTime());
        lqw.ge(bo.getNowTime() != null, MktActivity::getEndTime, bo.getNowTime());
        lqw.orderByDesc(MktActivity::getStatus);
        lqw.orderByDesc(MktActivity::getCreateTime);
        lqw.eq(bo.getShelvesStatus() != null, MktActivity::getShelvesStatus, bo.getShelvesStatus());
        return lqw;
    }

    /**
     * 新增活动
     *
     * @param bo 活动
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MktActivityBo bo) {
        if (bo.getStartTime() != null && bo.getStartTime().before(new Date())) {
            bo.setStatus(ActivityStatusEnum.STARTING.getCode());
        }
        MktActivity add = MapstructUtils.convert(bo, MktActivity.class);
        validEntityBeforeSaveV2(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改活动
     *
     * @param bo 活动
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MktActivityBo bo) {
        if (bo.getStartTime() != null && bo.getStartTime().before(new Date())) {
            bo.setStatus(ActivityStatusEnum.STARTING.getCode());
        }
        MktActivity update = MapstructUtils.convert(bo, MktActivity.class);
        validEntityBeforeSaveV2(update);
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            MktActivityVo mktActivityVo = baseMapper.selectVoById(bo.getId());
            Date endTime = mktActivityVo.getEndTime();
            long now = System.currentTimeMillis();
            if (endTime.getTime() > now) {
                long diffSeconds = (endTime.getTime() - now) / 1000;
                MktCouponBo mktCouponBo = new MktCouponBo();
                mktCouponBo.setActivityId(bo.getId());
                List<MktCouponVo> mktCouponVos = mktCouponService.queryCoupon(mktCouponBo);
                for (MktCouponVo mktCouponVo : mktCouponVos) {
                    mktCacheManager.deleteCouponStock(mktCouponVo.getId());
                    mktCacheManager.createCouponStock(mktCouponVo.getId(), mktCouponVo.getMargin(), mktCouponVo.getMaxNum(), diffSeconds);
                }
            }

        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MktActivity entity) {
        //TODO 做一些数据校验,如唯一约束
        if (ObjectUtils.isNotEmpty(entity.getId())) {
            if (entity.getGrantScope().equals(GrantScopeEnum.CITY.getCode())) {
                String cityCode = entity.getCityCode();
                String[] split = cityCode.split(",");
                for (String s : split) {
                    LambdaQueryWrapper<MktActivity> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(MktActivity::getGrantScope, entity.getGrantScope())
                            .eq(MktActivity::getType, entity.getType())
                            .eq(MktActivity::getGrantScope, entity.getGrantScope())
                            .like(ObjectUtils.isNotEmpty(entity.getCityCode()), MktActivity::getCityCode, s)
                            .nested(queryWrapper1 -> queryWrapper1.ge(MktActivity::getStartTime, entity.getStartTime())
                                    .or()
                                    .between(MktActivity::getEndTime, entity.getStartTime(), entity.getEndTime())
                            );
                    List<MktActivityVo> mktActivityVos = baseMapper.selectVoList(queryWrapper);
                    for (MktActivityVo mktActivityVo : mktActivityVos) {
                        if (ObjectUtils.isNotEmpty(mktActivityVo)) {
                            if (!mktActivityVo.getId().equals(entity.getId())) {
                                throw new ServiceException("该时间内地区活动已存在");
                            }
                        }
                    }
                }
            } else {
                LambdaQueryWrapper<MktActivity> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MktActivity::getGrantScope, entity.getGrantScope())
                        .eq(MktActivity::getType, entity.getType())
                        .eq(MktActivity::getGrantScope, entity.getGrantScope())
                        .nested(queryWrapper1 -> queryWrapper1.ge(MktActivity::getStartTime, entity.getStartTime())
                                .or()
                                .between(MktActivity::getEndTime, entity.getStartTime(), entity.getEndTime())
                        );
                List<MktActivityVo> mktActivityVos = baseMapper.selectVoList(queryWrapper);
                for (MktActivityVo mktActivityVo : mktActivityVos) {
                    if (ObjectUtils.isNotEmpty(mktActivityVo)) {
                        if (!mktActivityVo.getId().equals(entity.getId())) {
                            throw new ServiceException("该时间内地区活动已存在");
                        }
                    }
                }

            }
        } else {
            if (entity.getGrantScope().equals(GrantScopeEnum.CITY.getCode())) {
                String cityCode = entity.getCityCode();
                String[] split = cityCode.split(",");
                for (String s : split) {
                    LambdaQueryWrapper<MktActivity> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(MktActivity::getGrantScope, entity.getGrantScope())
                            .eq(MktActivity::getType, entity.getType())
                            .eq(MktActivity::getGrantScope, entity.getGrantScope())
                            .like(ObjectUtils.isNotEmpty(entity.getCityCode()), MktActivity::getCityCode, s)
                            .between(MktActivity::getStartTime, entity.getStartTime(), entity.getEndTime())
                            .nested(queryWrapper1 -> queryWrapper1.ge(MktActivity::getStartTime, entity.getStartTime())
                                    .or()
                                    .between(MktActivity::getEndTime, entity.getStartTime(), entity.getEndTime())
                            );

                    if (baseMapper.selectCount(queryWrapper) > 0) {
                        throw new ServiceException("该时间内地区活动已存在");
                    }
                }
            } else {
                LambdaQueryWrapper<MktActivity> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MktActivity::getGrantScope, entity.getGrantScope())
                        .eq(MktActivity::getType, entity.getType())
                        .eq(MktActivity::getGrantScope, entity.getGrantScope())
                        .nested(queryWrapper1 -> queryWrapper1.ge(MktActivity::getStartTime, entity.getStartTime())
                                .or()
                                .between(MktActivity::getEndTime, entity.getStartTime(), entity.getEndTime())
                        );
                if (baseMapper.selectCount(queryWrapper) > 0) {
                    throw new ServiceException("该时间内地区活动已存在");
                }
            }

        }
    }

    /**
     * 前置校验
     *
     * @param entity
     */
    private void validEntityBeforeSaveV2(MktActivity entity) {
        //优惠券名称重复校验
        boolean b = baseMapper.nameSame(entity.getId(), entity.getName());
        if (b) {
            throw new ServiceException("名称已存在");
        }
        if (entity.getEndTime().getTime() < System.currentTimeMillis()) {
            throw new ServiceException("结束时间不能小于当前时间");
        }
        //结束时间不能小于等于开始时间
        if (entity.getEndTime().getTime() <= entity.getStartTime().getTime()) {
            throw new ServiceException("结束时间不能小于等于开始时间");
        }

        //新增时，若为上架;编辑时，若为上架
        if (entity.getShelvesStatus().equals(ShelvesStatusEnum.ONLINE.getCode())) {
            var w = Wrappers.<MktActivity>lambdaQuery()
                    .eq(MktActivity::getStartTime, entity.getStartTime())
                    .eq(MktActivity::getEndTime, entity.getEndTime())
                    .eq(MktActivity::getShelvesStatus, ShelvesStatusEnum.ONLINE.getCode())
                    .ne(MktActivity::getStatus, ActivityStatusEnum.ENDED.getCode())
                    .ne(entity.getId() != null, MktActivity::getId, entity.getId());
            if (baseMapper.selectCount(w) > 0) {
                throw new ServiceException("该时间内活动已存在");
            }
        }
        if (entity.getId() != null) {
            MktActivity mktActivity = baseMapper.selectById(entity.getId());
            //已经开始的活动，只能进行有限的字段
            if (!ActivityStatusEnum.WAITING.getCode().equals(mktActivity.getStatus())) {
                if (!CollUtil.isEqualList(mktActivity.getCouponIds(), entity.getCouponIds()) ||
                        !mktActivity.getGrantScope().equals(entity.getGrantScope())) {
                    throw new ServiceException("当前活动状态下不可编辑！");
                }
            }
        }

        //冗余卡券绑定的城市code和线路id
        if (CollUtil.isEmpty(entity.getCouponIds())) {
            return;
        }
        var couponIds = new HashSet<>(entity.getCouponIds());
        entity.setCouponIds(couponIds.stream().toList());
        List<String> lineIds = new ArrayList<>();
        List<String> cityCodes = new ArrayList<>();
        List<MktCouponVo> couponVoList = mktCouponService.queryListByIds(couponIds);
        for (MktCouponVo couponVo : couponVoList) {
            if (StrUtil.isNotBlank(couponVo.getLineId())) {
                lineIds.addAll(StrUtil.split(couponVo.getLineId(), CharPool.COMMA));
            }
            if (StrUtil.isNotBlank(couponVo.getCityCode())) {
                cityCodes.addAll(StrUtil.split(couponVo.getCityCode(), CharPool.COMMA));
            }
        }
        entity.setLineIds(lineIds);
        entity.setCityCode(String.join(StrPool.COMMA, cityCodes));
    }

    /**
     * 校验并批量删除活动信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        if (CollUtil.isEmpty(ids)) {
            return false;
        }

        var w = Wrappers.<MktActivity>lambdaQuery().in(MktActivity::getId, ids).eq(MktActivity::getStatus, ActivityStatusEnum.STARTING.getCode());
        Long l = baseMapper.selectCount(w);
        if (l > 0) {
            throw new ServiceException("已生效的活动不允许被删除!");
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public void updateStatusTask() {
        LocalDateTime now = LocalDateTime.now();
        // 将状态为待开始，且开始时间已到、结束时间未到的任务改为（进行中）
        var toRunning = Wrappers.lambdaUpdate(MktActivity.class)
                .eq(MktActivity::getStatus, ActivityStatusEnum.WAITING.getCode())
                .le(MktActivity::getStartTime, now)
                .gt(MktActivity::getEndTime, now)
                .set(MktActivity::getStatus, ActivityStatusEnum.STARTING.getCode());
        int running = baseMapper.update(toRunning);
        log.info("将状态为待开始，且开始时间已到、结束时间未到的任务改为（进行中），数量为：{}", running);

        // 将未结束的任务（状态不为结束），且结束时间已到的任务改为（已结束）
        var toFinished = Wrappers.lambdaUpdate(MktActivity.class)
                .ne(MktActivity::getStatus, ActivityStatusEnum.ENDED.getCode())
                .le(MktActivity::getEndTime, now)
                .set(MktActivity::getStatus, ActivityStatusEnum.ENDED.getCode());
        int finished = baseMapper.update(null, toFinished);
        log.info("将未结束的任务（状态不为结束），且结束时间已到的任务改为（已结束），数量为：{}", finished);
    }
}
