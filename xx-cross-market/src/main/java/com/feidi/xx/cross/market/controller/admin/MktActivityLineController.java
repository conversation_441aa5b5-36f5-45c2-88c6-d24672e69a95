package com.feidi.xx.cross.market.controller.admin;

import java.util.List;

import com.feidi.xx.cross.market.domain.bo.MktActivityLineForm;
import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.cross.market.domain.vo.MktActivityLineVo;
import com.feidi.xx.cross.market.domain.bo.MktActivityLineBo;
import com.feidi.xx.cross.market.service.IMktActivityLineService;

/**
 * 后台 - 活动线路
 * 前端访问路由地址为:/market/activityLine
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/activity/line")
public class MktActivityLineController extends BaseController {

    private final IMktActivityLineService mktActivityLineService;

    /**
     * 查询活动线路列表
     */
    @SaCheckPermission("market:activityLine:list")
    @GetMapping("/list")
    public List<MktActivityLineVo> list(MktActivityLineBo bo) {
        return mktActivityLineService.queryList(bo);
    }

    /**
     * 新增活动线路
     */
    @SaCheckPermission("market:activityLine:add")
    @Log(title = "活动线路", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/{activityId}")
    public R<Void> add(@NotNull(message = "主键不能为空")
                           @PathVariable Long activityId,
                       @Validated(AddGroup.class) @RequestBody List<MktActivityLineForm> bo) {

        return toAjax(mktActivityLineService.bindActivityLine(activityId, bo));
    }

}
