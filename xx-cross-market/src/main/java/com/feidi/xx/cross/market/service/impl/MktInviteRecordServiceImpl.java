package com.feidi.xx.cross.market.service.impl;

import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.common.cache.market.manage.MktCacheManager;
import com.feidi.xx.cross.common.enums.finance.FlowTypeEnum;
import com.feidi.xx.cross.finance.api.RemoteFlowService;
import com.feidi.xx.cross.finance.api.domain.bo.RemoteFlowBo;
import com.feidi.xx.cross.finance.api.domain.vo.RemoteFlowVo;
import com.feidi.xx.cross.market.api.domain.RemoteMktInviteConfigVo;
import com.feidi.xx.cross.market.domain.MktInviteRecord;
import com.feidi.xx.cross.market.domain.bo.MktInviteRecordBo;
import com.feidi.xx.cross.market.domain.vo.DriverInviteCountVo;
import com.feidi.xx.cross.market.domain.vo.MktInviteRecordVo;
import com.feidi.xx.cross.market.mapper.MktInviteRecordMapper;
import com.feidi.xx.cross.market.service.IMktInviteRecordService;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDetailVo;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import com.feidi.xx.common.core.utils.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 拉新记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@RequiredArgsConstructor
@Service
public class MktInviteRecordServiceImpl implements IMktInviteRecordService {

    private final MktInviteRecordMapper baseMapper;

    private final MktCacheManager mktCacheManager;


    @DubboReference
    private final RemoteOrderService remoteOrderService;

    @DubboReference
    private final RemotePassengerService remotePassengerService;

    @DubboReference
    private final RemoteFlowService remoteFlowService;

    /**
     * 查询拉新记录
     *
     * @param id 主键
     * @return 拉新记录
     */
    @Override
    public MktInviteRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询拉新记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 拉新记录分页列表
     */
    @Override
    public TableDataInfo<MktInviteRecordVo> queryPageList(MktInviteRecordBo bo, PageQuery pageQuery) {
        //初始化
        if (ObjectUtils.isNull(pageQuery.getIsAsc())) {
            pageQuery.setIsAsc("desc");
        }
        if (ObjectUtils.isNull(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("create_time");
        }
        LambdaQueryWrapper<MktInviteRecord> lqw = buildQueryWrapper(bo);
        Page<MktInviteRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        if (result.getRecords() != null) {
                result.getRecords().forEach(item -> {
                RemoteOrderDetailVo remoteOrderDetailVo = remoteOrderService.queryById(item.getOrderId());
                if (remoteOrderDetailVo != null) {
                    item.setOrderPrice(remoteOrderDetailVo.getOrderPrice());
                    item.setOrderFinishTime(remoteOrderDetailVo.getFinishTime());
                    RemoteMktInviteConfigVo inviteConfigByAgentId = mktCacheManager.getInviteConfigByAgentId(item.getAgentId());
                    if (inviteConfigByAgentId != null) {
                        item.setCompanyName(inviteConfigByAgentId.getCompanyName());
                    }
                }
            });
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的拉新记录列表
     *
     * @param bo 查询条件
     * @return 拉新记录列表
     */
    @Override
    public List<MktInviteRecordVo> queryList(MktInviteRecordBo bo) {
        LambdaQueryWrapper<MktInviteRecord> lqw = buildQueryWrapper(bo);
        List<MktInviteRecordVo> inviteRecordVos = baseMapper.selectVoList(lqw);
        if (inviteRecordVos != null) {
            inviteRecordVos.forEach(item -> {
                RemoteOrderDetailVo remoteOrderDetailVo = remoteOrderService.queryById(item.getOrderId());
                if (remoteOrderDetailVo != null) {
                    item.setOrderPrice(remoteOrderDetailVo.getOrderPrice());
                    item.setOrderFinishTime(remoteOrderDetailVo.getFinishTime());
                    RemoteMktInviteConfigVo inviteConfigByAgentId = mktCacheManager.getInviteConfigByAgentId(item.getAgentId());
                    if (inviteConfigByAgentId != null) {
                        item.setCompanyName(inviteConfigByAgentId.getCompanyName());
                    }
                    double mul = ArithUtils.mul(item.getRewardRate().doubleValue(), 100L);
                    item.setRewardRate(new BigDecimal(mul));
                    item.setInviteType(item.getRewardType());


                }
            });
        }
        return inviteRecordVos;
    }

    private LambdaQueryWrapper<MktInviteRecord> buildQueryWrapper(MktInviteRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MktInviteRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(ObjectUtils.isNotNull(bo.getAgentId()), MktInviteRecord::getAgentId, bo.getAgentId());
        lqw.eq(ObjectUtils.isNotNull(bo.getDriverId()), MktInviteRecord::getDriverId, bo.getDriverId());
        lqw.like(ObjectUtils.isNotNull(bo.getFlowNo()), MktInviteRecord::getFlowNo, bo.getFlowNo());
        lqw.eq(bo.getOrderId() != null, MktInviteRecord::getOrderId, bo.getOrderId());
        lqw.eq(ObjectUtils.isNotNull(bo.getPassengerId()), MktInviteRecord::getPassengerId, bo.getPassengerId());
        lqw.eq(StringUtils.isNotBlank(bo.getInviteType()), MktInviteRecord::getInviteType, bo.getInviteType());
        lqw.eq(StringUtils.isNotBlank(bo.getRewardType()), MktInviteRecord::getRewardType, bo.getRewardType());
        lqw.eq(bo.getRewardRate() != null, MktInviteRecord::getRewardRate, bo.getRewardRate());
        lqw.gt( MktInviteRecord::getRewardPrice,0);
        lqw.between(StringUtils.isNotBlank(bo.getStartCreateTime()) && StringUtils.isNotBlank(bo.getEndCreateTime()),MktInviteRecord::getCreateTime, bo.getStartCreateTime(), bo.getEndCreateTime());
        return lqw;
    }



    /**
     * 修改拉新记录
     *
     * @param bo 拉新记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MktInviteRecordBo bo) {
        MktInviteRecord update = MapstructUtils.convert(bo, MktInviteRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MktInviteRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除拉新记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public DriverInviteCountVo inviteRecordCount(Long driverId) {
        //根据司机id查询邀请乘客数量
        DriverInviteCountVo driverInviteCountVo = new DriverInviteCountVo();
        Integer countByDriverId = remotePassengerService.getCountByDriverId(driverId);
        driverInviteCountVo.setSuccessInvite(countByDriverId);
        RemoteFlowBo remoteFlowBo = new RemoteFlowBo();
        remoteFlowBo.setDriverId(driverId);
        remoteFlowBo.setType(FlowTypeEnum.INVITE_ORDER_REWARD.getCode());
        List<RemoteFlowVo> remoteFlowVos = remoteFlowService.listByBo(remoteFlowBo);
        RemoteFlowBo complaintRemoteFlowBo = new RemoteFlowBo();
        complaintRemoteFlowBo.setDriverId(driverId);
        complaintRemoteFlowBo.setType(FlowTypeEnum.COMPENSATION_REFUND.getCode());
        List<RemoteFlowVo> complaintRemoteFlowVos = remoteFlowService.listByBo(complaintRemoteFlowBo);
        Long totalRewardAmount = remoteFlowVos.stream()
                .mapToLong(RemoteFlowVo::getAmount)
                .sum(); // 计算奖励总额

        Long totalComplaintAmount = complaintRemoteFlowVos.stream()
                .mapToLong(RemoteFlowVo::getAmount)
                .sum(); // 计算客诉总额
        Long finalAmount =   ArithUtils.sub(totalRewardAmount,totalComplaintAmount);
        driverInviteCountVo.setAmount(finalAmount);
        return driverInviteCountVo;
    }

    @Override
    public List<RemoteDriverVo> query() {
        List<RemoteDriverVo> remoteDriverVos = new ArrayList<>();
        Map<String, String> map = new HashMap<>();

        // 添加数据到 Map
        map.put("李师傅", "6932");
        map.put("王师傅", "9909");
        map.put("赵师傅", "4306");
        map.put("孙师傅", "5724");
        map.put("钱师傅", "6098");
        map.put("周师傅", "4981");
        map.put("吴师傅", "0991");
        map.put("郑师傅", "3713");
        map.put("冯师傅", "4485");
        map.put("陈师傅", "0768");

        // 将 Map 中的数据转换为 RemoteDriverVo 对象并添加到 List 中
        for (Map.Entry<String, String> entry : map.entrySet()) {
            RemoteDriverVo remoteDriverVo = new RemoteDriverVo();
            remoteDriverVo.setName(entry.getKey());
            remoteDriverVo.setPhone(entry.getValue());
            remoteDriverVos.add(remoteDriverVo);
        }

        return remoteDriverVos;
    }


}
