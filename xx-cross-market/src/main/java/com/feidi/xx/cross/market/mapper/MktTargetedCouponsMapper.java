package com.feidi.xx.cross.market.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.common.enums.market.TargetedCouponsStatusEnum;
import com.feidi.xx.cross.market.domain.MktTargetedCoupons;
import com.feidi.xx.cross.market.domain.vo.MktTargetedCouponsVo;
import jakarta.validation.constraints.NotBlank;

/**
 * 定向发券Mapper接口
 */
public interface MktTargetedCouponsMapper extends BaseMapperPlus<MktTargetedCoupons, MktTargetedCouponsVo> {
    default Long selectCountByCouponId(Long id, TargetedCouponsStatusEnum targetedCouponsStatusEnum) {
        var w = Wrappers.<MktTargetedCoupons>lambdaQuery()
                .eq(MktTargetedCoupons::getCouponId, id)
                .eq(MktTargetedCoupons::getStatus, targetedCouponsStatusEnum.getCode());
        return selectCount(w);
    }

    default boolean nameSame(String title) {
        var w = Wrappers.<MktTargetedCoupons>lambdaQuery()
                .eq(MktTargetedCoupons::getTitle, title);
        return selectCount(w) > 0;
    }
}