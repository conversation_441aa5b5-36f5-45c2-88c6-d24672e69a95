package com.feidi.xx.cross.market.controller.member;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.common.enums.market.PaidTypeEnum;
import com.feidi.xx.cross.market.domain.bo.MktCouponGrantBo;
import com.feidi.xx.cross.market.domain.pojo.coupon.GrantForm;
import com.feidi.xx.cross.market.domain.vo.MktCouponGrantVo;
import com.feidi.xx.cross.market.domain.vo.MktCouponVo;
import com.feidi.xx.cross.market.service.IMktCouponGrantService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 乘客端 - 优惠券
 * 前端访问路由地址为:/market/mbr/coupon/grant
 *
 * <AUTHOR>
 * @date 2025-03-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.MEMBER_ROUTE_PREFIX + "/coupon/grant")
public class MbrCouponController extends BaseController {

    private final IMktCouponGrantService mktCouponGrantService;

    /**
     * 乘客 优惠券列表
     */

    @GetMapping("/list")
    @Enum2TextAspect
    public TableDataInfo<MktCouponGrantVo> list(MktCouponGrantBo bo, PageQuery pageQuery) {

        Long userId = LoginHelper.getUserId();
        bo.setPassengerId(userId);
        //R 过滤掉已作废的
        bo.setPassengerQuery(true);
        return mktCouponGrantService.queryPageList(bo, pageQuery);
    }

    /**
     * 新增优惠券发放
     */
    @Log(title = "优惠券-领取", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GrantForm grantForm) {

        MktCouponGrantBo bo = MapstructUtils.convert(grantForm, MktCouponGrantBo.class);

        bo.setPaidType(PaidTypeEnum.GET.getCode());
        bo.setPaidUserType(UserTypeEnum.PASSENGER_USER.getUserType());
        String userPhone = LoginHelper.getUserPhone();
        bo.setPassengerPhones(new ArrayList<String>() {{
            add(userPhone);
        }});
        mktCouponGrantService.grantCoupon(bo);
        return R.ok();
    }

    /**
     * 获取当前城市下优惠券信息
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return 优惠券信息
     */
    @Enum2TextAspect
    @GetMapping("/queryCouponList")
    public R<List<MktCouponVo>> queryCouponList(@RequestParam(required = false) String longitude, @RequestParam(required = false) String latitude) {
        return R.ok(mktCouponGrantService.queryCouponList(longitude, latitude));
    }

}
