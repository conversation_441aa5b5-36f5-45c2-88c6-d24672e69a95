package com.feidi.xx.cross.market.controller.admin;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.PhoneUtil;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.market.domain.bo.CouponIssuanceParam;
import com.feidi.xx.cross.market.domain.bo.ImportPhoneNumbersBo;
import com.feidi.xx.cross.market.domain.bo.MktTargetedCouponsBo;
import com.feidi.xx.cross.market.domain.vo.MktTargetedCouponsVo;
import com.feidi.xx.cross.market.domain.vo.UploadExcelFileVo;
import com.feidi.xx.cross.market.service.IMktTargetedCouponsService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 后台-定向发放列表
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/targetedCoupons")
public class MktTargetedCouponsController {
    @Autowired
    private IMktTargetedCouponsService service;

    /**
     * 查询优惠券发放记录
     */
    @Enum2TextAspect
    @GetMapping("/list")
//    @SaCheckPermission("market:coupon:list")
    public TableDataInfo<MktTargetedCouponsVo> list(MktTargetedCouponsBo bo, PageQuery pageQuery) {
        return service.queryPageList(bo, pageQuery);
    }


    /**
     * 上传文件
     *
     * @param file
     * @return
     */
    @PostMapping(value = "uploadExcelFile", consumes = "multipart/form-data")
    public R<UploadExcelFileVo> uploadExcelFile(@NotNull @RequestParam("file") MultipartFile file) throws IOException {
        try (var is = file.getInputStream()) {
            List<ImportPhoneNumbersBo> list = ExcelUtil.importExcel(is, ImportPhoneNumbersBo.class);
            if (CollUtil.isEmpty(list)) {
                return R.fail("解析失败，文件内容为空！");
            }
            return R.ok(service.parserExcelData(list));
        }
    }

    /**
     * 优惠券发放
     */
    @Log(title = "优惠券发放", businessType = BusinessType.INSERT)
//    @SaCheckPermission("market:couponGrant:add")
    @PostMapping("issuance")
    @RepeatSubmit()
    public R<Void> issuance(@RequestBody @Validated CouponIssuanceParam p) {
        if (p.getPassengerPhones().stream().anyMatch(item -> !PhoneUtil.isMobile(item))) {
            throw new RuntimeException("手机号格式错误");
        }
        service.targetedCoupons(p);
        return R.ok();
    }

    /**
     * 撤销
     *
     * @param p
     * @return
     */
    @Log(title = "优惠券发放撤销", businessType = BusinessType.UPDATE)
    @PostMapping("revoke")
    @RepeatSubmit()
    public R<Void> revoke(@RequestBody @Validated MktTargetedCouponsBo p) {
        Assert.notNull(p.getId(), "请选择要撤销的优惠券！");
        service.revoke(p);
        return R.ok();
    }

    /**
     * 放券详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public R<MktTargetedCouponsVo> queryById(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(service.queryById(id));
    }
}
