package com.feidi.xx.cross.market.domain.bo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import lombok.Data;

import java.io.Serializable;


/**
 * 活动线路视图对象 mkt_activity_line
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Data
@ExcelIgnoreUnannotated
public class MktActivityLineForm implements Serializable {

    /**
     * 线路
     */
    @ExcelProperty(value = "线路")
    private Long lineId;

    /**
     * 状态
     */
    private String status;

}
