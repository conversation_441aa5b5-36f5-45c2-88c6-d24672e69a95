package com.feidi.xx.cross.market.service;

import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.cross.market.domain.bo.MktInviteConfigBo;
import com.feidi.xx.cross.market.domain.vo.MktInviteConfigVo;

import java.util.Collection;
import java.util.List;

/**
 * 邀请有奖配置Service接口
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
public interface IMktInviteConfigService {

    /**
     * 查询邀请有奖配置
     *
     * @param id 主键
     * @return 邀请有奖配置
     */
    MktInviteConfigVo queryById(Long id);

    /**
     * 分页查询邀请有奖配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 邀请有奖配置分页列表
     */
    TableDataInfo<MktInviteConfigVo> queryPageList(MktInviteConfigBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的邀请有奖配置列表
     *
     * @param bo 查询条件
     * @return 邀请有奖配置列表
     */
    List<MktInviteConfigVo> queryList(MktInviteConfigBo bo);

    /**
     * 新增邀请有奖配置
     *
     * @param bo 邀请有奖配置
     * @return 是否新增成功
     */
    Boolean insertByBo(MktInviteConfigBo bo);

    /**
     * 修改邀请有奖配置
     *
     * @param bo 邀请有奖配置
     * @return 是否修改成功
     */
    Boolean updateByBo(MktInviteConfigBo bo);

    /**
     * 校验并批量删除邀请有奖配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
