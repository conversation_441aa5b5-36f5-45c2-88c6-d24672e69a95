package com.feidi.xx.cross.market.domain.vo;

import cn.hutool.core.text.CharPool;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.cross.common.enums.market.CouponStatusEnum;
import com.feidi.xx.cross.common.enums.market.DiscountTypeEnum;
import com.feidi.xx.cross.common.enums.market.PaidTypeEnum;
import com.feidi.xx.cross.market.domain.MktCouponGrant;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 优惠券发放视图对象 mkt_coupon_grant
 *
 * <AUTHOR>
 * @date 2025-03-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MktCouponGrant.class)
public class MktCouponGrantVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 乘客
     */
    @ExcelProperty(value = "乘客")
    private Long passengerId;

    /**
     * 活动
     */
    @ExcelProperty(value = "活动")
    private Long activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 代理id
     */
    private Long agentId;

    /**
     * 优惠卷
     */
    @ExcelProperty(value = "优惠卷")
    private Long couponId;

    /**
     * 优惠卷名
     */
    @ExcelProperty(value = "优惠卷名")
    private String couponName;

    /**
     * 线路
     */
    @ExcelProperty(value = "线路")
    private String  lineId;

    /**
     * 优惠卷编码
     */
    @ExcelProperty(value = "优惠卷编码")
    private String couponCode;

    /**
     * 订单
     */
    @ExcelProperty(value = "订单")
    private Long orderId;

    /**
     * 优惠类型[DiscountTypeEnum]
     */
    @ExcelProperty(value = "折扣方式")
    @Enum2Text(enumClass = DiscountTypeEnum.class)
    private String discountType;

    /**
     * 折扣额度
     */
    @ExcelProperty(value = "折扣额度")
    private Long quota;

    /**
     * 领取方式[PaidTypeEnum]
     */
    @ExcelProperty(value = "领取方式")
    @Enum2Text(enumClass = PaidTypeEnum.class)
    private String paidType;

    /**
     * 城市编码
     */
    @ExcelProperty(value = "城市编码")
    private String cityCode;

    /**
     * 领取人类型
     */
    @ExcelProperty(value = "领取人类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "UserTypeEnum")
    @Enum2Text(enumClass = UserTypeEnum.class)
    private String paidUserType;

    /**
     * 使用状态[CouponStatusEnum]
     */
    @Enum2Text(enumClass = CouponStatusEnum.class)
    private String usingStatus;

    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 核销时间
     */
    @ExcelProperty(value = "核销时间")
    private Date wipedTime;

    /**
     * 使用规则
     */
    @ExcelProperty(value = "使用规则")
    private String rule;
    /**
     * 默认标识
     */
    private Boolean defaulted;

    /**
     * 乘客名称
     */
    private String passengerName;

    /**
     * 乘客手机尾号
     */
    private String phoneEnd;

    /**
     * 乘客手机号
     */
    private String passengerPhone;
    /**
     * 产品范围：0=全部，1=独享，2=拼车
     */
    private String productScope;

    /**
     *  优惠券来源[UserCouponSourceEnum] 来源类型：1=活动，2=定向发放
     */
    private String sourceType;
    /**
     * 来源ID，如活动ID、发放表ID等
     */
    private Long sourceId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 创建时间
     */
    private Date createTime;

    public List<String> getCityCodeList() {
        if (StrUtil.isNotBlank(cityCode)) {
            return StrUtil.split(cityCode, CharPool.COMMA).stream().filter(StrUtil::isNotBlank).toList();
        }
        return List.of();
    }

    public List<Long> getLineIdList() {
        if (StrUtil.isNotBlank(lineId)) {
            return StrUtil.split(lineId, CharPool.COMMA).stream().filter(StrUtil::isNotBlank).map(NumberUtil::parseLong).toList();
        }
        return List.of();
    }

    /**
     * 城市名称列表
     */
    private List<String> cityNames = List.of();

    /**
     * 线路名称列表
     */
    private List<String> lineNames = List.of();

}
