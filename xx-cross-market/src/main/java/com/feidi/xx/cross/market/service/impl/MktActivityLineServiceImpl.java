package com.feidi.xx.cross.market.service.impl;

import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.market.domain.MktActivity;
import com.feidi.xx.cross.market.domain.bo.MktActivityLineForm;
import com.feidi.xx.cross.market.mapper.MktActivityMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.feidi.xx.cross.market.domain.bo.MktActivityLineBo;
import com.feidi.xx.cross.market.domain.vo.MktActivityLineVo;
import com.feidi.xx.cross.market.domain.MktActivityLine;
import com.feidi.xx.cross.market.mapper.MktActivityLineMapper;
import com.feidi.xx.cross.market.service.IMktActivityLineService;
import com.feidi.xx.common.core.utils.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 活动线路Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@RequiredArgsConstructor
@Service
public class MktActivityLineServiceImpl implements IMktActivityLineService {

    private final MktActivityLineMapper baseMapper;

    private final MktActivityMapper activityMapper;

    /**
     * 查询活动线路
     *
     * @param id 主键
     * @return 活动线路
     */
    @Override
    public MktActivityLineVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询活动线路列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 活动线路分页列表
     */
    @Override
    public TableDataInfo<MktActivityLineVo> queryPageList(MktActivityLineBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MktActivityLine> lqw = buildQueryWrapper(bo);
        Page<MktActivityLineVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的活动线路列表
     *
     * @param bo 查询条件
     * @return 活动线路列表
     */
    @Override
    public List<MktActivityLineVo> queryList(MktActivityLineBo bo) {
        LambdaQueryWrapper<MktActivityLine> lqw = buildQueryWrapper(bo);
        List<MktActivityLineVo> mktActivityLineVos = baseMapper.selectVoList(lqw);

        if ( mktActivityLineVos == null || mktActivityLineVos.isEmpty() ) {
            for (MktActivityLineVo mktActivityLineVo : mktActivityLineVos) {

                // 活动名
                MktActivity mktActivity = activityMapper.selectById(mktActivityLineVo.getActivityId());
                mktActivityLineVo.setActivityName(mktActivity.getName());

                // 线路名
                mktActivityLineVo.setLineName("线路名");

            }
        }

        return mktActivityLineVos;
    }

    private LambdaQueryWrapper<MktActivityLine> buildQueryWrapper(MktActivityLineBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MktActivityLine> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getActivityId() != null, MktActivityLine::getActivityId, bo.getActivityId());
//        lqw.eq(bo.getLineId() != null, MktActivityLine::getLineId, bo.getLineId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), MktActivityLine::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增活动线路
     *
     * @param bo 活动线路
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MktActivityLineBo bo) {



        MktActivityLine add = MapstructUtils.convert(bo, MktActivityLine.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改活动线路
     *
     * @param bo 活动线路
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MktActivityLineBo bo) {
        MktActivityLine update = MapstructUtils.convert(bo, MktActivityLine.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MktActivityLine entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除活动线路信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据活动id删除活动线路信息
     * @param activityId
     * @return
     */
    private Boolean deleteByActivityId(Long activityId) {
        LambdaQueryWrapper<MktActivityLine> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MktActivityLine::getActivityId, activityId);
        return baseMapper.delete(lqw) > 0;
    }

    @Override
    public Boolean bindActivityLine(Long activityId, List<MktActivityLineForm> bo) {

        //TODO 绑定活动线路
        deleteByActivityId(activityId);

        for (MktActivityLineForm lineForm : bo) {
            MktActivityLine line = new MktActivityLine();
            line.setActivityId(activityId);
            line.setLineId(lineForm.getLineId());
            line.setStatus(lineForm.getStatus());
            baseMapper.insert(line);
        }

        return true;
    }
}
