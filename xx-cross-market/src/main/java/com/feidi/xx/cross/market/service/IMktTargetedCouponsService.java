package com.feidi.xx.cross.market.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.market.domain.bo.CouponIssuanceParam;
import com.feidi.xx.cross.market.domain.bo.ImportPhoneNumbersBo;
import com.feidi.xx.cross.market.domain.bo.MktTargetedCouponsBo;
import com.feidi.xx.cross.market.domain.pojo.coupon.GrantForm;
import com.feidi.xx.cross.market.domain.vo.MktTargetedCouponsVo;
import com.feidi.xx.cross.market.domain.vo.UploadExcelFileVo;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 定向放券
 */
public interface IMktTargetedCouponsService {
    TableDataInfo<MktTargetedCouponsVo> queryPageList(MktTargetedCouponsBo bo, PageQuery pageQuery);

    UploadExcelFileVo parserExcelData(List<ImportPhoneNumbersBo> list);

    void  targetedCoupons(CouponIssuanceParam p);

    void revoke(MktTargetedCouponsBo p);

    MktTargetedCouponsVo queryById(Long id);
}
