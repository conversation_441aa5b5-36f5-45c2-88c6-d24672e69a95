package com.feidi.xx.cross.market.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.constant.TenantConstants;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.market.enums.MktCacheKeyEnum;
import com.feidi.xx.cross.common.constant.market.MarketCacheConstants;
import com.feidi.xx.cross.market.api.RemoteInviteConfigService;
import com.feidi.xx.cross.market.api.domain.RemoteMktInviteConfigVo;
import com.feidi.xx.cross.market.domain.MktInviteConfig;
import com.feidi.xx.cross.market.mapper.MktInviteConfigMapper;
import com.feidi.xx.system.api.RemoteConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 邀请有奖配置服务接口实现类
 *
 * <AUTHOR>
 * @date 2024/9/7
 */
@Slf4j
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteInviteConfigServiceImpl implements RemoteInviteConfigService {

    private final MktInviteConfigMapper mktInviteConfigMapper;
    @DubboReference
    private final RemoteConfigService configService;

    @Override
    public void updateAgentName(Long agentId, String agentName) {
        LambdaUpdateWrapper<MktInviteConfig> lqw = Wrappers.lambdaUpdate();
        lqw.set(MktInviteConfig::getCompanyName, agentName)
                .eq(MktInviteConfig::getAgentId, agentId);
        mktInviteConfigMapper.update(null, lqw);
        String key = MktCacheKeyEnum.MKT_INVITE_CONFIG_KEY.create(agentId);
        if (RedisUtils.hasKey(key)){
            RedisUtils.deleteObject(key);
        }
    }

    @Override
    public RemoteMktInviteConfigVo getInviteConfigByAgentId(Long agentId) {
        LambdaQueryWrapper<MktInviteConfig> wrapper = Wrappers.lambdaQuery(MktInviteConfig.class)
                .eq(MktInviteConfig::getAgentId, agentId).eq(MktInviteConfig::getStatus, StatusEnum.ENABLE.getCode())
                .orderByDesc(MktInviteConfig::getId)
                .last(Constants.LIMIT_ONE);
        MktInviteConfig mktInviteConfig = mktInviteConfigMapper.selectOne(wrapper);
        if (mktInviteConfig != null) {
            return BeanUtils.copyProperties(mktInviteConfig, RemoteMktInviteConfigVo.class);
        }
        log.info("代理商id为:{} 被禁用", agentId);
        return null;
    }

    /**
     * 创建拉新配置
     *
     * @param agentId
     * @return
     */
    @Override
    public void createInviteConfig(Long agentId, String agentName) {
        // 拉新返奖比例6%，奖励司机抽成80%，奖励代理商抽成20%。
        // change since v1.3.4: 拉新返奖比例 default 0%，can be updated.

        String s = configService.selectValueByKey(MarketCacheConstants.RECRUIT_REWARD_RATIO);
        BigDecimal rate;
        try {
            rate = StringUtils.isEmpty(s) ? BigDecimal.ZERO : new BigDecimal(s);
        } catch (NumberFormatException e) {
            throw new ServiceException("参数设置-邀请有奖默认分佣比例: 格式存在问题");
        }
        MktInviteConfig mktInviteConfig = new MktInviteConfig();
        mktInviteConfig.setAgentId(agentId);
        mktInviteConfig.setTenantId(TenantConstants.DEFAULT_TENANT_ID);
        mktInviteConfig.setCompanyName(agentName);
        mktInviteConfig.setRecruitRewardRatio(rate);
        mktInviteConfig.setDriverBonusRatio(BigDecimal.valueOf(80.0));
        mktInviteConfig.setAgentBonusRatio(BigDecimal.valueOf(20.0));
        mktInviteConfigMapper.insert(mktInviteConfig);
    }

    /**
     * 查询所有代理商配置
     * @return
     */
    @Override
    public List<RemoteMktInviteConfigVo> getAllConfig() {
        List<MktInviteConfig> mktInviteConfigs = mktInviteConfigMapper.selectList();
        return BeanUtils.copyToList(mktInviteConfigs, RemoteMktInviteConfigVo.class);
    }

    @Override
    public Boolean addAgent(List<RemoteMktInviteConfigVo> configVoList) {
        if (CollectionUtils.isEmpty(configVoList)) {
            return false;
        }
        configVoList.forEach(vo ->
                createInviteConfig(vo.getId(), vo.getCompanyName())
        );
        return true;
    }
}
