package com.feidi.xx.cross.market.controller.agent;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.common.enums.market.PaidTypeEnum;
import com.feidi.xx.cross.market.domain.bo.MktCouponGrantBo;
import com.feidi.xx.cross.market.domain.pojo.coupon.GrantForm;
import com.feidi.xx.cross.market.domain.vo.MktCouponGrantVo;
import com.feidi.xx.cross.market.service.IMktCouponGrantService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Map;

/**
 * 代理商 - 优惠券发放
 * 前端访问路由地址为:/market/couponGrant
 *
 * <AUTHOR>
 * @date 2025-03-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/coupon/grant")
public class AgtCouponGrantController extends BaseController {

    private final IMktCouponGrantService mktCouponGrantService;

    /**
     * 查询优惠券发放列表
     */
    @GetMapping("/list")
    public TableDataInfo<MktCouponGrantVo> list(MktCouponGrantBo bo, PageQuery pageQuery) {
        return mktCouponGrantService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取优惠券发放详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<MktCouponGrantVo> getInfo(@NotNull(message = "主键不能为空")
                                       @PathVariable Long id) {
        return R.ok(mktCouponGrantService.queryById(id));
    }

    /**
     * 优惠券发放
     */
    @Log(title = "优惠券发放", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GrantForm grantForm) {

        Map<Long, Integer> couponIds = grantForm.getCouponIds();
        couponIds.forEach((key, value) -> {
            for (Integer i = value; i > 0; i--) {
                ArrayList<Long> longs = new ArrayList<>();
                longs.add(key);
                MktCouponGrantBo bo = new MktCouponGrantBo();
                bo.setPaidType(PaidTypeEnum.ARTIFICIAL.getCode());
                bo.setPaidUserType(UserTypeEnum.AGENT_USER.getUserType());
                bo.setPassengerPhones(grantForm.getPassengerPhones());
                bo.setCouponIds(longs);
                mktCouponGrantService.grantCoupon(bo);
            }
        });
        return R.ok();

    }
}
