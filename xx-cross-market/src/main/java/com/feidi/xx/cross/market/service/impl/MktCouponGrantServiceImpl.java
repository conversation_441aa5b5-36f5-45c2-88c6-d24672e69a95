package com.feidi.xx.cross.market.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.cache.system.SysDistrictCacheVo;
import com.feidi.xx.common.core.enums.SystemCacheKeyEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.ServletUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.ip.AddressUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.map.model.tencent.regeo.TxMapReGeocode;
import com.feidi.xx.common.map.utils.TxMapUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.market.manage.MktCacheManager;
import com.feidi.xx.cross.common.enums.market.*;
import com.feidi.xx.cross.market.api.RemoteCouponService;
import com.feidi.xx.cross.market.api.domain.RemoteActivityBo;
import com.feidi.xx.cross.market.domain.MktActivity;
import com.feidi.xx.cross.market.domain.MktCouponGrant;
import com.feidi.xx.cross.market.domain.MktTargetedCoupons;
import com.feidi.xx.cross.market.domain.bo.MktCouponBo;
import com.feidi.xx.cross.market.domain.bo.MktCouponGrantBo;
import com.feidi.xx.cross.market.domain.vo.MktActivityVo;
import com.feidi.xx.cross.market.domain.vo.MktCouponGrantVo;
import com.feidi.xx.cross.market.domain.vo.MktCouponVo;
import com.feidi.xx.cross.market.helper.NameSetterUtil;
import com.feidi.xx.cross.market.mapper.MktActivityMapper;
import com.feidi.xx.cross.market.mapper.MktCouponGrantMapper;
import com.feidi.xx.cross.market.mapper.MktTargetedCouponsMapper;
import com.feidi.xx.cross.market.service.IMktCouponGrantService;
import com.feidi.xx.cross.market.service.IMktCouponService;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerVo;
import com.feidi.xx.system.api.RemoteDistrictService;
import com.feidi.xx.system.api.domain.bo.RemoteDistrictBo;
import com.feidi.xx.system.api.domain.vo.RemoteDistrictVo;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 优惠券发放Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-22
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MktCouponGrantServiceImpl implements IMktCouponGrantService {

    private final MktCouponGrantMapper baseMapper;
    private final MktActivityMapper mktActivityMapper;
    private final IMktCouponService mktCouponService;
    private final MktCacheManager mktCacheManager;
    private final ScheduledExecutorService scheduledExecutorService;
    private final MktTargetedCouponsMapper mktTargetedCouponsMapper;
    @DubboReference
    private final RemoteCouponService remoteCouponService;
    @DubboReference
    private final RemotePassengerService remotePassengerService;
    @DubboReference
    private final RemoteDistrictService reversedDistrictService;


    /**
     * 查询优惠券发放
     *
     * @param id 主键
     * @return 优惠券发放
     */
    @Override
    public MktCouponGrantVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 批量查询优惠券发放
     *
     * @param ids
     * @return
     */
    @Override
    public List<MktCouponGrantVo> queryByIds(Collection<Long> ids) {
        LambdaQueryWrapper<MktCouponGrant> lqw = new LambdaQueryWrapper<>();
        lqw.in(CollUtil.isNotEmpty(ids), MktCouponGrant::getId, ids);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public void updateStatus(MktCouponGrantBo bo) {
        MktCouponGrantVo mktCouponGrantVo = baseMapper.selectVoById(bo.getId());
        LambdaUpdateWrapper<MktCouponGrant> lambdaUpdateWrapper = new LambdaUpdateWrapper<MktCouponGrant>()
                .eq(MktCouponGrant::getId, bo.getId());
        if (bo.getUsingStatus().equals(CouponStatusEnum.EXPIRED.getCode())) {
            if (!CouponStatusEnum.NOT_USED.getCode().equals(mktCouponGrantVo.getUsingStatus())) {
                return;
            }
        } else if (CouponStatusEnum.NOT_USED.getCode().equals(bo.getUsingStatus())) {
            //比较是否过期
            if (mktCouponGrantVo.getEndTime().getTime() < System.currentTimeMillis()) {
                bo.setUsingStatus(CouponStatusEnum.EXPIRED.getCode());
                lambdaUpdateWrapper.set(MktCouponGrant::getWipedTime, null);
                lambdaUpdateWrapper.set(MktCouponGrant::getOrderId, null);
            }
        } else if (bo.getUsingStatus().equals(CouponStatusEnum.USED.getCode())) {
            lambdaUpdateWrapper.set(MktCouponGrant::getWipedTime, new Date());
            lambdaUpdateWrapper.set(MktCouponGrant::getOrderId, bo.getOrderId());
        }
        lambdaUpdateWrapper.set(MktCouponGrant::getUsingStatus, bo.getUsingStatus());
        baseMapper.update(lambdaUpdateWrapper);
    }

    /**
     * 分页查询优惠券发放列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 优惠券发放分页列表
     */
    @Override
    public TableDataInfo<MktCouponGrantVo> queryPageList(MktCouponGrantBo bo, PageQuery pageQuery) {
        if (ObjectUtils.isEmpty(pageQuery.getIsAsc())) {
            pageQuery.setIsAsc("desc");
        }
        if (ObjectUtils.isEmpty(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("create_time");
        }
        LambdaQueryWrapper<MktCouponGrant> lqw = buildQueryWrapper(bo);

        List<RemotePassengerVo> passengerInfoList = new ArrayList<>();
        if (StringUtils.isNotBlank(bo.getPhoneEnd())) {
            List<RemotePassengerVo> passengerInfos = remotePassengerService.queryByPhoneEnd(bo.getPhoneEnd());
            if (CollUtil.isNotEmpty(passengerInfos)) {
                passengerInfoList.addAll(passengerInfos);
                Set<Long> passengerIds = passengerInfos.stream().map(RemotePassengerVo::getId).collect(Collectors.toSet());
                lqw.in(CollUtil.isNotEmpty(passengerIds), MktCouponGrant::getPassengerId, passengerIds);
            } else {
                return TableDataInfo.build();
            }
        }

        Page<MktCouponGrantVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);


        // 乘客信息
        List<Long> passengerIds = result.getRecords().stream().map(MktCouponGrantVo::getPassengerId).toList();
        //乘客端，不查询用户信息
        if (CollUtil.isNotEmpty(passengerIds) && !LoginHelper.getUserType().equals(UserTypeEnum.PASSENGER_USER)) {
            passengerInfoList.addAll(remotePassengerService.getPassengerInfo(passengerIds));
        }
        Map<Long, RemotePassengerVo> passengerMap = passengerInfoList.stream().collect(Collectors.toMap(RemotePassengerVo::getId, Function.identity()));

        // 卡券信息
        var couponVoMap = mktCouponService.queryMapByIds(result.getRecords().stream().map(MktCouponGrantVo::getCouponId).collect(Collectors.toSet()));

        // 设置活动名称,定向发放名称
        setActivityNameAndDirectGrantName(result.getRecords());

        result.getRecords().parallelStream().forEach(mktCouponGrantVo -> {
            //乘客端，不查询用户信息
            if (CollUtil.isNotEmpty(passengerMap)) {
                // 设置用户信息
                Optional.ofNullable(passengerMap.get(mktCouponGrantVo.getPassengerId()))
                        .ifPresent(passenger -> {
                            mktCouponGrantVo.setPhoneEnd(StringUtils.substring(passenger.getPhone(), -4));
                            mktCouponGrantVo.setPassengerPhone(passenger.getPhone());
                            mktCouponGrantVo.setPassengerName(passenger.getName());
                        });
            }

            // 设置优惠券信息
            Optional.ofNullable(couponVoMap.get(mktCouponGrantVo.getCouponId()))
                    .ifPresent(coupon -> mktCouponGrantVo.setRule(coupon.getRule()));

        });
        //线路名称
        NameSetterUtil.lineNameSetter(result.getRecords(), MktCouponGrantVo::getLineIdList, MktCouponGrantVo::setLineNames);
        //城市
        NameSetterUtil.cityNameSetterByCode(result.getRecords(), MktCouponGrantVo::getCityCodeList, MktCouponGrantVo::setCityNames);
        return TableDataInfo.build(result);
    }

    private void setActivityNameAndDirectGrantName(List<MktCouponGrantVo> records) {
        // 根据sourceType分组获取活动名称
        Map<String, List<MktCouponGrantVo>> sourceTypeGroups = records.stream()
                .filter(record -> StringUtils.isNotBlank(record.getSourceType()) && record.getSourceId() != null)
                .collect(Collectors.groupingBy(MktCouponGrantVo::getSourceType));
        Map<Long, String> activityNameMap = new HashMap<>();
        Map<Long, String> directGrantNameMap = new HashMap<>();
        // 批量查询活动信息 (sourceType = "1")
        if (sourceTypeGroups.containsKey(UserCouponSourceEnum.ACTIVITY.getCode())) {
            Set<Long> activityIds = sourceTypeGroups.get(UserCouponSourceEnum.ACTIVITY.getCode()).stream()
                    .map(MktCouponGrantVo::getSourceId)
                    .collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(activityIds)) {
                activityNameMap.putAll(mktActivityMapper.selectBatchIds(activityIds).stream()
                        .collect(Collectors.toMap(MktActivity::getId, MktActivity::getName)));
            }
        }
        // 批量查询定向发放信息 (sourceType = "2")
        if (sourceTypeGroups.containsKey(UserCouponSourceEnum.DIRECT.getCode())) {
            Set<Long> directGrantIds = sourceTypeGroups.get(UserCouponSourceEnum.DIRECT.getCode()).stream()
                    .map(MktCouponGrantVo::getSourceId)
                    .collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(directGrantIds)) {
                directGrantNameMap.putAll(mktTargetedCouponsMapper.selectBatchIds(directGrantIds).stream()
                        .collect(Collectors.toMap(MktTargetedCoupons::getId, MktTargetedCoupons::getTitle)));
            }
        }
        records.forEach(mktCouponGrantVo -> {
            // 根据sourceType设置活动名称
            if (StringUtils.isNotBlank(mktCouponGrantVo.getSourceType()) && mktCouponGrantVo.getSourceId() != null) {
                if (UserCouponSourceEnum.ACTIVITY.getCode().equals(mktCouponGrantVo.getSourceType())) {
                    // 活动来源
                    mktCouponGrantVo.setActivityName(activityNameMap.get(mktCouponGrantVo.getSourceId()));
                } else if (UserCouponSourceEnum.DIRECT.getCode().equals(mktCouponGrantVo.getSourceType())) {
                    // 定向发放来源
                    mktCouponGrantVo.setActivityName(directGrantNameMap.get(mktCouponGrantVo.getSourceId()));
                }
            }
        });
    }

    /**
     * 查询符合条件的优惠券发放列表
     *
     * @param bo 查询条件
     * @return 优惠券发放列表
     */
    @Override
    public List<MktCouponGrantVo> queryList(MktCouponGrantBo bo) {
        LambdaQueryWrapper<MktCouponGrant> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MktCouponGrant> buildQueryWrapper(MktCouponGrantBo bo) {
        Map<String, Object> params = bo.getParams();

        LambdaQueryWrapper<MktCouponGrant> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPassengerId() != null, MktCouponGrant::getPassengerId, bo.getPassengerId());
        lqw.eq(bo.getActivityId() != null, MktCouponGrant::getActivityId, bo.getActivityId());
        lqw.eq(bo.getAgentId() != null, MktCouponGrant::getAgentId, bo.getAgentId());
        lqw.eq(bo.getCouponId() != null, MktCouponGrant::getCouponId, bo.getCouponId());
        lqw.like(StringUtils.isNotBlank(bo.getCouponName()), MktCouponGrant::getCouponName, bo.getCouponName());
        lqw.like(bo.getLineId() != null, MktCouponGrant::getLineId, bo.getLineId());
        lqw.like(StringUtils.isNotBlank(bo.getCityCode()), MktCouponGrant::getCityCode, bo.getCityCode());
        lqw.eq(StringUtils.isNotBlank(bo.getCouponCode()), MktCouponGrant::getCouponCode, bo.getCouponCode());
        lqw.eq(bo.getOrderId() != null, MktCouponGrant::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getDiscountType()), MktCouponGrant::getDiscountType, bo.getDiscountType());
        lqw.eq(StringUtils.isNotBlank(bo.getPaidType()), MktCouponGrant::getPaidType, bo.getPaidType());
        lqw.eq(StringUtils.isNotBlank(bo.getPaidUserType()), MktCouponGrant::getPaidUserType, bo.getPaidUserType());
        lqw.eq(StringUtils.isNotBlank(bo.getUsingStatus()), MktCouponGrant::getUsingStatus, bo.getUsingStatus());
        lqw.eq(bo.getStartTime() != null, MktCouponGrant::getStartTime, bo.getStartTime());
        lqw.eq(bo.getEndTime() != null, MktCouponGrant::getEndTime, bo.getEndTime());
        lqw.eq(bo.getWipedTime() != null, MktCouponGrant::getWipedTime, bo.getWipedTime());
        if (bo.getPassengerQuery()) {
            //过滤掉已作废的乘客端查询过滤掉已作废的
            lqw.ne(MktCouponGrant::getUsingStatus, CouponStatusEnum.INVALID.getCode());
        }
        lqw.eq(bo.getSourceId() != null, MktCouponGrant::getSourceId, bo.getSourceId());
        lqw.eq(StrUtil.isNotBlank(bo.getSourceType()), MktCouponGrant::getSourceType, bo.getSourceType());
        lqw.eq(StrUtil.isNotBlank(bo.getProductScope()), MktCouponGrant::getProductScope, bo.getProductScope());
        return lqw;
    }

    /**
     * 新增优惠券发放
     *
     * @param bo 优惠券发放
     * @return 是否新增成功
     */
    @Override
    public void insertByBo(MktCouponGrantBo bo, List<Long> userIds) {

        MktCouponGrant add = MapstructUtils.convert(bo, MktCouponGrant.class);
        MktCouponVo mktCouponVo = validEntityBeforeSave(add);
        add.setCouponName(mktCouponVo.getName());
        add.setActivityId(mktCouponVo.getActivityId());
        add.setAgentId(mktCouponVo.getAgentId());
        add.setDiscountType(mktCouponVo.getDiscountType());
        add.setQuota(bo.getQuota());
        add.setUsingStatus(CouponStatusEnum.NOT_USED.getCode());
        // 批量新增优惠券发放记录
        for (Long userId : userIds) {
            add.setPassengerId(userId);
            add.setCouponCode(makeGrantCode());
            baseMapper.insert(add);
/*            ExpiredCouponEvent expiredCouponEvent = ExpiredCouponEvent
                    .builder()
                    .couponGrantId(add.getId())
                    .expireTime(mktCouponVo.getExpireTime())
                    .requestNo(MDC.get(TraceIdUtils.TRACE_ID))
                    .build();
            expiringProducer.sendMessage(expiredCouponEvent);*/
        }
    }


    /**
     * 修改优惠券发放
     *
     * @param bo 优惠券发放
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MktCouponGrantBo bo) {
        MktCouponGrant update = MapstructUtils.convert(bo, MktCouponGrant.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private MktCouponVo validEntityBeforeSave(MktCouponGrant entity) {

        // TODO 做一些数据校验,如唯一约束

        // 获取优惠券信息
        MktCouponVo mktCouponVo = mktCouponService.verifyCoupon(entity.getCouponId());

        // 校验用户是否已经领取过该优惠券
        Integer maxNum = mktCouponVo.getMaxNum();

        if (maxNum != 0) {
            // 校验用户是否已经领取过该优惠券
            LambdaQueryWrapper<MktCouponGrant> mktCgqw = new LambdaQueryWrapper<>();
            mktCgqw.eq(MktCouponGrant::getCouponId, entity.getCouponId())
                    .eq(MktCouponGrant::getPassengerId, entity.getPassengerId());

            Long count = baseMapper.selectCount(mktCgqw);

            if (count >= maxNum.longValue()) {
                throw new ServiceException("该优惠券已被领取过！");
            }
        }
        return mktCouponVo;
    }

    /**
     * 校验并批量删除优惠券发放信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }


    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void grantCoupon(RemoteActivityBo remoteActivityBo, MktCouponVo mktCouponVo) {
        boolean stock = false;
        try {
            stock = remoteCouponService.deductStock(mktCouponVo.getId(), remoteActivityBo.getPassengerId());
            if (!stock) {
                log.error("库存不足");
                return;
            }
            log.info("库存扣减成功");
            // 发放优惠券
            MktCouponGrantBo couponGrantBo = BeanUtils.copyProperties(mktCouponVo, MktCouponGrantBo.class);
            couponGrantBo.setId(null);
            couponGrantBo.setTenantId(remoteActivityBo.getTenantId());
            couponGrantBo.setPassengerId(remoteActivityBo.getPassengerId());
            couponGrantBo.setCouponId(mktCouponVo.getId());
            couponGrantBo.setAgentId(mktCouponVo.getAgentId());
            couponGrantBo.setCouponName(mktCouponVo.getName());
            couponGrantBo.setCityCode(mktCouponVo.getCityCode());
            couponGrantBo.setPaidType(PaidTypeEnum.AUTO.getCode());
            couponGrantBo.setUsingStatus(CouponStatusEnum.NOT_USED.getCode());
            couponGrantBo.setPaidUserType(UserTypeEnum.AUTO_USER.getUserType());
            couponGrantBo.setProductScope(mktCouponVo.getProductScope());
            couponGrantBo.setSourceType(UserCouponSourceEnum.ACTIVITY.getCode());
            couponGrantBo.setSourceId(remoteActivityBo.getActivityId());
            Long expireTime = mktCouponVo.getExpireTime();
            if (expireTime != null) {
                // 过期时间
                DateTime date = DateUtil.date();
                couponGrantBo.setStartTime(date);
                Double day = ArithUtils.div(expireTime, 86400);
                DateTime expireDate = DateUtil.endOfDay(DateUtil.offsetDay(date, day.intValue()));
                couponGrantBo.setEndTime(expireDate);
            } else {
                couponGrantBo.setStartTime(mktCouponVo.getStartTime());
                couponGrantBo.setEndTime(mktCouponVo.getEndTime());
            }
            List<Long> userIds = new ArrayList<>();
            userIds.add(remoteActivityBo.getPassengerId());
            this.insertByBo(couponGrantBo, userIds);
            // 优惠券发放成功标识
            remoteActivityBo.setSuccess(true);
        } catch (Exception e) {
            log.info("发放优惠券失败{}", mktCouponVo.getId());
            // 回滚库存
            if (stock) {
                mktCacheManager.backCouponStock(mktCouponVo.getId(), remoteActivityBo.getPassengerId());
            }
            throw e;
        }
    }

    @Override
    public void grantCoupon(MktCouponGrantBo couponGrantBo) {
        //校验用户手机号
        List<Long> passengerIdList = new ArrayList<>();
        if (CollUtil.isNotEmpty(couponGrantBo.getPassengerPhones())) {
            List<String> passengerPhones = couponGrantBo.getPassengerPhones();
            for (String passengerPhone : passengerPhones) {
                RemotePassengerVo remotePassengerVo = remotePassengerService.queryByPhone(passengerPhone);
                if (ObjectUtils.isEmpty(remotePassengerVo)) {
                    throw new ServiceException("手机号为: " + passengerPhone + " 用户不存在");
                }
                passengerIdList.add(remotePassengerVo.getId());
            }
        }

        List<Long> couponIds = couponGrantBo.getCouponIds();
        for (Long couponId : couponIds) {
            for (Long passengerId : passengerIdList) {
                MktCouponVo mktCouponVo = mktCouponService.queryById(couponId);
                //看是否有缓存
                addCache(mktCouponVo);
                boolean stock = false;
                try {
                    stock = remoteCouponService.deductStock(mktCouponVo.getId(), passengerId);
                    if (!stock) {
                        log.info("库存不足");
                        return;
                    }
                    log.info("库存扣减成功");
                    if (mktCouponVo.getStatus().equals(CouponTemplateStatusEnum.ENDED.getCode())) {
                        throw new ServiceException("优惠券已结束");
                    }
                    // 发放优惠券
                    couponGrantBo.setCouponId(mktCouponVo.getId());
                    couponGrantBo.setAgentId(mktCouponVo.getAgentId());
                    couponGrantBo.setLineId(mktCouponVo.getLineId());
                    couponGrantBo.setCouponName(mktCouponVo.getName());
                    couponGrantBo.setCityCode(mktCouponVo.getCityCode());
                    couponGrantBo.setQuota(mktCouponVo.getQuota());
                    couponGrantBo.setUsingStatus(CouponStatusEnum.NOT_USED.getCode());
                    couponGrantBo.setPaidUserType(UserTypeEnum.AUTO_USER.getUserType());
                    couponGrantBo.setProductScope(mktCouponVo.getProductScope());
                    Long expireTime = mktCouponVo.getExpireTime();
                    if (expireTime != null) {
                        // 过期时间
                        DateTime date = DateUtil.date();
                        couponGrantBo.setStartTime(date);
                        Double day = ArithUtils.div(expireTime, 86400);
                        DateTime expireDate = DateUtil.endOfDay(DateUtil.offsetDay(date, day.intValue()));
                        couponGrantBo.setEndTime(expireDate);
                    } else {
                        couponGrantBo.setStartTime(mktCouponVo.getStartTime());
                        couponGrantBo.setEndTime(mktCouponVo.getEndTime());
                    }
                    List<Long> userIds = new ArrayList<>();
                    userIds.add(passengerId);
                    this.insertByBo(couponGrantBo, userIds);
                } catch (Exception e) {
                    log.info("发放优惠券失败{}", mktCouponVo.getId());
                    // 回滚库存
                    if (stock) {
                        mktCacheManager.backCouponStock(mktCouponVo.getId(), passengerId);
                    }
                    throw e;
                }
            }
        }
    }

    /**
     * 获取当前城市下优惠券信息
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return 优惠券信息
     */
    @Override
    public List<MktCouponVo> queryCouponList(String longitude, String latitude) {
        List<MktCouponVo> mktCouponVos = new ArrayList<>();
        String cityCode = null;

        if (StringUtils.isNotBlank(longitude) && StringUtils.isNotBlank(latitude)) {
            TxMapReGeocode reGeo = TxMapUtils.regeo(longitude, latitude);
            if (reGeo != null && reGeo.getAdInfo() != null) {
                String adCode = reGeo.getAdInfo().getAdcode();
                if (StringUtils.isNotBlank(adCode)) {
                    SysDistrictCacheVo sysDistrictVo = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_AD_CODE_KEY.create(), adCode);
                    if (sysDistrictVo != null) {
                        cityCode = sysDistrictVo.getCityCode();
                    }
                }
            }
        }

        if (StringUtils.isBlank(cityCode)) {
            String ip = ServletUtils.getClientIP();
            log.info("ip地址：【{}】", ip);
            String address = AddressUtils.getRealAddressByIP(ip);
            log.info("地址：【{}】", address);
            if (StringUtils.isNotBlank(address) && !address.equals("内网IP") && !address.equals("XX XX")) {
                if (address.contains("|")) {
                    String[] split = address.split("\\|");
                    String cityAddress = split[2];
                    if (cityAddress.contains("市")) {
                        log.info("城市名称：【{}】", cityAddress);
                        RemoteDistrictBo remoteDistrictBo = new RemoteDistrictBo();
                        remoteDistrictBo.setName(cityAddress);
                        List<RemoteDistrictVo> remoteDistrictVos = reversedDistrictService.queryByBo(remoteDistrictBo);
                        log.info("城市信息：【{}】", JsonUtils.toJsonString(remoteDistrictVos));
                        if (CollUtil.isNotEmpty(remoteDistrictVos)) {
                            RemoteDistrictVo remoteDistrictVo = remoteDistrictVos.get(0);
                            cityCode = remoteDistrictVo.getCityCode();
                        }
                    }
                }
            }
        }
        log.debug("城市编码：【{}】", cityCode);

        MktActivityVo mktActivityVo = null;
        if (StringUtils.isNotBlank(cityCode)) {
            LambdaQueryWrapper<MktActivity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MktActivity::getScope, ActivityScopeEnum.NEW.getCode())
                    .like(MktActivity::getCityCode, cityCode)
                    .eq(MktActivity::getStatus, ActivityStatusEnum.STARTING.getCode())
                    .eq(MktActivity::getShelvesStatus, ShelvesStatusEnum.ONLINE.getCode())
                    // 开始时间 < 当前
                    .le(MktActivity::getStartTime, DateUtil.now())
                    // 结束时间 > 当前
                    .ge(MktActivity::getEndTime, DateUtil.now())
                    .orderByDesc(MktActivity::getCreateTime)
                    .last("limit 1");
            mktActivityVo = mktActivityMapper.selectVoOne(queryWrapper);
        }
        log.debug("活动信息：【{}】", mktActivityVo);

        if (ObjectUtils.isNotEmpty(mktActivityVo)) {
            // 获取优惠券
            MktCouponBo couponBo = new MktCouponBo();
            couponBo.setCouponIds(new HashSet<>(mktActivityVo.getCouponIds()));
            couponBo.setPaidType(PaidTypeEnum.AUTO.getCode());
            couponBo.setStatus(CouponTemplateStatusEnum.ACTIVE.getCode());
            couponBo.setOnlyValid(true);//查询有效的
            mktCouponVos = mktCouponService.queryCoupon(couponBo);
        }
        log.debug("优惠券信息：【{}】", JsonUtils.toJsonString(mktCouponVos));
        return mktCouponVos;
    }

    /**
     * 优惠券过期
     */
    @Override
    public void expiredCoupon() {
        LambdaQueryWrapper<MktCouponGrant> lqw = Wrappers.lambdaQuery();
        lqw.eq(MktCouponGrant::getUsingStatus, CouponStatusEnum.NOT_USED.getCode())
                .le(MktCouponGrant::getEndTime, DateUtils.getNowDate());
        List<MktCouponGrant> mktCouponGrants = baseMapper.selectList(lqw);
        if (CollUtil.isNotEmpty(mktCouponGrants)) {
            mktCouponGrants.forEach(couponGrant -> {
                // 异步更新，即使更新失败也不需要回滚
                scheduledExecutorService.schedule(() -> {
                    MktCouponGrantBo mktCouponGrantBo = new MktCouponGrantBo();
                    mktCouponGrantBo.setId(couponGrant.getId());
                    mktCouponGrantBo.setUsingStatus(CouponStatusEnum.EXPIRED.getCode());
                    this.updateStatus(mktCouponGrantBo);
                }, 0, TimeUnit.SECONDS);
            });
        }
    }

    /**
     * 生成优惠券发放码
     *
     * @return 优惠券发放码
     */
    private String makeGrantCode() {

        String grantCode = RandomUtil.randomString(16);

        if (getCouponGrantByCode(grantCode) != null) {
            return makeGrantCode();
        }

        return grantCode;
    }

    /**
     * 根据优惠券码获取优惠券发放信息
     *
     * @param couponCode
     * @return
     */
    private MktCouponGrantVo getCouponGrantByCode(String couponCode) {

        LambdaQueryWrapper<MktCouponGrant> eq = new LambdaQueryWrapper<MktCouponGrant>()
                .eq(MktCouponGrant::getCouponCode, couponCode);

        MktCouponGrant mktCouponGrant = baseMapper.selectOne(eq);

        if (mktCouponGrant == null) {
            return null;
        }

        return MapstructUtils.convert(mktCouponGrant, MktCouponGrantVo.class);
    }


    private void addCache(MktCouponVo add) {
        //原因卡券的库存缓存时间不应该跟活动时间匹配，因为还有定向发券。考虑在卡券删除上下架时处理

        //根据结束时间获取缓存时间
//        MktActivityVo mktActivityVo = mktActivityMapper.selectVoById(add.getActivityId());
//        if (ObjectUtils.isNotEmpty(mktActivityVo)) {
//            Date endTime = mktActivityVo.getEndTime();
//            long now = System.currentTimeMillis();
//            long diffSeconds = (endTime.getTime() - now) / 1000;
//            mktCacheManager.createCouponStock(add.getId(), add.getMargin(), add.getMaxNum(), diffSeconds);
//        } else {
        mktCacheManager.createCouponStock(add.getId(), add.getMargin(), add.getMaxNum());
//        }

    }

}
