package com.feidi.xx.cross.market.helper;

import org.redisson.api.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Random;

@Component
public class CampaignInviteCodeHelper {

    private final RedissonClient redissonClient;

    private static final String INVITE_CODE_PREFIX = "invite:code:";     // 邀请码 -> 用户ID
    private static final String USER_INVITE_PREFIX = "invite:user:";     // 用户ID -> 邀请码
    private static final String CODE_SET_PREFIX = "invite:codes:";       // 活动下所有邀请码集合

    // 字符集，避免容易混淆的字符
    private static final String CHAR_SET = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789";
    private static final int CODE_LENGTH = 8; // 邀请码长度

    public CampaignInviteCodeHelper(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    /**
     * 生成随机邀请码
     */
    private String generateInviteCode(Long activityId) {
        Random random = new Random(System.nanoTime() + activityId);
        StringBuilder sb = new StringBuilder(CODE_LENGTH);

        for (int i = 0; i < CODE_LENGTH; i++) {
            int index = random.nextInt(CHAR_SET.length());
            sb.append(CHAR_SET.charAt(index));
        }

        return sb.toString();
    }

    /**
     * 生成唯一邀请码（避免重复）
     */
    private String generateUniqueInviteCode(Long activityId) {
        int retryCount = 0;
        int maxRetry = 10;
        String inviteCode;

        do {
            inviteCode = generateInviteCode(activityId);
        } while (existsInviteCode(activityId, inviteCode) && retryCount++ < maxRetry);

        // 如果重试次数过多，调整长度
        if (retryCount >= maxRetry) {
            inviteCode = generateInviteCodeWithLength(activityId, CODE_LENGTH + (retryCount / 3));
        }

        return inviteCode;
    }

    /**
     * 生成指定长度的邀请码
     */
    private String generateInviteCodeWithLength(Long activityId, int length) {
        Random random = new Random(System.nanoTime() + activityId + length);
        StringBuilder sb = new StringBuilder(length);

        for (int i = 0; i < length && sb.length() < 16; i++) {
            int index = random.nextInt(CHAR_SET.length());
            sb.append(CHAR_SET.charAt(index));
        }

        return sb.toString();
    }

    /**
     * 保存邀请码到Redis（包含重复检查）
     */
    private boolean saveInviteCode(Long activityId, String inviteCode, Long userId) {
        // 检查该用户是否已经有邀请码
        if (existsUserInviteCode(activityId, userId)) {
            throw new RuntimeException("User already has an invite code for this activity");
        }

        // 检查邀请码是否已被使用
        if (existsInviteCode(activityId, inviteCode)) {
            return false;
        }

        RTransaction transaction = redissonClient.createTransaction(TransactionOptions.defaults());

        try {
            // 存储邀请码 -> 用户ID 的映射
            RMap<String, String> codeMap = transaction.getMap(INVITE_CODE_PREFIX + activityId);
            codeMap.put(inviteCode, userId.toString());

            // 存储用户ID -> 邀请码 的映射
            RMap<String, String> userMap = transaction.getMap(USER_INVITE_PREFIX + activityId);
            userMap.put(userId.toString(), inviteCode);

            // 添加到邀请码集合中（用于去重检查）
            RSet<String> codeSet = transaction.getSet(CODE_SET_PREFIX + activityId);
            codeSet.add(inviteCode);

            transaction.commit();
            return true;
        } catch (Exception e) {
            transaction.rollback();
            throw new RuntimeException("Failed to save invite code", e);
        }
    }

    /**
     * 完整的生成并保存邀请码流程（保证唯一性）
     */
    public String createAndSaveInviteCode(Long activityId, Long userId) {
        // 检查用户是否已经有邀请码
        String existingCode = getInviteCodeByUser(activityId, userId);
        if (existingCode != null) {
            return existingCode;
        }

        String inviteCode;
        boolean success = false;
        int retryCount = 0;
        int maxRetry = 5;

        do {
            inviteCode = generateUniqueInviteCode(activityId);
            success = saveInviteCode(activityId, inviteCode, userId);

            if (!success) {
                retryCount++;
                if (retryCount >= maxRetry) {
                    throw new RuntimeException("Failed to generate unique invite code after " + maxRetry + " attempts");
                }

                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Interrupted while generating invite code");
                }
            }
        } while (!success);

        return inviteCode;
    }

    /**
     * 通过邀请码获取用户ID
     */
    public Long getUserIdByInviteCode(Long activityId, String inviteCode) {
        RMap<String, String> map = redissonClient.getMap(INVITE_CODE_PREFIX + activityId);
        String userIdStr = map.get(inviteCode);
        return userIdStr != null ? Long.valueOf(userIdStr) : null;
    }

    /**
     * 通过用户ID和活动ID获取邀请码
     */
    public String getInviteCodeByUser(Long activityId, Long userId) {
        RMap<String, String> map = redissonClient.getMap(USER_INVITE_PREFIX + activityId);
        return map.get(userId.toString());
    }

    /**
     * 删除邀请码（双向删除）
     */
    public void deleteInviteCode(Long activityId, String inviteCode) {
        Long userId = getUserIdByInviteCode(activityId, inviteCode);
        if (userId != null) {
            RTransaction transaction = redissonClient.createTransaction(TransactionOptions.defaults());

            try {
                RMap<String, String> codeMap = transaction.getMap(INVITE_CODE_PREFIX + activityId);
                codeMap.remove(inviteCode);

                RMap<String, String> userMap = transaction.getMap(USER_INVITE_PREFIX + activityId);
                userMap.remove(userId.toString());

                RSet<String> codeSet = transaction.getSet(CODE_SET_PREFIX + activityId);
                codeSet.remove(inviteCode);

                transaction.commit();
            } catch (Exception e) {
                transaction.rollback();
                throw new RuntimeException("Failed to delete invite code", e);
            }
        }
    }

    /**
     * 删除用户的邀请码
     */
    public void deleteUserInviteCode(Long activityId, Long userId) {
        String inviteCode = getInviteCodeByUser(activityId, userId);
        if (inviteCode != null) {
            RTransaction transaction = redissonClient.createTransaction(TransactionOptions.defaults());

            try {
                RMap<String, String> userMap = transaction.getMap(USER_INVITE_PREFIX + activityId);
                userMap.remove(userId.toString());

                RMap<String, String> codeMap = transaction.getMap(INVITE_CODE_PREFIX + activityId);
                codeMap.remove(inviteCode);

                RSet<String> codeSet = transaction.getSet(CODE_SET_PREFIX + activityId);
                codeSet.remove(inviteCode);

                transaction.commit();
            } catch (Exception e) {
                transaction.rollback();
                throw new RuntimeException("Failed to delete user invite code", e);
            }
        }
    }

    /**
     * 根据活动ID删除所有相关的邀请码数据
     *
     * @param activityId 活动ID
     * @return 删除的key数量
     */
    public int deleteAllInviteCodesByActivity(Long activityId) {
        int deletedKeys = 0;

        try {
            // 删除邀请码映射
            String codeMapKey = INVITE_CODE_PREFIX + activityId;
            RKeys keys = redissonClient.getKeys();
            if (keys.delete(codeMapKey) > 0) {
                deletedKeys++;
            }

            // 删除用户邀请码映射
            String userMapKey = USER_INVITE_PREFIX + activityId;
            if (keys.delete(userMapKey) > 0) {
                deletedKeys++;
            }

            // 删除邀请码集合
            String codeSetKey = CODE_SET_PREFIX + activityId;
            if (keys.delete(codeSetKey) > 0) {
                deletedKeys++;
            }

        } catch (Exception e) {
            throw new RuntimeException("Failed to delete all invite codes for activity: " + activityId, e);
        }

        return deletedKeys;
    }

    /**
     * 批量删除多个活动的邀请码数据
     *
     * @param activityIds 活动ID列表
     * @return 删除的key总数
     */
    public int deleteAllInviteCodesByActivities(List<Long> activityIds) {
        int totalDeleted = 0;
        for (Long activityId : activityIds) {
            totalDeleted += deleteAllInviteCodesByActivity(activityId);
        }
        return totalDeleted;
    }

    /**
     * 检查邀请码是否存在
     */
    public boolean existsInviteCode(Long activityId, String inviteCode) {
        RSet<String> codeSet = redissonClient.getSet(CODE_SET_PREFIX + activityId);
        return codeSet.contains(inviteCode);
    }

    /**
     * 检查用户是否已有邀请码
     */
    public boolean existsUserInviteCode(Long activityId, Long userId) {
        RMap<String, String> map = redissonClient.getMap(USER_INVITE_PREFIX + activityId);
        return map.containsKey(userId.toString());
    }

    /**
     * 获取活动下所有邀请码数量
     */
    public long getInviteCodeCount(Long activityId) {
        RSet<String> codeSet = redissonClient.getSet(CODE_SET_PREFIX + activityId);
        return codeSet.size();
    }
}