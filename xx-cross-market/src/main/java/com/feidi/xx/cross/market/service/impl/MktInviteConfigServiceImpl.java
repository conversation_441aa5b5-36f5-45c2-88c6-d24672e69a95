package com.feidi.xx.cross.market.service.impl;

import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.market.enums.MktCacheKeyEnum;
import com.feidi.xx.cross.market.domain.MktInviteConfig;
import com.feidi.xx.cross.market.domain.MktInviteRecord;
import com.feidi.xx.cross.market.domain.bo.MktInviteConfigBo;
import com.feidi.xx.cross.market.domain.vo.MktInviteConfigVo;
import com.feidi.xx.cross.market.mapper.MktInviteConfigMapper;
import com.feidi.xx.cross.market.service.IMktInviteConfigService;
import com.feidi.xx.cross.market.service.IMktInviteRecordService;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.system.api.RemoteConfigService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import com.feidi.xx.common.core.utils.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 邀请有奖配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@RequiredArgsConstructor
@Service
public class MktInviteConfigServiceImpl implements IMktInviteConfigService {

    private final MktInviteConfigMapper baseMapper;

    @DubboReference
    private RemoteConfigService configService;

    @DubboReference
    private RemoteAgentService remoteAgentService;

    /**
     * 查询邀请有奖配置
     *
     * @param id 主键
     * @return 邀请有奖配置
     */
    @Override
    public MktInviteConfigVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询邀请有奖配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 邀请有奖配置分页列表
     */
    @Override
    public TableDataInfo<MktInviteConfigVo> queryPageList(MktInviteConfigBo bo, PageQuery pageQuery) {
        //初始化
        if (ObjectUtils.isNull(pageQuery.getIsAsc())) {
            pageQuery.setIsAsc("desc");
        }
        if (ObjectUtils.isNull(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("create_time");
        }
        LambdaQueryWrapper<MktInviteConfig> lqw = buildQueryWrapper(bo);
        Page<MktInviteConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        result.getRecords().forEach(
                item -> {
                    RemoteAgentVo agentInfoById = remoteAgentService.getAgentInfoById(item.getAgentId());
                    item.setStatus(agentInfoById.getStatus());
                }
        );
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的邀请有奖配置列表
     *
     * @param bo 查询条件
     * @return 邀请有奖配置列表
     */
    @Override
    public List<MktInviteConfigVo> queryList(MktInviteConfigBo bo) {
        LambdaQueryWrapper<MktInviteConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MktInviteConfig> buildQueryWrapper(MktInviteConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MktInviteConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getAgentId() != null, MktInviteConfig::getAgentId, bo.getAgentId());
        lqw.eq(StringUtils.isNotBlank(bo.getCompanyName()), MktInviteConfig::getCompanyName, bo.getCompanyName());
        lqw.eq(bo.getRecruitRewardRatio() != null, MktInviteConfig::getRecruitRewardRatio, bo.getRecruitRewardRatio());
        lqw.eq(bo.getDriverBonusRatio() != null, MktInviteConfig::getDriverBonusRatio, bo.getDriverBonusRatio());
        lqw.eq(bo.getAgentBonusRatio() != null, MktInviteConfig::getAgentBonusRatio, bo.getAgentBonusRatio());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), MktInviteConfig::getStatus, bo.getStatus());
        lqw.between(StringUtils.isNotBlank(bo.getStartCreateTime()) && StringUtils.isNotBlank(bo.getEndCreateTime()), MktInviteConfig::getCreateTime, bo.getStartCreateTime(), bo.getEndCreateTime());
        return lqw;
    }

    /**
     * 新增邀请有奖配置
     *
     * @param bo 邀请有奖配置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MktInviteConfigBo bo) {
        MktInviteConfig add = MapstructUtils.convert(bo, MktInviteConfig.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改邀请有奖配置
     *
     * @param bo 邀请有奖配置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MktInviteConfigBo bo) {
        MktInviteConfig update = MapstructUtils.convert(bo, MktInviteConfig.class);
        validEntityBeforeSave(update);
        MktInviteConfig mktInviteConfig = baseMapper.selectById(update.getId());
        String key = MktCacheKeyEnum.MKT_INVITE_CONFIG_KEY.create(mktInviteConfig.getAgentId());
        if (RedisUtils.hasKey(key)){
            RedisUtils.deleteObject(key);
        }
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MktInviteConfig entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除邀请有奖配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

}
