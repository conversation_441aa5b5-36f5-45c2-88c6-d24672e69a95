package com.feidi.xx.cross.market.domain.vo;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.cross.common.enums.market.*;
import com.feidi.xx.cross.market.domain.MktActivity;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 活动视图对象 mkt_activity
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MktActivity.class)
public class MktActivityVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 代理id
     */
    private Long agentId;

    /**
     * 活动名称
     */
    @ExcelProperty(value = "活动名称")
    private String name;

    /**
     * 活动描述
     */
    private String refer;

    /**
     * 内容
     */
    private String content;

    /**
     * 广告图
     */
//    @ExcelProperty(value = "广告图")
    private String adImage;

    /**
     * 活动方式
     * 活动方式[优惠券|免佣|返现]
     */
    @Enum2Text(enumClass = ActivityMannerEnum.class)
    private String manner;

    /**
     * 范围
     * 范围[全部|新|老]
     */
    @Enum2Text(enumClass = ActivityScopeEnum.class)
    private String scope;

    /**
     * 活动对象
     */
    @Enum2Text(enumClass = ActivityColonyEnum.class)
    private String colony;

    /**
     * 活动类型{@link com.feidi.xx.cross.common.enums.market.ActivityTypeEnum}
     */
    @Enum2Text(enumClass = ActivityTypeEnum.class)
    private String type;

    /**
     * 投放渠道
     */
    @Enum2Text(enumClass = ActivityIssuingChannelEnum.class)
    private String issuingChannel;

    /**
     * 优惠券发放范围{@link com.feidi.xx.cross.common.enums.market.GrantScopeEnum}
     */
    @Enum2Text(enumClass = GrantScopeEnum.class)
    private String grantScope;
    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 状态
     * ActivityStatusEnum
     */
    @Enum2Text(enumClass = ActivityStatusEnum.class)
    private String status;

    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间")
    private Date endTime;


    /**
     * 上下架状态：0=下架，1=上架
     */
    @Enum2Text(enumClass = ShelvesStatusEnum.class)
    private String shelvesStatus;

    /**
     * 活动关联的优惠券ids
     */
    private List<Long> couponIds;

    /**
     * 活动关联的线路ids
     */
    private List<String> lineIds;

    /**
     * 优惠券列表
     */
    private List<MktCouponVo> coupons;
}
