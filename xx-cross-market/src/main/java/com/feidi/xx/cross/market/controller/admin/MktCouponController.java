package com.feidi.xx.cross.market.controller.admin;

import java.io.ByteArrayOutputStream;
import java.util.List;

import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.cross.market.domain.vo.MktCouponVo;
import com.feidi.xx.cross.market.domain.bo.MktCouponBo;
import com.feidi.xx.cross.market.service.IMktCouponService;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 优惠券
 * 前端访问路由地址为:/market/coupon
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/coupon")
public class MktCouponController extends BaseController {

    private final IMktCouponService mktCouponService;

    /**
     * 查询优惠券列表
     */
    @SaCheckPermission("market:coupon:list")
    @Enum2TextAspect
    @GetMapping("/list")
    public TableDataInfo<MktCouponVo> list(MktCouponBo bo, PageQuery pageQuery) {
        return mktCouponService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出优惠券列表
     */
    @SaCheckPermission("market:coupon:export")
    @Log(title = "优惠券", businessType = BusinessType.EXPORT)
    @Download(name="优惠券",module = ModuleConstants.MARKET,mode="no")
    @PostMapping("/export")
    public Object export(MktCouponBo bo,HttpServletResponse response) {
        List<MktCouponVo> list = mktCouponService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(list, "优惠券", MktCouponVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取优惠券详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("market:coupon:query")
    @GetMapping("/{id}")
    @Enum2TextAspect
    public R<MktCouponVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mktCouponService.queryById(id));
    }

    /**
     * 新增优惠券
     */
    @SaCheckPermission("market:coupon:add")
    @Log(title = "优惠券", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MktCouponBo bo) {
        bo.validate();
        return toAjax(mktCouponService.insertByBo(bo));
    }

    /**
     * 修改优惠券
     */
    @SaCheckPermission("market:coupon:edit")
    @Log(title = "优惠券", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MktCouponBo bo) {
        bo.validate();
        return toAjax(mktCouponService.updateByBo(bo));
    }

    /**
     * 删除优惠券
     *
     * @param ids 主键串
     */
    @SaCheckPermission("market:coupon:remove")
    @Log(title = "优惠券", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mktCouponService.deleteWithValidByIds(List.of(ids), true));
    }
}
