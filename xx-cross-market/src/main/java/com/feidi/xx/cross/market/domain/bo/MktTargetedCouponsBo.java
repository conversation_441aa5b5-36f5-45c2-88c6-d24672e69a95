package com.feidi.xx.cross.market.domain.bo;

import lombok.Data;

/**
 * 放券列表查询参数
 *
 * <AUTHOR>
 */
@Data
public class MktTargetedCouponsBo {
    /**
     * 主键
     */
    private Long id;
    /**
     * 发放标题
     */
    private String title;
    /**
     * 优惠卷
     */
    private Long couponId;

    private String couponName;

    /**
     * TargetedCouponsStatusEnum 状态，1：已发放，2：已撤销
     */
    private String status;

    /**
     * 优惠对象[CouponTargetEnum],0:线路专属；1:全平台通用；2:城市专属
     */
    private String target;
}
