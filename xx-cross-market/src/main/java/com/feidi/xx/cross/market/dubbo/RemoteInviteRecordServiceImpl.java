package com.feidi.xx.cross.market.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.core.utils.xx.RandomUtils;
import com.feidi.xx.cross.common.enums.market.RewardTypeEnum;
import com.feidi.xx.cross.market.api.RemoteInviteRecordService;
import com.feidi.xx.cross.market.api.domain.RemoteInviteRecordVo;
import com.feidi.xx.cross.market.domain.MktInviteRecord;
import com.feidi.xx.cross.market.mapper.MktInviteRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 邀请有代理商记录流水服务接口实现类
 *
 * <AUTHOR>
 * @date 2024/9/7
 */
@Slf4j
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteInviteRecordServiceImpl implements RemoteInviteRecordService {

    private final MktInviteRecordMapper inviteRecordMapper;

    @Override
    public void insertByBo(RemoteInviteRecordVo inviteRecordVo) {
        //代理商id 不能为空
        if (ObjectUtils.isNull(inviteRecordVo.getAgentId())) {
            throw new ServiceException("代理商id不能为空");
        }
         inviteRecordVo.setFlowNo(generateNo());
        log.info("邀请有代理商记录流水服务接口实现类，插入数据：{}", inviteRecordVo);
        MktInviteRecord mktInviteRecord = BeanUtils.copyProperties(inviteRecordVo, MktInviteRecord.class);
        log.info("邀请有代理商记录流水服务接口实现类，插入数据：{}", mktInviteRecord);
        inviteRecordMapper.insert(mktInviteRecord);

    }

    @Override
    public boolean updateRebateStatus(Long userId, Long orderId) {
        LambdaUpdateWrapper<MktInviteRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MktInviteRecord::getDriverId, userId)
                .eq(MktInviteRecord::getOrderId, orderId)
                .set(MktInviteRecord::getRewardType, RewardTypeEnum.REWARD_REFUND.getCode());
        return inviteRecordMapper.update(updateWrapper) > 0;
    }

    /**
     * 根据订单id查询拉新奖励记录
     *
     * @param orderIds 订单id
     * @return 拉新奖励记录集合
     */
    @Override
    public List<RemoteInviteRecordVo> queryByOrderIds(List<Long> orderIds) {
        if (CollUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<MktInviteRecord> lqw = new LambdaQueryWrapper<>();
        lqw.in(MktInviteRecord::getOrderId, orderIds);
        List<MktInviteRecord> inviteRecords = inviteRecordMapper.selectList(lqw);

        return BeanUtils.copyToList(inviteRecords, RemoteInviteRecordVo.class);
    }

    /**
     * 创建流水号
     */
    public static String generateNo() {
        return "flow" + DateUtils.dateTimeNow(DateUtils.YYYYMMDDHHMMSS) + RandomUtils.randomInt(10, 99);
    }
}
