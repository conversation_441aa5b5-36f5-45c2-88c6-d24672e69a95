package com.feidi.xx.cross.market.helper;

import cn.hutool.core.collection.CollUtil;
import com.feidi.xx.common.core.cache.system.SysDistrictCacheVo;
import com.feidi.xx.common.core.enums.SystemCacheKeyEnum;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.operate.api.RemoteLineService;
import com.feidi.xx.cross.operate.api.domain.line.vo.RemoteLineVo;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class NameSetterUtil implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        NameSetterUtil.applicationContext = applicationContext;
    }

    public static RemoteLineService getRemoteLineService() {
        return applicationContext.getBean(RemoteLineService.class);
    }

    public static <T> void cityNameSetterByCode(Collection<T> collection, Function<T, List<String>> getter, BiConsumer<T, List<String>> setter) {
        if (CollUtil.isEmpty(collection)) {
            return;
        }

        Set<String> cityCodes = collection.stream()
                .map(getter)
                .filter(Objects::nonNull)
                .flatMap(List::stream)  // 这里改为 List::stream
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (cityCodes.isEmpty()) {
            return;
        }

        Map<String, SysDistrictCacheVo> cityInfo = RedisUtils.getMultiCacheMapValue(
                SystemCacheKeyEnum.SYS_CITY_CODE_KEY.create(), cityCodes);
        Map<String, String> code2Name = cityInfo.values().stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        SysDistrictCacheVo::getCityCode,
                        SysDistrictCacheVo::getName,
                        (existing, replacement) -> existing
                ));

        collection.forEach(item -> {
            List<String> cityCodeList = getter.apply(item);
            if (CollUtil.isNotEmpty(cityCodeList)) {
                List<String> cityNameList = cityCodeList.stream()
                        .filter(Objects::nonNull)
                        .map(code2Name::get)
                        .toList();
                setter.accept(item, cityNameList);
            }
        });
    }

    public static <T> void lineNameSetter(Collection<T> collection, Function<T, List<Long>> getter, BiConsumer<T, List<String>> setter) {
        if (CollUtil.isEmpty(collection)) {
            return;
        }

        Set<Long> lineIds = collection.stream()
                .map(getter)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (lineIds.isEmpty()) {
            return;
        }

        List<RemoteLineVo> remoteLineVos = getRemoteLineService().queryByLineIds(new ArrayList<>(lineIds));
        Map<Long, String> lineNameMap = Optional.ofNullable(remoteLineVos)
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        RemoteLineVo::getId,
                        RemoteLineVo::getName,
                        (existing, replacement) -> existing
                ));

        collection.forEach(item -> {
            Collection<Long> lineIdList = getter.apply(item);
            if (CollUtil.isNotEmpty(lineIdList)) {
                List<String> lineNameList = lineIdList.stream()
                        .filter(Objects::nonNull)
                        .map(lineNameMap::get)
                        .toList();
                setter.accept(item, lineNameList);
            }
        });
    }
}
