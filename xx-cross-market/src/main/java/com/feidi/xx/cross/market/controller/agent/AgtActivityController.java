package com.feidi.xx.cross.market.controller.agent;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.market.domain.bo.MktActivityBo;
import com.feidi.xx.cross.market.domain.vo.MktActivityVo;
import com.feidi.xx.cross.market.service.IMktActivityService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 代理商 - 活动
 * 前端访问路由地址为:/market/agt/activity
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/activity")
public class AgtActivityController extends BaseController {

    private final IMktActivityService mktActivityService;

    /**
     * 查询活动列表
     */
    @Enum2TextAspect
    @GetMapping("/list")
    public TableDataInfo<MktActivityVo> list(MktActivityBo bo, PageQuery pageQuery) {
        bo.setAgentId(LoginHelper.getUserId());
        return mktActivityService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询活动列表 - 不分页
     */
    @GetMapping("/list/all")
    public List<MktActivityVo> listAll(MktActivityBo bo) {
        return mktActivityService.queryList(bo);
    }

    /**
     * 导出活动列表
     */
    @Log(title = "活动", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MktActivityBo bo,HttpServletResponse response) {
        List<MktActivityVo> list = mktActivityService.queryList(bo);
        bo.setAgentId(LoginHelper.getUserId());
        ExcelUtil.exportExcel(list, "活动", MktActivityVo.class, response);
    }

    /**
     * 获取活动详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<MktActivityVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mktActivityService.queryById(id));
    }

    /**
     * 新增活动
     */
    @Log(title = "活动", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MktActivityBo bo) {
        bo.setAgentId(LoginHelper.getUserId());
        return toAjax(mktActivityService.insertByBo(bo));
    }

    /**
     * 修改活动
     */
    @Log(title = "活动", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MktActivityBo bo) {
        return toAjax(mktActivityService.updateByBo(bo));
    }

    /**
     * 删除活动
     *
     * @param ids 主键串
     */
    @Log(title = "活动", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mktActivityService.deleteWithValidByIds(List.of(ids), true));
    }
}
