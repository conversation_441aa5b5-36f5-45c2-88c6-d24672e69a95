package com.feidi.xx.cross.market.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.cross.common.cache.market.manage.MktCacheManager;
import com.feidi.xx.cross.market.api.RemoteCouponService;
import com.feidi.xx.cross.market.api.domain.coupon.RemoteCouponVo;
import com.feidi.xx.cross.market.domain.MktActivityLine;
import com.feidi.xx.cross.market.domain.MktCoupon;
import com.feidi.xx.cross.market.domain.vo.MktCouponVo;
import com.feidi.xx.cross.market.mapper.MktCouponMapper;
import com.feidi.xx.cross.market.service.IMktActivityLineService;
import com.feidi.xx.cross.market.service.IMktCouponService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 优惠券服务接口实现类
 *
 * <AUTHOR>
 * @date 2024/9/7
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteCouponServiceImpl implements RemoteCouponService {

    private final IMktCouponService mktCouponService;
    private final MktCouponMapper mktMapper;
    private final MktCacheManager  mktCacheManager;

    /**
     * 获取优惠券详情
     * @param couponId
     * @return
     */
    @Override
    public RemoteCouponVo getCoupon(Long couponId) {
        MktCouponVo mktCouponVo = mktCouponService.queryById(couponId);
        return MapstructUtils.convert(mktCouponVo, RemoteCouponVo.class);
    }

    @Override
    public boolean deductStock(Long couponId, Long passengerId) {
        boolean stock = mktCacheManager.updateCouponStock(couponId, passengerId);
        if (stock){
            Long couponStock = mktCacheManager.getCouponStock(couponId);
            LambdaUpdateWrapper<MktCoupon> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(MktCoupon::getId, couponId)
                    .set(MktCoupon::getMargin, couponStock);
            return mktMapper.update(updateWrapper)>0;
        }
        return false;
    }

}
