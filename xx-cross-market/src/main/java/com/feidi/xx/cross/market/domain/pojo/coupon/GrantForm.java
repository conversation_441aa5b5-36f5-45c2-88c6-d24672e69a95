package com.feidi.xx.cross.market.domain.pojo.coupon;

import com.feidi.xx.common.core.validate.AddGroup;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 优惠卷发放参数
 *
 * <AUTHOR>
 */
@Data
public class GrantForm implements Serializable {
    private static final long serialVersionUID = 1L;

    private Map<Long, Integer> couponIds;

    private List<String> passengerPhones =new ArrayList<>();

}
