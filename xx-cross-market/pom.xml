<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.feidi.xx</groupId>
        <artifactId>xx-cross</artifactId>
        <version>3.0.0</version>
    </parent>

    <artifactId>xx-cross-market</artifactId>

    <description>
        xx-cross-market 营销模块
    </description>
    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-cross-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-common-oss</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-cross-api-power</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-cross-api-member</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-cross-api-order</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-cross-api-market</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-cross-api-finance</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-cross-api-message</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-common-xxl-job</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-common-map</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>3.1.1</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>