<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.feidi.xx</groupId>
        <artifactId>xx-parent</artifactId>
        <version>3.0.0</version>
        <relativePath/>
    </parent>
    <artifactId>xx-cross</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>
    <name>xx-cross</name>

    <modules>
        <module>xx-cross-api</module>
        <module>xx-cross-common</module>
        <module>xx-cross-finance</module>
        <module>xx-cross-operate</module>
        <module>xx-cross-order</module>
        <module>xx-cross-power</module>
        <module>xx-cross-member</module>
        <module>xx-cross-platform</module>
        <module>xx-cross-market</module>
        <module>xx-cross-message</module>
    </modules>

    <properties>
        <revision>3.0.0</revision>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>
    </properties>

</project>