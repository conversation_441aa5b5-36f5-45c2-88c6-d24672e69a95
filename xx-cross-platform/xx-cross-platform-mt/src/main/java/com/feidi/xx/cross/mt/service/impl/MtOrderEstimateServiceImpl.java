package com.feidi.xx.cross.mt.service.impl;

import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.map.model.tencent.regeo.TxMapReGeocode;
import com.feidi.xx.common.map.model.tencent.route.TxMapRoute;
import com.feidi.xx.common.map.utils.TxMapUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import com.feidi.xx.cross.common.cache.order.enums.OrdCacheKeyEnum;
import com.feidi.xx.cross.common.enums.operate.ProductCodeEnum;
import com.feidi.xx.cross.mt.constant.MtConstants;
import com.feidi.xx.cross.mt.domain.MtR;
import com.feidi.xx.cross.mt.domain.bo.MtEstimateBo;
import com.feidi.xx.cross.mt.domain.vo.MtEstimateVo;
import com.feidi.xx.cross.mt.service.IMtOrderEstimateService;
import com.feidi.xx.cross.operate.api.RemoteLineService;
import com.feidi.xx.cross.operate.api.RemotePriceService;
import com.feidi.xx.cross.operate.api.domain.price.bo.RemoteCalculateBo;
import com.feidi.xx.cross.operate.api.domain.price.dto.PriceDto;
import com.feidi.xx.cross.operate.api.domain.price.vo.RemoteCalculateVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 美团处理订单询价接口实现类
 *
 * <AUTHOR>
 * @date 2025/3/14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MtOrderEstimateServiceImpl implements IMtOrderEstimateService {

    private final OprCacheManager oprCacheManager;
    @DubboReference
    private final RemotePriceService remotePriceService;
    @DubboReference
    private final RemoteLineService remoteLineService;

    /**
     * 查询预估价
     *
     * @param estimateBo 预估价参数
     * @return 预估价结果
     */
    @Override
    public MtR estimatePrice(MtEstimateBo estimateBo) {
        if (log.isInfoEnabled()) {
            log.info("美团 - 询价参数：【{}】", estimateBo);
        }

        // 开始时间
        long startTime = DateUtils.getUnixTimeStamps();

        // 返回值
        MtEstimateVo mtEstimateVo = new MtEstimateVo();
        String estimateId = null;

        // 经纬度转换
        //Map<String, Double> startLatLon = LatLonUtils.map2tx(estimateBo.getStartPointLng(), estimateBo.getStartPointLat());
        //estimateBo.setStartPointLng(startLatLon.get("longitude"));
        //estimateBo.setStartPointLat(startLatLon.get("latitude"));
        //Map<String, Double> endLatLon = LatLonUtils.map2tx(estimateBo.getEndPointLng(), estimateBo.getEndPointLat());
        //estimateBo.setEndPointLng(endLatLon.get("longitude"));
        //estimateBo.setEndPointLat(endLatLon.get("latitude"));

        // 根据经纬度计算距离及所需时间（默认返回第一种路线方案）
        TxMapRoute.Route route = TxMapUtils.route(String.valueOf(estimateBo.getStartPointLng()), String.valueOf(estimateBo.getStartPointLat()),
                String.valueOf(estimateBo.getEndPointLng()), String.valueOf(estimateBo.getEndPointLat()));

        int distance = 0;
        if (ObjectUtils.isNotNull(route)) {
            // 默认使用第一种驾车方案
            distance = route.getDistance();
            mtEstimateVo.setDistance(distance);
            mtEstimateVo.setEstimateTime(route.getDuration());
        }

        // 计算 距离和预估时间 时间
        long routeTime = DateUtils.getUnixTimeStamps();
        if (log.isInfoEnabled()) {
            log.info("MT 询价 计算距离和预估时间 -时长【{}】", ArithUtils.sub(routeTime, startTime));
        }

        // 出发地
        TxMapReGeocode startReGeo = TxMapUtils.regeo(String.valueOf(estimateBo.getStartPointLng()), String.valueOf(estimateBo.getStartPointLat()));
        // 目的地
        TxMapReGeocode endReGeo = TxMapUtils.regeo(String.valueOf(estimateBo.getEndPointLng()), String.valueOf(estimateBo.getEndPointLat()));
        // 计算 逆地理 时间
        long regeoTime = DateUtils.getUnixTimeStamps();
        if (log.isInfoEnabled()) {
            log.info("MT 询价 计算逆地理 -时长【{}】", ArithUtils.sub(regeoTime, routeTime));
        }

        if (ObjectUtils.isNotNull(startReGeo) && ObjectUtils.isNotNull(endReGeo)) {
            // 计算预估价格
            RemoteCalculateBo remoteCalculateBo = this.createRemoteCalculateBo(estimateBo);
            remoteCalculateBo
                    .setMileage(distance)
                    .setStartAdCode(startReGeo.getAdInfo().getAdcode())
                    .setEndAdCode(endReGeo.getAdInfo().getAdcode());

            RemoteCalculateVo calculateVo = remotePriceService.calculatePrice(remoteCalculateBo);
            estimateId = calculateVo.getEstimateKey();
            mtEstimateVo.setEstimateId(estimateId);

            // 计算预估价格时间
            long estimateTime = DateUtils.getUnixTimeStamps();
            if (log.isInfoEnabled()) {
                log.info("MT 询价 预估价格【{}】", calculateVo);
                log.info("MT 询价 成功 计算预估价格 -时长【{}】", ArithUtils.sub(estimateTime, regeoTime));
            }

            // 将预估价按照 产品编码 分组
            Map<String, PriceDto> productCode2PriceMap = calculateVo.getPriceDtoList()
                    .stream().collect(Collectors.toMap(PriceDto::getProductCode, Function.identity(), (v1, v2) -> v1));

            // 拼车价
            PriceDto fitPrice = productCode2PriceMap.get(ProductCodeEnum.FIT.getCode());
            if (ObjectUtils.isNotNull(fitPrice)) {
                mtEstimateVo.setPoolSucPrice(Math.toIntExact(fitPrice.getCalculatePrice()));
                mtEstimateVo.setPoolFailPrice(Math.toIntExact(fitPrice.getCalculatePrice()));
            }

            // 独享价
            PriceDto rentPrice = productCode2PriceMap.get(ProductCodeEnum.RENT.getCode());
            if (ObjectUtils.isNotNull(rentPrice)) {
                mtEstimateVo.setEstimatePrice(Math.toIntExact(rentPrice.getCalculatePrice()));
            }
        }

        // 缓存数据
        String key = OrdCacheKeyEnum.ORD_ORDER_ESTIMATE_PRICE_KEY.create(MtConstants.PLATFORM_CODE, estimateId);
        RedisUtils.setCacheObject(key, mtEstimateVo, OrdCacheKeyEnum.ORD_ORDER_ESTIMATE_PRICE_KEY.getDuration());

        return MtR.ok(mtEstimateVo);
    }

    /**
     * 创建询价对象
     *
     * @param estimateBo 询价参数
     * @return 询价对象
     */
    private RemoteCalculateBo createRemoteCalculateBo(MtEstimateBo estimateBo) {
        return new RemoteCalculateBo()
                .setStartTime(estimateBo.getEarliestDepartureTime() / 1000)
                .setEndTime(estimateBo.getLatestDepartureTime() / 1000)
                .setPlatformCode(MtConstants.PLATFORM_CODE)
                //.setPriceType(PriceTypeEnum.STEP.getCode())
                .setPassengerCount(estimateBo.getPassengerNum())
                .setProductCode(ProductCodeEnum.FIT.getCode())
                .setStartLongitude(estimateBo.getStartPointLng())
                .setStartLatitude(estimateBo.getStartPointLat())
                .setStartAddress(estimateBo.getStartPointName())
                .setEndLongitude(estimateBo.getEndPointLng())
                .setEndLatitude(estimateBo.getEndPointLat())
                .setEndAddress(estimateBo.getEndPointName());
    }
}
