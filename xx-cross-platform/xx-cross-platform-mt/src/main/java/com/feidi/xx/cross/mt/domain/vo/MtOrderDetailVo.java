package com.feidi.xx.cross.mt.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 订单详情返回值
 *
 * <AUTHOR>
 * @date 2025/3/18
 */
@Data
public class MtOrderDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 是否拼成，司机操作到达目的地时需要同步该信息，实际账单金额依赖是否拼成 - N
     */
    private boolean isPoolSuccess;

    /**
     * 订单状态，具体值参见订单状态定义 - Y
     */
    private String status;

    /**
     * 合作方订单Id - Y
     */
    private String partnerOrderId;

    /**
     * 美团订单号 - Y
     */
    private String mtOrderId;

    /**
     * unix时间戳,ms - Y
     */
    private Long eventTime;

    /**
     * 司机选择接到乘客时间 unix时间戳,ms - Y
     */
    private Long pickUpTime;

    /**
     * 接单后司机信息，接单后取消场景必须传，其他情况不传
     */
    private MtDriverInfoVo driverInfo;

    /**
     * 客服等合作方取消订单相关信息，取消状态必传，其他状态不传
     */
    private MtOrderCancelVo customerServiceInfo;

    /**
     * 接单后车辆信息，接单后场景必须传，其他情况不传
     */
    private MtCarInfoVo carInfo;

    /**
     * 接单后车辆信息，接单后场景必须传，其他情况不传
     */
    private MtBillVo bill;
}
