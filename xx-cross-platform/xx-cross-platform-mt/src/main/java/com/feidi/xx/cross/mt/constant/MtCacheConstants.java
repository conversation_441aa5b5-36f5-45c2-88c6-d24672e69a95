package com.feidi.xx.cross.mt.constant;


import com.feidi.xx.common.core.constant.GlobalConstants;

/**
 * 哈啰平台 缓存常量
 *
 * <AUTHOR>
 * @date 2025/3/14
 */
public interface MtCacheConstants {

    /**
     * 缓存前缀
     */
    String PREFIX = GlobalConstants.GLOBAL_REDIS_KEY + "platform:mt:";

    /**
     * Token
     */
    String MT_TOKEN = PREFIX + "token:";

    /**
     * 订单过滤KEY前缀
     */
    String MT_ORDER_FILTER = PREFIX + "filter:";

    /**
     * 订单过滤 默认时间（分钟）
     */
    Integer MT_ORDER_FILTER_TIME = 20;

    /**
     * 预估KEY前缀
     */
    String MT_ESTIMATE = PREFIX + "estimate:";

    /**
     * 预估标识有效期
     */
    Integer MT_ESTIMATE_TIME = 10;

    /**
     * 订单KEY前缀
     */
    String MT_ORDER = PREFIX + "order:";

    /**
     * 订单缓存 默认时间（分钟）
     */
    Integer MT_ORDER_TIME = 20;

    /**
     * 取消费KEY前缀
     */
    String MT_CANCEL_FEE = PREFIX + "cancel:fee:";

    /**
     * 取消费缓存 默认时间（分钟）
     */
    Integer MT_CANCEL_FEE_TIME = 3 * 60;

}
