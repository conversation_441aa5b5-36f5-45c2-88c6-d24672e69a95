package com.feidi.xx.cross.mt.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.Serial;
import java.io.Serializable;

/**
 * 美团模块配置文件
 *
 * <AUTHOR>
 * @date 2024/8/29
 */
@Data
@Component
@ConfigurationProperties(prefix = "platform.mt")
public class MtConfig implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 接口地址
     */
    private String baseUrl;
}
