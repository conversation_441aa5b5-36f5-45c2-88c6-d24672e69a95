package com.feidi.xx.cross.mt.controller;

import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.mt.domain.MtBaseEntity;
import com.feidi.xx.cross.mt.domain.MtR;
import com.feidi.xx.cross.mt.domain.bo.MtEstimateBo;
import com.feidi.xx.cross.mt.domain.bo.MtOrderBo;
import com.feidi.xx.cross.mt.service.IMtOrderEstimateService;
import com.feidi.xx.cross.mt.service.IMtOrderService;
import com.feidi.xx.cross.platfrom.annotation.Tenant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 美团平台调用我方平台接口控制器类
 *
 * <AUTHOR>
 * @date 2025/3/13
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/partner")
public class MtController {

    private final IMtOrderService mtOrderService;
    private final IMtOrderEstimateService mtOrderEstimateService;

    /**
     * 查询预估价
     *
     * @param estimateBo 美团询价参数
     * @return 询价结果
     */
    //@Log(title = "美团", businessType = BusinessType.ENQUIRY)
    @Tenant(platformCode = PlatformCodeEnum.MT, channelName = "channel")
    @RequestMapping(value = "/estimatePrice", method = {RequestMethod.POST, RequestMethod.GET})
    public MtR estimatePrice(@ModelAttribute MtEstimateBo estimateBo) {
        return mtOrderEstimateService.estimatePrice(estimateBo);
    }

    /**
     * 下单
     *
     * @param mtOrderBo 下单参数
     * @return 下单结果
     */
    @Tenant(platformCode = PlatformCodeEnum.MT, channelName = "channel")
    @Log(title = "美团", businessType = BusinessType.SEND_ORDER)
    @PostMapping("/postOrder")
    public MtR postOrder(@ModelAttribute MtOrderBo mtOrderBo) {
        return mtOrderService.postOrder(mtOrderBo);
    }

    /**
     * 通知派单
     *
     * @param mtBaseEntity 通知派单参数
     * @return 通知派单结果
     */
    @Tenant(platformCode = PlatformCodeEnum.MT, channelName = "channel")
    @Log(title = "美团", businessType = BusinessType.CONFIRM_NOTIFY)
    @PostMapping("/confirmNotify")
    public MtR confirmNotify(@ModelAttribute MtBaseEntity mtBaseEntity) {
        return mtOrderService.confirmNotify(mtBaseEntity);
    }

    /**
     * 获取取消费
     *
     * @param mtBaseEntity 取消费参数
     * @return 取消费
     */
    @Tenant(platformCode = PlatformCodeEnum.MT, channelName = "channel")
    @Log(title = "美团", businessType = BusinessType.CANCEL_FEE)
    @PostMapping("/getOrderCancelFee")
    public MtR getOrderCancelFee(@ModelAttribute MtBaseEntity mtBaseEntity) {
        return mtOrderService.getOrderCancelFee(mtBaseEntity);
    }

    /**
     * 取消订单
     *
     * @param mtBaseEntity 取消订单参数
     * @return 取消订单结果
     */
    @Tenant(platformCode = PlatformCodeEnum.MT, channelName = "channel")
    @Log(title = "美团", businessType = BusinessType.CANCEL_ORDER)
    @PostMapping("/cancelOrder")
    public MtR cancelOrder(@ModelAttribute MtBaseEntity mtBaseEntity) {
        return mtOrderService.cancelOrder(mtBaseEntity);
    }

    /**
     * 查询订单详情（轮询接口）
     *
     * @param mtBaseEntity 查询订单详情参数
     * @return 订单详情信息
     */
    @Tenant(platformCode = PlatformCodeEnum.MT, channelName = "channel")
    @Log(title = "美团", businessType = BusinessType.ORDER_INFO)
    @RequestMapping(value = "/pollingOrderStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public MtR pollingOrderStatus(@ModelAttribute MtBaseEntity mtBaseEntity) {
        return mtOrderService.pollingOrderStatus(mtBaseEntity);
    }

    /**
     * 乘客确认上车
     *
     * @param mtBaseEntity 确认上车参数
     * @return 确认上车结果
     */
    @Tenant(platformCode = PlatformCodeEnum.MT, channelName = "channel")
    @Log(title = "美团", businessType = BusinessType.TRIP_START)
    @PostMapping("/aboardNotify")
    public MtR aboardNotify(@ModelAttribute MtBaseEntity mtBaseEntity) {
        return mtOrderService.aboardNotify(mtBaseEntity);
    }

    /**
     * 乘客确认下车
     *
     * @param mtBaseEntity 确认下车参数
     * @return 确认下车结果
     */
    @Tenant(platformCode = PlatformCodeEnum.MT, channelName = "channel")
    @Log(title = "美团", businessType = BusinessType.TRIP_END)
    @PostMapping("/reachDestinationNotify")
    public MtR reachDestinationNotify(@ModelAttribute MtBaseEntity mtBaseEntity) {
        return mtOrderService.reachDestinationNotify(mtBaseEntity);
    }

    /**
     * 查询司机位置
     *
     * @param mtBaseEntity 查询司机位置参数
     * @return 司机位置信息
     */
    @Tenant(platformCode = PlatformCodeEnum.MT, channelName = "channel")
    @Log(title = "美团", businessType = BusinessType.LOCATION)
    @RequestMapping(value = "/driverLocation", method = {RequestMethod.POST, RequestMethod.GET})
    public MtR driverLocation(@ModelAttribute MtBaseEntity mtBaseEntity) {
        return mtOrderService.driverLocation(mtBaseEntity);
    }

    /**
     * 获取司机手机号
     *
     * @param mtBaseEntity 获取司机手机号参数
     * @return 获取司机手机号结果
     */
    @Tenant(platformCode = PlatformCodeEnum.MT, channelName = "channel")
    @Log(title = "美团", businessType = BusinessType.VIRTUAL)
    @RequestMapping(value = "/queryDriverPhoneInfo", method = {RequestMethod.POST, RequestMethod.GET})
    public MtR queryDriverPhoneInfo(@ModelAttribute MtBaseEntity mtBaseEntity) {
        return mtOrderService.queryDriverPhoneInfo(mtBaseEntity);
    }

    /**
     * 推送乘客消息
     *
     * @param mtBaseEntity
     * @return
     */
    @Log(title = "美团", businessType = BusinessType.RECEIVE_IM)
    @PostMapping("/passengerSendMsg")
    public MtR passengerSendMsg(@ModelAttribute MtBaseEntity mtBaseEntity) {
        return mtOrderService.passengerSendMsg(mtBaseEntity);
    }

    /**
     * 推送乘客已读消息
     *
     * @param mtBaseEntity
     * @return
     */
    @Log(title = "美团", businessType = BusinessType.RECEIVE_IM)
    @PostMapping("/passengerReadMsg")
    public MtR passengerReadMsg(@ModelAttribute MtBaseEntity mtBaseEntity) {
        return mtOrderService.passengerReadMsg(mtBaseEntity);
    }

}
