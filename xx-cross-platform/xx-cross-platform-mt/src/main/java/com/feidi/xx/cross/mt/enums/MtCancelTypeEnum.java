package com.feidi.xx.cross.mt.enums;


import com.feidi.xx.cross.common.enums.order.CancelTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 美团订单取消类型
 *
 * <AUTHOR>
 * @date 2025/3/14
 */
@Getter
@AllArgsConstructor
public enum MtCancelTypeEnum {
    PARTNER("1", "合作商原因取消", CancelTypeEnum.THIRD.getCode()),
    PASSENGER("2", "乘客原因取消", CancelTypeEnum.PASSENGER.getCode()),
    DRIVER("3", "司机原因取消", CancelTypeEnum.DRIVER.getCode()),
    CUSTOMER("4", "客服原因取消", CancelTypeEnum.CUSTOMER_SERVICE.getCode()),
    OVERTIME_UNPAID("5", "超时未支付原因取消", CancelTypeEnum.UNPAID.getCode()),
    OVERTIME_NO("6", "超时未接单原因取消", CancelTypeEnum.RECEIVE.getCode()),
    OVERTIME_DEPART("7", "超时未出发原因取消", CancelTypeEnum.TIME_OUT.getCode()),
    THIRD_CUSTOMER("8", "第三方客服原因取消", CancelTypeEnum.CUSTOMER_SERVICE.getCode()),
    FAILURE_FAIL("9", "抢单失败原因取消", CancelTypeEnum.TIME_OUT.getCode()),
    OTHER("10", "异常取消", CancelTypeEnum.TIME_OUT.getCode()),
    COMPLAIN("11", "客诉", CancelTypeEnum.COMPLAIN.getCode());

    private final String code;
    private final String info;
    private final String cancelType;

    public static String getInfoByCode(String code) {
        for (MtCancelTypeEnum value : MtCancelTypeEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getInfo();
            }
        }
        return null;
    }
}
