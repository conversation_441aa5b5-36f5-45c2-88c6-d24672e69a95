package com.feidi.xx.cross.mt.service.api;

import cn.hutool.core.lang.Dict;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.feidi.xx.common.core.domain.platform.PlatformApiResponseVo;
import com.feidi.xx.common.core.enums.SuccessFailEnum;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import com.feidi.xx.cross.mt.config.MtConfig;
import com.feidi.xx.cross.mt.constant.MtApiConstants;
import com.feidi.xx.cross.mt.constant.MtConstants;
import com.feidi.xx.cross.platfrom.utils.MtSignUtil;
import com.feidi.xx.cross.operate.api.domain.platform.vo.RemotePlatformVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * 调用美团平台的接口
 *
 * <AUTHOR>
 * @date 2024/9/11
 */
@Slf4j
@Service
public class MtApiService {

    @Resource
    private MtConfig mtConfig;
    @Resource
    private OprCacheManager oprCacheManager;

    /**
     * 发送请求
     *
     * @param paramMap 请求参数
     * @param url      请求地址
     * @return
     */
    public PlatformApiResponseVo sendHttp(Map<String, Object> paramMap, String url) {

        log.info("url: {}, mtConfig: {}", url, JsonUtils.toJsonString(mtConfig));

        RemotePlatformVo platformVo = oprCacheManager.getPlatformInfoByCode(MtConstants.PLATFORM_CODE);

        paramMap.put(MtApiConstants.PARAM_FIELD_CHANNEL, platformVo.getAppId());
        paramMap.put(MtApiConstants.PARAM_FIELD_TIMESTAMP, DateUtils.getUnixTimeStamps());
        paramMap.put(MtApiConstants.PARAM_FIELD_SIGN, MtSignUtil.sign(paramMap, platformVo.getAppSecret()));

        // 最终的参数
        String params = JSON.toJSONString(paramMap);

        if (log.isInfoEnabled()) {
            log.info("MT ++++++++++++++++++ MT  API  START +++++++++++++++++++++++++++ MT");
            log.info("美团请求参数：【{}】， 请求路径：【{}】", params, url);
        }

        String result = HttpRequest.post(mtConfig.getBaseUrl() + url)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                // 参数
                .body(params)
                .execute()
                .body();

        if (log.isInfoEnabled()) {
            log.info("美团请求返回值：【{}】+++++", result);
            log.info("MT ++++++++++++++++++ MT  API  END +++++++++++++++++++++++++++ MT");
        }

        Dict dict = JsonUtils.parseMap(result);
        // 返回的数据信息
        Integer code = dict.getInt(MtApiConstants.RETURN_CODE_FIELD);
        String message = dict.getStr(MtApiConstants.RETURN_MSG_FIELD);

        PlatformApiResponseVo apiResponseVo = new PlatformApiResponseVo();
        // 默认成功
        apiResponseVo.setStatus(SuccessFailEnum.SUCCESS.getCode());

        // 是否成功 - 失败
        if (!Objects.equals(code, MtApiConstants.MT_API_SUCCESS)) {
            apiResponseVo.setStatus(SuccessFailEnum.FAIL.getCode());
        }

        // 状态码
        apiResponseVo.setCode(code);
        // 返回的提示信息
        apiResponseVo.setMsg(message);
        // 返回的所有信息
        apiResponseVo.setResponse(result);

        // 返回的数据信息
        Object data = dict.getObj(MtApiConstants.RETURN_DATA_FIELD);
        // 返回的数据信息
        apiResponseVo.setData(JSON.toJSONString(data));

        return apiResponseVo;
    }
}
