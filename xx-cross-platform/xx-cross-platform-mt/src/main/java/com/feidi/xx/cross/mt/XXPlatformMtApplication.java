package com.feidi.xx.cross.mt;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 第三方平台 - 美团模块
 *
 * <AUTHOR>
 */
@EnableDubbo
@MapperScan("com.feidi.xx.**.mapper")
@SpringBootApplication(scanBasePackages = {"com.feidi.xx.**"})
public class XXPlatformMtApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(XXPlatformMtApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  第三方平台 - 美团模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }

}
