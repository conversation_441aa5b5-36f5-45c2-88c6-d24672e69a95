package com.feidi.xx.cross.mt.domain.bo;

import com.feidi.xx.cross.mt.domain.MtBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 下单参数
 *
 * <AUTHOR>
 * @date 2025/3/14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MtOrderBo extends MtBaseEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 城市id 必须
     */
    private String cityId;

    /**
     * 起点地址，如"北京市朝阳区望京东路4号"
     */
    private String startPointAddress;

    /**
     * 出发地经度 必须
     */
    private double startPointLng;

    /**
     * 出发地纬度 必须
     */
    private double startPointLat;

    /**
     * 出发地名称
     */
    private String startPointName;

    /**
     * 终点地址，如"北京市丰台区莲花池东路118号"
     */
    private String endPointAddress;

    /**
     * 目的地经度 必须
     */
    private Double endPointLng;

    /**
     * 目的地纬度 必须
     */
    private Double endPointLat;

    /**
     * 目的地名称
     */
    private String endPointName;

    /**
     * 美团订单ID，长度31-33个字符
     */
    private String mtOrderId;

    /**
     * 真实手机号后4位
     */
    private String realPhoneSuffix;

    /**
     * 用户唯一身份id，固定长度66位
     */
    private String uniqueUserId;

    /**
     * 最早出发时间，Unixtimestamp，单位毫秒 必须
     */
    private Long earliestDepartureTime;

    /**
     * 最晚出发时间，Unixtimestamp，单位毫秒 必须
     */
    private Long latestDepartureTime;

    /**
     * 乘客人数 必须
     */
    private Integer passengerNum;

    /**
     * 1-独享 2-拼车 必须
     */
    private Integer poolType;

    /**
     * 1-愿意承担高速费 2-不愿承担高速费 3-愿意分摊高速费
     */
    private Integer bearHighwayFeeType;

    /**
     * 捎句话
     */
    private List<String> message;

    /**
     * 乘客支付金额，单位为分，非必传
     */
    private Integer amount;

    /**
     * 预估标识，创建订单时使用，有效期10分钟 必须
     */
    private String estimateId;

}
