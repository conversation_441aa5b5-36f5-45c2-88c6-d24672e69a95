package com.feidi.xx.cross.mt.service.helper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.feidi.xx.common.core.enums.PaymentStatusEnum;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.map.model.amap.regeo.Regeocode;
import com.feidi.xx.common.map.utils.GaoDeMapUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.operate.enums.OprCacheKeyEnum;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import com.feidi.xx.cross.common.cache.operate.vo.OprEstimateRecordCacheVo;
import com.feidi.xx.cross.common.cache.order.enums.OrdCacheKeyEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.enums.platform.MtOrderStatusEnum;
import com.feidi.xx.cross.mt.constant.MtApiConstants;
import com.feidi.xx.cross.mt.constant.MtCacheConstants;
import com.feidi.xx.cross.mt.constant.MtConstants;
import com.feidi.xx.cross.mt.domain.MtBaseEntity;
import com.feidi.xx.cross.mt.domain.bo.MtOrderBo;
import com.feidi.xx.cross.mt.domain.vo.MtEstimateVo;
import com.feidi.xx.cross.mt.enums.MTPoolTypeEnum;
import com.feidi.xx.cross.mt.enums.MtCancelTypeEnum;
import com.feidi.xx.cross.operate.api.domain.platform.vo.RemotePlatformVo;
import com.feidi.xx.cross.operate.api.domain.product.vo.RemoteProductVo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderBo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderHandleBo;
import com.feidi.xx.cross.order.api.domain.bo.RemotePositionBo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Arrays;
import java.util.Objects;

/**
 * 美团订单服务辅助类
 *
 * <AUTHOR>
 * @date 2025/3/14
 */
@Slf4j
@Component
@AllArgsConstructor
public class MtOrderHelper {

    private final OprCacheManager oprCacheManager;

    /**
     * 订单过滤
     *
     * @param mtOrderBo        美团订单参数
     * @param remotePlatformVo 平台信息
     */
    public void orderFilter(MtOrderBo mtOrderBo, RemotePlatformVo remotePlatformVo) {
        // 去验签
        this.checkSign(mtOrderBo);

        // 订单是否存在
        String allParamsKey = MtCacheConstants.MT_ORDER_FILTER + SecureUtil.md5(JsonUtils.toJsonString(mtOrderBo));
        if (RedisUtils.hasKey(allParamsKey)) {
            throw new ServiceException("重复下单", MtApiConstants.RETURN_CODE_OTHER);
        }

        // 将全部参数缓存起来
        RedisUtils.setCacheObject(allParamsKey, mtOrderBo.getMtOrderId() + mtOrderBo.getPoolType(), Duration.ofMinutes(MtCacheConstants.MT_ORDER_FILTER_TIME));
    }

    /**
     * 验签
     *
     * @param mtOrderBo
     */
    private void checkSign(MtOrderBo mtOrderBo) {

    }

    /**
     * 整理订单参数
     *
     * @param mtOrderBo
     * @param remotePlatformVo
     * @return
     */
    public RemoteOrderBo handlePlaceOrderParams(MtOrderBo mtOrderBo, RemotePlatformVo remotePlatformVo) {
        // 整理下单参数
        RemoteOrderBo orderBo = new RemoteOrderBo()
                .setPhoneEnd(mtOrderBo.getRealPhoneSuffix())
                .setPlatformCode(MtConstants.PLATFORM_CODE)
                .setPlatformNo(mtOrderBo.getMtOrderId())
                .setOrderPrice(Long.valueOf(mtOrderBo.getAmount()))
                .setPayPrice(Long.valueOf(mtOrderBo.getAmount()));

        String key = OprCacheKeyEnum.OPR_ESTIMATE_RECORD_KEY.create(mtOrderBo.getEstimateId());
        OprEstimateRecordCacheVo estimateRecord = RedisUtils.getCacheObject(key);

        if (ObjectUtils.isNull(estimateRecord)) {
            log.error("订单询价记录不存在，估价id: 【{}】", mtOrderBo.getEstimateId());
            throw new ServiceException("订单询价记录不存在，下单失败");
        }

        // 线路id
        orderBo.setLineId(estimateRecord.getLineId());

        // 产品类型
        String productCode = MTPoolTypeEnum.getProductCodeByCode(mtOrderBo.getPoolType());
        // 校验产品类型
        RemoteProductVo remoteProductVo = oprCacheManager.getProductInfoByCode(productCode);
        if (ObjectUtils.isNull(remoteProductVo) || Objects.equals(remoteProductVo.getStatus(), StatusEnum.DISABLE.getCode())) {
            log.error("产品编码不存在，产品编码: 【{}】", productCode);
            throw new ServiceException("产品编码不存在");
        }
        orderBo.setProductCode(productCode);
        // 第三方订单状态
        orderBo.setThirdStatus(MtOrderStatusEnum.SUBMIT.getCode());

        // 整理时间
        if (ObjectUtils.isNotNull(mtOrderBo.getEarliestDepartureTime())) {
            orderBo.setEarliestTime(DateUtils.timestampToStringTime(mtOrderBo.getEarliestDepartureTime()));
        }
        if (ObjectUtils.isNotNull(mtOrderBo.getLatestDepartureTime())) {
            orderBo.setLatestTime(DateUtils.timestampToStringTime(mtOrderBo.getLatestDepartureTime()));
        }
        if (ObjectUtils.isNotNull(mtOrderBo.getEarliestDepartureTime()) && ObjectUtils.isNotNull(mtOrderBo.getLatestDepartureTime())) {
            long minute = (mtOrderBo.getLatestDepartureTime() - mtOrderBo.getEarliestDepartureTime()) / 1000 / 60;
            orderBo.setMaxWaitDuration((int) minute);
        }

        // 整理乘客信息
        orderBo.setPassengerNum(mtOrderBo.getPassengerNum());

        if (CollUtil.isNotEmpty(mtOrderBo.getMessage())) {
            // 处理乘客备注
            orderBo.setPassengerRemark(this.handlePassengerRemark(JsonUtils.toJsonString(mtOrderBo.getMessage()), mtOrderBo.getBearHighwayFeeType()));
        }

        // 线路方向
        OprEstimateRecordCacheVo cacheVo = RedisUtils.getCacheObject(OprCacheKeyEnum.OPR_ESTIMATE_RECORD_KEY.create(mtOrderBo.getEstimateId()));
        if (ObjectUtils.isNotNull(cacheVo)) {
            orderBo.setLineDirection(cacheVo.getRouteType());
        }

        // 获取预估价格
        String estimateKey = OrdCacheKeyEnum.ORD_ORDER_ESTIMATE_PRICE_KEY.create(MtConstants.PLATFORM_CODE, mtOrderBo.getEstimateId());
        MtEstimateVo mtEstimateVo = RedisUtils.getCacheObject(estimateKey);
        // 里程
        orderBo.setMileage(mtEstimateVo.getDistance());
        // 预计时长
        orderBo.setExpectDuration(mtEstimateVo.getEstimateTime());

        // 订单状态
        orderBo.setStatus(OrderStatusEnum.CREATE.getCode());
        // 支付状态
        if (mtOrderBo.getAmount() != null && mtOrderBo.getAmount() > 0) {
            orderBo.setPayStatus(PaymentStatusEnum.SUCCESS.getCode());
            orderBo.setPayTime(DateUtils.getNowDate());
        } else {
            orderBo.setPayStatus(PaymentStatusEnum.NO.getCode());
        }
        // 高速费类型
        if (mtOrderBo.getBearHighwayFeeType() != null) {
            orderBo.setHighwayType(String.valueOf(mtOrderBo.getBearHighwayFeeType()));
        }

        //  起点地址
        RemotePositionBo startPosition = this.bingPositionBo(mtOrderBo.getStartPointLng(), mtOrderBo.getStartPointLat(), mtOrderBo.getStartPointName());
        orderBo.setStartCityCode(startPosition.getCityCode());
        orderBo.setStartPosition(startPosition);

        // 终点地址
        RemotePositionBo endPosition = this.bingPositionBo(mtOrderBo.getEndPointLng(), mtOrderBo.getEndPointLat(), mtOrderBo.getEndPointName());
        orderBo.setEndCityCode(endPosition.getCityCode());
        orderBo.setEndPosition(endPosition);

        // 整理订单时间
        if (log.isInfoEnabled()) {
            log.info("MT 下单 整理订单时间 -时长【{}】", ArithUtils.sub(DateUtils.getUnixTimeStamps(), mtOrderBo.getTimestamp()));
        }

        // 美团发单时候时间戳
        //orderBo.setTimeStamp(mtOrderBo.getTimestamp());

        return orderBo;
    }

    /**
     * 处理乘客备注
     * 处理前: ["[乘客承担高速费、过桥费","有大件行李，需要后备箱","携带宠物，我会照顾好]"]
     * 处理后: 1.有大件行李，需要后备箱 2.携带宠物，我会照顾好
     *
     * @param passengerRemark    乘客备注信息
     * @param bearHighwayFeeType 高速费类型
     * @return 处理后的乘客备注信息
     */
    private String handlePassengerRemark(String passengerRemark, Integer bearHighwayFeeType) {
        if (StringUtils.isBlank(passengerRemark) || passengerRemark.length() <= 6) {
            return null;
        }
        try {
            passengerRemark = passengerRemark.replaceAll(" ", "").replaceAll("\"", "");
            passengerRemark = passengerRemark.substring(2, passengerRemark.length() - 2);
            String[] passengerRemarks = passengerRemark.split(",");

            passengerRemarks = bearHighwayFeeType != null ? Arrays.copyOfRange(passengerRemarks, 1, passengerRemarks.length) : passengerRemarks;

            StringBuilder remark = new StringBuilder();
            for (int index = 0; index < passengerRemarks.length; index++) {
                remark.append(index + 1).append(".").append(passengerRemarks[index]).append(" ");
            }

            return remark.toString();
        } catch (Exception e) {
            log.error("美团处理乘客备注异常", e);
        }

        return null;
    }

    /**
     * 绑定地址信息
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return 地址信息
     */
    private RemotePositionBo bingPositionBo(Double longitude, Double latitude, String shortAddr) {
        Regeocode regeo = GaoDeMapUtils.regeo(longitude.toString(), latitude.toString());

        if (StringUtils.isBlank(shortAddr) || Objects.equals(shortAddr, "当前位置")) {
            // 处理地址信息，去除全量地址的省市区信息
            shortAddr = regeo.getFormattedAddress();
            if (StringUtils.isNotBlank(regeo.getFormattedAddress())) {
                shortAddr = StrUtil.replace(shortAddr, regeo.getAddressComponent().getProvince(), "");
                shortAddr = StrUtil.replace(shortAddr, regeo.getAddressComponent().getCity(), "");
                shortAddr = StrUtil.replace(shortAddr, regeo.getAddressComponent().getDistrict(), "");
            }
        }
        return new RemotePositionBo()
                .setProvince(regeo.getAddressComponent().getProvince())
                .setDistrict(regeo.getAddressComponent().getDistrict())
                .setCity(regeo.getAddressComponent().getCity())
                .setCityCode(regeo.getAddressComponent().getCitycode())
                .setAdCode(regeo.getAddressComponent().getAdcode())
                .setAddress(regeo.getFormattedAddress())
                .setShortAddr(shortAddr)
                .setLongitude(longitude)
                .setLatitude(latitude)
                .setStatus(StatusEnum.ENABLE.getCode());
    }

    /**
     * 获取订单金额
     *
     * @param amount     美团订单金额
     * @param poolType   美团订单类型
     * @param estimateId 预估价ID
     * @return 订单金额
     */
    public Integer getOrderAmount(Integer amount, Integer poolType, String estimateId) {
        if (amount != null && amount > 0) {
            return amount;
        }
        String key = OrdCacheKeyEnum.ORD_ORDER_ESTIMATE_PRICE_KEY.create(MtConstants.PLATFORM_CODE, estimateId);
        MtEstimateVo mtEstimateVo = RedisUtils.getCacheObject(key);
        if (ObjectUtils.isNull(mtEstimateVo)) {
            throw new ServiceException("预估价信息不存在", MtApiConstants.RETURN_CODE_OTHER);
        }
        if (Objects.equals(poolType, MTPoolTypeEnum.RENT.getCode())) {
            // 独享
            amount = mtEstimateVo.getEstimatePrice();
        } else if (Objects.equals(poolType, MTPoolTypeEnum.FIT.getCode())) {
            // 拼车
            amount = mtEstimateVo.getPoolSucPrice();
        }
        return amount;
    }

    /**
     * 创建订单处理对象
     * @param mtBaseEntity 基础参数
     * @param thirdStatus 第三方状态
     * @param remark 备注
     * @return
     */
    public RemoteOrderHandleBo createHandleBo(MtBaseEntity mtBaseEntity, String thirdStatus, String remark) {
        RemoteOrderHandleBo handleBo = new RemoteOrderHandleBo();
        handleBo.setPlatformCode(MtConstants.PLATFORM_CODE);
        handleBo.setPlatformNo(mtBaseEntity.getMtOrderId());
        handleBo.setUserType(UserTypeEnum.MT.getUserType());
        handleBo.setUserId(0L);
        handleBo.setTimeStamp(mtBaseEntity.getTimestamp());
        handleBo.setThirdStatus(thirdStatus);
        handleBo.setRemark(remark);
        return handleBo;
    }

    /**
     * 处理取消类型和取消备注
     *
     * @param reason   取消原因
     * @param handleBo 取消参数
     */
    public void handleCancelTypeAndRemark(String reason, RemoteOrderHandleBo handleBo) {
        String cancelRemark = reason;
        switch (reason) {
            case "1001":
                handleBo.setCancelType(MtCancelTypeEnum.PASSENGER.getCancelType());
                cancelRemark += "-用户取消：用户主动发起的取消。";
                break;
            case "1002":
                handleBo.setCancelType(MtCancelTypeEnum.OVERTIME_DEPART.getCancelType());
                cancelRemark += "-超时取消：合作方响应慢导致的取消，场景包括：1. 平台默认的时间窗口内(基于供需状况调整)没有响应；2. 同一订单别的合作方响应后n秒（基于供需状况调整）内没有响应。";
                break;
            case "1003":
                handleBo.setCancelType(MtCancelTypeEnum.FAILURE_FAIL.getCancelType());
                cancelRemark += "-竞争失败取消：多个合作方返回候选运力场景下，平台会基于用户体验原则将订单分配给最优的一个合作方，其他合作方则为竞争失败。";
                break;
            case "1004":
                handleBo.setCancelType(MtCancelTypeEnum.OTHER.getCancelType());
                cancelRemark += "-准入过滤取消：平台对候选运力有接驾时间和接驾距离的要求，不满足要求的运力会被平台取消。运力要求参见下单接口maxEda和maxEta参数。";
                break;
            case "1005":
                handleBo.setCancelType(MtCancelTypeEnum.OTHER.getCancelType());
                cancelRemark += "-风控取消：平台会基于风控规则对运力进行过滤，进而导致的取消。";
                break;
            case "1006":
                handleBo.setCancelType(MtCancelTypeEnum.OTHER.getCancelType());
                cancelRemark += "-司机不符合接单条件：因限行、司乘拉黑、司机封禁等原因导致的取消。";
                break;
            default:
                handleBo.setCancelType(MtCancelTypeEnum.PASSENGER.getCancelType());
                break;
        }
        // 取消备注
        handleBo.setCancelRemark(cancelRemark);
    }
}
