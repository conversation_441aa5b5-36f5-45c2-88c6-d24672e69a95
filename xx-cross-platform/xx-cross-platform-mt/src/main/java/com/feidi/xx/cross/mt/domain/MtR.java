package com.feidi.xx.cross.mt.domain;

import com.feidi.xx.common.core.constant.HttpStatus;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 美团响应信息主体
 *
 * <AUTHOR>
 * @date 2025/3/13
 */
@Data
@NoArgsConstructor
public class MtR<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 成功
     */
    public static final int SUCCESS = 0;

    /**
     * 失败
     */
    public static final int FAIL = 500;

    /**
     * 消息状态码
     */
    private int result;

    /**
     * 消息内容
     */
    private String message;

    /**
     * 数据对象
     */
    private T data;

    public static <T> MtR<T> ok() {
        return restResult(null, SUCCESS, "操作成功");
    }

    public static <T> MtR<T> ok(T data) {
        return restResult(data, SUCCESS, "操作成功");
    }

    public static <T> MtR<T> ok(String msg) {
        return restResult(null, SUCCESS, msg);
    }

    public static <T> MtR<T> ok(String msg, T data) {
        return restResult(data, SUCCESS, msg);
    }

    public static <T> MtR<T> fail() {
        return restResult(null, FAIL, "操作失败");
    }

    public static <T> MtR<T> fail(String msg) {
        return restResult(null, FAIL, msg);
    }

    public static <T> MtR<T> fail(T data) {
        return restResult(data, FAIL, "操作失败");
    }

    public static <T> MtR<T> fail(String msg, T data) {
        return restResult(data, FAIL, msg);
    }

    public static <T> MtR<T> fail(int code, String msg) {
        return restResult(null, code, msg);
    }

    /**
     * 返回警告消息
     *
     * @param msg 返回内容
     * @return 警告消息
     */
    public static <T> MtR<T> warn(String msg) {
        return restResult(null, HttpStatus.WARN, msg);
    }

    /**
     * 返回警告消息
     *
     * @param msg  返回内容
     * @param data 数据对象
     * @return 警告消息
     */
    public static <T> MtR<T> warn(String msg, T data) {
        return restResult(data, HttpStatus.WARN, msg);
    }

    private static <T> MtR<T> restResult(T data, int code, String msg) {
        MtR<T> r = new MtR<>();
        r.setResult(code);
        r.setData(data);
        r.setMessage(msg);
        return r;
    }

    public static <T> Boolean isError(MtR<T> ret) {
        return !isSuccess(ret);
    }

    public static <T> Boolean isSuccess(MtR<T> ret) {
        return MtR.SUCCESS == ret.getResult();
    }
}
