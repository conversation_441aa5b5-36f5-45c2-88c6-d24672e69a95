package com.feidi.xx.cross.mt.constant;

/**
 * 美团平台 API 常量
 *
 * <AUTHOR>
 * @date 2025/3/14
 */
public interface MtApiConstants {
    /**
     * 美团调平台时回执 超时时间 1000 毫秒
     */
    int TIME_OUT = 10000;

    /**
     * 美团调平台时回执状态码 - 成功
     */
    int RETURN_CODE_SUCCESS = 0;

    /**
     * 美团调平台时回执状态码 - 服务异常
     */
    int RETURN_CODE_FAIL = 500;

    /**
     * 美团调平台时回执状态码 - 围栏校验不返回价格
     */
    int RETURN_CODE_LINE = 300;

    /**
     * 美团调平台时回执状态码 - 起点不在围栏内
     */
    int RETURN_CODE_NO_START = 301;

    /**
     * 美团调平台时  返回 状态码 - 终点不在围栏内
     */
    int RETURN_CODE_NO_END = 302;

    /**
     * 美团调平台时  返回 状态码 - 线路不存在
     */
    int RETURN_CODE_NO_LINE = 303;

    /**
     * 美团调平台时  返回 状态码 - 价格未配置(各业务方可返回具体的错误信息，如601，602)
     */
    int RETURN_CODE_NO_PRICE = 600;

    /**
     * 美团调平台时  返回 状态码 - 其他异常(各业务方可返回具体的错误信息)
     */
    int RETURN_CODE_OTHER = 1000;

    //========================================================
    //=========  平台调美团相关  ==============
    //========================================================
    /**
     * 平台调美团时 必传公共参数字段  请求的方法名
     */
    String PARAM_FIELD_CHANNEL = "channel";

    /**
     * 平台调美团时 必传公共参数字段  时间戳，单位s
     */
    String PARAM_FIELD_TIMESTAMP = "timestamp";

    /**
     * 平台调美团时 必传公共参数字段  签名
     */
    String PARAM_FIELD_SIGN = "sign";

    //========================================================
    //=========  平台调美团相关 状态码  ==============
    //========================================================
    /**
     * 平台调美团 返回值 成功
     */
    Integer MT_API_SUCCESS = 0;


    /**
     * 平台响应的请求状态码 状态码字段
     */
    String RETURN_CODE_FIELD = "code";
    /**
     * 平台响应的请求状态描述  消息字段
     */
    String RETURN_MSG_FIELD = "msg";

    /**
     * 业务响应的数据  数据字段
     */
    String RETURN_DATA_FIELD = "data";
}
