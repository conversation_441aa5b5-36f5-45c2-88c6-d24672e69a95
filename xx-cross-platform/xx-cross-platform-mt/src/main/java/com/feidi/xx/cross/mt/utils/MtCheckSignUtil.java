package com.feidi.xx.cross.mt.utils;

import com.feidi.xx.cross.platfrom.utils.MtSignUtil;

import java.util.Map;
import java.util.Objects;

/**
 * 美团验签工具
 *
 * <AUTHOR>
 * @date 2025/4/2
 */
public class MtCheckSignUtil {

    /**
     * 验签
     *
     * @param params 待验签参数
     * @param signKey 密钥
     * @param signValue 签名
     * @return 验签是否通过
     */
    public static boolean checkSign(Map<String, Object> params, String signKey, String signValue) {
        String sign = MtSignUtil.sign(params, signKey);
        return Objects.equals(sign, signValue);
    }
}
