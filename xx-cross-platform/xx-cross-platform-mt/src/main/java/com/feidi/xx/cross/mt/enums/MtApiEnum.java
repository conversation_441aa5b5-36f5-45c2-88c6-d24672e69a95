package com.feidi.xx.cross.mt.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 美团平台 方法url枚举
 *
 * <AUTHOR>
 * @date @date 2025/3/14
 */
@Getter
@AllArgsConstructor
public enum MtApiEnum {

    PUSH_ORDER_STATUS("/api/hitchhike/callback/common/v1/pushOrderStatus", "推送订单状态"),
    PUSH_POOL_FRIENDS_INFO("/api/hitchhike/callback/common/v1/pushPoolFriendsInfo", "推送拼友信息"),
    QUERY_PASSENGER_PHONE("/api/hitchhike/callback/common/v1/queryPassengerPhone", "获取乘客手机号"),
    DRIVER_SEND_MSG("/api/hitchhike/callback/common/driverSendMsg", "推送司机消息"),
    DRIVER_READ_MSG("/api/hitchhike/callback/common/driverAlreadyReadMsg", "推送司机消息"),
    QUERY_ORDER_BY_USER_PHONE("/api/hitchhike/callback/common/queryOrderByUserPhone", "根据乘客号码查询订单"),
    APPLY_REFUND("/api/hitchhike/callback/common/applyRefund", "退款通知"),
    ;

    private final String url;
    private final String info;

}
