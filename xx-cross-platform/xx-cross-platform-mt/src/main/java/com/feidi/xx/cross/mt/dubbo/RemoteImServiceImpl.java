package com.feidi.xx.cross.mt.dubbo;

import cn.hutool.core.bean.BeanUtil;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.cross.mt.domain.bo.MtDriverReadMsgBo;
import com.feidi.xx.cross.mt.domain.bo.MtDriverSendMsgBo;
import com.feidi.xx.cross.mt.enums.MtApiEnum;
import com.feidi.xx.cross.mt.service.api.MtApiService;
import com.feidi.xx.cross.platform.api.hbk.domain.hbk.vo.RemotePlatformApiResponseVo;
import com.feidi.xx.cross.platform.api.mt.RemoteMtImService;
import com.feidi.xx.cross.platform.api.mt.domian.bo.RemoteMtMsgReadBo;
import com.feidi.xx.cross.platform.api.mt.domian.bo.RemoteMtMsgSendBo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@DubboService
@RequiredArgsConstructor
public class RemoteImServiceImpl extends MtApiService implements RemoteMtImService {

    /**
     * 司机发消息给乘客
     *
     * @param bo
     * @return
     */
    @Override
    @Log(type = "api", platform = "美团", title = "IM消息")
    public RemotePlatformApiResponseVo sendMsg(RemoteMtMsgSendBo bo) {
        MtDriverSendMsgBo mtDriverSendMsgBo = MtDriverSendMsgBo.getInstance(bo);
        Map<String, Object> stringObjectMap = BeanUtil.beanToMap(mtDriverSendMsgBo);
        return BeanUtil.copyProperties(sendHttp(stringObjectMap, MtApiEnum.DRIVER_SEND_MSG.getUrl()), RemotePlatformApiResponseVo.class);
    }

    /**
     * 推送司机已读乘客消息
     *
     * @param bo
     * @return
     */
    @Override
    @Log(type = "api", platform = "美团", title = "IM消息")
    public RemotePlatformApiResponseVo readMsg(RemoteMtMsgReadBo bo) {
        MtDriverReadMsgBo readMsgBo = MtDriverReadMsgBo.getInstance(bo);
        Map<String, Object> stringObjectMap = BeanUtil.beanToMap(readMsgBo);
        return BeanUtil.copyProperties(sendHttp(stringObjectMap, MtApiEnum.DRIVER_READ_MSG.getUrl()), RemotePlatformApiResponseVo.class);
    }

}
