package com.feidi.xx.cross.mt.enums;

import com.feidi.xx.cross.common.enums.operate.ProductCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 订单拼车枚举
 *
 * <AUTHOR>
 * @date 2025/3/14
 */
@Getter
@AllArgsConstructor
public enum MTPoolTypeEnum {

    RENT(1, "独享", ProductCodeEnum.RENT.getCode()),
    FIT(2, "拼车", ProductCodeEnum.FIT.getCode()),
    ;

    private final int code;
    private final String info;
    private final String productCode;

    /**
     * 根据美团产品编码获取产品code
     *
     * @param code 美团产品编码
     * @return 产品code
     */
    public static String getProductCodeByCode(int code) {
        for (MTPoolTypeEnum value : MTPoolTypeEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getProductCode();
            }
        }
        return null;
    }
}
