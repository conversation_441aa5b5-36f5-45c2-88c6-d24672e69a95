package com.feidi.xx.cross.mt.domain;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.log.enums.PlatformEnum;
import com.feidi.xx.cross.common.enums.message.MsgTypeEnum;
import com.feidi.xx.cross.message.api.domain.RemoteImMsgInfo;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 美团基础字段
 *
 * <AUTHOR>
 * @date 2025/3/13
 */
@Data
public class MtBaseEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分配的合作方的标识，每个合作方一个，对应分配给合作方的client_id 必须
     */
    private String channel;

    /**
     * 请求时间，UnixTimestamp单位毫秒 必须
     */
    private Long timestamp;

    /**
     * 签名 必须
     */
    private String sign;

    /**
     * 美团订单号
     */
    private String mtOrderId;

    /**
     * 合作方订单号
     */
    private String partnerOrderId;

    /**
     * 取消原因
     */
    private String reason;

    /**
     * 美团手机号（虚拟号）
     */
    private String mtPhone;

    /**
     * 用于自定义虚拟号的绑定时长, 单位:分钟, 不传递则使用默认值30分钟
     */
    private Integer duration;

    /**
     * 美团消息ID
     */
    private String mtMsgId;

    /**
     * 消息类型：1文本消息，当前仅支持文本，其他类型正在建设中
     */
    private Integer msgType;

    /**
     * msgContent
     */
    private String msgContent;

    /**
     * 消息发送时间 (毫秒)
     */
    private Long createTime;

    public RemoteImMsgInfo toRemoteImMsg() {
        RemoteImMsgInfo msg = new RemoteImMsgInfo();
        msg.setThirdMsgId(mtMsgId);
        msg.setTimestamp(createTime);

        msg.setPlatformCode(PlatformEnum.MT.getCode());
        msg.setPlatformNo(mtOrderId);
        msg.setOrderNo(partnerOrderId);

        msg.setMsgType(MsgTypeEnum.TEXT.getCode());
        try {
            msg.setSendUserId(Long.valueOf(mtPhone));
        } catch (Exception e) {
            //
        }
        msg.setSendUserType(UserTypeEnum.PASSENGER_USER.getUserType());
        msg.setReceiveUserType(UserTypeEnum.DRIVER_USER.getUserType());

        JSONObject object = JSONUtil.parseObj(msgContent);
        msg.setMsgContent(object.getStr("text"));
        return msg;
    }
}
