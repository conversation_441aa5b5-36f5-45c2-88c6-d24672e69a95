package com.feidi.xx.cross.mt.service.impl;

import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.enums.SuccessFailEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.enums.order.CancelTypeEnum;
import com.feidi.xx.cross.common.enums.order.OperateTypeEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.enums.platform.MtOrderStatusEnum;
import com.feidi.xx.cross.common.enums.platform.MtSexEnum;
import com.feidi.xx.cross.common.helper.OrdOrderOperateHelper;
import com.feidi.xx.cross.common.mq.event.OrdOrderOperateEvent;
import com.feidi.xx.cross.common.mq.producer.OrdOrderOperateProducer;
import com.feidi.xx.cross.message.api.RemoteImService;
import com.feidi.xx.cross.mt.constant.MtApiConstants;
import com.feidi.xx.cross.mt.constant.MtCacheConstants;
import com.feidi.xx.cross.mt.constant.MtConstants;
import com.feidi.xx.cross.mt.domain.MtBaseEntity;
import com.feidi.xx.cross.mt.domain.MtR;
import com.feidi.xx.cross.mt.domain.bo.MtOrderBo;
import com.feidi.xx.cross.mt.domain.vo.*;
import com.feidi.xx.cross.mt.service.IMtOrderService;
import com.feidi.xx.cross.mt.service.helper.MtOrderHelper;
import com.feidi.xx.cross.operate.api.domain.platform.vo.RemotePlatformVo;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderBo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderHandleBo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDriverVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderInfoVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Date;
import java.util.Objects;

/**
 * 美团处理订单接口实现类
 *
 * <AUTHOR>
 * @date 2025/3/13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MtOrderServiceImpl implements IMtOrderService {

    private final MtOrderHelper mtOrderHelper;
    private final OprCacheManager oprCacheManager;
    private final OrdCacheManager ordCacheManager;
    private final OrdOrderOperateProducer ordOrderOperateProducer;
    @DubboReference
    private final RemoteOrderService remoteOrderService;
    @DubboReference
    private final RemoteImService remoteImService;


    /**
     * 下单
     *
     * @param mtOrderBo 下单参数
     * @return 下单结果
     */
    @Override
    public MtR postOrder(MtOrderBo mtOrderBo) {
        // 开始时间
        long startTime = DateUtils.getUnixTimeStamps();
        if (log.isInfoEnabled()) {
            log.info("美团 - 下单参数：【{}】", JsonUtils.toJsonString(mtOrderBo));
        }

        // 操作记录
        OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildMtOrderOperateEvent(null, OperateTypeEnum.PLACE.getCode(), JsonUtils.toJsonString(mtOrderBo));

        // 平台信息
        RemotePlatformVo remotePlatformVo = oprCacheManager.getPlatformInfoByCode(MtConstants.PLATFORM_CODE);
        if (remotePlatformVo == null || Objects.equals(remotePlatformVo.getStatus(), StatusEnum.DISABLE.getCode())) {
            log.error("MT - 平台信息不存在");
            return MtR.fail(MtApiConstants.RETURN_CODE_OTHER, "平台信息不存在");
        }

        // 平台信息时间
        long platformTime = DateUtils.getUnixTimeStamps();

        if (log.isInfoEnabled()) {
            log.info("MT 下单 平台信息时间 -时长【{}】", ArithUtils.sub(platformTime, startTime));
        }

        // 订单去重过滤处理
        mtOrderHelper.orderFilter(mtOrderBo, remotePlatformVo);

        // 订单去重过滤处理时间
        long filterTime = DateUtils.getUnixTimeStamps();

        if (log.isInfoEnabled()) {
            log.info("MT 下单 订单去重过滤处理时间 -时长【{}】", ArithUtils.sub(filterTime, startTime));
        }

        // 获取订单金额
        mtOrderBo.setAmount(mtOrderHelper.getOrderAmount(mtOrderBo.getAmount(), mtOrderBo.getPoolType(), mtOrderBo.getEstimateId()));

        MtOrderVo mtOrderVo = new MtOrderVo();

        RemoteOrderVo remoteOrderVo = null;
        try {
            // 整理下单参数
            RemoteOrderBo orderBo = mtOrderHelper.handlePlaceOrderParams(mtOrderBo, remotePlatformVo);
            //placeBo.setOriginalJson(JsonUtils.toJsonString(mtOrderBo));

            // 开始下单
            remoteOrderVo = remoteOrderService.placeOrder(orderBo);

            // 下单时间
            long orderTime = DateUtils.getUnixTimeStamps();
            if (log.isInfoEnabled()) {
                log.info("MT 下单 下单时间 -时长【{}】", ArithUtils.sub(orderTime, startTime));
            }

            if (ObjectUtils.isNull(remoteOrderVo) || ObjectUtils.isNull(remoteOrderVo.getOrderNo())) {
                // 直接返回
                return MtR.fail(MtApiConstants.RETURN_CODE_OTHER, "订单处理失败或订单已存在");
            }

            long totalTime = ArithUtils.sub(orderTime, startTime);
            if (log.isInfoEnabled()) {
                log.info("MT 下单 成功 单号【{}】-时长【{}】", remoteOrderVo.getOrderNo(), totalTime);
            }

            // TODO 超时逻辑暂时不写
            //if (!(totalTime < MtApiConstants.TIME_OUT)) {
            //    // 修改订单状态 - 取消
            //    String remark = "平台处理订单超时:" + totalTime;
            //    String cancelRemark = remark + "【开始时间：" + startTime + "；结束时间：" + orderTime + "】";
            //    HandleBo handleBo = this.createHandleBo(remotePlaceOrderVo.getPlatformNo(), cancelRemark, remark, orderTime);
            //    this.cancel(handleBo);
            //
            //    // 直接返回
            //    return MtR.fail(MtApiConstants.RETURN_CODE_OTHER, remark);
            //}

            mtOrderVo.setPartnerOrderId(remoteOrderVo.getOrderNo());

            log.info("美团 - 下单返回：【{}】", JsonUtils.toJsonString(mtOrderVo));

            // TODO-NEW 将订单信息缓存
            RedisUtils.setCacheObject(MtCacheConstants.MT_ORDER + remoteOrderVo.getOrderNo(), remoteOrderVo, Duration.ofMinutes(MtCacheConstants.MT_ORDER_TIME));
        } catch (Exception e) {
            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
            // TODO 异常处理逻辑暂时不写
            log.error(e.getMessage(), e);
            log.error("MT 下单 START");
            log.error("MT 下单 失败：【{}】", e.getMessage());
            throw new ServiceException(e.getMessage());
            // 修改订单状态 - 取消
            /*String cancelRemark = "系统处理失败";
            HandleBo handleBo = this.createHandleBo(hbkOrderBo.getOrderNo(), cancelRemark, exception.getMessage(), DateUtils.getUnixTimeStamps());
            hbkOrderService.cancel(handleBo);
            log.error("HBK 下单 END");*/
        } finally {
            // 订单操作记录
            long orderId = ObjectUtils.isNotNull(remoteOrderVo) ? remoteOrderVo.getId() : 0L;
            operateEvent.setOrderId(orderId);
            operateEvent.setResponseJson(JsonUtils.toJsonString(mtOrderVo));
            ordOrderOperateProducer.sendMessage(operateEvent);
        }

        return MtR.ok(mtOrderVo);
    }

    /**
     * 通知派单
     *
     * @param mtBaseEntity 通知派单参数
     * @return 通知派单结果
     */
    @Override
    public MtR confirmNotify(MtBaseEntity mtBaseEntity) {
        if (log.isInfoEnabled()) {
            log.info("美团 - 通知派单参数：【{}】", JsonUtils.toJsonString(mtBaseEntity));
        }
        // 开始时间
        long startTime = DateUtils.getUnixTimeStamps();

        // 操作记录
        OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildMtOrderOperateEvent(null, OperateTypeEnum.CONFIRM_NOTIFY.getCode(), JsonUtils.toJsonString(mtBaseEntity));

        Boolean flag = false;
        try {
            // 创建订单处理参数
            RemoteOrderHandleBo handleBo = mtOrderHelper.createHandleBo(mtBaseEntity, MtOrderStatusEnum.CONFIRM.getCode(), "美团调《通知派单》");

            flag = remoteOrderService.confirmNotify(handleBo);
            // 结束时间
            long endTime = DateUtils.getUnixTimeStamps();
            if (flag) {
                if (log.isInfoEnabled()) {
                    log.info("美团 【通知派单】 调用成功 平台单号【{}】-时长【{}】", handleBo.getPlatformNo(), ArithUtils.sub(endTime, startTime));
                }
                return MtR.ok();
            }
        } catch (Exception e) {
            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
            log.error("美团 - 通知派单异常：【{}】", e.getMessage());
        } finally {
            RemoteOrderVo remoteOrderVo = ordCacheManager.getOrderInfoByOrderNo(mtBaseEntity.getPartnerOrderId());
            operateEvent.setOrderId(remoteOrderVo.getId());
            operateEvent.setResponseJson(JsonUtils.toJsonString(flag));
            ordOrderOperateProducer.sendMessage(operateEvent);
        }
        return MtR.fail();
    }

    /**
     * 获取取消费
     *
     * @param mtBaseEntity 取消费参数
     * @return 取消费
     */
    @Override
    public MtR getOrderCancelFee(MtBaseEntity mtBaseEntity) {
        if (log.isInfoEnabled()) {
            log.info("美团 - 获取取消费：【{}】", JsonUtils.toJsonString(mtBaseEntity));
        }
        // 开始时间
        long startTime = DateUtils.getUnixTimeStamps();

        // 操作记录
        OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildMtOrderOperateEvent(null, OperateTypeEnum.CANCEL_FEE.getCode(), JsonUtils.toJsonString(mtBaseEntity));

        MtCancelFeeVo mtCancelFeeVo = new MtCancelFeeVo();
        try {
            // 创建订单处理参数
            RemoteOrderHandleBo handleBo = mtOrderHelper.createHandleBo(mtBaseEntity, MtOrderStatusEnum.CANCEL_BY_USER.getCode(), "美团调《获取取消费》");

            // 获取取消费
            Long fee = remoteOrderService.orderCancelFee(handleBo);

            // TODO-NEW将取消费保存到redis
            String key = MtCacheConstants.MT_CANCEL_FEE + mtBaseEntity.getMtOrderId();
            RedisUtils.setCacheObject(key, fee, Duration.ofMinutes(MtCacheConstants.MT_CANCEL_FEE_TIME));

            mtCancelFeeVo.setCancelFee(fee);

            // 结束时间
            long endTime = DateUtils.getUnixTimeStamps();
            if (log.isInfoEnabled()) {
                log.info("美团 - 获取取消费：【{}】", mtCancelFeeVo);
                log.info("美团 【获取取消费】 调用成功 平台单号【{}】-时长【{}】", handleBo.getPlatformNo(), ArithUtils.sub(endTime, startTime));
            }

            return MtR.ok(mtCancelFeeVo);
        } catch (Exception e) {
            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
            log.error("美团 - 获取取消费异常：【{}】", e.getMessage());
        } finally {
            RemoteOrderVo remoteOrderVo = ordCacheManager.getOrderInfoByOrderNo(mtBaseEntity.getPartnerOrderId());
            operateEvent.setOrderId(remoteOrderVo.getId());
            operateEvent.setResponseJson(JsonUtils.toJsonString(mtCancelFeeVo));
            ordOrderOperateProducer.sendMessage(operateEvent);
        }
        return MtR.fail();
    }

    /**
     * 取消订单
     *
     * @param mtBaseEntity 取消订单参数
     * @return 取消订单结果
     */
    @Override
    public MtR cancelOrder(MtBaseEntity mtBaseEntity) {
        if (log.isInfoEnabled()) {
            log.info("美团 - 取消订单参数：【{}】", JsonUtils.toJsonString(mtBaseEntity));
        }
        // 开始时间
        long startTime = DateUtils.getUnixTimeStamps();

        // 操作记录
        OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildMtOrderOperateEvent(null, OperateTypeEnum.CANCEL.getCode(), JsonUtils.toJsonString(mtBaseEntity));

        String remark = "美团调《取消订单》 - 取消原因：【{" + mtBaseEntity.getReason() + "}】";
        // 创建订单处理参数
        RemoteOrderHandleBo handleBo = mtOrderHelper.createHandleBo(mtBaseEntity, MtOrderStatusEnum.CANCEL_BY_USER.getCode(), remark);

        // 处理取消类型和取消备注
        mtOrderHelper.handleCancelTypeAndRemark(mtBaseEntity.getReason(), handleBo);
        MtCancelFeeVo mtCancelFeeVo = new MtCancelFeeVo();
        try {
            // 取消订单
            boolean flag = remoteOrderService.cancelOrder(handleBo);

            // 结束时间
            long endTime = DateUtils.getUnixTimeStamps();
            long sub = ArithUtils.sub(endTime, startTime);

            if (flag) {
                // 获取取消费
                Integer cancelFee = RedisUtils.getOrDefault(MtCacheConstants.MT_CANCEL_FEE + mtBaseEntity.getMtOrderId(), 0);

                mtCancelFeeVo.setCancelFee(Long.valueOf(cancelFee));

                if (log.isInfoEnabled()) {
                    log.info("美团 - 取消订单：【{}】", mtCancelFeeVo);
                    log.info("美团 【取消订单】 调用成功 平台单号【{}】-时长【{}】", handleBo.getPlatformNo(), sub);
                }
                return MtR.ok(mtCancelFeeVo);
            }
        } catch (Exception exception) {
            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
            log.error("美团 【取消订单】 异常：【{}】", exception.getMessage());
        } finally {
            RemoteOrderVo remoteOrderVo = ordCacheManager.getOrderInfoByOrderNo(mtBaseEntity.getPartnerOrderId());
            operateEvent.setOrderId(remoteOrderVo.getId());
            operateEvent.setResponseJson(JsonUtils.toJsonString(mtCancelFeeVo));
            ordOrderOperateProducer.sendMessage(operateEvent);
        }

        return MtR.fail();
    }

    /**
     * 查询订单详情（轮询接口）
     *
     * @param mtBaseEntity 查询订单详情参数
     * @return 订单详情信息
     */
    @Override
    public MtR pollingOrderStatus(MtBaseEntity mtBaseEntity) {
        if (log.isInfoEnabled()) {
            log.info("美团 - 查询订单详情（轮询接口）参数：【{}】", JsonUtils.toJsonString(mtBaseEntity));
        }
        // 开始时间
        long startTime = DateUtils.getUnixTimeStamps();

        try {
            // 获取订单详情
            RemoteOrderVo orderVo = remoteOrderService.queryByPlatformCodeAndPlatformNo(MtConstants.PLATFORM_CODE, mtBaseEntity.getMtOrderId());

            if (ObjectUtils.isNull(orderVo)) {
                log.error("美团 - 查询订单详情 订单不存在 {}", mtBaseEntity);
                throw new ServiceException("订单信息不存在");
            }

            // 创建订单处理参数
            RemoteOrderHandleBo handleBo = mtOrderHelper.createHandleBo(mtBaseEntity, MtOrderStatusEnum.CANCEL_BY_USER.getCode(), "美团调《查询订单详情》");

            // 订单详情
            MtOrderDetailVo mtOrderDetailVo = new MtOrderDetailVo();
            mtOrderDetailVo.setMtOrderId(orderVo.getPlatformNo());
            mtOrderDetailVo.setPartnerOrderId(orderVo.getOrderNo());
            mtOrderDetailVo.setEventTime(DateUtils.getUnixTimeStamps());
            // 订单状态
            mtOrderDetailVo.setStatus(MtOrderStatusEnum.getCodeByOrderStatus(orderVo.getStatus()));

            RemoteOrderInfoVo remoteOrderInfoVo = null;
            if (!Objects.equals(mtOrderDetailVo.getStatus(), MtOrderStatusEnum.SUBMIT.getCode())) {
                // 订单关联信息
                remoteOrderInfoVo = ordCacheManager.getOrderSubInfoByOrderId(orderVo.getId());

                mtOrderDetailVo.setPoolSuccess(true);
                Date tripStartTime = orderVo.getTripStartTime();
                log.info("美团 - 订单详情 - tripStartTime：【{}】", tripStartTime);
                if (tripStartTime != null) {
                    mtOrderDetailVo.setPickUpTime(tripStartTime.getTime() * 1000);
                }
                // 账单
                MtBillVo mtBillVo = new MtBillVo();
                mtBillVo.setHighwayPrice(0);
                if (remoteOrderInfoVo.getCancelFee() != null) {
                    mtBillVo.setCancelPrice(remoteOrderInfoVo.getCancelFee().intValue());
                }
                if (orderVo.getOrderPrice() != null) {
                    mtBillVo.setFixedPrice(orderVo.getOrderPrice().intValue());
                }
                mtOrderDetailVo.setBill(mtBillVo);
            }

            // 订单司机
            RemoteOrderDriverVo orderDriverVo = ordCacheManager.getOrderDispatchDriverInfoByOrderId(orderVo.getId());
            if (ObjectUtils.isNotNull(orderDriverVo)) {
                // 司机信息
                MtDriverInfoVo mtDriverInfoVo = new MtDriverInfoVo();
                mtDriverInfoVo.setDriverGender(MtSexEnum.getCodeBySex(orderDriverVo.getDriverSex()));
                mtDriverInfoVo.setPartnerDriverId(orderDriverVo.getDriverId().toString());
                mtDriverInfoVo.setDriverLastName(orderDriverVo.getDriverName().substring(0, 1));
                mtDriverInfoVo.setDriverName(orderDriverVo.getDriverName().substring(1));
                mtDriverInfoVo.setDriverMobile(orderDriverVo.getDriverPhone());
                mtOrderDetailVo.setDriverInfo(mtDriverInfoVo);

                // 车辆信息
                MtCarInfoVo mtCarInfoVo = new MtCarInfoVo();
                mtCarInfoVo.setCarColor(orderDriverVo.getCarColor());
                mtCarInfoVo.setCarNumber(orderDriverVo.getCarNumber());
                mtCarInfoVo.setBrandName(orderDriverVo.getCarBrand());
                mtOrderDetailVo.setCarInfo(mtCarInfoVo);
            }

            // 取消
            if (orderVo.getStatus().equals(OrderStatusEnum.CANCEL.getCode())) {
                // 订单取消信息
                MtOrderCancelVo mtOrderCancelVo = new MtOrderCancelVo();
                mtOrderCancelVo.setOpName(remoteOrderInfoVo != null ? remoteOrderInfoVo.getCancelUserName() : UserTypeEnum.CS_USER.getInfo());
                mtOrderCancelVo.setCancelReason(CancelTypeEnum.getInfoByCode(orderVo.getCancelType()));
                mtOrderDetailVo.setCustomerServiceInfo(mtOrderCancelVo);
            }

            // 结束时间
            long endTime = DateUtils.getUnixTimeStamps();
            if (log.isInfoEnabled()) {
                log.info("美团 - 查询订单详情 结果：【{}】", mtOrderDetailVo);
                log.info("美团 【查询订单详情】 调用成功 平台单号【{}】-时长【{}】", handleBo.getPlatformNo(), ArithUtils.sub(endTime, startTime));
            }

            return MtR.ok(mtOrderDetailVo);
        } catch (Exception e) {
            log.error("美团 - 查询订单详情 异常：【{}】", e.getMessage());
        }
        return MtR.fail();
    }

    /**
     * 乘客确认上车
     *
     * @param mtBaseEntity 乘客确认上车参数
     * @return 乘客确认上车结果
     */
    @Override
    public MtR aboardNotify(MtBaseEntity mtBaseEntity) {
        if (log.isInfoEnabled()) {
            log.info("美团 - 乘客确认上车参数：【{}】", JsonUtils.toJsonString(mtBaseEntity));
        }
        // 开始时间
        long startTime = DateUtils.getUnixTimeStamps();
        // 操作记录
        OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildMtOrderOperateEvent(null, OperateTypeEnum.INTO.getCode(), JsonUtils.toJsonString(mtBaseEntity));
        boolean flag = false;
        try {
            // 创建订单处理参数
            RemoteOrderHandleBo handleBo = mtOrderHelper.createHandleBo(mtBaseEntity, MtOrderStatusEnum.DRIVING.getCode(), "美团调《乘客确认上车》");

            flag = remoteOrderService.tripStart(handleBo);
            // 结束时间
            long endTime = DateUtils.getUnixTimeStamps();
            if (flag) {
                if (log.isInfoEnabled()) {
                    log.info("美团 【乘客确认上车】 调用成功 平台单号【{}】-时长【{}】", handleBo.getPlatformNo(), ArithUtils.sub(endTime, startTime));
                }
                return MtR.ok();
            }
        } catch (Exception e) {
            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
            log.error("美团 - 乘客确认上车 异常：【{}】", e.getMessage());
        } finally {
            RemoteOrderVo remoteOrderVo = ordCacheManager.getOrderInfoByOrderNo(mtBaseEntity.getPartnerOrderId());
            operateEvent.setOrderId(remoteOrderVo.getId());
            operateEvent.setResponseJson(JsonUtils.toJsonString(flag));
            ordOrderOperateProducer.sendMessage(operateEvent);
        }

        return MtR.fail();
    }

    /**
     * 乘客确认下车
     *
     * @param mtBaseEntity 确认下车参数
     * @return 确认下车结果
     */
    @Override
    public MtR reachDestinationNotify(MtBaseEntity mtBaseEntity) {
        if (log.isInfoEnabled()) {
            log.info("美团 - 确认下车参数：【{}】", JsonUtils.toJsonString(mtBaseEntity));
        }
        // 开始时间
        long startTime = DateUtils.getUnixTimeStamps();
        // 操作记录
        OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildMtOrderOperateEvent(null, OperateTypeEnum.FINISH.getCode(), JsonUtils.toJsonString(mtBaseEntity));
        boolean flag = false;
        try {
            // 创建订单处理参数
            RemoteOrderHandleBo handleBo = mtOrderHelper.createHandleBo(mtBaseEntity, MtOrderStatusEnum.DRIVING.getCode(), "美团调《乘客确认下车》");

            flag = remoteOrderService.tripEnd(handleBo);
            // 结束时间
            long endTime = DateUtils.getUnixTimeStamps();
            if (flag) {
                if (log.isInfoEnabled()) {
                    log.info("美团 【乘客确认下车】 调用成功 平台单号【{}】-时长【{}】", handleBo.getPlatformNo(), ArithUtils.sub(endTime, startTime));
                }
                return MtR.ok();
            }
        } catch (Exception e) {
            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
            log.error("美团 - 乘客确认下车 异常：【{}】", e.getMessage());
        } finally {
            RemoteOrderVo remoteOrderVo = ordCacheManager.getOrderInfoByOrderNo(mtBaseEntity.getPartnerOrderId());
            operateEvent.setOrderId(remoteOrderVo.getId());
            operateEvent.setResponseJson(JsonUtils.toJsonString(flag));
            ordOrderOperateProducer.sendMessage(operateEvent);
        }

        return MtR.fail();
    }

    /**
     * 查询司机位置
     *
     * @param mtBaseEntity 查询司机位置参数
     * @return 司机位置信息
     */
    @Override
    public MtR driverLocation(MtBaseEntity mtBaseEntity) {
        if (log.isInfoEnabled()) {
            log.info("美团 - 查询司机位置参数：【{}】", JsonUtils.toJsonString(mtBaseEntity));
        }
        // 开始时间
        long startTime = DateUtils.getUnixTimeStamps();

        try {
            // 创建订单处理参数
            RemoteOrderHandleBo handleBo = mtOrderHelper.createHandleBo(mtBaseEntity, null, "美团调《5.9.查询司机位置》");

            // TODO 查询司机位置暂时不写
            // remoteOrderService.driverLocation(orderVo.getId());
            MtDriverLocationVo mtDriverLocationVo = new MtDriverLocationVo();

            // 结束时间
            long endTime = DateUtils.getUnixTimeStamps();
            if (log.isInfoEnabled()) {
                log.info("美团 【查询司机位置】 调用成功 平台单号【{}】-时长【{}】", handleBo.getPlatformNo(), ArithUtils.sub(endTime, startTime));
            }

            return MtR.ok(mtDriverLocationVo);
        } catch (Exception e) {
            log.error("美团 - 查询司机位置 异常：【{}】", e.getMessage());
        }

        return MtR.fail();
    }

    /**
     * 获取司机手机号
     *
     * @param mtBaseEntity 获取司机手机号参数
     * @return 获取司机手机号结果
     */
    @Override
    public MtR queryDriverPhoneInfo(MtBaseEntity mtBaseEntity) {
        if (log.isInfoEnabled()) {
            log.info("美团 - 获取司机手机号参数：【{}】", JsonUtils.toJsonString(mtBaseEntity));
        }
        // 开始时间
        long startTime = DateUtils.getUnixTimeStamps();
        // 操作记录
        OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildMtOrderOperateEvent(null, OperateTypeEnum.DRIVER_PHONE.getCode(), JsonUtils.toJsonString(mtBaseEntity));
        MtDriverInfoVo mtDriverInfoVo = new MtDriverInfoVo();
        try {
            // 获取订单信息
            RemoteOrderVo orderVo = remoteOrderService.queryByPlatformCodeAndPlatformNo(MtConstants.PLATFORM_CODE, mtBaseEntity.getMtOrderId());

            if (ObjectUtils.isNull(orderVo)) {
                log.error("美团 - 查询司机电话 订单不存在 {}", mtBaseEntity);
                return MtR.fail();
            }

            // 获取订单司机调度信息
            RemoteOrderDriverVo orderDriverVo = ordCacheManager.getOrderDispatchDriverInfoByOrderId(orderVo.getId());

            mtDriverInfoVo.setDriverPhone(orderDriverVo.getDriverPhone());
            mtDriverInfoVo.setDriverName(orderDriverVo.getDriverName());

            // 创建订单处理参数
            RemoteOrderHandleBo handleBo = mtOrderHelper.createHandleBo(mtBaseEntity, null, "美团调《查询司机电话》，状态：成功，司机电话：" + orderDriverVo.getDriverPhone());

            // TODO-NEW 记录订单操作日志MQ
            //OrderOperateEventHelper.publishOrderOperateEvent(orderVo.getTenantId(), orderVo.getId(), CxOperateTypeEnum.VIRTUAL.getCode(), SuccessOrFailEnum.SUCCESS.getCode(), handleBo);

            // 结束时间
            long endTime = DateUtils.getUnixTimeStamps();
            if (log.isInfoEnabled()) {
                log.info("美团 【查询司机电话】 调用成功 平台单号【{}】-时长【{}】", handleBo.getPlatformNo(), ArithUtils.sub(endTime, startTime));
            }
        } catch (Exception e) {
            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
            log.error("美团 - 获取司机手机号 异常：【{}】", e.getMessage());
        } finally {
            RemoteOrderVo remoteOrderVo = ordCacheManager.getOrderInfoByOrderNo(mtBaseEntity.getPartnerOrderId());
            operateEvent.setOrderId(remoteOrderVo.getId());
            operateEvent.setResponseJson(JsonUtils.toJsonString(mtDriverInfoVo));
            ordOrderOperateProducer.sendMessage(operateEvent);
        }
        return MtR.ok(mtDriverInfoVo);
    }

    /**
     * 乘客发送消息
     * @param mtBaseEntity
     * @return
     */
    @Override
    public MtR passengerSendMsg(MtBaseEntity mtBaseEntity) {
        if (log.isInfoEnabled()) {
            log.info("美团 - 推送乘客消息参数：【{}】", mtBaseEntity);
        }
        return MtR.ok(remoteImService.sendMsg2Driver(mtBaseEntity.toRemoteImMsg()));
    }

    /**
     * 乘客发送已读消息
     * @param mtBaseEntity
     * @return
     */
    @Override
    public MtR passengerReadMsg(MtBaseEntity mtBaseEntity) {
        if (log.isInfoEnabled()) {
            log.info("美团 - 推送乘客已读消息参数：【{}】", mtBaseEntity);
        }
        return MtR.ok();
    }
}
