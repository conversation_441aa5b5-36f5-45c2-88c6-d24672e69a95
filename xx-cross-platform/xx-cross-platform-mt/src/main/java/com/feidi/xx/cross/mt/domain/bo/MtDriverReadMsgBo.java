package com.feidi.xx.cross.mt.domain.bo;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.feidi.xx.cross.mt.domain.MtBaseEntity;
import com.feidi.xx.cross.platform.api.mt.domian.bo.RemoteMtMsgReadBo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * 推送司机消息 - 参数
 *
 * <AUTHOR>
 * @date 2024/9/11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MtDriverReadMsgBo extends MtBaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 	美团消息ID,json字符串
     * 	"[/"123/", /"123/"]"
     */
    private String mtMsgIds;

    public static MtDriverReadMsgBo getInstance(RemoteMtMsgReadBo bo) {
        MtDriverReadMsgBo instance = new MtDriverReadMsgBo();
        instance.setMtOrderId(bo.getPlatformNo());
        instance.setMtMsgIds(JSONUtil.toJsonStr(new JSONArray(bo.getOriginMsgIds())));
        return instance;
    }

}
