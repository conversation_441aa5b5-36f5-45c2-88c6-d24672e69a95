package com.feidi.xx.cross.mt.domain.bo;

import com.feidi.xx.cross.mt.domain.MtBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 询价参数
 *
 * <AUTHOR>
 * @date 2025/3/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MtEstimateBo extends MtBaseEntity {

    /**
     * 出发地经度
     */
    private Double startPointLng;

    /**
     * 出发地纬度
     */
    private Double startPointLat;

    /**
     * 出发地名称
     */
    private String startPointName;

    /**
     * 目的地经度
     */
    private Double endPointLng;

    /**
     * 目的地纬度
     */
    private Double endPointLat;

    /**
     * 目的地名称
     */
    private String endPointName;

    /**
     * 城市code，默认为出发地所在城市code
     */
    private String cityId;

    /**
     * 最早出发时间，Unixtimestamp，单位毫秒
     */
    private Long earliestDepartureTime;

    /**
     * 最晚出发时间，Unixtimestamp，单位毫秒
     */
    private Long latestDepartureTime;

    /**
     * 乘客人数
     */
    private Integer passengerNum;
}
