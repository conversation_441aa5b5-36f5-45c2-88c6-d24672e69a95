package com.feidi.xx.cross.mt.service;

import com.feidi.xx.cross.mt.domain.MtBaseEntity;
import com.feidi.xx.cross.mt.domain.MtR;
import com.feidi.xx.cross.mt.domain.bo.MtOrderBo;

/**
 * 美团处理订单接口
 *
 * <AUTHOR>
 * @date 2025/3/13
 */
public interface IMtOrderService {

    /**
     * 下单
     *
     * @param mtOrderBo 下单参数
     * @return 下单结果
     */
    MtR postOrder(MtOrderBo mtOrderBo);

    /**
     * 通知派单
     *
     * @param mtBaseEntity 通知派单参数
     * @return 通知派单结果
     */
    MtR confirmNotify(MtBaseEntity mtBaseEntity);

    /**
     * 获取取消费
     *
     * @param mtBaseEntity 取消费参数
     * @return 取消费
     */
    MtR getOrderCancelFee(MtBaseEntity mtBaseEntity);

    /**
     * 取消订单
     *
     * @param mtBaseEntity 取消订单参数
     * @return 取消订单结果
     */
    MtR cancelOrder(MtBaseEntity mtBaseEntity);

    /**
     * 查询订单详情（轮询接口）
     *
     * @param mtBaseEntity 查询订单详情参数
     * @return 订单详情信息
     */
    MtR pollingOrderStatus(MtBaseEntity mtBaseEntity);

    /**
     * 乘客确认上车
     *
     * @param mtBaseEntity 乘客确认上车参数
     * @return 乘客确认上车结果
     */
    MtR aboardNotify(MtBaseEntity mtBaseEntity);

    /**
     * 乘客确认下车
     *
     * @param mtBaseEntity 确认下车参数
     * @return 确认下车结果
     */
    MtR reachDestinationNotify(MtBaseEntity mtBaseEntity);

    /**
     * 查询司机位置
     *
     * @param mtBaseEntity 查询司机位置参数
     * @return 司机位置信息
     */
    MtR driverLocation(MtBaseEntity mtBaseEntity);

    /**
     * 获取司机手机号
     *
     * @param mtBaseEntity 获取司机手机号参数
     * @return 获取司机手机号结果
     */
    MtR queryDriverPhoneInfo(MtBaseEntity mtBaseEntity);

    /**
     * 美团发送消息
     * @param mtBaseEntity
     * @return
     */
    MtR passengerSendMsg(MtBaseEntity mtBaseEntity);

    /**
     * 美团发送消息
     * @param mtBaseEntity
     * @return
     */
    MtR passengerReadMsg(MtBaseEntity mtBaseEntity);

}
