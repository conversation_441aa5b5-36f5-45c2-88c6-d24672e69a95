package com.feidi.xx.cross.mt.domain.bo;

import com.feidi.xx.cross.mt.domain.MtBaseEntity;
import com.feidi.xx.cross.platform.api.mt.domian.bo.RemoteMtMsgSendBo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * 推送司机消息 - 参数
 *
 * <AUTHOR>
 * @date 2024/9/11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MtDriverSendMsgBo extends MtBaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 合作方司机ID
     */
    private String partnerDriverId;

    /**
     * 合作方司机消息ID
     */
    private String partnerMsgId;

    /**
     * 消息类型：1文本消息，当前仅支持文本，其他类型正在建设中
     */
    private Integer msgType;

    /**
     * 消息内容。json string格式。文本：{"text":"内容"}
     */
    private String msgContent;

    public static MtDriverSendMsgBo getInstance(RemoteMtMsgSendBo bo) {
        MtDriverSendMsgBo instance = new MtDriverSendMsgBo();
        instance.setMtOrderId(bo.getPlatformNo());
        instance.setPartnerDriverId(bo.getDriverId());
        instance.setPartnerMsgId(bo.getMsgId());
        instance.setMsgType(bo.getMsgType());
        instance.setMsgContent(bo.getMsgContent());
        instance.setCreateTime(bo.getCreateTime());
        return instance;
    }

}
