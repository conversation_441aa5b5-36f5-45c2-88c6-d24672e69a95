package com.feidi.xx.cross.mt.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 美团询价返回值
 *
 * <AUTHOR>
 * @date 2025/3/13
 */
@Data
public class MtEstimateVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 预估标识，创建订单时使用，有效期10分钟 - N
     */
    private String estimateId;

    /**
     * 预估距离，单位米（m），不足1m，返回1
     */
    private Integer distance;

    /**
     * 预估时间，单位秒（S），不足60秒，返回60
     */
    private Integer estimateTime;

    /**
     * 独享价预估费用（包含高速费）
     */
    private Integer estimatePrice;

    /**
     * 拼车价 - N
     */
    private Integer poolSucPrice;

    /**
     * 拼车未拼成价 - N
     */
    private Integer poolFailPrice;
}
