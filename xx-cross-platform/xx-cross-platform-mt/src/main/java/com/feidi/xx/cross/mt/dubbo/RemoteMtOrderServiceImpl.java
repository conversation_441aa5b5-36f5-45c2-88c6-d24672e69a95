package com.feidi.xx.cross.mt.dubbo;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.feidi.xx.cross.mt.enums.MtApiEnum;
import com.feidi.xx.cross.mt.service.api.MtApiService;
import com.feidi.xx.cross.platform.api.hbk.domain.hbk.vo.RemotePlatformApiResponseVo;
import com.feidi.xx.cross.platform.api.mt.RemoteMtOrderService;
import com.feidi.xx.cross.platform.api.mt.domian.bo.*;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 平台调美团接口服务
 *
 * <AUTHOR>
 * @date 2024/9/11
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteMtOrderServiceImpl extends MtApiService implements RemoteMtOrderService {

    /**
     * 推送订单状态
     *
     * @param pushStatusBo
     * @return
     */
    @Override
    public RemotePlatformApiResponseVo pushOrderStatus(RemoteMtPushStatusBo pushStatusBo) {
        // 发送请求
        Map<String, Object> stringObjectMap = BeanUtil.beanToMap(pushStatusBo);

        if (pushStatusBo.getBill() != null) {
            stringObjectMap.put("bill", JSON.toJSONString(pushStatusBo.getBill()));
        }
        if (pushStatusBo.getCarInfo() != null) {
            stringObjectMap.put("carInfo", JSON.toJSONString(pushStatusBo.getCarInfo()));
        }
        if (pushStatusBo.getDriverInfo() != null) {
            stringObjectMap.put("driverInfo", JSON.toJSONString(pushStatusBo.getDriverInfo()));
        }
        if (pushStatusBo.getCustomerServiceInfo() != null) {
            stringObjectMap.put("customerServiceInfo", JSON.toJSONString(pushStatusBo.getCustomerServiceInfo()));
        }
        if (pushStatusBo.getDriverLocation() != null) {
            stringObjectMap.put("driverLocation", JSON.toJSONString(pushStatusBo.getDriverLocation()));
        }

        return BeanUtil.copyProperties(sendHttp(stringObjectMap, MtApiEnum.PUSH_ORDER_STATUS.getUrl()), RemotePlatformApiResponseVo.class);
    }

    /**
     * 推送拼友信息
     *
     * @param pushPoolFriendsInfoBo
     * @return
     */
    @Override
    public RemotePlatformApiResponseVo pushPoolFriendsInfo(RemoteMtPushPoolFriendsInfoBo pushPoolFriendsInfoBo) {
        Map<String, Object> stringObjectMap = BeanUtil.beanToMap(pushPoolFriendsInfoBo);
        return BeanUtil.copyProperties(sendHttp(stringObjectMap, MtApiEnum.PUSH_POOL_FRIENDS_INFO.getUrl()), RemotePlatformApiResponseVo.class);
    }

    /**
     * 获取乘客手机号
     *
     * @param queryPassengerPhoneBo
     * @return
     */
    @Override
    public RemotePlatformApiResponseVo queryPassengerPhone(RemoteMtQueryPassengerPhoneBo queryPassengerPhoneBo) {
        Map<String, Object> stringObjectMap = BeanUtil.beanToMap(queryPassengerPhoneBo);
        return BeanUtil.copyProperties(sendHttp(stringObjectMap, MtApiEnum.QUERY_PASSENGER_PHONE.getUrl()), RemotePlatformApiResponseVo.class);
    }

    /**
     * 根据乘客号码查询订单
     *
     * @param queryOrderByPhoneBo
     * @return
     */
    @Override
    public RemotePlatformApiResponseVo queryOrderByUserPhone(RemoteMtQueryOrderByPhoneBo queryOrderByPhoneBo) {
        Map<String, Object> stringObjectMap = BeanUtil.beanToMap(queryOrderByPhoneBo);
        return BeanUtil.copyProperties(sendHttp(stringObjectMap, MtApiEnum.QUERY_ORDER_BY_USER_PHONE.getUrl()), RemotePlatformApiResponseVo.class);
    }

    /**
     * 退款通知
     *
     * @param applyRefundBo
     * @return
     */
    @Override
    public RemotePlatformApiResponseVo applyRefund(RemoteMtApplyRefundBo applyRefundBo) {
        Map<String, Object> stringObjectMap = BeanUtil.beanToMap(applyRefundBo);
        return BeanUtil.copyProperties(sendHttp(stringObjectMap, MtApiEnum.APPLY_REFUND.getUrl()), RemotePlatformApiResponseVo.class);
    }
}
