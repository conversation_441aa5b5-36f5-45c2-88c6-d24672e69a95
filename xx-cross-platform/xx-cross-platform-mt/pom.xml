<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.feidi.xx</groupId>
        <artifactId>xx-cross-platform</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>xx-cross-platform-mt</artifactId>

    <description>
        xx--cross-platform-mt 第三方平台 - 美团模块
    </description>
    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-cross-platform-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.feidi.xx</groupId>
            <artifactId>xx-cross-api-message</artifactId>
            <version>${revision}</version>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>