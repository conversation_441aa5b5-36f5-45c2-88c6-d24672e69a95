package com.feidi.xx.cross.platfrom.aspect;

import cn.dev33.satoken.stp.SaLoginModel;
import com.feidi.xx.common.core.domain.model.LoginUser;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.ServletUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.operate.api.domain.platform.vo.RemotePlatformVo;
import com.feidi.xx.cross.platfrom.annotation.Tenant;
import com.feidi.xx.cross.platfrom.utils.MtSignUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.boot.autoconfigure.AutoConfiguration;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 租户切面
 *
 * <AUTHOR>
 * @date 2025/3/13
 */
@Slf4j
@Aspect
@AutoConfiguration
@RequiredArgsConstructor
public class TenantAspect {

    private final OprCacheManager oprCacheManager;

    /**
     * 前置通知
     *
     * @param joinPoint
     * @param tenant
     */
    @Before(value = "@annotation(tenant)")
    public void doBefore(JoinPoint joinPoint, Tenant tenant) {
        String platformCode = tenant.platformCode().getCode();
        String channelName = tenant.channelName();
        Map<String, String> paramsMap = ServletUtils.getParamMap(ServletUtils.getRequest());
        log.info("租户切面：{}", paramsMap);
        if (CollUtils.isNotEmpty(paramsMap) && paramsMap.containsKey(channelName)) {
            RemotePlatformVo platformVo = oprCacheManager.getPlatformInfoByCodeAndAppKey(platformCode, paramsMap.get(channelName));
            if (ObjectUtils.isNull(platformVo)) {
                throw new RuntimeException("平台不存在");
            }

            LoginUser loginUser = new LoginUser();
            loginUser.setTenantId(platformVo.getTenantId());
            loginUser.setUserId(Long.parseLong(platformVo.getTenantId()));
            loginUser.setUserType(tenant.platformCode().name());

            SaLoginModel model = new SaLoginModel();
            model.setTimeout(30);
            model.setActiveTimeout(30);

            // 生成token
            LoginHelper.login(loginUser, model);

            if (Objects.equals(platformCode, PlatformCodeEnum.MT.getCode())) {
                String sign = paramsMap.get("sign");
                Map<String, Object> params = new HashMap<>(paramsMap);
                params.remove("sign");
                log.info("租户切面111：{}", params);

                if (!MtSignUtil.checkSign(params, platformVo.getAppSecret(), sign)) {
                    throw new RuntimeException("签名错误");
                }
            }
        }
    }

}
