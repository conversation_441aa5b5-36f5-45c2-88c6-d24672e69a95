package com.feidi.xx.cross.platfrom.utils;

import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.feidi.xx.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 美团加签工具
 *
 * <AUTHOR>
 * @date 2024/9/11
 */
@Slf4j
public class MtSignUtil {

    /**
     * 加签
     *
     * @param params
     * @param signKey
     * @return
     * @throws Exception
     */
    public static String sign(Map<String, Object> params, String signKey) {
        Map<String, String> needVerify = new HashMap<>();
        Map<String, Object> signMap = Maps.filterEntries(params, entry -> null != entry.getValue() && StringUtils.isNotBlank(String.valueOf(entry.getValue())));
        TreeMap<String, String> sortMap = new TreeMap<>();
        for (Map.Entry<String, Object> entry : signMap.entrySet()) {
            needVerify.put(entry.getKey(), String.valueOf(entry.getValue()));
        }
        //sign_key 是美团分配的密钥
        needVerify.put("sign_key", signKey);
        sortMap.putAll(needVerify);
        List<Map.Entry<String, String>> entryList = new ArrayList<>(sortMap.entrySet());

        StringBuilder signStr = new StringBuilder();
        for (Map.Entry<String, String> entry : entryList) {
            signStr.append(entry.getKey()).append(entry.getValue());
        }
        if (log.isInfoEnabled()) {
            log.info("sha1 签名参数:{}", signStr);
        }
        try {
            return org.apache.commons.codec.digest.DigestUtils.sha1Hex(signStr.toString());
        } catch (Exception e) {
            log.error("sha1 签名异常,参数:{},异常:{}", signStr, e.getMessage());
            throw e;
        }
    }

    /**
     * 验签
     *
     * @param params 待验签参数
     * @param signKey 密钥
     * @param signValue 签名
     * @return 验签是否通过
     */
    public static boolean checkSign(Map<String, Object> params, String signKey, String signValue) {
        String sign = sign(params, signKey);
        return Objects.equals(sign, signValue);
    }

}
