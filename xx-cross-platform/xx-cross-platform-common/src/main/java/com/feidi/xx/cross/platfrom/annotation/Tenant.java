package com.feidi.xx.cross.platfrom.annotation;

import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;

import java.lang.annotation.*;

/**
 * 租户注解
 *
 * <AUTHOR>
 * @date 2025/3/13
 */
@Target({ElementType.TYPE, ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Tenant {

    PlatformCodeEnum platformCode() default PlatformCodeEnum.MT;

    String channelName() default "channel";
}
