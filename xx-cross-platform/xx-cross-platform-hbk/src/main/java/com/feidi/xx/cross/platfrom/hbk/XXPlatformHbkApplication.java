package com.feidi.xx.cross.platfrom.hbk;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 第三方平台 - 哈啰模块
 *
 * <AUTHOR>
 */
@EnableDubbo
@MapperScan("com.feidi.xx.**.mapper")
@SpringBootApplication(scanBasePackages = {"com.feidi.xx.**"})
public class XXPlatformHbkApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(XXPlatformHbkApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  第三方平台 - 哈啰模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }

}
