package com.feidi.xx.cross.operate.api.domain.price.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 价格模版
 */
@Data
public class InitiateDataDto  implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 起步里程 单位公里
     */
    private Double startMileage;

    /**
     * 起步价 单位分
     */
    private Long startPrice;

}
