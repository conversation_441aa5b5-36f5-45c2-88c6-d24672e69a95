package com.feidi.xx.cross.operate.api.domain.price.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 计算价格参数
 *
 * <AUTHOR>
 * @date 2024/9/7
 */
@Data
@Accessors(chain = true)
public class RemoteCalculateBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 出发时间 时间戳(秒级)
     */
    private Long startTime;

    /**
     * 最晚出发时间 时间戳(秒级)
     */
    private Long endTime;

    /**
     * 平台code PlatformCodeEnum
     */
    private String platformCode;

    /**
     * 价格类型 PriceTypeEnum
     */
        private String priceType;
    /**
     * 价格模版id
     */
    private Long priceId;
    /**
     * 乘客数量
     */
    private Integer passengerCount;
    /**
     * 产品code ProductCodeEnum
     */
    private String productCode;
    /**
     * 里程（单位：米）
     */
    private Integer mileage;

    //预估key
    private String estimateKey;

    //线路id
    private Long lineId;
    //出发地 起点区编码
    private String startAdCode;
    //出发地 短地址
    private String startShortAddress;
    //出发地 详细地址
    private String startAddress;
    //起点经度
    private Double startLongitude;
    //起点纬度
    private Double startLatitude;

    //目的地 终点区编码
    private String endAdCode;
    //目的地 短地址
    private String endShortAddress;
    //目的地 详细地址
    private String endAddress;
    //终点经度
    private Double endLongitude;
    //终点纬度
    private Double endLatitude;


}
