package com.feidi.xx.cross.operate.api.domain.linefilter.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 线路过滤规则对象 ox_line_filter
 *
 * <AUTHOR>
 * @date 2024-11-07
 */
@Data
public class RemoteLineFilter implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 线路id
     */
    private Long lineId;

    /**
     * 过滤状态[IsYesEnum]
     */
    private String filterSwitch;

    /**
     * 过滤长度
     */
    private Integer filterLength;

    /**
     * 过滤时长（分）
     */
    private Integer filterDuration;

    /**
     * 过滤金额
     */
    private Integer filterAmount;

    /**
     * 过滤里程
     */
    private Integer filterMileage;
}
