package com.feidi.xx.cross.operate.api.domain.price.vo;


import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 定价视图对象 uf_pricing
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Data
public class RemotePriceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 价格模版主表
     */
    private Long id;

    /**
     * 价格模版名称
     */
    private String name;

    /**
     * 价格类型[PriceTypeEnum]
     */
    private String pricingType;

    /**
     * 是否节假日模板
     */
    private String holidayTemplate;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否模板
     */
    private String template;

    /**
     * 备注
     */
    private String remark;


}
