package com.feidi.xx.cross.operate.api.domain.city.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 城市对象
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
public class RemoteCityVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 价格模版
     */
    private Long priceId;

    /**
     * 省ID
     */
    private Long provinceId;

    /**
     * 市ID
     */
    private Long cityId;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 开始运营时间
     */
    private String startTime;
    /**
     * 结束运营时间
     */
    private String endTime;
    /**
     * 最大接单数量
     */
    private Integer maxNumber;

    /**
     * 状态[StatusEnum]
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
}
