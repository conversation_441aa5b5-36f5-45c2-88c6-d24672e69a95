package com.feidi.xx.cross.operate.api.domain.estimate.vo;

import com.feidi.xx.cross.operate.api.domain.price.dto.PriceDto;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 询价记录视图对象 opr_estimate_record
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Data
public class RemoteEstimateRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
//    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 线路id
     */
//    @ExcelProperty(value = "线路id")
    private Long lineId;

    /**
     * 计价模板id
     */
//    @ExcelProperty(value = "计价模板id")
    private Long priceId;

    /**
     * 起点省
     */
//    @ExcelProperty(value = "起点省")
    private String startProvinceName;

    /**
     * 起点市
     */
    private String startCityName;

    /**
     * 起点市编码
     */
//    @ExcelProperty(value = "起点市编码")
    private String startCityCode;

    /**
     * 起点区
     */
//    @ExcelProperty(value = "起点区")
    private String startDistrictName;

    /**
     * 起点区编码
     */
//    @ExcelProperty(value = "起点区编码")
    private String startAdCode;

    /**
     * 起点位置（长地址）
     */
//    @ExcelProperty(value = "起点位置（长地址）")
    private String startAddress;

    /**
     * 起点位置（短地址）
     */
    private String startShortAddress;

    /**
     * 起点经度
     */
//    @ExcelProperty(value = "起点经度")
    private Double startLongitude;

    /**
     * 起点纬度
     */
//    @ExcelProperty(value = "起点纬度")
    private Double startLatitude;

    /**
     * 终点省
     */
//    @ExcelProperty(value = "终点省")
    private String endProvinceName;

    /**
     * 终点市
     */
    private String endCityName;

    /**
     * 终点市编码
     */
//    @ExcelProperty(value = "终点市编码")
    private String endCityCode;

    /**
     * 终点区id
     */
//    @ExcelProperty(value = "终点区Name")
    private String endDistrictName;

    /**
     * 终点区编码
     */
//    @ExcelProperty(value = "终点区编码")
    private String endAdCode;

    /**
     * 终点位置（长地址）
     */
//    @ExcelProperty(value = "终点位置（长地址）")
    private String endAddress;

    /**
     * 终点位置（短地址）
     */
    private String endShortAddress;

    /**
     * 终点经度
     */
//    @ExcelProperty(value = "终点经度")
    private Double endLongitude;

    /**
     * 终点纬度
     */
//    @ExcelProperty(value = "终点纬度")
    private Double endLatitude;

    /**
     * 最早出发时间
     */
//    @ExcelProperty(value = "最早出发时间")
    private Date earliestTime;

    /**
     * 最晚出发时间
     */
//    @ExcelProperty(value = "最晚出发时间")
    private Date latestTime;

    /**
     * 里程
     */
//    @ExcelProperty(value = "里程")
    private Integer mileage;

    /**
     * 产品类型[productCodeEnum]
     */
//    @ExcelProperty(value = "产品类型[productCodeEnum]")
    private String productCode;

    /**
     * 乘客数量
     */
//    @ExcelProperty(value = "乘客数量")
    private Integer passengerNum;

    /**
     * 返回的价格
     */
//    @ExcelProperty(value = "返回的价格")
    private List<PriceDto> price;

    /**
     * 计价KEY
     */
//    @ExcelProperty(value = "计价KEY")
    private String estimateKey;

    /**
     * 乘客电话
     */
    private String passengerPhone;

    /**
     * 乘客创建时间
     */
    private Date passengerCreateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    private Long orderId;
}
