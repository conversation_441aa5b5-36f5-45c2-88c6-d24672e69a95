package com.feidi.xx.cross.operate.api.domain.line.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 线路视图对象 ox_line
 *
 * <AUTHOR>
 * @date 2024-08-29
 */
@Data
public class RemoteLineVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 起点省
     */
    private Long startProvinceId;

    /**
     * 起点城市
     */
    private Long startCityId;

    /**
     * 起点城市编码
     */
    private String startCityCode;

    /**
     * 起点定价
     */
    private Long startPricingId;

    /**
     * 终点省
     */
    private Long endProvinceId;

    /**
     * 终点城市
     */
    private Long endCityId;

    /**
     * 终点城市编码
     */
    private String endCityCode;

    /**
     * 终点定价
     */
    private Long endPricingId;

    /**
     * 类型
     */
    private String type;

    /**
     * 状态
     */
    private String status;
}
