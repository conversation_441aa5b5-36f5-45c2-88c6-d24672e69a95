package com.feidi.xx.cross.operate.api.domain.price.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 长途对象
 */
@Data
public class MileageDataDto  implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 结束里程 单位公里 开区间 当为0时无线大
     */
    private Double end;
    /**
     * 开始里程 单位公里 闭区间
     */
    private Double start;
    /**
     * 单价 分
     */
    private Integer unit;

}
