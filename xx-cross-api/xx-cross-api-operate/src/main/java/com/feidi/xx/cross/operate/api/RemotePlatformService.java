package com.feidi.xx.cross.operate.api;


import com.feidi.xx.cross.operate.api.domain.platform.vo.RemotePlatformVo;

/**
 * 平台远程服务
 *
 * <AUTHOR>
 * @date 2024/9/4
 */
public interface RemotePlatformService {

    /**
     * 根据联盟编码获取平台信息
     *
     * @param platformCode 平台编码
     * @return 平台信息
     */
    RemotePlatformVo queryByCode(String platformCode);

    /**
     * 根据平台编码和appKey获取平台信息
     *
     * @param code 平台编码
     * @param appKey appKey
     * @return 平台信息
     */
    RemotePlatformVo queryByCodeAndAppKey(String code, String appKey);

}
