package com.feidi.xx.cross.operate.api.domain.price.dto;


import com.feidi.xx.cross.market.api.domain.couponGrant.RemoteCouponGrantVo;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 价格返回值
 */
@Data
public class PriceDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    // 起步价
    private Long initPrice = 0L;

    // 里程金额（单位：分）【不包含startMileagePrice】
    private Long mileagePrice = 0L;

    // 总金额（单位：分）【initPrice+mileagePrice】
    private Long totalPrice = 0L;

    // 附加费金额（单位：分）【独立】
    private Long addPrice = 0L;

    // 拼车实际金额（单位：分）【totalPrice*discount】
    private Long calculatePrice = 0L;

    // 产品code ProductCodeEnum
    private String productCode;

    /**
     * 优惠卷
     */
    private List<RemoteCouponGrantVo> couponGrantVoList;

}
