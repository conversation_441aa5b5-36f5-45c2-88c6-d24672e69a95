package com.feidi.xx.cross.operate.api.domain.product.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 产品对象
 *
 * <AUTHOR>
 * @date 2024/10/18
 */
@Data
public class RemoteProductVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 业务线
     */
    private Long businessId;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 图标
     */
    private String icon;

    /**
     * 状态
     */
    private String status;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
}
