package com.feidi.xx.cross.operate.api;

import com.feidi.xx.cross.operate.api.domain.line.vo.RemoteLineVo;

import java.util.List;

/**
 * 路线远程服务
 *
 * <AUTHOR>
 * @date 2025/3/14
 */
public interface RemoteLineService {

    /**
     * 根据id查询路线信息
     *
     * @param id 路线id
     * @return 路线信息
     */
    RemoteLineVo queryByLineId(Long id);

    /**
     * 根据id集合查询路线信息
     *
     * @param ids 路线id集合
     * @return 路线信息
     */
    List<RemoteLineVo> queryByLineIds(List<Long> ids);

    /**
     * 根据起点AdCode和终点AdCode查询路线id
     *
     * @param startAdCode 起点AdCode
     * @param endAdCode   终点AdCode
     * @return 路线id
     */
    Long matchLine(String startAdCode, String endAdCode);


    /**
     * 根据起点AdCode和终点AdCode查询路线id
     *
     * @param startAdCode 起点AdCode
     * @param endAdCode   终点AdCode
     * @return 路线id
     */
    Long matchLine(String startAdCode, String endAdCode,Double startLatitudem,Double startLongitudem,Double endLatitudem,Double endLongitudem);

    /**
     * 获取全部路线
     *
     * @return 路线集合
     */
    List<RemoteLineVo> getAll();
}
