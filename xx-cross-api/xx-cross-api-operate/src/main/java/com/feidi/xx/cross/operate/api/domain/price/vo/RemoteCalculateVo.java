package com.feidi.xx.cross.operate.api.domain.price.vo;

import com.feidi.xx.cross.operate.api.domain.price.dto.PriceDto;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 预估价返回值
 *
 * <AUTHOR>
 * @date 2024/9/7
 */
@Data
public class RemoteCalculateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private List<PriceDto> priceDtoList;

    //预估key
    private String estimateKey;

}
