package com.feidi.xx.cross.operate.api;

import com.feidi.xx.cross.operate.api.domain.city.vo.RemoteCityVo;

import java.util.List;

/**
 * 城市服务
 *
 * <AUTHOR>
 * @date 2025/3/18
 */
public interface RemoteCityService {

    /**
     * 根据cityCode查询城市
     * @param cityCode
     * @return
     */
    RemoteCityVo queryByCityCode(String cityCode);


    /**
     * 根据cityIds查询城市名称
     * @param cityIds
     * @return
     */
    List<String> queryByCityIds(List<Long> cityIds);

    /**
     * 根据开城城市表id查询开城城市信息
     *
     * @param id 开城城市表id
     * @return 开城城市信息
     */
    RemoteCityVo queryById(Long id);

    /**
     * 根据开城城市表id集合查询开城城市信息
     *
     * @param ids 开城城市表id
     * @return 开城城市信息集合
     */
    List<RemoteCityVo> queryByIds(List<Long> ids);


}
