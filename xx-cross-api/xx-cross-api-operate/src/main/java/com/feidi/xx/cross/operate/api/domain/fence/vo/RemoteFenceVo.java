package com.feidi.xx.cross.operate.api.domain.fence.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 电子围栏对象
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
public class RemoteFenceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 省ID
     */
    private Long provinceId;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 围栏坐标点
     */
    private Double[] points;

    /**
     * 类型
     */
    private String type;

    /**
     * 状态[StatusEnum]
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
}
