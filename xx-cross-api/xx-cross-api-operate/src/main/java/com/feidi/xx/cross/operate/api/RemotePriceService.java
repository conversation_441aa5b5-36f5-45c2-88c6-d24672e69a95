package com.feidi.xx.cross.operate.api;

import com.feidi.xx.cross.operate.api.domain.price.bo.RemoteCalculateBo;
import com.feidi.xx.cross.operate.api.domain.price.vo.RemoteCalculateVo;
import com.feidi.xx.cross.operate.api.domain.price.vo.RemotePriceVo;

import java.util.List;

/**
 * 定价服务接口
 *
 * <AUTHOR>
 * @date 2024/9/7
 */
public interface RemotePriceService {

    /**
     * 计算价格
     *
     * @param remotePricingBo 计算价格参数
     * @return
     */
    RemoteCalculateVo calculatePrice(RemoteCalculateBo remotePricingBo);


    /**
     * 根据价格id获取价格信息
     *
     * @param priceId
     * @return
     */

    RemotePriceVo getPriceInfoByPriceId(Long priceId);

}
