package com.feidi.xx.cross.operate.api;
import com.feidi.xx.cross.operate.api.domain.estimate.vo.RemoteEstimateRecordVo;

/**
 * 定价服务接口
 *
 * <AUTHOR>
 */
public interface RemoteEstimateRecordService {

    /**
     * 根据询价key获取询价信息
     *
     * @param estimateKey 询价
     * @return emoteEstimateRecordVo
     */

    RemoteEstimateRecordVo getEstimateRecordByEstimateKey(String estimateKey);

    void updateEstimateRecordById(Long estimateId, Long orderId);

}
