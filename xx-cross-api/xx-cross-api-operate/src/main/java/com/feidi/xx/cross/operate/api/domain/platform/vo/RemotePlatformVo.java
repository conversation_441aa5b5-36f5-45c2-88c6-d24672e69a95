package com.feidi.xx.cross.operate.api.domain.platform.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 平台信息
 *
 * <AUTHOR>
 * @date 2024/9/5
 */
@Data
public class RemotePlatformVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 主键
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 应用
     */
    private String appId;

    /**
     * 接口KEY
     */
    private String appKey;

    /**
     * 接口密钥
     */
    private String appSecret;

    /**
     * 接口私钥
     */
    private String appPrivateSecret;

    /**
     * 状态
     */
    private String status;

    /**
     * 平台抽成比例
     */
    private BigDecimal rate;
}
