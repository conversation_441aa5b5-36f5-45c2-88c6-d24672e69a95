package com.feidi.xx.cross.operate.api;

import com.feidi.xx.cross.operate.api.domain.business.vo.RemoteBusinessVo;

import java.util.List;

/**
 * 业务线服务
 *
 * <AUTHOR>
 * @date 2024/9/6
 */
public interface RemoteBusinessService {

    /**
     * 通过code获取业务线数据
     *
     * @param code 业务线code
     * @return
     */
    RemoteBusinessVo getByCode(String code);

    /**
     * 通过id集合获取业务线数据
     * @param ids
     * @return
     */
    List<RemoteBusinessVo> queryByBusinessIds(List<Long> ids);
}
