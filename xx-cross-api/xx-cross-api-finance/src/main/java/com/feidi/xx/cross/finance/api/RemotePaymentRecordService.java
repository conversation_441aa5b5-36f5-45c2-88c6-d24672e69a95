package com.feidi.xx.cross.finance.api;

import com.feidi.xx.common.core.enums.PaymentStatusEnum;
import com.feidi.xx.common.core.enums.PaymentTypeEnum;
import com.feidi.xx.cross.finance.api.domain.bo.RemotePaymentRecordBo;
import com.feidi.xx.cross.finance.api.domain.vo.RemotePaymentRecordVo;

import java.util.List;

/**
 * 支付流水服务
 *
 * <AUTHOR>
 */
public interface RemotePaymentRecordService {

    /**
     * 获取支付流水列表
     * @param joinTable
     * @param joinIds
     * @return
     */
    List<RemotePaymentRecordVo> queryList(String joinTable, List<Long> joinIds);

    /**
     *  保存支付流水
     * @param  bo
     * @return
     */
    void savePaymentRecord(RemotePaymentRecordBo bo);

    /**
     * 获取流水信息
     * @param tenantId
     * @param id
     * @param joinTable
     * @param paymentType 支付方式
     */
    RemotePaymentRecordVo getPaymentRecord(String tenantId, Long id, String joinTable, PaymentTypeEnum paymentType);

    /**
     * 获取流水信息
     * @param tenantId
     * @param appId
     * @param outBizNo
     * @param paymentType 支付方式
     * @param status
     * @return
     */
    RemotePaymentRecordVo getPaymentRecord(String tenantId, String appId, String outBizNo, PaymentTypeEnum paymentType, PaymentStatusEnum status);
}
