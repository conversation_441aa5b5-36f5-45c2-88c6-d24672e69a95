package com.feidi.xx.cross.finance.api.domain.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 订单返利参数
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RemoteRebateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单IDs
     */
    private List<Long> orderIds;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 父代理商id
     */
    private Long parentId;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 订单金额[基础金额+附加费金额-投诉扣款金额]
     */
    private Long orderPrice;

    /**
     * 司机佣金比例
     */
    private BigDecimal driverRate;

    /**
     * 司机收益
     */
    private Long driverProfit;

    /**
     * 返利状态
     */
    private String rebateStatus;

    /**
     * 投诉扣款金额
     */
    private Long complainPrice;

    /**
     * 备注
     */
    private String remark;

    /**
     * 相关订单id
     */
    private List<Long> relatedOrder;

}
