package com.feidi.xx.cross.finance.api.domain.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 资金流水对象
 */
@Data
public class RemoteFlowBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 流水单号
     */
    private String flowNo;

    /**
     * 代理id
     */
    private Long agentId;

    /**
     * 父代理id
     */
    private Long parentId;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 关联表
     */
    private String joinTable;

    /**
     * 关联单号
     */
    private String joinNo;

    /**
     * 关联id
     */
    private Long joinId;

    /**
     * 关联ids
     */
    private List<Long> joinIds;

    /**
     * 进出
     */
    private String direction;

    /**
     * 类型
     */
    private String type;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

}
