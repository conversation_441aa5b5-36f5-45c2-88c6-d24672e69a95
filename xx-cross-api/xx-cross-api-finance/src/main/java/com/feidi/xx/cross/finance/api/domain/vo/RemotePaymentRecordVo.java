package com.feidi.xx.cross.finance.api.domain.vo;

import com.feidi.xx.common.core.enums.JoinEnum;
import com.feidi.xx.common.core.enums.PaymentStatusEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单返利参数
 *
 * <AUTHOR>
 */
@Data
public class RemotePaymentRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 主键
     */
    private Long id;

    /**
     * 支付方式 {@link com.feidi.xx.common.core.enums.PaymentTypeEnum}
     */
    private String paymentType;

    /**
     * AppId
     */
    private String appId;

    /**
     * 商户ID|OpenId
     */
    private String mchId;

    /**
     * 关联表 {@link JoinEnum}
     */
    private String joinTable;

    /**
     * 关联单号
     */
    private String joinNo;

    /**
     * 关联id
     */
    private Long joinId;

    /**
     * 业务单号
     */
    private String outBizNo;

    /**
     * 流水单号
     */
    private String flowNo;

    /**
     * 交易ID
     */
    private String orderId;

    /**
     * 金额
     */
    private Long amount;

    /**
     * 交易时间
     */
    private String tradeTime;

    /**
     * 类型
     */
    private String type;

    /**
     * 进出 {@link com.feidi.xx.common.core.enums.DirectionEnum}
     */
    private String direction;

    /**
     * 状态 {@link PaymentStatusEnum}
     */
    private String status;

    /**
     * 是否退款
     */
    private String isRefund;

    /**
     * 退款单号
     */
    private String refundNo;

    /**
     * 退款金额
     */
    private Long refundAmount;

    /**
     * 交易状态码
     */
    private String code;

    /**
     * 交易提示信息
     */
    private String msg;

    /**
     * 请求参数
     */
    private String paramsJson;

    /**
     * 返回结果集
     */
    private String responseJson;

    /**
     * 是否回调插入
     */
    private String notify;

    /**
     * 创建时间
     */
    private Date createTime;
}
