package com.feidi.xx.cross.finance.api;

import com.feidi.xx.cross.finance.api.domain.bo.RemoteFlowBo;
import com.feidi.xx.cross.finance.api.domain.vo.RemoteFlowVo;

import java.util.List;

/***
 *  远程流水服务
 **/
public interface RemoteFlowService {

    /**
     * 根据Bo查询流水，注意里面逻辑并未完善，并非所有字段都有查询
     * @param bo
     * @return
     */
    List<RemoteFlowVo> listByBo(RemoteFlowBo bo);

    /**
     * 根据Bo更新流水
     * @param updateBo 更新的字段
     * @param queryBo 查询的字段
     * @return
     */
    int updateByBo(RemoteFlowBo updateBo, RemoteFlowBo queryBo);

}
