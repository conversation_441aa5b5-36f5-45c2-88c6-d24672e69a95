package com.feidi.xx.cross.finance.api;

import com.feidi.xx.cross.finance.api.domain.bo.RemoteRebateBo;
import com.feidi.xx.cross.finance.api.domain.vo.RemoteCashVo;
import com.feidi.xx.cross.finance.api.domain.vo.RemoteWalletVo;

/***
 *  司机钱包服务
 * @description <TODO description class purpose>
 * <AUTHOR>
 * @version 1.0.0
 * @create 2024/8/31 13:07
 **/
public interface RemoteDrvWalletService {

    /**
     * 获取司机钱包信息
     * @param driverId
     * @return
     */
    RemoteWalletVo getWallet(Long driverId);

    /**
     * 创建司机的钱包
     * @param driverId
     * @return
     */
     void makeDrvWallet(Long driverId);

    /**
     * 司机提现情况查询
     * @param driverId
     * @return
     */
    RemoteCashVo cashStatus(Long driverId);

    /**
     * 订单客诉
     *
     * @param remoteRebateBo
     * @return
     */
    <PERSON><PERSON><PERSON> complain(RemoteRebateBo remoteRebateBo);

    /**
     * 订单返利-资金冻结
     *
     * @param remoteRebateBo
     * @return
     */
    Boolean orderFreezeAdd(RemoteRebateBo remoteRebateBo);

    /**
     * 记录司机可结算订单的总利润流水
     *
     * @param remoteRebateBo
     * @return
     */
    Boolean insertTotalProfitFlow(RemoteRebateBo remoteRebateBo);

    /**
     * 记录司机可结算订单的总利润流水
     *
     * @param remoteRebateBo
     * @return
     */
    Boolean insertTotalProfitFlowForResell(RemoteRebateBo remoteRebateBo);

    /**
     * 更改钱包状态
     */
    Boolean updateWallerStatus(Long driverId,String status);

    /**
     * 订单返利-订单转卖收益冻结
     *
     * @param remoteRebateVo
     * @return
     */
    boolean orderFreezeAddForResell(RemoteRebateBo remoteRebateVo);

    /**
     * 订单转卖客诉
     *
     * @param rebateBo
     * @return
     */
    boolean resellComplain(RemoteRebateBo rebateBo);
}
