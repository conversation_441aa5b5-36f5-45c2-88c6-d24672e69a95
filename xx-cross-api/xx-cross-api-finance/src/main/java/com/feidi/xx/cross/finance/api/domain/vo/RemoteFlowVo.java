package com.feidi.xx.cross.finance.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 资金流水对象
 */
@Data
public class RemoteFlowVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 流水单号
     */
    private String flowNo;

    /**
     * 代理id
     */
    private Long agentId;

    /**
     * 父代理id
     */
    private Long parentId;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 关联表
     */
    private String joinTable;

    /**
     * 关联单号
     */
    private String joinNo;

    /**
     * 关联id
     */
    private Long joinId;

    /**
     * 变动前金额
     */
    private Long beforeAmount;

    /**
     * 变动金额
     */
    private Long amount;

    /**
     * 变动后金额
     */
    private Long afterAmount;

    /**
     * 变动前可提现金额可提现金额
     */
    private Long beforeCash;

    /**
     * 变动可提现金额
     */
    private Long cash;

    /**
     * 变动后可提现金额
     */
    private Long afterCash;

    /**
     * 进出
     */
    private String direction;

    /**
     * 类型
     */
    private String type;

    /**
     * 备注
     */
    private String remark;

    /**
     * 上一条记录id
     */
    private Long preId;

    /**
     * 相关订单id
     */
    private List<Long> relatedOrder;

}
