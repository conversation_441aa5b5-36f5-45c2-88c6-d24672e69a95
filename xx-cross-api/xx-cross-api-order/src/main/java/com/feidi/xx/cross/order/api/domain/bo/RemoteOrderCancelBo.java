package com.feidi.xx.cross.order.api.domain.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单取消操作参数
 *
 * <AUTHOR>
 * @date 2025/3/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RemoteOrderCancelBo extends OrderHandleBaseBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 取消类型
     */
    private String cancelType;

    /**
     * 取消备注
     */
    private String cancelRemark;

    /**
     * 是否客诉
     */
    private String complain;

    /**
     * 客诉时间
     */
    private Date complainTime;

    /**
     * 客诉类型
     */
    private String complainType;

    /**
     * 客诉备注
     */
    private String complainRemark;

    /**
     * 投诉扣款金额
     */
    private Long complainPrice;
}
