package com.feidi.xx.cross.order.api.domain;

import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDriverVo;

/**
 * 订单司机信息服务
 *
 * <AUTHOR>
 * @date 2025/3/17
 */
public interface RemoteOrderDriverService {

    /**
     * 根据订单id查询订单司机信息
     *
     * @param orderId 订单id
     * @return 订单司机信息
     */
    RemoteOrderDriverVo queryByOrderId(Long orderId);

    /**
     * 根据订单id获取订单调度司机信息
     *
     * @param orderId 订单id
     * @return 订单调度司机信息
     */
    RemoteOrderDriverVo queryDispatchByOrderId(Long orderId);
}
