package com.feidi.xx.cross.order.api;


import com.feidi.xx.cross.order.api.domain.bo.OperateBo;

/**
 * 订单操作记录服务
 *
 * <AUTHOR>
 * @date 2024/9/4
 */
public interface RemoteOperateService {

    /**
     * 保存订单操作记录
     *
     * @param operateBo 订单操作记录参数
     */
    void saveOperate(OperateBo operateBo);

    /**
     * 修改订单操作记录备注
     *
     * @param orderId     订单id
     * @param operateType 操作类型
     * @param remark      备注
     */
    void updateOperateRemark(Long orderId, String operateType, String remark);
}
