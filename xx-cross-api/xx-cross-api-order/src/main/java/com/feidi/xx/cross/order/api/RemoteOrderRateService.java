package com.feidi.xx.cross.order.api;


import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderRateVo;

import java.util.List;

/**
 * 订单分佣服务
 *
 * <AUTHOR>
 * @date 2025/3/17
 */
public interface RemoteOrderRateService {

    /**
     * 根据订单id查询分佣信息
     *
     * @param orderId 订单id
     * @return 订单分佣信息
     */
    List<RemoteOrderRateVo> queryByOrderId(Long orderId);

    /**
     * 根据订单id查询分佣信息
     *
     * @param orderIds 订单id集合
     * @return 订单分佣信息
     */
    List<RemoteOrderRateVo> queryByOrderIds(List<Long> orderIds);

    /**
     * 根据订单id和分佣类型查询分佣信息
     *
     * @param orderId 订单id
     * @param rateType 分佣类型
     * @return 订单分佣信息
     */
    RemoteOrderRateVo queryByOrderIdAndRateType(Long orderId, String rateType);

    /**
     * 根据订单id和分佣类型查询分佣信息
     *
     * @param orderIds 订单id集合
     * @param rateType 分佣类型
     * @return 订单分佣信息
     */
    List<RemoteOrderRateVo> queryByOrderIdsAndRateType(List<Long> orderIds, String rateType);
}
