package com.feidi.xx.cross.order.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


@Data
public class RemoteRobVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 编号
     */
    private String robNo;

    /**
     * 代理ID
     */
    private Long agentId;

    /**
     * 线路ID
     */
    private Long lineId;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 开始省ID
     */
    private Long startProvinceId;

    /**
     * 开始省
     */
    private String startProvince;

    /**
     * 开始城市ID
     */
    private Long startCityId;

    /**
     * 开始城市编码
     */
    private String startCityCode;

    /**
     * 开始市
     */
    private String startCity;

    /**
     * 开始区域ID
     */
    private Long startDistrictId;

    /**
     * 开始区域编码
     */
    private String startAdCode;

    /**
     * 开始区域
     */
    private String startDistrict;

    /**
     * 开始地址
     */
    private String startAddress;

    /**
     * 开始经度
     */
    private String startLongitude;

    /**
     * 开始纬度
     */
    private String startLatitude;

    /**
     * 开始半径
     */
    private Long startRadius;

    /**
     * 结束省ID
     */
    private Long endProvinceId;

    /**
     * 结束省
     */
    private String endProvince;

    /**
     * 结束城市ID
     */
    private Long endCityId;

    /**
     * 结束城市编码
     */
    private String endCityCode;

    /**
     * 结束市
     */
    private String endCity;

    /**
     * 结束区域ID
     */
    private Long endDistrictId;

    /**
     * 结束区域编码
     */
    private String endAdCode;

    /**
     * 结束区域
     */
    private String endDistrict;

    /**
     * 结束地址
     */
    private String endAddress;

    /**
     * 结束经度
     */
    private String endLongitude;

    /**
     * 结束纬度
     */
    private String endLatitude;

    /**
     * 结束半径
     */
    private Long endRadius;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 排队时间
     */
    private String sortTime;

    /**
     * 等待时间
     */
    private Integer maxWaitDuration;

    /**
     * 座位数
     */
    private Integer seat;

    /**
     * 剩余座位数
     */
    private Integer surplusSeat;

    /**
     * 抢单产品编码
     */
    private String robProduct;

    /**
     * 乘客数量
     */
    private Integer passengerNum;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 续期天数
     */
    private Integer renewal;

    /**
     * 状态
     */
    private String status;

    /**
     * 方案
     */
    private String plan;

    /**
     * 司机
     */
    private String driverJson;

    /**
     * 备注
     */
    private String remark;

    /**
     * 主线路ID
     */
    private Long mainLineId;

    /**
     * 主线路名称
     */
    private String mainLineName;

}
