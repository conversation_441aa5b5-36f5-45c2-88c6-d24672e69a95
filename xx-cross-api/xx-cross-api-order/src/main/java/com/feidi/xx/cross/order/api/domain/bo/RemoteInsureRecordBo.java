package com.feidi.xx.cross.order.api.domain.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 保险记录对象 cx_insure_record
 *
 * <AUTHOR>
 * @date 2024-10-08
 */
@Data
public class RemoteInsureRecordBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 操作人类型
     */
    private String userType;

    /**
     * 操作人ID
     */
    private Long userId;

    /**
     * 操作人
     */
    private String userName;

    /**
     * 保险记录类型
     */
    private String recordType;

    /**
     * 操作时间戳
     */
    private Long timeStamp;

    /**
     * 参数
     */
    private String paramsJson;

    /**
     * 结果
     */
    private String responseJson;

    /**
     * 状态
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 投保信息
     */
    private String insureContent;
}
