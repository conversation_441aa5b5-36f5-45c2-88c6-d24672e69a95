package com.feidi.xx.cross.order.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 订单司机、车辆信息
 *
 * <AUTHOR>
 */
@Data
public class RemoteOrderDriverVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 手机号
     */
    private String driverPhone;

    /**
     * 名称
     */
    private String driverName;

    /**
     * 头像
     */
    private String driverAvatar;

    /**
     * 性别[SexEnum]
     */
    private String driverSex;

    /**
     * 身份证号
     */
    private String driverCardNo;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 品牌
     */
    private String carBrand;

    /**
     * 型号
     */
    private String carModel;

    /**
     * 颜色
     */
    private String carColor;

    /**
     * 样式[CarStyleEnum]
     */
    private String carType;

    /**
     * 驱动方式[DriveTypeEnum]
     */
    private String driveType;

    /**
     * 车辆识别码（车架号）
     */
    private String vin;

    /**
     * 发动机编号
     */
    private String engine;

    /**
     * 调度类型[DispatchTypeEnum]
     */
    private String type;

    /**
     * 调度人类型[UserTypeEnum]
     */
    private String dispatchUserType;

    /**
     * 调度人ID
     */
    private String dispatchUserId;

    /**
     * 调度状态[SuccessFailEnum]
     */
    private String dispatchStatus;

    /**
     * 调度失败类型[DispatchFailTypeEnum]
     */
    private String dispatchFailType;

    /**
     * 调度备注
     */
    private String dispatchRemark;

    /**
     * 状态[StatusEnum]
     */
    private String status;

}
