package com.feidi.xx.cross.order.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单分佣对象
 *
 * <AUTHOR>
 * @date 2025-3-17
 */
@Data
public class RemoteOrderRateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 账单类型[BillTypeEnum]
     */
    private String rateType;

    /**
     * 总价
     */
    private Long total;

    /**
     * 分佣比例
     */
    private BigDecimal rate;

    /**
     * 分佣金额
     */
    private Long amount;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户类型[UserTypeEnum]
     */
    private String userType;

    /**
     * 状态[StatusEnum]
     */
    private String status;

    /**
     * 排序
     */
    private Integer sort;
}
