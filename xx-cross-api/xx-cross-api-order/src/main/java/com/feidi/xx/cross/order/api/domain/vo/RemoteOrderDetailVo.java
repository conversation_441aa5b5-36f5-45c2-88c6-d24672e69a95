package com.feidi.xx.cross.order.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单详情
 *
 * <AUTHOR>
 * @date 2024/9/4
 */
@Data
public class RemoteOrderDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 平台ID
     */
    private Long platformId;

    /**
     * 平台编号
     */
    private String platformCode;

    /**
     * 平台单号
     */
    private String platformNo;

    /**
     * 线路ID
     */
    private Long lineId;

    /**
     * 线路名称
     */
    private String lineName;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 调度司机ID
     */
    private Long dispatchDriverId;

    /**
     * 业务线ID
     */
    private Long businessId;

    /**
     * 业务线编码
     */
    private String businessCode;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 司机单号
     */
    private String driverNo;

    /**
     * 基础金额
     */
    private Long basePrice;

    /**
     * 附加费金额
     */
    private Long addPrice;

    /**
     * 投诉扣款金额
     */
    private Long complainPrice;

    /**
     * 订单金额
     */
    private Long orderPrice;

    /**
     * 取消费
     */
    private Long cancelFee;

    /**
     * 高速类型
     */
    private String highwayType;

    /**
     * 司机佣金比例
     */
    private Integer driverRate;

    /**
     * 司机收益
     */
    private Long driverProfit;

    /**
     * 乘客数量
     */
    private Integer passengerNum;

    /**
     * 司乘虚拟号
     */
    private String virtualPhone;

    /**
     * 调度虚拟号
     */
    private String virtualDispatch;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 乘客真实手机号
     */
    private String passengerPhone;

    /**
     * 乘客电话尾号
     */
    private String phoneEnd;

    /**
     * 乘客备注
     */
    private String passengerRemark;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 第三方订单状态
     */
    private String thirdStatus;

    /**
     * 返利状态
     */
    private String rebateStatus;

    /**
     * 返利时间
     */
    private Date rebateTime;

    /**
     * 支付状态
     */
    private String payStatus;

    /**
     * 支付方式
     */
    private String payMode;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 支付单号
     */
    private String payNo;

    /**
     * 预计出发时间
     */
    private Date earliestTime;

    /**
     * 最大等待时长
     */
    private Integer maxWaitDuration;

    /**
     * 预计最晚出发时间
     */
    private Date latestTime;

    /**
     * 接单时间
     */
    private Date receiveTime;

    /**
     * 接驾时间
     */
    private Date pickTime;

    /**
     * 行程开始时间
     */
    private Date tripStartTime;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 保险编码
     */
    private String insureCode;

    /**
     * 保险单号
     */
    private String insureNo;

    /**
     * 投保时间
     */
    private Date insureTime;

    /**
     * 是否调度
     */
    private String isDispatch;

    /**
     * 调度时间
     */
    private Date dispatchTime;

    /**
     * 调度类型
     */
    private String dispatchType;

    /**
     * 调度状态
     */
    private String dispatchStatus;

    /**
     * 调度失败类型
     */
    private String dispatchFailType;

    /**
     * 是否预约
     */
    private String isDue;

    /**
     * 取消备注
     */
    private String cancelRemark;

    /**
     * 取消人类型
     */
    private String cancelUserType;

    /**
     * 取消人ID
     */
    private Long cancelUserId;

    /**
     * 取消时间
     */
    private Date cancelTime;

    /**
     * 取消类型
     */
    private String cancelType;

    /**
     * 司机信息
     */
    private RemoteOrderDriverVo driverVo;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 调度司机名称
     */
    private String dispatchDriverName;

    /**
     * 调度司机电话
     */
    private String dispatchDriverPhone;
}
