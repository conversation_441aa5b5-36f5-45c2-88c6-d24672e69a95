package com.feidi.xx.cross.order.api;

import com.feidi.xx.cross.order.api.domain.bo.RelationBo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteRobVo;

import java.util.List;

/**
 * 抢单服务
 *
 * <AUTHOR>
 * @date 2024/9/4
 */
public interface RemoteRobService {

    /**
     * 是否存在代理和线路的抢单
     * @param agentId
     * @param lineId
     * @return
     */
    List<RemoteRobVo> listByAgentIdAndLineId(Long agentId, Long lineId);

    /**
     * 关掉司机所有的抢单
     *
     * @param driverIds
     */
    void disableDriverRob(Long... driverIds);

    /**
     * 关掉代理所有的抢单
     *
     * @param agentIds
     */
    void disableAgentRob(Long... agentIds);

    /**
     * 关掉线路所有的抢单
     *
     * @param lineIds
     */
    void disableLineRob(Long... lineIds);

    /**
     * 关掉抢单
     *
     * @param bo
     */
    void disableRob(RelationBo bo);

    /**
     * 切换代理商
     * @param driverId
     */
    void switchAgent(Long driverId);
}
