package com.feidi.xx.cross.order.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 下单返回值
 *
 * <AUTHOR>
 */
@Data
public class RemotePlaceOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long id;

    /**
     * 线路ID
     */
    private Long lineId;

    /**
     * 系统单号
     */
    private String orderNo;

    /**
     * 平台单号
     */
    private String platformNo;

    /**
     * 是否被过滤
     */
    private String isFiltered;
}
