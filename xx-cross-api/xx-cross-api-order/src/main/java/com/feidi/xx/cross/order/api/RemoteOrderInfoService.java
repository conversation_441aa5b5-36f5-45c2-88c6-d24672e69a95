package com.feidi.xx.cross.order.api;

import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderInfoVo;

import java.util.List;

/**
 * 订单关联信息服务
 *
 * <AUTHOR>
 * @date 2025/3/17
 */
public interface RemoteOrderInfoService {

    /**
     * 根据订单id查询订单关联信息
     *
     * @param orderId 订单id
     * @return 订单关联信息
     */
    RemoteOrderInfoVo queryByOrderId(Long orderId);

    /**
     * 根据订单id查询订单关联信息
     *
     * @param orderIds 订单id
     * @return
     */
    List<RemoteOrderInfoVo> queryByOrderIds(List<Long> orderIds);

    Boolean updateOrderInsure(Long orderId, String insureCode, String isInsure);
}
