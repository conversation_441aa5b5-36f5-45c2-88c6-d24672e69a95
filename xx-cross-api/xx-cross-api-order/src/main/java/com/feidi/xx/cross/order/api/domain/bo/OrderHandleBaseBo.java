package com.feidi.xx.cross.order.api.domain.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 订单操作基础参数
 *
 * <AUTHOR>
 * @date 2025/3/16
 */
@Data
public class OrderHandleBaseBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 平台编号
     */
    private String platformCode;

    /**
     * 产品编号
     */
    private String productCode;

    /**
     * 平台单号
     */
    private String platformNo;

    /**
     * 操作用户类型
     */
    private String userType;

    /**
     * 操作用户ID
     */
    private Long userId = 0L;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作时间戳
     */
    private Long timeStamp;

    /**
     * 第三方订单状态
     */
    private String thirdStatus;
}
