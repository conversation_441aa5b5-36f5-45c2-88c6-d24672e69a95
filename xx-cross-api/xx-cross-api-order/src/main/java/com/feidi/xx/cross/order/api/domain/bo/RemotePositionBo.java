package com.feidi.xx.cross.order.api.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 订单位置对象
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@Accessors(chain = true)
public class RemotePositionBo  implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 省ID
     */
    private Long provinceId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 市
     */
    private String city;

    /**
     * 区域ID
     */
    private Long districtId;

    /**
     * 区域编码
     */
    private String adCode;

    /**
     * 区域
     */
    private String district;

    /**
     * 短地址
     */
    private String shortAddr;

    /**
     * 地址
     */
    private String address;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 类型[StartEndEnum]
     */
    private String type;

    /**
     * 状态[StatusEnum]
     */
    private String status;

    /**
     * 备注
     */
    private String remark;
}
