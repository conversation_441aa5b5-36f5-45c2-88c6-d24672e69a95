package com.feidi.xx.cross.order.api.domain.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单支付操作参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RemoteOrderPaymentBo extends OrderHandleBaseBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 支付状态
     */
    private String payStatusEnum;

    /**
     * 支付类型
     */
    private String payTypeEnum;

    /**
     * 支付时间
     */
    private Date payTime;

}
