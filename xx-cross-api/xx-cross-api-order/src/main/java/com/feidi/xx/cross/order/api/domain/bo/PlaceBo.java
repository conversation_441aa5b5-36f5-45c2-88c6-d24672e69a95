package com.feidi.xx.cross.order.api.domain.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 下单参数
 *
 * <AUTHOR>
 */
@Data
public class PlaceBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 平台ID
     */
    private Long platformId;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 平台单号
     */
    private String platformNo;

    /**
     * 调度司机ID
     */
    private Long dispatchDriverId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 基础金额
     */
    private Long basePrice;

    /**
     * 附加费金额
     */
    private Long addPrice;

    /**
     * 订单金额[基础金额+附加费金额]
     */
    private Long orderPrice;

    /**
     * 平台佣金比例
     */
    private Integer platformRate;

    /**
     * 平台收益
     */
    private Long platformProfit;

    /**
     * 乘客数量
     */
    private Integer passengerNum;

    /**
     * 司乘虚拟号
     */
    private String virtualPhone;

    /**
     * 调度虚拟号
     */
    private String virtualDispatch;

    /**
     * 乘客真实手机号
     */
    private String passengerPhone;

    /**
     * 乘客电话尾号
     */
    private String phoneEnd;

    /**
     * 乘客备注
     */
    private String passengerRemark;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 支付状态
     */
    private String payStatus;

    /**
     * 预计出发时间
     */
    private Date earliestTime;

    /**
     * 最大等待时长
     */
    private Long maxWaitDuration;

    /**
     * 预计最晚出发时间
     */
    private Date latestTime;

    /**
     * 里程
     */
    private Long mileage;

    /**
     * 预计时长
     */
    private Long expectDuration;

    /**
     * 标签（JSON）
     */
    private String label;

    /**
     * 订单KEY
     */
    private String orderKey;

    /**
     * 开始位置
     */
    private PositionBo startPosition;

    /**
     * 结束位置
     */
    private PositionBo endPosition;

    /**
     * 操作时间戳
     */
    private Long timeStamp;

    /**
     * 业务线ID
     */
    private Long businessId;

    /**
     * 业务线编码
     */
    private String businessCode;

    /**
     * 高速费类型
     */
    private String highwayType;

    /**
     * 第三方订单状态
     */
    private String thirdStatus;

    /**
     * 原始参数
     */
    private String originalJson;
}
