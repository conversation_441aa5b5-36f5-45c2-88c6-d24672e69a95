package com.feidi.xx.cross.order.api;


import com.feidi.xx.cross.order.api.domain.vo.RemotePositionVo;

import java.util.List;

/**
 * 订单位置服务
 *
 * <AUTHOR>
 * @date 2024/9/4
 */
public interface RemotePositionService {

    /**
     * 根据订单id查询订单位置
     *
     * @param orderId 订单id
     * @return 订单位置
     */
    List<RemotePositionVo> queryByOrderId(Long orderId);

    /**
     * 根据订单id查询订单位置
     *
     * @param orderIds 订单id集合
     * @return 订单位置
     */
    List<RemotePositionVo> queryByOrderIds(List<Long> orderIds);

    /**
     * 根据订单id和类型查询订单位置
     *
     * @param orderId 订单id
     * @param type    位置类型
     * @return 订单位置
     */
    RemotePositionVo queryByOrderIdAndType(Long orderId, String type);
}
