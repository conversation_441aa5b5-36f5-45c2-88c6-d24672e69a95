package com.feidi.xx.cross.order.api.domain.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单操作参数
 *
 * <AUTHOR>
 * @date 2024/9/4
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RemoteOrderHandleBo extends OrderHandleBaseBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 取消类型
     */
    private String cancelType;

    /**
     * 取消备注
     */
    private String cancelRemark;

    /**
     * 虚拟号
     */
    private String virtualMobile;

}
