package com.feidi.xx.cross.order.api.domain.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 操作订单参数
 *
 * <AUTHOR>
 * @date 2024/9/4
 */
@Data
public class OperateBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 操作人类型
     */
    private String userType;

    /**
     * 操作人ID
     */
    private Long userId;

    /**
     * 操作人
     */
    private String userName;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 操作时间戳
     */
    private Long timeStamp;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 原始参数
     */
    private String originalJson;

    /**
     * 参数
     */
    private String paramsJson;

    /**
     * 结果
     */
    private String responseJson;

    /**
     * 状态
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

}
