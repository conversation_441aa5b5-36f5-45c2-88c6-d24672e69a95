package com.feidi.xx.cross.order.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单关联信息对象
 *
 * <AUTHOR>
 * @date 2025/3/17
 */
@Data
public class RemoteOrderInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 司机单号
     */
    private String driverNo;

    /**
     * 基础金额
     */
    private Long basePrice;

    /**
     * 附加费金额
     */
    private Long addPrice;

    /**
     * 技术服务费
     */
    private Long serviceFee;

    /**
     * 取消费
     */
    private Long cancelFee;

    /**
     * 投诉扣款金额
     */
    private Long complainPrice;

    /**
     * 司乘虚拟号
     */
    private String virtualPhone;

    /**
     * 调度虚拟号
     */
    private String virtualDispatch;

    /**
     * 乘客电话尾号
     */
    private String phoneEnd;

    /**
     * 服务时长
     */
    private String duration;

    /**
     * 取消人ID
     */
    private String cancelUserId;

    /**
     * 取消人
     */
    private String cancelUserName;

    /**
     * 取消备注
     */
    private String cancelRemark;

    /**
     * 客诉时间
     */
    private Date complainTime;

    /**
     * 客诉类型[ComplainTypeEnum]
     */
    private String complainType;

    /**
     * 客诉备注
     */
    private String complainRemark;

    /**
     * 保险平台[InsurePlatformEnum]
     */
    private String insurePlatform;

    /**
     * 保险编码
     */
    private String insureCode;

    /**
     * 投保时间
     */
    private Date insureTime;

    /**
     * 取消投保时间
     */
    private Date cancelInsureTime;

    /**
     * 取消投保原因[cancelInsureReasonEnum]
     */
    private String cancelInsureReason;

    /**
     * 资金流向[OrderFlowTypeEnum]
     */
    private String flow;

    /**
     * 优惠券额度
     */
    private Long couponGrantQuota;

    /**
     * 是否已评价
     * 0:未评价
     * 1:已评价
     */
    private Integer isRated;
}
