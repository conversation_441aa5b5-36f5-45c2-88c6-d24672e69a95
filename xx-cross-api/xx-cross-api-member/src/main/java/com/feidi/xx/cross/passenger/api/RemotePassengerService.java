package com.feidi.xx.cross.passenger.api;

import com.feidi.xx.common.core.domain.model.XcxLoginUser;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerLoginVo;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerVo;

import java.util.List;

/**
 * 乘客服务
 *
 * <AUTHOR>
 */
public interface RemotePassengerService {

    /**
     * 乘客端登录
     * @param remotePassengerLoginVo
     * @return
     */
    XcxLoginUser login(RemotePassengerLoginVo remotePassengerLoginVo);

    /**
     * 乘客端 OpenId
     * @param passengerId
     * @return
     */
    String getOpenId(Long passengerId);

    /**
     * 乘客信息
     * @param id
     * @return
     */
    RemotePassengerVo getPassengerInfo(Long id);

    /**
     * 批量查询乘客信息
     * @param ids
     * @return
     */
    List<RemotePassengerVo> getPassengerInfo(List<Long> ids);

    /**
     * 注销
     *
     * @param id
     * @return
     */
    boolean signOut(Long id);

    /**
     * 获取乘客信息
     * RemotePassengerLoginVo
     */
    RemotePassengerVo getPassengerInfo(RemotePassengerLoginVo remotePassengerLoginVo);


    Integer getCountByDriverId(Long driverId);

    void bindPassenger(String userType, Long inviteAgentId, Long inviteDriverId, Long passengerId);

    /**
     * 根据手机尾号查询乘客
     *
     * @param phoneEnd 手机尾号
     * @return 乘客信息集合
     */
    List<RemotePassengerVo> queryByPhoneEnd(String phoneEnd);

    /**
     * 根据手机号查询乘客
     *
     * @param phone 手机号
     * @return 乘客信息集合
     */
    RemotePassengerVo queryByPhone(String phone);

    /**
     * 根据创建时间查询
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 乘客信息集合
     */
    List<RemotePassengerVo> getByCreateTime(String startTime, String endTime);
}
