package com.feidi.xx.cross.passenger.api.domain;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 乘客信息
 */
@Data
public class RemotePassengerVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 上级id
     */
    private Long parentId;

    /**
     * openid
     */
    private String openId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String cardNo;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 性别
     */
    private String sex;

    /**
     * 状态
     */
    private String status;

    /**
     * 来源
     */
    private String source;

    /**
     * 邀请码
     */
    private String code;

    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 司机ID
     */
    private Long driverId;


    /**
     * 司机类型
     */
    private String type;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 是否黑名单
     *
     * @see com.feidi.xx.common.core.enums.IsYesEnum
     */
    private String blacklisted;
}
