package com.feidi.xx.cross.passenger.api.domain;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class RemotePassengerLoginVo implements Serializable {


    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 小程序ID
     */
    private String appId;

    /**
     * openId
     */
    private String openId;

    /**
     * Uid
     */
    private String unionId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 来源
     */
    private String source;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 邀请用户ID
     */
    private Long inviteUserId;

    /**
     * 邀请类型
     */
    private String inviteType;

    /**
     * 登录类型
     */
    private String type;

    /**
     * 渠道
     */
    private String channel;

    /**
     * cityCode
     */
    private String cityCode;

}
