package com.feidi.xx.cross.platform.api.mt.domian.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 司机发送消息
 */
@Data
public class RemoteMtMsgSendBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 平台单号
     */
    private String platformNo;

    /**
     * 司机id
     */
    private String driverId;

    /**
     * 我方消息id
     */
    private String msgId;

    /**
     * 消息类型：1文本消息，当前仅支持文本，其他类型正在建设中
     */
    private Integer msgType;

    /**
     * 消息内容。json string格式。
     * 文本：
     * {"text":"内容"}
     */
    private String msgContent;

    /**
     * 	消息发送时间 (毫秒)
     */
    private long createTime;

}
