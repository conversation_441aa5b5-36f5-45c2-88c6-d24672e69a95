package com.feidi.xx.cross.platform.api.mt.domian.bo;

import com.feidi.xx.cross.platform.api.mt.domian.vo.*;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 推送订单状态 - 参数
 *
 * <AUTHOR>
 * @date 2024/9/11
 */
@Data
public class RemoteMtPushStatusBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分配的合作方的标识，每个合作方一个，对应分配给合作方的client_id 必须
     */
    private String channel;

    /**
     * 请求时间，UnixTimestamp单位毫秒 必须
     */
    private Long timestamp;

    /**
     * 签名 必须
     */
    private String sign;

    /**
     * 美团订单号
     */
    private String mtOrderId;

    /**
     * 合作方订单号
     */
    private String partnerOrderId;

    /**
     * 取消原因
     */
    private String reason;

    /**
     * 美团手机号（虚拟号）
     */
    private String mtPhone;

    /**
     * 用于自定义虚拟号的绑定时长, 单位:分钟, 不传递则使用默认值30分钟
     */
    private Integer duration;

    /**
     * 美团消息ID
     */
    private String mtMsgId;

    /**
     * 消息类型：1文本消息，当前仅支持文本，其他类型正在建设中
     */
    private Integer msgType;

    /**
     * msgContent
     */
    private String msgContent;

    /**
     * 消息发送时间 (毫秒)
     */
    private Long createTime;

    /**
     * 0:订单状态变更回调事件
     */
    private Integer eventCode;

    /**
     * 司机选择接到乘客时间 unix时间戳,ms
     */
    private Long pickUpTime;

    /**
     * unix时间戳,ms
     */
    private Long eventTime;

    /**
     * 订单状态，具体值参见订单状态定义,赋值为英文名称
     */
    private String status;

    /**
     * 是否拼成，司机操作到达目的地时需要同步该信息，实际账单金额依赖是否拼成
     */
    private Boolean isPoolSuccess;

    /**
     * 账单，司机到达目的地必传，金额用来对账 - Y
     */
    private RemoteMtBillVo bill;

    /**
     * 接单后车辆信息，接单后取消的场景也必须传，其他情况不传 - Y
     */
    private RemoteMtCarInfoVo carInfo;

    /**
     * 客服等合作方取消订单相关信息，取消状态必传，其他状态不传
     */
    private RemoteMtOrderCancelVo customerServiceInfo;

    /**
     * 接单后司机信息，接单后取消的场景也必须传，其他情况不传
     */
    private RemoteMtDriverInfoVo driverInfo;

    /**
     * 司机当前经纬度
     */
    private RemoteMtDriverLocationVo driverLocation;

}
