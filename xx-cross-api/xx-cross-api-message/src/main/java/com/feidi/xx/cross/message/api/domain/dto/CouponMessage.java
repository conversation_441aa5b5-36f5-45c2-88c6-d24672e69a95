package com.feidi.xx.cross.message.api.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CouponMessage implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 消息类型[CouponMessageTypeEnum]
     */
    private String messageType;

    /**
     * 消息文案
     */
    private String message;
}
