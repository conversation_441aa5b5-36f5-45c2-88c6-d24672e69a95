package com.feidi.xx.cross.message.api.domain;

import com.feidi.xx.cross.message.api.enums.DiyMessageEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * IM消息对象 im_msg
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Data
public class RemoteImMsgInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 平台单号
     */
    private String platformNo;

    /**
     * 我方订单号
     */
    private String orderNo;

    /**
     * 我方订单ID
     */
    private Long orderId;

    /**
     * 第三方消息
     */
    private String thirdMsgId;

    /**
     * 消息类型
     */
    private String msgType;

    /**
     * 消息内容
     */
    private String msgContent;

    /**
     * 媒体
     */
    private String mediaId;

    /**
     * 媒体链接
     */
    private String mediaUrl;

    /**
     * 坐标 经度，纬度
     */
    private String coordinate;

    /**
     * 定位名称
     */
    private String locateName;

    /**
     * 消息地址
     */
    private String address;

    /**
     * 发送人
     */
    private Long sendUserId;

    /**
     * 发送人类型
     */
    private String sendUserType;

    /**
     * 接收人
     */
    private Long receiveUserId;

    /**
     * 接收人类型
     */
    private String receiveUserType;

    /**
     * 消息发送时间戳
     */
    private Long timestamp;

    /**
     * 构建DIY消息
     * @param orderId
     * @param receiveUserType
     * @param receiveUserId
     * @param diyMessage
     * @return
     */
    public static RemoteImMsgInfo buildDiyInfo(Long orderId, String receiveUserType, Long receiveUserId, DiyMessageEnum diyMessage) {
        RemoteImMsgInfo info = new RemoteImMsgInfo();
        info.setOrderId(orderId);
        info.setReceiveUserType(receiveUserType);
        info.setReceiveUserId(receiveUserId);
        // 自定义消息
        info.setMsgType("7");
        info.setMsgContent(diyMessage.getCode());
        return info;
    }

}
