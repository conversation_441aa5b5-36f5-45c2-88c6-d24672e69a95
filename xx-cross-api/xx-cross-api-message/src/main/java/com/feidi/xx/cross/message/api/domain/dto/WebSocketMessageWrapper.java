package com.feidi.xx.cross.message.api.domain.dto;

import com.feidi.xx.common.core.enums.UserTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WebSocketMessageWrapper<T>implements Serializable {

    private static final long serialVersionUID = 1L;
    // 消息类型（ORDER, CHAT, ANNOUNCEMENT）WebSocketTypeEnum
    /**
     * 消息类型
     */
    private String type;
    /**
     * 用户类型（USER, SYSTEM） UserTypeEnum {@link UserTypeEnum}
     */
    private String senderUserType;
    // 发送者 ID
    private String senderId;

    /**
     * 用户类型（USER, SYSTEM） UserTypeEnum {@link UserTypeEnum}
     */
    private String receiverUserType;
    // 接收者 ID（群发可为空）
    private String receiverId;

    // 具体的业务数据
    private T data;

    /**
     * 拼接 userType 和 senderId
     */
    public String getSenderId() {
        if (senderUserType == null || senderId == null) {
            return senderId;
        }
        return senderUserType + ":" + senderId;
    }

    /**
     * 拼接 userType 和 receiverId
     */
    public String getReceiverId() {
        if (receiverUserType == null || receiverId == null) {
            return receiverId;
        }
        return receiverUserType + ":" + receiverId;
    }
}
