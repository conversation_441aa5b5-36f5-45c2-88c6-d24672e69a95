package com.feidi.xx.cross.message.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 自定义消息类型
 */
@Getter
@AllArgsConstructor
public enum DiyMessageEnum {

    ORDER_START("ORDER_START", "乘客行程开始提醒"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String info;

    /**
     * 根据CODE获取枚举
     * @param code
     * @return
     */
    public static DiyMessageEnum getByCode(String code) {
        for (DiyMessageEnum itemEnum : DiyMessageEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取INFO
     * @param code
     * @return
     */
    public static String getInfoByCode(String code) {
        for (DiyMessageEnum itemEnum : DiyMessageEnum.values()) {
            if (itemEnum.getCode().equals(code)) {
                return itemEnum.getInfo();
            }
        }
        return null;
    }
}

