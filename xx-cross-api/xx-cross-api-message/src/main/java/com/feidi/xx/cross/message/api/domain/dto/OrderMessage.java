package com.feidi.xx.cross.message.api.domain.dto;


import com.feidi.xx.common.core.enums.IsYesEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderMessage implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 消息类型[OrderMessageTypeEnum]
     */
    private String messageType;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 昵称 尾号**99
     */
    private String nickname;

    /**
     * 标签（JSON）
     */
    private String label;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 司机收益-展示金额
     */
    private Long driverProfit;


    /**
     * 支付状态[PaymentStatusEnum]
     */
    private String payStatus;
    /**
     * 完单奖励
     */
    private Long driverReward;

    /**
     * 距离起点距离 单位：米
     */
    private Double startDistance;

    /**
     * 距离终点距离 单位：米
     */
    private Double endDistance;

    /**
     * 起点纬度
     */
    private Double startLatitude;

    /**
     * 起点经度
     */
    private Double startLongitude;

    /**
     * 终点纬度
     */
    private Double endLatitude;

    /**
     * 终点经度
     */
    private Double endLongitude;

    /**
     * 产品类型
     */
    private String productCode;
    private String productCodeText;


    /**
     * 乘客数量
     */
    private Integer passengerNum;

    /**
     * 乘客备注
     */
    private String passengerRemark;

    /**
     * 预计出发时间
     */
    private Date earliestTime;

    /**
     * 预计最晚出发时间
     */
    private Date latestTime;

    /**
     * 是否预约
     */
    private String isDue;
    private String isDueText;
    public String getIsDueText() {
        return IsYesEnum.getInfoByCode(isDue);
    }
    /**
     * 地市
     */
    private String startCity;

    /**
     * 区
     */
    private String startDistrict;

    /**
     * 详细地址
     */
    private String startAddr;

    /**
     * 地市
     */
    private String endCity;

    /**
     * 区
     */
    private String endDistrict;

    /**
     * 详细地址
     */
    private String endAddr;

    /**
     * 平台编码
     */
    private String platformCode;


    /**
     * 平台订单号
     */
    private String platformNo;

    /**
     * 里程
     */
    private Integer mileage;


}
