package com.feidi.xx.cross.message.api;


import com.feidi.xx.cross.message.api.domain.RemoteImMsgInfo;

import java.util.List;

/**
 * IM服务远程接口
 *
 * <AUTHOR>
 */
public interface RemoteImService {

    /**
     * 发送消息
     * @param msg
     * @return
     */
    boolean sendMsg2Driver(RemoteImMsgInfo msg);

    /**
     * 获取cid
     * @param userType
     * @param userId
     * @return
     */
    List<String> getCid(String userType, Long... userId);

    /**
     * 改派
     * @param orderId 订单ID
     * @param driverId 司机ID
     * @return
     */
    boolean dispatch(Long orderId, Long driverId);

    /**
     * 获取未读消息数量
     * @param orderId
     * @return
     */
    Integer getUnreadNum(Long orderId);

    /**
     * 发送消息
     * @param msg
     * @return
     */
    Boolean sengMessage(RemoteImMsgInfo msg);

}
