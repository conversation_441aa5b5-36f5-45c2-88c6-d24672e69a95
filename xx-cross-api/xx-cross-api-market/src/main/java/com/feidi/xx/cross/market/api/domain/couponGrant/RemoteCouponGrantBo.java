package com.feidi.xx.cross.market.api.domain.couponGrant;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 优惠卷查询条件
 */
@Data
public class RemoteCouponGrantBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 乘客
     */
    private Long passengerId;

    /**
     * 订单价格
     */
    private Long orderPrice;

    /**
     * 线路id
     */
    private String lineId;

    /**
     * 城市code
     */
    private String cityCode;


    /**
     * 使用状态
     */
    private String usingStatus;


    /**
     * 订单
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 产品code {@link ProductCodeEnum}
     */
    private String productCode;

}
