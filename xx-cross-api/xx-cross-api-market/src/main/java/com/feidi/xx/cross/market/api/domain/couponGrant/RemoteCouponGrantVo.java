package com.feidi.xx.cross.market.api.domain.couponGrant;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 优惠卷领取记录
 */
@Data
public class RemoteCouponGrantVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;
    /**
     * 乘客
     */
    private Long passengerId;

    /**
     * 活动
     */
    private Long activityId;

    /**
     * 代理id
     */
    private Long agentId;

    /**
     * 优惠卷
     */
    private Long couponId;

    /**
     * 优惠卷名
     */
    private String couponName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 线路
     */
    private Long lineId;

    /**
     * 优惠卷编码
     */
    private String couponCode;

    /**
     * 订单
     */
    private Long orderId;

    /**
     * 优惠类型[DiscountTypeEnum]
     */
    private String discountType;

    /**
     * 折扣额度
     */
    private Long quota;

    /**
     * 领取方式[PaidTypeEnum]
     */
    private String paidType;

    /**
     * 领取人类型
     */
    private String paidUserType;

    /**
     * 使用状态[CouponStatusEnum]
     */
    private String usingStatus;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 核销时间
     */
    private Date wipedTime;

    /**
     * 默认标识
     */
    private Boolean defaulted;
}
