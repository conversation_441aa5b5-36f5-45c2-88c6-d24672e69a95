package com.feidi.xx.cross.market.api.domain;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 邀请有奖配置
 * <AUTHOR>
 * @date 2023/3/23 15:08
 */
@Data
public class RemoteMktInviteConfigVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 注解id
     */
    private Long id;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 主体名称
     */
    private String companyName;

    /**
     * 拉新返奖比例
     */
    private BigDecimal recruitRewardRatio;

    /**
     * 奖励司机抽成
     */
    private BigDecimal driverBonusRatio;

    /**
     * 奖励代理商抽成
     */
    private BigDecimal agentBonusRatio;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建时间
     */
    private String createTime;
}
