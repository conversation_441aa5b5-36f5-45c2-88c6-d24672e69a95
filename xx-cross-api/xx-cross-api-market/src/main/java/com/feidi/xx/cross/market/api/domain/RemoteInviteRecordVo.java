package com.feidi.xx.cross.market.api.domain;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 拉新记录视图对象 mkt_invite_record
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RemoteInviteRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private String tenantId;
    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 流水编号
     */
    private String flowNo;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 拉新类型[InviteTypeEnum]
     */
    private String inviteType;

    /**
     * 奖励类型[RewardTypeEnum]
     */
    private String rewardType;

    /**
     * 奖励比例
     */
    private BigDecimal rewardRate;

    /**
     * 奖励金额
     */
    private Long rewardPrice;

}
