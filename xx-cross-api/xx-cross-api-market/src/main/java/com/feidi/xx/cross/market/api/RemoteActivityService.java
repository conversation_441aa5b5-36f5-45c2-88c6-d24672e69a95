package com.feidi.xx.cross.market.api;

import com.feidi.xx.cross.market.api.domain.RemoteActivityBo;
import com.feidi.xx.cross.market.api.domain.RemoteActivityVo;

import java.util.List;
import java.util.Set;

/**
 * 活动服务
 *
 * <AUTHOR>
 */
public interface RemoteActivityService {

    /**
     *  根据活动类型参与活动
     */

    void joinActivity(RemoteActivityBo remoteActivityBo);

    /**
     * 根据活动id查询活动信息
     *
     * @param activityIds 活动id
     * @return 活动信息
     */
    List<RemoteActivityVo> queryByIds(Set<Long> activityIds);
}
