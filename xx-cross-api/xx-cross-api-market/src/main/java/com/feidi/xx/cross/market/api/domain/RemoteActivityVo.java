package com.feidi.xx.cross.market.api.domain;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 活动信息
 */
@Data
public class RemoteActivityVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 代理id
     */
    private Long agentId;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动描述
     */
    private String refer;

    /**
     * 内容
     */
    private String content;

    /**
     * 广告图
     */
    private String adImage;

    /**
     * 活动方式
     */
    private String manner;

    /**
     * 范围
     */
    private String scope;

    /**
     * 活动对象
     */
    private String colony;

    /**
     * 活动类型
     */
    private String type;

    /**
     * 投放渠道
     */
    private String issuingChannel;

    /**
     * 优惠券发放范围
     */
    private String grantScope;
    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 状态
     */
    private String status;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;
}
