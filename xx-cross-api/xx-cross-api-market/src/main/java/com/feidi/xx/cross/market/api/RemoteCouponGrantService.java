package com.feidi.xx.cross.market.api;

import com.feidi.xx.cross.market.api.domain.couponGrant.RemoteCouponGrantBo;
import com.feidi.xx.cross.market.api.domain.couponGrant.RemoteCouponGrantVo;

import java.util.List;

/**
 * 优惠卷领取服务
 *
 * <AUTHOR>
 */
public interface RemoteCouponGrantService {

    /**
     * 获取乘客 有效 可用的优惠卷
     *
     * @param passengerId
     * @return
     */
    List<RemoteCouponGrantVo> getPassengerUsableCoupon(Long passengerId);

    /**
     * 使用优惠卷
     */
    void updateStatus(RemoteCouponGrantBo bo);

    /**
     * 获取可用优惠卷
     */
    List<RemoteCouponGrantVo> getCouponGrant(RemoteCouponGrantBo remoteCouponGrantBo);

    /**
     * 批量获取优惠券发放信息
     */
    List<RemoteCouponGrantVo> getCouponGrantByIds(List<Long> ids);
}
