package com.feidi.xx.cross.market.api;

import com.feidi.xx.cross.market.api.domain.coupon.RemoteCouponVo;

/**
 * 优惠卷服务
 *
 * <AUTHOR>
 */
public interface RemoteCouponService {

    /**
     * 获取优惠卷
     *
     * @param couponId
     * @return
     */
    RemoteCouponVo getCoupon(Long couponId);


    /**
     *
     * 扣减库存
     *
     */
    boolean deductStock(Long couponId, Long passengerId);


}
