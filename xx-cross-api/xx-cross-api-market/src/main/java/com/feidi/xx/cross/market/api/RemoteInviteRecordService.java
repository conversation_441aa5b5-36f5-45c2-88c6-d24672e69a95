package com.feidi.xx.cross.market.api;


import com.feidi.xx.cross.market.api.domain.RemoteInviteRecordVo;

import java.util.List;

/**
 * 邀请有奖配置服务
 *
 * <AUTHOR>
 */
public interface RemoteInviteRecordService {

    /**
     * 新增
     */

    void insertByBo(RemoteInviteRecordVo inviteRecordVo);

    /**
     * 更新返利状态
     * @param userId
     * @param orderId
     * @return
     */
    boolean updateRebateStatus(Long userId, Long orderId);

    /**
     * 根据订单id查询拉新奖励记录
     *
     * @param orderIds 订单id
     * @return 拉新奖励记录集合
     */
    List<RemoteInviteRecordVo> queryByOrderIds(List<Long> orderIds);
}
