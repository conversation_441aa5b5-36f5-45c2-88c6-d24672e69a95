package com.feidi.xx.cross.market.api.domain;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 活动信息
 */
@Data
public class RemoteActivityBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 成功标识
     */
    private Boolean success;

    /**
     * 活动id
     */
    private Long activityId;

}
