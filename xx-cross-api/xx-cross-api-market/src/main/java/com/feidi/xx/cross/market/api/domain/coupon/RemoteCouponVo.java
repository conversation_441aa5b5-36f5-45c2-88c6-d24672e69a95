package com.feidi.xx.cross.market.api.domain.coupon;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 乘客信息
 */
@Data
public class RemoteCouponVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 名称
     */
    private String name;

    /**
     * 折扣方式
     */
    private String discountType;

    /**
     * 额度
     */
    private Long quota;

    /**
     * 总数
     */
    private Long total;

    /**
     * 余量
     */
    private Long margin;

    /**
     * 领取方式
     */
    private String paidType;

    /**
     * 最大领取数量
     */
    private Long maxNum;

    /**
     * 状态
     */
    private String status;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;
}
