package com.feidi.xx.cross.market.api;


import com.feidi.xx.cross.market.api.domain.RemoteMktInviteConfigVo;

import java.util.List;

/**
 * 邀请有奖配置服务
 *
 * <AUTHOR>
 */
public interface RemoteInviteConfigService {

    /**
     * 更新代理商名称根据代理商id
     *
     * @param
     * @return
     */
    void updateAgentName(Long agentId, String agentName);

    /**
     * 根据代理商id获取邀请有奖配置
     */
    RemoteMktInviteConfigVo getInviteConfigByAgentId(Long agentId);

    /**
     * 创建邀请有奖配置
     */
    void createInviteConfig(Long agentId, String agentName);

    /**
     * 获取所有邀请有奖配置
     * @return
     */
    List<RemoteMktInviteConfigVo> getAllConfig();

    /**
     * 批量插入
     * @param configVoList
     * @return
     */
    Boolean addAgent(List<RemoteMktInviteConfigVo> configVoList);

}
