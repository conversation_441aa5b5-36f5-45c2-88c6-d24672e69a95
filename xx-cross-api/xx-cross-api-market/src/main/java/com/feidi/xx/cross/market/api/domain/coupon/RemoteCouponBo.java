package com.feidi.xx.cross.market.api.domain.coupon;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 乘客信息
 */
@Data
public class RemoteCouponBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 名称
     */
    private String name;


    /**
     * 余量
     */
    private Long margin;

    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 状态
     */
    private String status;

}
