package com.feidi.xx.cross.power.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.power.domain.PowAgent;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 代理商列表业务对象 pow_agent
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PowAgent.class, reverseConvertGenerate = false)
public class PowAgentBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 上级代理商ID
     */
    private Long parentId;

    /**
     * 营业执照
     */
    private String license;

    /**
     * 名称
     */
    private String companyName;

    /**
     * 税号[组织代码]
     */
    private String taxNo;
    /**
     * 技术服务费比例
     */
    private BigDecimal technicalFeeRatio;
    /**
     * 客服电话
     */
    private String servicesPhone;
    /**
     * 法人
     */
    private String legalPerson;


    /**
     * 联系人姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 法人身份证号
     */
    private String carNo;

    /**
     * 开户行
     */
    private String bank;

    /**
     * 支行
     */
    private String subBank;

    /**
     * 银行卡号
     */
    private String bankNo;

    /**
     * 省
     */
    private Long provinceId;

    /**
     * 市
     */
    private Long cityId;

    /**
     * 城市编码
     */
    private String cityCode;


    /**
     * 区
     */
    private Long districtId;

    /**
     * 地址
     */
    private String address;

    /**
     * 保证金
     */
    private Long earnest;

    /**
     * 状态[StatusEnum]
     */
    private String status;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 是否展示运营城市的全部订单[IsYesEnum]
     */
    private String showCityOrder;

    /**
     * 佣金比例
     */
    private List<PowAgentRateBo> rates = new ArrayList<>();

}
