package com.feidi.xx.cross.power.controller.driver;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserStatusEnum;
import com.feidi.xx.common.id2name.annotation.Id2NameAspect;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.common.enums.power.DriverIdentityEnum;
import com.feidi.xx.cross.common.enums.power.DrvAuditStatusEnum;
import com.feidi.xx.cross.power.domain.bo.PowDriverBo;
import com.feidi.xx.cross.power.domain.pojo.bo.*;
import com.feidi.xx.cross.power.domain.vo.PowDriverVo;
import com.feidi.xx.cross.power.service.IPowDriverApplyService;
import com.feidi.xx.cross.power.service.IPowDriverService;
import com.feidi.xx.cross.power.validate.*;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 司机端 - 个人中心
 * 前端访问路由地址为:/power/drv
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.DRIVER_ROUTE_PREFIX)
public class DrvDriverController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(DrvDriverController.class);

    private final IPowDriverService powDriverService;

    private final IPowDriverApplyService powDriverApplyService;

    /**
     * 查询司机列表
     */
    @GetMapping("/list")
    public R<TableDataInfo<PowDriverVo>> list(PowDriverBo bo, PageQuery pageQuery) {
        bo.setAgentId(LoginHelper.getAgentId());
        bo.setIdentity(DriverIdentityEnum.CROSS_DRV.getCode());
        bo.setStatus(UserStatusEnum.OK.getCode());
        bo.setExcludedId(LoginHelper.getUserId());
        return R.ok(powDriverService.queryPageList(bo, pageQuery));
    }

    /**
     * 订单转卖司机接单列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @GetMapping("/resell/list")
    public R<TableDataInfo<PowDriverVo>> resellList(PowDriverBo bo, PageQuery pageQuery) {
        bo.setAgentId(LoginHelper.getAgentId());
        bo.setIdentity(DriverIdentityEnum.CROSS_DRV.getCode());
        bo.setStatus(UserStatusEnum.OK.getCode());
        bo.setExcludedId(LoginHelper.getUserId());
        bo.setAuditStatus(DrvAuditStatusEnum.SUCCESS.getCode());
        return R.ok(powDriverService.queryResellPageList(bo, pageQuery));
    }

    /**
     * 司机 信息
     */
    @Log(title = "司机信息", businessType = BusinessType.OTHER)
    @GetMapping("/info")
    public R<PowDriverVo> info() {
        return R.ok(powDriverService.getInfoById(LoginHelper.getUserId()));
    }

    /**
     * 司机申请身份信息上传
     */
    @Log(title = "司机申请身份信息上传", businessType = BusinessType.OTHER)
    @PostMapping("/identify")
    public R<Boolean> idCard(@Validated(ReviewGroup.class) @RequestBody PowDriverIdCardForm bo) {
        bo.setDriverId(LoginHelper.getUserId());
        return R.ok(powDriverApplyService.uploadIdCard(bo));
    }

    /**
     * 司机申请驾驶证信息上传
     */
    @Log(title = "司机申请驾驶证信息上传", businessType = BusinessType.OTHER)
    @PostMapping("/driverLicense")
    public R<Boolean> driving(@Validated(ReviewGroup.class) @RequestBody PowDriverDrivingForm bo) {
        bo.setDriverId(LoginHelper.getUserId());
        return R.ok(powDriverApplyService.uploadDriving(bo));
    }

    /**
     * 司机申请车辆信息上传
     */
    @Log(title = "司机申请车辆信息上传", businessType = BusinessType.OTHER)
    @PostMapping("/car")
    public R<Boolean> car(@Validated(ReviewGroup.class)@RequestBody PowDriverCarForm bo) {
        bo.setDriverId(LoginHelper.getUserId());
        return R.ok(powDriverApplyService.uploadCar(bo));
    }

    /**
     * 司机申请认证图片上传
     */
    @Log(title = "司机申请认证图片上传", businessType = BusinessType.OTHER)
    @GetMapping("/auth/{ossId}")
    public R<Boolean> auth(@PathVariable Long ossId) {
        return R.ok(powDriverApplyService.uploadAuthImg(ossId));
    }

    /**
     * 自营司机代扣协议上传
     */
    @Log(title = "自营司机代扣协议上传", businessType = BusinessType.OTHER)
    @PostMapping("/agreement")
    public R<Boolean> agreement(@Validated(ReviewGroup.class) @RequestBody PowDriverAgreementForm bo) {
        bo.setDriverId(LoginHelper.getUserId());
        return R.ok(powDriverApplyService.uploadAgreement(bo));
    }

    /**
     * 司机申请
     */
    @Log(title = "司机发起申请", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PostMapping("/apply")
    public R<Void> apply(@Validated(ApplyGroup.class) @RequestBody PowDriverBo bo) {
        bo.setId(LoginHelper.getUserId());
        return toAjax(powDriverApplyService.apply(bo));
    }

    /**
     * 获取司机申请结果
     */
    @GetMapping("/getResult")
    public R<PowDriverVo> getResult() {
        return R.ok(powDriverApplyService.getResult());
    }

    /**
     * 司机申请暂存
     *
     * @param bo 暂存数据bo
     * @return
     */
    @Log(title = "司机申请暂存", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PostMapping("/storage")
    public R<Void> storage(@RequestBody PowDriverBo bo) {
        bo.setId(LoginHelper.getUserId());
        return toAjax(powDriverApplyService.storage(bo));
    }

    /**
     * 获取暂存数据
     */
    @Id2NameAspect()
    @PostMapping("/getStorage")
    public R<PowDriverVo> getStorage() {
        Long driverId = LoginHelper.getUserId();
        return R.ok(powDriverApplyService.getStorage(driverId));
    }

    /**
     * 司机 修改手机号
     */
    @Log(title = "司机修改手机号", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PostMapping("/phone")
    public R<Void> phone(@RequestBody @Validated PowDriverPhoneForm bo) {
        return toAjax(powDriverService.setPhone(bo));
    }

    /**
     * 司机 设置/忘记交易密码
     */
    @Log(title = "司机设置/忘记交易密码", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PostMapping("/pwd/reset")
    public R<Void> resetCapitalPassword(@RequestBody @Validated(SmsGroup.class) PowDriverCapitalPwdForm bo) {
        return toAjax(powDriverService.resetCapitalPassword(bo));
    }

    /**
     * 司机 修改交易密码
     */
    @Log(title = "司机修改交易密码", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PostMapping("/pwd/change")
    public R<Void> changeCapitalPassword(@RequestBody @Validated(PwdGroup.class) PowDriverCapitalPwdForm bo) {
        return toAjax(powDriverService.changeCapitalPassword(bo));
    }

    /**
     * 司机 校验交易密码
     */
    @RepeatSubmit()
    @PutMapping("/pwd/verify")
    public R<String> verifyCapitalPassword(@RequestBody @Validated(VerifyGroup.class) PowDriverCapitalPwdForm bo) {
        return R.ok(powDriverService.verifyCapitalPassword(bo));
    }

    /**
     * 司机 修改接单状态（接受派单）
     */
    @Log(title = "司机修改接单状态", businessType = BusinessType.OTHER)
    @PutMapping("/receive")
    public R<Void> receiveStatus() {
        return toAjax(powDriverService.receiveStatus(LoginHelper.getUserId()));
    }

    /**
     * 司机 位置上报
     */
    @PutMapping("/locReport/{longitude}/{latitude}")
    public R locReport(@PathVariable String longitude, @PathVariable String latitude) {
        powDriverService.locReport(LoginHelper.getUserId(), longitude, latitude);
        return R.ok();
    }

    /**
     * 司机邀请码
     */
    @GetMapping("/invite")
    public R<PowDriverVo> generateInviteCode() {
        return R.ok(powDriverService.generateInviteCode());
    }
    /**
     * 司机邀请有奖二维码
     */
    @GetMapping("/invite/prize")
    public R<String> generateInvitePrizeCode() {
        Long userId = LoginHelper.getUserId();
        return R.ok(powDriverService.generateInvitePrizeCode(userId));
    }

}
