package com.feidi.xx.cross.power.service;

import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDrvLoginVo;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverCapitalPwdForm;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverPhoneForm;
import com.feidi.xx.cross.power.domain.vo.PowDriverVo;
import com.feidi.xx.cross.power.domain.bo.PowDriverBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 司机Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IPowDriverService {

    PowDriverVo getInfoById(Long id);

    /**
     * 查询司机
     *
     * @param id 主键
     * @return 司机
     */
    PowDriverVo queryById(Long id);

    /**
     * 分页查询司机列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机分页列表
     */
    TableDataInfo<PowDriverVo> queryPageList(PowDriverBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的司机列表
     *
     * @param bo 查询条件
     * @return 司机列表
     */
    List<PowDriverVo> queryList(PowDriverBo bo);

    /**
     * 新增司机
     *
     * @param bo 司机
     * @return 是否新增成功
     */
    Boolean insertByBo(PowDriverBo bo);



    /**
     * 修改司机
     *
     * @param bo 司机
     * @return 是否修改成功
     */
    Boolean updateByBo(PowDriverBo bo);

    /**
     * 司机登录、注册
     * @param remoteDrvLoginVo
     * @return
     */
    PowDriverVo login(RemoteDrvLoginVo remoteDrvLoginVo);

    /**
     * 封禁司机
     *
     * @param id     待删除的主键
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean sealById(Long id, Boolean isValid);

    /**
     * 解禁司机
     */
    Boolean unsealById(Long id);

    /**
     * 查询调度司机列表
     */
    TableDataInfo<PowDriverVo> dispatch(PowDriverBo bo, PageQuery pageQuery);


    /**
     * 导入司机
     * @param file
     */
    void importDriver(MultipartFile file);

    /**
     * 修改状态
     * @param id
     * @param status
     * @return
     */
    Boolean updateStatus(Long id, String status);

    /**
     *  修改司机手机号
     */
    Boolean setPhone(PowDriverPhoneForm bo);

    /**
     *  设置/忘记司机资金密码
     */
    Boolean resetCapitalPassword(PowDriverCapitalPwdForm bo);

    /**
     *  修改司机资金密码
     */
    Boolean changeCapitalPassword(PowDriverCapitalPwdForm bo);

    /**
     *  验证司机资金密码
     */
    String verifyCapitalPassword(PowDriverCapitalPwdForm bo);

    /**
     *  修改司机接单状态（接受派单）
     * @param id
     * @return
     */
    Boolean receiveStatus(Long id);

    /**
     * 获取 司机 位置
     * @param driverId
     * @return
     */
    String getLoc(Long driverId);

    /**
     *  司机 位置上报
     * @param driverId
     * @param longitude
     * @param latitude
     * @return
     */
    void locReport(Long driverId, String longitude, String latitude);

    /**
     * 司机邀请码
     */
    PowDriverVo generateInviteCode() ;

    /**
     * 修改司机组
     *
     * @param bo 修改司机组参数
     * @return 是否修改成功
     */
    Boolean updateDriverGroup(PowDriverBo bo);

    /**
     * 邀请有奖 生成司机邀请码
     * @param userId
     * @return
     */
    String generateInvitePrizeCode(Long userId);

    /**
     * 订单转卖司机接单列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<PowDriverVo> queryResellPageList(PowDriverBo bo, PageQuery pageQuery);
}
