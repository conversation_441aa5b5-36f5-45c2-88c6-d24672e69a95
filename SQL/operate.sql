
-- 平台 SQL

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330347364354, '平台', '1894258215308713985', '1', 'platform', 'operate/platform/index', 1, 0, 'C', '0', '0', 'operate:platform:list', '#', 103, 1, sysdate(), null, null, '平台菜单');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330347364355, '平台查询', 1894680330347364354, '1',  '#', '', 1, 0, 'F', '0', '0', 'operate:platform:query',        '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330347364356, '平台新增', 1894680330347364354, '2',  '#', '', 1, 0, 'F', '0', '0', 'operate:platform:add',          '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330347364357, '平台修改', 1894680330347364354, '3',  '#', '', 1, 0, 'F', '0', '0', 'operate:platform:edit',         '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330347364358, '平台删除', 1894680330347364354, '4',  '#', '', 1, 0, 'F', '0', '0', 'operate:platform:remove',       '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330347364359, '平台导出', 1894680330347364354, '5',  '#', '', 1, 0, 'F', '0', '0', 'operate:platform:export',       '#', 103, 1, sysdate(), null, null, '');


-- 产品 SQL

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330674520065, '产品', '1894258215308713985', '1', 'product', 'operate/product/index', 1, 0, 'C', '0', '0', 'operate:product:list', '#', 103, 1, sysdate(), null, null, '产品菜单');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330674520066, '产品查询', 1894680330674520065, '1',  '#', '', 1, 0, 'F', '0', '0', 'operate:product:query',        '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330674520067, '产品新增', 1894680330674520065, '2',  '#', '', 1, 0, 'F', '0', '0', 'operate:product:add',          '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330674520068, '产品修改', 1894680330674520065, '3',  '#', '', 1, 0, 'F', '0', '0', 'operate:product:edit',         '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330674520069, '产品删除', 1894680330674520065, '4',  '#', '', 1, 0, 'F', '0', '0', 'operate:product:remove',       '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330674520070, '产品导出', 1894680330674520065, '5',  '#', '', 1, 0, 'F', '0', '0', 'operate:product:export',       '#', 103, 1, sysdate(), null, null, '');


-- 标签 SQL

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680329873408002, '标签', '1894258215308713985', '1', 'label', 'operate/label/index', 1, 0, 'C', '0', '0', 'operate:label:list', '#', 103, 1, sysdate(), null, null, '平台-产品菜单');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680329873408003, '标签查询', 1894680329873408002, '1',  '#', '', 1, 0, 'F', '0', '0', 'operate:label:query',        '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680329873408004, '标签新增', 1894680329873408002, '2',  '#', '', 1, 0, 'F', '0', '0', 'operate:label:add',          '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680329873408005, '标签修改', 1894680329873408002, '3',  '#', '', 1, 0, 'F', '0', '0', 'operate:label:edit',         '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680329873408006, '标签删除', 1894680329873408002, '4',  '#', '', 1, 0, 'F', '0', '0', 'operate:label:remove',       '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680329873408007, '标签导出', 1894680329873408002, '5',  '#', '', 1, 0, 'F', '0', '0', 'operate:label:export',       '#', 103, 1, sysdate(), null, null, '');

-- 线路 SQL

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330112483330, '线路', '1894258215308713985', '1', 'line', 'operate/line/index', 1, 0, 'C', '0', '0', 'operate:line:list', '#', 103, 1, sysdate(), null, null, '线路菜单');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330112483331, '线路查询', 1894680330112483330, '1',  '#', '', 1, 0, 'F', '0', '0', 'operate:line:query',        '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330112483332, '线路新增', 1894680330112483330, '2',  '#', '', 1, 0, 'F', '0', '0', 'operate:line:add',          '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330112483333, '线路修改', 1894680330112483330, '3',  '#', '', 1, 0, 'F', '0', '0', 'operate:line:edit',         '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330112483334, '线路删除', 1894680330112483330, '4',  '#', '', 1, 0, 'F', '0', '0', 'operate:line:remove',       '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330112483335, '线路导出', 1894680330112483330, '5',  '#', '', 1, 0, 'F', '0', '0', 'operate:line:export',       '#', 103, 1, sysdate(), null, null, '');

-- 线路详情 SQL

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330246701057, '线路详情', '1894258215308713985', '1', 'lineDetail', 'operate/lineDetail/index', 1, 0, 'C', '0', '0', 'operate:lineDetail:list', '#', 103, 1, sysdate(), null, null, '线路详情菜单');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330246701058, '线路详情查询', 1894680330246701057, '1',  '#', '', 1, 0, 'F', '0', '0', 'operate:lineDetail:query',        '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330246701059, '线路详情新增', 1894680330246701057, '2',  '#', '', 1, 0, 'F', '0', '0', 'operate:lineDetail:add',          '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330246701060, '线路详情修改', 1894680330246701057, '3',  '#', '', 1, 0, 'F', '0', '0', 'operate:lineDetail:edit',         '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330246701061, '线路详情删除', 1894680330246701057, '4',  '#', '', 1, 0, 'F', '0', '0', 'operate:lineDetail:remove',       '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894680330246701062, '线路详情导出', 1894680330246701057, '5',  '#', '', 1, 0, 'F', '0', '0', 'operate:lineDetail:export',       '#', 103, 1, sysdate(), null, null, '');
