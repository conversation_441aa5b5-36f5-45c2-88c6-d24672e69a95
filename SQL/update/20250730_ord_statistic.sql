
CREATE TABLE `ord_statistic` (
     `id` bigint NOT NULL COMMENT '主键',
     `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户id',
     `platform_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台编号 ',
     `agent_id` bigint DEFAULT '0' COMMENT '代理商id',
     `agent_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '代理商名称',
     `line_id` bigint DEFAULT '0' COMMENT '线路id',
     `line_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '线路名称',
     `statistic_date` date DEFAULT NULL COMMENT '统计时间',
     `volume_number` int DEFAULT '0' COMMENT '总发单量',
     `depart_number` int DEFAULT '0' COMMENT '当日出发订单',
     `accept_number` int DEFAULT '0' COMMENT '接单数',
     `create_number` int DEFAULT '0' COMMENT '待接单数量',
     `receive_number` int DEFAULT '0' COMMENT '待出发数量',
     `pick_number` int DEFAULT '0' COMMENT '前往上车点数量',
     `pick_start_number` int DEFAULT '0' COMMENT '到达上车点数量',
     `ing_number` int DEFAULT '0' COMMENT '行程中数量',
     `finish_number` int DEFAULT '0' COMMENT '已完成数量',
     `cancel_number` int DEFAULT '0' COMMENT '已取消数量',
     `unpay_cancel_number` int DEFAULT '0' COMMENT '未支付取消数量',
     `pay_cancel_number` int DEFAULT '0' COMMENT '已支付取消数量',
     `finish_summary` int DEFAULT '0' COMMENT '完单汇总',
     `receive_finish_rate` decimal(6,2) DEFAULT '0.00' COMMENT '接完率',
     `finish_amount` bigint DEFAULT '0' COMMENT '完单金额',
     `pay_amount` bigint DEFAULT '0' COMMENT '支付金额',
     `pay_number` int DEFAULT '0' COMMENT '支付订单数',
     `complain_number` int DEFAULT '0' COMMENT '客诉订单数',
     `complain_amount` int DEFAULT '0' COMMENT '客诉金额',
     `passenger_register_number` int DEFAULT '0' COMMENT '新增乘客注册数',
     `passenger_visit_number` int DEFAULT '0' COMMENT '乘客访问数量',
     `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
     `create_by` bigint DEFAULT NULL COMMENT '创建者',
     `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
     `update_by` bigint DEFAULT NULL COMMENT '更新者',
     `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
     `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单统计表';