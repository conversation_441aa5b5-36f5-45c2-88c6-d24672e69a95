-- pow_agent 添加 客服电话字段

ALTER TABLE `pow_agent`
    ADD COLUMN `services_phone` varchar(20) NULL COMMENT '客服电话' AFTER `legal_person`,
    ADD COLUMN `technical_fee_ratio` decimal(10, 2) NULL COMMENT '技术服务费' AFTER `services_phone`;


-- opr_city 添加运营时间
ALTER TABLE `opr_city`
    ADD COLUMN `start_time` varchar(20) NULL COMMENT '开始运营时间' AFTER `city_code`,
ADD COLUMN `end_time` varchar(20) NULL COMMENT '结束运营时间' AFTER `start_time`,
    ADD COLUMN `max_number` int NULL COMMENT '最大接单数' AFTER `city_code`;

ALTER TABLE `opr_city`
    MODIFY COLUMN `max_number` int NULL DEFAULT 0 COMMENT '最大接单数' AFTER `city_code`;

-- opr_city_price
CREATE TABLE `opr_city_price`
(
    `id`            bigint NOT NULL COMMENT '主键id',
    `tenant_id`     varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '租户ID',
    `city_id`       bigint                                                        DEFAULT NULL COMMENT '城市表id',
    `city_code`     varchar(10) COLLATE utf8mb4_general_ci                        DEFAULT NULL COMMENT '城市code',
    `price_id`      bigint                                                        DEFAULT NULL COMMENT '价格id',
    `platform_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '平台code[PlatformCodeEnum]',
    `create_dept`   bigint                                                        DEFAULT NULL COMMENT '创建部门',
    `create_by`     bigint                                                        DEFAULT NULL COMMENT '创建者',
    `create_time`   datetime(3) DEFAULT NULL COMMENT '创建时间',
    `update_by`     bigint                                                        DEFAULT NULL COMMENT '更新者',
    `update_time`   datetime(3) DEFAULT NULL COMMENT '更新时间',
    `del_flag`      char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='城市价格关联表';


-- 订单转卖
ALTER TABLE `ord_order`
    ADD COLUMN `resell_driver_id` bigint NULL DEFAULT 0 COMMENT '转卖司机ID' AFTER `channel`,
    ADD COLUMN `resell_agent_id` bigint NULL DEFAULT 0 COMMENT '转卖代理商ID' AFTER `resell_driver_id`,
    ADD COLUMN `resell_driver_price` bigint NULL DEFAULT 0 COMMENT '转卖司机接单金额' AFTER `resell_agent_id`;

ALTER TABLE `pow_driver`
    ADD COLUMN `resell_service_rate` decimal(5, 2) NULL DEFAULT 0.00 COMMENT '订单转卖服务费比例' AFTER `audit_time`;

-- 乘客详情
ALTER TABLE `ord_order_info`
    ADD COLUMN `passenger_detail` varchar(50) NULL COMMENT '乘客详情' AFTER `complain_type`;

ALTER TABLE `ord_order`
    ADD COLUMN `line_direction` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '线路方向[StartEndEnum]' AFTER `line_id`;


INSERT INTO `sys_config` (`config_id`, `tenant_id`, `config_name`, `config_key`, `config_value`, `config_type`,
                          `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `input_type`,
                          `enum_name`)
VALUES (1922477963035906049, '000000', '订单转卖-司机接单金额比例', 'cross.resell.driver.price.rate', '80', 'Y', 103, 1,
        '2025-05-14 10:23:59', 1, '2025-05-14 10:23:59', '单位：百分比，默认80%', NULL, NULL);
INSERT INTO `sys_config` (`config_id`, `tenant_id`, `config_name`, `config_key`, `config_value`, `config_type`,
                          `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `input_type`,
                          `enum_name`)
VALUES (1922463559707889666, '000000', '订单转卖-乘客最小支付金额', 'cross.resell.passenger.min.pay', '1', 'Y', 103, 1,
        '2025-05-14 09:26:45', 1, '2025-05-14 13:44:29', '单位：元', NULL, NULL);


ALTER TABLE `mkt_coupon`
    MODIFY COLUMN `city_code` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市编码' AFTER `line_id`;

ALTER TABLE `mkt_coupon_grant`
    MODIFY COLUMN `city_code` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市编码' AFTER `line_id`;

ALTER TABLE `mkt_activity`
    MODIFY COLUMN `city_code` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市code' AFTER `issuing_channel`;


ALTER TABLE `ord_order`
    ADD COLUMN `resell_rebate_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '订单转卖返利状态[RebateStatusEnum]' AFTER `resell_driver_price`,
    ADD COLUMN `resell_rebate_time` datetime(3) NULL DEFAULT NULL COMMENT '订单转卖返利时间' AFTER `resell_rebate_status`;