SET
FOREIGN_KEY_CHECKS=0;

CREATE TABLE `ord_driver_evaluation`
(
    `id`               bigint                                                    NOT NULL COMMENT '主键',
    `tenant_id`        varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
    `agent_id`         bigint                                                    NOT NULL DEFAULT 0 COMMENT '代理商id',
    `driver_id`        bigint                                                    NOT NULL DEFAULT 0 COMMENT '司机id',
    `driver_phone`     char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '司机手机号',
    `driver_name`      varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '司机姓名',
    `total_score`      decimal(5, 1)                                             NOT NULL DEFAULT 0.0 COMMENT '总评分',
    `evaluation_count` int                                                       NOT NULL DEFAULT 0 COMMENT '评价数量',
    `create_dept`      bigint NULL DEFAULT NULL COMMENT '创建部门',
    `create_by`        bigint NULL DEFAULT NULL COMMENT '创建者',
    `create_time`      datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `update_by`        bigint NULL DEFAULT NULL COMMENT '更新者',
    `update_time`      datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    `del_flag`         char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX              `idx_drvph`(`driver_phone` ASC) USING BTREE,
    INDEX              `idx_drv`(`driver_id` ASC) USING BTREE,
    INDEX              `idx_agid`(`agent_id` ASC) USING BTREE,
    INDEX              `idx_updt`(`update_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '司机-行程评价表' ROW_FORMAT = Dynamic;

CREATE TABLE `ord_evaluations`
(
    `id`              bigint                                                        NOT NULL COMMENT '主键',
    `tenant_id`       varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
    `agent_id`        bigint NULL DEFAULT 0 COMMENT '代理商id',
    `order_id`        bigint                                                        NOT NULL DEFAULT 0 COMMENT '订单ID',
    `order_no`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '订单号',
    `driver_id`       bigint                                                        NOT NULL DEFAULT 0 COMMENT '司机id',
    `passenger_id`    bigint                                                        NOT NULL COMMENT '乘客id',
    `passenger_phone` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci     NOT NULL COMMENT '乘客真实手机号',
    `passenger_name`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
    `rating`          tinyint                                                       NOT NULL COMMENT '评分 (1-5星)',
    `is_anonymous`    tinyint                                                       NOT NULL DEFAULT 0 COMMENT '是否匿名。0：否，1：是',
    `comment`         varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '评价内容',
    `tags`            json NULL COMMENT '选择的标签。',
    `create_dept`     bigint NULL DEFAULT NULL COMMENT '创建部门',
    `create_by`       bigint NULL DEFAULT NULL COMMENT '创建者',
    `create_time`     datetime(3) NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `update_by`       bigint NULL DEFAULT NULL COMMENT '更新者',
    `update_time`     datetime(3) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    `del_flag`        char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX             `order_id`(`order_id` ASC) USING BTREE,
    INDEX             `order_no`(`order_no` ASC) USING BTREE,
    INDEX             `driver_id`(`driver_id` ASC) USING BTREE,
    INDEX             `passenger_id`(`passenger_id` ASC) USING BTREE,
    INDEX             `passenger_phone`(`passenger_phone` ASC) USING BTREE,
    INDEX             `create_time`(`create_time` ASC) USING BTREE,
    INDEX             `idx_tags_str`(`cast(``tags`` as char(255) array)`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '行程评价表' ROW_FORMAT = Dynamic;

ALTER TABLE `ord_order_info`
    ADD COLUMN `is_rated` tinyint NULL DEFAULT 0 COMMENT '是否已评价（0：否，1：是）' AFTER `coupon_grant_quota`;

SET
FOREIGN_KEY_CHECKS=1;


ALTER TABLE `pow_group`
    ADD COLUMN `sync` varchar(1) NULL DEFAULT 'N' COMMENT '是否同步[IsYesEnum]' AFTER `sort`;