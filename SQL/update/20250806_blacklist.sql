CREATE TABLE `mbr_passenger_blacklist`
(
    `id`           bigint                                                   NOT NULL AUTO_INCREMENT COMMENT '主键',
    `passenger_id` bigint                                                   NOT NULL COMMENT '乘客id',
    `blacklisted`  char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Y' COMMENT '是否，Y,N',
    `reason`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci     DEFAULT NULL COMMENT '原因',
    `del_flag`     char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '逻辑删除 0存在 2删除',
    `create_dept`  bigint                                                            DEFAULT NULL COMMENT '创建部门',
    `create_by`    bigint                                                            DEFAULT NULL COMMENT '创建者',
    `create_time`  datetime(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `update_by`    bigint                                                            DEFAULT NULL COMMENT '更新者',
    `update_time`  datetime(3) DEFAULT NULL COMMENT '更新时间',
    `tenant_id`    varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      DEFAULT NULL COMMENT '租户id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='乘客黑名单记录';

ALTER TABLE `test_xx_cross`.`mbr_passenger`
    ADD COLUMN `blacklisted` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'N' COMMENT '黑名单 是否，Y,N' AFTER `terms_agreed_at`;

