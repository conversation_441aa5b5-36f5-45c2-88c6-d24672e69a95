-- 活动表表增加‘type’字段
ALTER TABLE `mkt_activity`
    ADD COLUMN `type` char(1)  NULL DEFAULT 0 COMMENT '活动类型[ActivityTypeEnum]' AFTER `colony`;
ALTER TABLE `ord_order_info`
    ADD COLUMN `coupon_grant_id` bigint NULL DEFAULT 0 COMMENT '优惠券id' AFTER `flow`,
    ADD COLUMN `coupon_grant_quota` bigint NULL DEFAULT 0 COMMENT '优惠券额度' AFTER `coupon_grant_id`;
-- 优惠券表增加‘line_id’,'city_code'字段
ALTER TABLE `mkt_coupon`
    ADD COLUMN `line_id` bigint  NULL DEFAULT 0 COMMENT '线路id' AFTER `agent_id`;
ALTER TABLE `mkt_coupon`
    ADD COLUMN `city_code` varchar(16) DEFAULT NULL COMMENT '城市编码' AFTER `line_id`;
-- 优惠券发放表增加'city_code'字段
ALTER TABLE `mkt_coupon_grant`
    ADD COLUMN `city_code` varchar(16) DEFAULT NULL COMMENT '城市编码' AFTER `line_id`;

-- 订单表添加‘pay_price’字段
ALTER TABLE `ord_order`
    ADD COLUMN `pay_price` bigint UNSIGNED NULL DEFAULT 0 COMMENT '实际支付金额' AFTER `order_price`;

ALTER TABLE `mkt_coupon`
    MODIFY COLUMN `margin` bigint UNSIGNED NULL DEFAULT 0 COMMENT '余量' AFTER `total`;

ALTER TABLE `ord_order`
    ADD COLUMN `channel` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '渠道' AFTER `code`;
