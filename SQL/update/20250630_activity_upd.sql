SET FOREIGN_KEY_CHECKS=0;

ALTER TABLE `mkt_activity` ADD COLUMN `shelves_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '0=下架 1=上架' AFTER `grant_scope`;

ALTER TABLE `mkt_activity` ADD COLUMN `coupon_ids` json NULL COMMENT '活动关联的优惠券ids' AFTER `shelves_status`;

ALTER TABLE `mkt_activity` ADD COLUMN `line_ids` json NULL COMMENT '关联的优惠券的所有线路id' AFTER `coupon_ids`;

SET FOREIGN_KEY_CHECKS=1;