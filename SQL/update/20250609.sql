

ALTER TABLE `fin_bill`
    MODIFY COLUMN `related_order` text NULL COMMENT '相关单号' AFTER `finance_user_id`;

ALTER TABLE `fin_bill_detail`
    ADD COLUMN `related_order` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '相关单号' AFTER `coupon_quota_amount`;

CREATE TABLE `fin_order_fee` (
     `id` bigint NOT NULL COMMENT '主键',
     `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '000000' COMMENT '租户id',
     `finance_date` date DEFAULT NULL COMMENT '账单周期(精确到日)',
     `agent_id` bigint DEFAULT '0' COMMENT '代理商id',
     `agent_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '代理商名称',
     `driver_id` bigint DEFAULT NULL COMMENT '司机id',
     `driver_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '司机姓名',
     `order_id` bigint DEFAULT NULL COMMENT '订单id',
     `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单号',
     `platform_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单渠道',
     `order_price` bigint unsigned DEFAULT '0' COMMENT '订单金额',
     `pay_price` bigint unsigned DEFAULT '0' COMMENT '实际支付金额',
     `finance_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '账单对账类型[FinanceTypeEnum]',
     `create_model` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '下单类型[CreateModelEnum]',
     `proxy_amount` bigint DEFAULT '0' COMMENT '代收款',
     `reward_amount` bigint DEFAULT '0' COMMENT '奖励金额',
     `complain_amount` bigint DEFAULT '0' COMMENT '客诉金额',
     `technical_service_rate` decimal(5,2) DEFAULT '0.00' COMMENT '技术服务比例',
     `technical_service_fee` bigint DEFAULT '0' COMMENT '技术服务费',
     `info_service_rate` decimal(5,2) DEFAULT '0.00' COMMENT '应收信息服务费比例',
     `info_service_fee` bigint DEFAULT '0' COMMENT '应收信息服务费',
     `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
     `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
     `create_by` bigint DEFAULT NULL COMMENT '创建者',
     `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
     `update_by` bigint DEFAULT NULL COMMENT '更新者',
     `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
     `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单应收服务费';


CREATE TABLE `fin_complain_fee` (
    `id` bigint NOT NULL COMMENT '主键',
    `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '000000' COMMENT '租户id',
    `finance_date` date DEFAULT NULL COMMENT '账单周期(精确到日)',
    `fee_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客诉费用类型[ComplainFeeTypeEnum]',
    `agent_id` bigint DEFAULT '0' COMMENT '代理商id',
    `agent_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '代理商名称',
    `driver_id` bigint DEFAULT NULL COMMENT '司机id',
    `driver_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '司机姓名',
    `order_id` bigint DEFAULT NULL COMMENT '订单id',
    `order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单号',
    `platform_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单渠道',
    `order_price` bigint unsigned DEFAULT '0' COMMENT '订单金额',
    `pay_price` bigint unsigned DEFAULT '0' COMMENT '实际支付金额',
    `amount` bigint DEFAULT '0' COMMENT '金额',
    `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
    `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
    `create_by` bigint DEFAULT NULL COMMENT '创建者',
    `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
    `update_by` bigint DEFAULT NULL COMMENT '更新者',
    `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
    `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单客诉费用';



-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1931984039821213698, '订单应收服务费', '1927178601162354689', '1', 'orderFee', 'finance/orderFee/index', 1, 0, 'C', '0', '0', 'finance:orderFee:list', '#', 103, 1, sysdate(), null, null, '订单应收服务费菜单');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1931984039821213699, '订单应收服务费查询', 1931984039821213698, '1',  '#', '', 1, 0, 'F', '0', '0', 'finance:orderFee:query',        '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1931984039821213703, '订单应收服务费导出', 1931984039821213698, '5',  '#', '', 1, 0, 'F', '0', '0', 'finance:orderFee:export',       '#', 103, 1, sysdate(), null, null, '');


-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1931984039422754818, '订单客诉费用', '1927178601162354689', '1', 'complainFee', 'finance/complainFee/index', 1, 0, 'C', '0', '0', 'finance:complainFee:list', '#', 103, 1, sysdate(), null, null, '订单客诉费用菜单');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1931984039422754819, '订单客诉费用查询', 1931984039422754818, '1',  '#', '', 1, 0, 'F', '0', '0', 'finance:complainFee:query',        '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1931984039422754821, '订单客诉费用修改', 1931984039422754818, '3',  '#', '', 1, 0, 'F', '0', '0', 'finance:complainFee:edit',         '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1931984039422754823, '订单客诉费用导出', 1931984039422754818, '5',  '#', '', 1, 0, 'F', '0', '0', 'finance:complainFee:export',       '#', 103, 1, sysdate(), null, null, '');


