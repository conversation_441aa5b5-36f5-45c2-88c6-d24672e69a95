


CREATE TABLE `fin_bill` (
    `id` bigint NOT NULL COMMENT '主键',
    `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '000000' COMMENT '租户id',
    `agent_id` bigint DEFAULT '0' COMMENT '代理商id',
    `agent_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '代理商名称',
    `month` date DEFAULT NULL COMMENT '账单月份',
    `order_number` bigint DEFAULT '0' COMMENT '订单量',
    `order_amount` bigint DEFAULT '0' COMMENT '订单金额',
    `driver_profit` bigint DEFAULT '0' COMMENT '司机收益',
    `proxy_amount` bigint DEFAULT '0' COMMENT '代收款',
    `info_service_fee` bigint DEFAULT '0' COMMENT '应收信息服务费',
    `technical_service_fee` bigint DEFAULT '0' COMMENT '技术服务费',
    `reward_amount` bigint DEFAULT '0' COMMENT '奖励金额',
    `complain_amount` bigint DEFAULT '0' COMMENT '客诉金额',
    `coupon_quota_amount` bigint DEFAULT '0' COMMENT '优惠券使用总金额',
    `other_amount` bigint DEFAULT '0' COMMENT '其他金额',
    `rebate_amount` bigint DEFAULT '0' COMMENT '应结算金额',
    `already_rebate_amount` bigint DEFAULT '0' COMMENT '已结算金额',
    `not_rebate_amount` bigint DEFAULT '0' COMMENT '待结算金额',
    `already_cash_amount` bigint DEFAULT '0' COMMENT '已提现金额',
    `not_cash_amount` bigint DEFAULT '0' COMMENT '可提现金额',
    `finance_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '对账状态[---Status]',
    `finance_time` datetime(3) DEFAULT NULL COMMENT '对账状态确认时间',
    `finance_user_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '对账确认人类型[UserTypeEnum]',
    `finance_user_id` bigint DEFAULT NULL COMMENT '对账确认人id',
    `related_order` json DEFAULT NULL COMMENT '相关单号',
    `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
    `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
    `create_by` bigint DEFAULT NULL COMMENT '创建者',
    `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
    `update_by` bigint DEFAULT NULL COMMENT '更新者',
    `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
    `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单对账表';



CREATE TABLE `fin_bill_detail` (
   `id` bigint NOT NULL COMMENT '主键',
   `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '000000' COMMENT '租户id',
   `agent_id` bigint DEFAULT '0' COMMENT '代理商id',
   `agent_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '代理商名称',
   `driver_id` bigint DEFAULT NULL COMMENT '司机id',
   `driver_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '司机姓名',
   `driver_phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '司机电话',
   `finance_date` date DEFAULT NULL COMMENT '账单周期(精确到日)',
   `platform_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单渠道',
   `finance_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '账单对账类型[----Type]',
   `order_number` bigint DEFAULT '0' COMMENT '总单量',
   `order_amount` bigint DEFAULT '0' COMMENT '订单总金额',
   `order_profit` bigint DEFAULT '0' COMMENT '总收益',
   `other_profit` bigint DEFAULT '0' COMMENT '其他收益',
   `wallet_balance` bigint DEFAULT '0' COMMENT '钱包余额',
   `complain_amount` bigint DEFAULT '0' COMMENT '客诉金额',
   `coupon_quota_amount` bigint DEFAULT '0' COMMENT '优惠券使用总金额',
   `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
   `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
   `create_by` bigint DEFAULT NULL COMMENT '创建者',
   `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
   `update_by` bigint DEFAULT NULL COMMENT '更新者',
   `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
   `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单对账明细表';


CREATE TABLE `fin_remit` (
     `id` bigint NOT NULL COMMENT '主键',
     `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户id',
     `bill_id` bigint DEFAULT '0' COMMENT '订单对账id',
     `remit_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '打款单号',
     `agent_id` bigint DEFAULT '0' COMMENT '代理商id',
     `agent_name` varchar(64) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '代理商名称',
     `remit_account_id` bigint NOT NULL COMMENT '打款账户id',
     `remit_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '打款账号',
     `remit_account_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '打款账户姓名',
     `receive_account_id` bigint NOT NULL COMMENT '打款账户id',
     `receive_account` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '打款账号',
     `receive_account_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '打款账户姓名',
     `account_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '打款类型[AccountTypeEnum]',
     `finance_amount` bigint unsigned NOT NULL COMMENT '账单金额',
     `remit_amount` char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '打款金额',
     `flow_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '打款流水号',
     `trans_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '交易单号',
     `trans_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '交易状态',
     `remit_time` datetime(3) DEFAULT NULL COMMENT '打款时间',
     `remit_user_id` bigint DEFAULT '0' COMMENT '打款人id',
     `remit_user_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '打款人姓名',
     `remit_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '打款类型[RemitTypeEnum]',
     `audit_user_id` bigint DEFAULT '0' COMMENT '审批人id',
     `audit_user_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批人姓名',
     `audit_time` datetime(3) DEFAULT NULL COMMENT '审批时间',
     `finance_date` date DEFAULT NULL COMMENT '账单日期',
     `trans_date` datetime(3) DEFAULT NULL COMMENT '交易时间',
     `trans_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '交易信息',
     `voucher` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '凭证 自动打款保存的是支付订单号，手动打款保存的是打款凭证照片的ossId',
     `app_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'AppId',
     `mch_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商户ID|OpenId',
     `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
     `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
     `create_by` bigint DEFAULT NULL COMMENT '创建者',
     `create_time` datetime(3) DEFAULT NULL COMMENT '创建时间',
     `update_by` bigint DEFAULT NULL COMMENT '更新者',
     `update_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
     `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='打款记录表';


-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1927186482729320450, '订单对账', '1927178601162354689', '1', 'bill', 'finance/bill/index', 1, 0, 'C', '0', '0', 'finance:bill:list', '#', 103, 1, sysdate(), null, null, '订单对账菜单');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1927186482729320451, '订单对账查询', 1927186482729320450, '1',  '#', '', 1, 0, 'F', '0', '0', 'finance:bill:query',        '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1927186482729320455, '订单对账导出', 1927186482729320450, '5',  '#', '', 1, 0, 'F', '0', '0', 'finance:bill:export',       '#', 103, 1, sysdate(), null, null, '');



-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1927186482376998914, '流水明细', '1927178601162354689', '1', 'billDetail', 'finance/billDetail/index', 1, 0, 'C', '0', '0', 'finance:billDetail:list', '#', 103, 1, sysdate(), null, null, '流水明细菜单');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1927186482376998915, '流水明细查询', 1927186482376998914, '1',  '#', '', 1, 0, 'F', '0', '0', 'finance:billDetail:query',        '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1927186482376998919, '流水明细导出', 1927186482376998914, '5',  '#', '', 1, 0, 'F', '0', '0', 'finance:billDetail:export',       '#', 103, 1, sysdate(), null, null, '');



-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1927186483194888194, '打款记录', '1927178601162354689', '1', 'remit', 'finance/remit/index', 1, 0, 'C', '0', '0', 'finance:remit:list', '#', 103, 1, sysdate(), null, null, '打款记录菜单');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1927186483194888195, '打款记录查询', 1927186483194888194, '1',  '#', '', 1, 0, 'F', '0', '0', 'finance:remit:query',        '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1927186483194888199, '打款记录导出', 1927186483194888194, '5',  '#', '', 1, 0, 'F', '0', '0', 'finance:remit:export',       '#', 103, 1, sysdate(), null, null, '');



