-- 司机钱包流水表
CREATE TABLE `fin_drv_wallet_flow`
(
    `id`                bigint                                                       NOT NULL COMMENT '主键',
    `tenant_id`         varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '租户id',
    `order_id`          bigint                                                       NOT NULL COMMENT '订单id',
    `order_no`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联单号',
    `agent_id`          bigint                                                       NOT NULL COMMENT '代理商id',
    `driver_id`         bigint                                                       NOT NULL COMMENT '司机id',
    `order_profit`      bigint                                                        DEFAULT '0' COMMENT '佣金收益',
    `reward_profit`     bigint                                                        DEFAULT '0' COMMENT '奖励收益',
    `resell_profit`     bigint                                                        DEFAULT '0' COMMENT '订单转卖收益',
    `account_amount`    bigint                                                        DEFAULT '0' COMMENT '账户余额(冻结金额+可提现金额)',
    `balance`           bigint                                                        DEFAULT '0' COMMENT '可提现金额',
    `freeze`            bigint                                                        DEFAULT '0' COMMENT '冻结金额',
    `expend`            bigint                                                        DEFAULT '0' COMMENT '累计提现收益',
    `withdrawal_amount` bigint                                                        DEFAULT '0' COMMENT '提现中金额',
    `status`            char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      DEFAULT '0' COMMENT '状态[WalletStatusEnum]',
    `remark`            varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
    `create_dept`       bigint                                                        DEFAULT NULL COMMENT '创建部门',
    `create_by`         bigint                                                        DEFAULT NULL COMMENT '创建者',
    `create_time`       datetime(3) DEFAULT NULL COMMENT '创建时间',
    `update_by`         bigint                                                        DEFAULT NULL COMMENT '更新者',
    `update_time`       datetime(3) DEFAULT NULL COMMENT '更新时间',
    `del_flag`          char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='司机钱包流水表';



CREATE TABLE `fin_approval_log`
(
    `id`           bigint                                 NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `approval_id`  bigint                                 NOT NULL COMMENT '关联账单审批ID',
    `operator_id`  bigint                                 NOT NULL COMMENT '操作人ID',
    `operation`    varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型，如提交、审批通过、驳回等',
    `operate_time` datetime                               NOT NULL COMMENT '操作时间',
    `remark`       varchar(255) COLLATE utf8mb4_general_ci                      DEFAULT NULL COMMENT '操作备注',
    `create_dept`  bigint                                                       DEFAULT NULL COMMENT '创建部门',
    `create_by`    bigint                                                       DEFAULT NULL COMMENT '创建者',
    `create_time`  datetime(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `tenant_id`    varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '000000' COMMENT '租户id',
    PRIMARY KEY (`id`),
    KEY            `ap_id_idx` (`approval_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='账单审批操作记录表';
CREATE TABLE `fin_approval_request_details`
(
    `id`                  bigint                                                    NOT NULL COMMENT '主键ID',
    `approval_request_id` bigint                                                    NOT NULL,
    `driver_id`           bigint                                                    NOT NULL COMMENT '司机id',
    `driver_name`         varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '司机姓名',
    `driver_phone`        char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '司机手机号',
    `relate_orders`       json                                                      NOT NULL COMMENT '关联订单',
    `transaction_type`    tinyint                                                   NOT NULL COMMENT '交易类型，1:人工调账',
    `create_dept`         bigint NULL DEFAULT NULL COMMENT '创建部门',
    `create_by`           bigint NULL DEFAULT NULL COMMENT '创建者',
    `create_time`         datetime(3) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`           bigint NULL DEFAULT NULL COMMENT '更新者',
    `update_time`         datetime(3) NULL DEFAULT NULL COMMENT '更新时间',
    `del_flag`            char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    `account_amount`      bigint                                                    NOT NULL DEFAULT 0 COMMENT '账户余额(冻结金额+可提现金额)',
    `balance`             bigint                                                    NOT NULL DEFAULT 0 COMMENT '可提现金额',
    `freeze`              bigint                                                    NOT NULL DEFAULT 0 COMMENT '冻结金额',
    `tenant_id`           varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户id',
    `agent_id`            bigint                                                    NOT NULL COMMENT '代理id',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '账单审批关联订单表' ROW_FORMAT = Dynamic;

CREATE TABLE `fin_approval_requests`
(
    `id`                 bigint                                  NOT NULL COMMENT '审批ID',
    `name`               varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '审批名称',
    `status`             tinyint                                 NOT NULL              DEFAULT '0' COMMENT '审批状态：0待审批，1已通过，2已驳回，3已撤销',
    `amount`             bigint                                  NOT NULL COMMENT '调账金额，可为负，单位分',
    `approval_time`      datetime                                                      DEFAULT NULL COMMENT '审核时间，审核通过时间',
    `approver_id`        bigint                                                        DEFAULT NULL COMMENT '审批人ID',
    `approver_name`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批人名字',
    `approval_user_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '审核人类型[UserTypeEnum]',
    `create_dept`        bigint                                                        DEFAULT NULL COMMENT '创建部门',
    `create_by`          bigint                                                        DEFAULT NULL COMMENT '创建者',
    `create_time`        datetime(3) DEFAULT NULL COMMENT '创建时间',
    `update_by`          bigint                                                        DEFAULT NULL COMMENT '更新者',
    `update_time`        datetime(3) DEFAULT NULL COMMENT '更新时间',
    `del_flag`           char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci      DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    `tenant_id`          varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  DEFAULT NULL COMMENT '租户id',
    `review_notes`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审核备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='账单审批记录表';