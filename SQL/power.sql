
-- 代理商 SQL

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659606786228226, '代理商列表', '1894262794440105985', '1', 'powerAgent', 'power/powerAgent/index', 1, 0, 'C', '0', '0', 'power:powerAgent:list', '#', 103, 1, sysdate(), null, null, '代理商列表菜单');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659606786228227, '代理商列表查询', 1894659606786228226, '1',  '#', '', 1, 0, 'F', '0', '0', 'power:powerAgent:query',        '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659606786228228, '代理商列表新增', 1894659606786228226, '2',  '#', '', 1, 0, 'F', '0', '0', 'power:powerAgent:add',          '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659606786228229, '代理商列表修改', 1894659606786228226, '3',  '#', '', 1, 0, 'F', '0', '0', 'power:powerAgent:edit',         '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659606786228230, '代理商列表删除', 1894659606786228226, '4',  '#', '', 1, 0, 'F', '0', '0', 'power:powerAgent:remove',       '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659606786228231, '代理商列表导出', 1894659606786228226, '5',  '#', '', 1, 0, 'F', '0', '0', 'power:powerAgent:export',       '#', 103, 1, sysdate(), null, null, '');


-- 代理商 - 账号 SQL

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659607235018754, '代理商用户', '1894262794440105985', '1', 'powerAgentUser', 'power/powerAgentUser/index', 1, 0, 'C', '0', '0', 'power:powerAgentUser:list', '#', 103, 1, sysdate(), null, null, '代理商用户菜单');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659607235018755, '代理商用户查询', 1894659607235018754, '1',  '#', '', 1, 0, 'F', '0', '0', 'power:powerAgentUser:query',        '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659607235018756, '代理商用户新增', 1894659607235018754, '2',  '#', '', 1, 0, 'F', '0', '0', 'power:powerAgentUser:add',          '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659607235018757, '代理商用户修改', 1894659607235018754, '3',  '#', '', 1, 0, 'F', '0', '0', 'power:powerAgentUser:edit',         '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659607235018758, '代理商用户删除', 1894659607235018754, '4',  '#', '', 1, 0, 'F', '0', '0', 'power:powerAgentUser:remove',       '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659607235018759, '代理商用户导出', 1894659607235018754, '5',  '#', '', 1, 0, 'F', '0', '0', 'power:powerAgentUser:export',       '#', 103, 1, sysdate(), null, null, '');


-- 司机 SQL

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659607591534594, '司机', '1894263076783874050', '1', 'powerDriver', 'power/powerDriver/index', 1, 0, 'C', '0', '0', 'power:powerDriver:list', '#', 103, 1, sysdate(), null, null, '司机菜单');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659607591534595, '司机查询', 1894659607591534594, '1',  '#', '', 1, 0, 'F', '0', '0', 'power:powerDriver:query',        '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659607591534596, '司机新增', 1894659607591534594, '2',  '#', '', 1, 0, 'F', '0', '0', 'power:powerDriver:add',          '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659607591534597, '司机修改', 1894659607591534594, '3',  '#', '', 1, 0, 'F', '0', '0', 'power:powerDriver:edit',         '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659607591534598, '司机删除', 1894659607591534594, '4',  '#', '', 1, 0, 'F', '0', '0', 'power:powerDriver:remove',       '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659607591534599, '司机导出', 1894659607591534594, '5',  '#', '', 1, 0, 'F', '0', '0', 'power:powerDriver:export',       '#', 103, 1, sysdate(), null, null, '');


-- 车辆 SQL

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659607461511170, '车辆', '1894262794440105985', '1', 'powerCar', 'power/powerCar/index', 1, 0, 'C', '0', '0', 'power:powerCar:list', '#', 103, 1, sysdate(), null, null, '车辆菜单');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659607461511171, '车辆查询', 1894659607461511170, '1',  '#', '', 1, 0, 'F', '0', '0', 'power:powerCar:query',        '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659607461511172, '车辆新增', 1894659607461511170, '2',  '#', '', 1, 0, 'F', '0', '0', 'power:powerCar:add',          '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659607461511173, '车辆修改', 1894659607461511170, '3',  '#', '', 1, 0, 'F', '0', '0', 'power:powerCar:edit',         '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659607461511174, '车辆删除', 1894659607461511170, '4',  '#', '', 1, 0, 'F', '0', '0', 'power:powerCar:remove',       '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1894659607461511175, '车辆导出', 1894659607461511170, '5',  '#', '', 1, 0, 'F', '0', '0', 'power:powerCar:export',       '#', 103, 1, sysdate(), null, null, '');


-- 司机组 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1898282336907649026, '司机组', '1894257868733374465', '1', 'group', 'power/group/index', 1, 0, 'C', '0', '0', 'power:group:list', '#', 103, 1, sysdate(), null, null, '司机组菜单');

-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1898282336907649027, '司机组查询', 1898282336907649026, '1',  '#', '', 1, 0, 'F', '0', '0', 'power:group:query',        '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1898282336907649028, '司机组新增', 1898282336907649026, '2',  '#', '', 1, 0, 'F', '0', '0', 'power:group:add',          '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1898282336907649029, '司机组修改', 1898282336907649026, '3',  '#', '', 1, 0, 'F', '0', '0', 'power:group:edit',         '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1898282336907649030, '司机组删除', 1898282336907649026, '4',  '#', '', 1, 0, 'F', '0', '0', 'power:group:remove',       '#', 103, 1, sysdate(), null, null, '');

insert into sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_dept, create_by, create_time, update_by, update_time, remark)
values(1898282336907649031, '司机组导出', 1898282336907649026, '5',  '#', '', 1, 0, 'F', '0', '0', 'power:group:export',       '#', 103, 1, sysdate(), null, null, '');
