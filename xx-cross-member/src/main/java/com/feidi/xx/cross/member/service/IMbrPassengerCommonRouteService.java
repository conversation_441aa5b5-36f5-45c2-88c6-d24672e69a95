package com.feidi.xx.cross.member.service;

import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerCommonRouteBo;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerCommonRouteVo;

import java.util.Collection;
import java.util.List;

/**
 * 乘客常用路线Service接口
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
public interface IMbrPassengerCommonRouteService {

    /**
     * 查询乘客常用路线
     *
     * @param id 主键
     * @return 乘客常用路线
     */
    MbrPassengerCommonRouteVo queryById(Long id);

    /**
     * 分页查询乘客常用路线列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客常用路线分页列表
     */
    TableDataInfo<MbrPassengerCommonRouteVo> queryPageList(MbrPassengerCommonRouteBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的乘客常用路线列表
     *
     * @param bo 查询条件
     * @return 乘客常用路线列表
     */
    List<MbrPassengerCommonRouteVo> queryList(MbrPassengerCommonRouteBo bo);

    /**
     * 新增乘客常用路线
     *
     * @param bo 乘客常用路线
     * @return 是否新增成功
     */
    Boolean insertByBo(MbrPassengerCommonRouteBo bo);

    /**
     * 修改乘客常用路线
     *
     * @param bo 乘客常用路线
     * @return 是否修改成功
     */
    Boolean updateByBo(MbrPassengerCommonRouteBo bo);

    /**
     * 校验并批量删除乘客常用路线信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
