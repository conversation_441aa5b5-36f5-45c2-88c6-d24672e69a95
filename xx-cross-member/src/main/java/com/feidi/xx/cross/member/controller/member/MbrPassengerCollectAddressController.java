package com.feidi.xx.cross.member.controller.member;

import java.util.List;

import com.feidi.xx.common.core.cache.system.DistrictCacheVo;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerCollectAddressVo;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerCollectAddressBo;
import com.feidi.xx.cross.member.service.IMbrPassengerCollectAddressService;

/**
 * 后台 - 乘客收藏地址
 * 前端访问路由地址为:/member/passengerCollectAddress
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.MEMBER_ROUTE_PREFIX +"/passengerCollectAddress")
public class MbrPassengerCollectAddressController extends BaseController {

    private final IMbrPassengerCollectAddressService mbrPassengerCollectAddressService;
    private final OprCacheManager oprCacheManager;

    //查询乘客收藏地址列表
    //@SaCheckPermission("member:passengerCollectAddress:list")
    /*@GetMapping("/list")
    public TableDataInfo<MbrPassengerCollectAddressVo> list(MbrPassengerCollectAddressBo bo, PageQuery pageQuery) {
        return mbrPassengerCollectAddressService.queryPageList(bo, pageQuery);
    }*/

    //导出乘客收藏地址列表
   /* @SaCheckPermission("member:passengerCollectAddress:export")
    @Log(title = "乘客收藏地址", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MbrPassengerCollectAddressBo bo, HttpServletResponse response) {
        List<MbrPassengerCollectAddressVo> list = mbrPassengerCollectAddressService.queryList(bo);
        ExcelUtil.exportExcel(list, "乘客收藏地址", MbrPassengerCollectAddressVo.class, response);
    }*/

    /**
     * 获取乘客收藏地址详细信息列表
     *
     */
    //@SaCheckPermission("member:passengerCollectAddress:query")
    @GetMapping()
    public R<List<MbrPassengerCollectAddressVo>> getInfo() {
        return R.ok(mbrPassengerCollectAddressService.queryByPassengerId());
    }

    /**
     * 乘客新增收藏地址
     */
    //@SaCheckPermission("member:passengerCollectAddress:add")
    @Log(title = "乘客收藏地址", businessType = BusinessType.INSERT)
    @RepeatSubmit(message = "不允许重复提交，请稍候再试")
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MbrPassengerCollectAddressBo bo) {
        if(bo.getCityCode() == null){
            DistrictCacheVo vo = oprCacheManager.getDistrictCacheVByAdCode(bo.getAdCode());
            bo.setCityCode(vo != null ? vo.getCityCode() : null);
        }
        return toAjax(mbrPassengerCollectAddressService.insertByBo(bo));
    }

    /**
     * 修改乘客收藏地址
     */
    //@SaCheckPermission("member:passengerCollectAddress:edit")
    @Log(title = "乘客收藏地址", businessType = BusinessType.UPDATE)
    @RepeatSubmit(message = "不允许重复提交，请稍候再试")
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MbrPassengerCollectAddressBo bo) {
        if(bo.getCityCode() == null){
            DistrictCacheVo vo = oprCacheManager.getDistrictCacheVByAdCode(bo.getAdCode());
            bo.setCityCode(vo != null ? vo.getCityCode() : null);
        }
        return toAjax(mbrPassengerCollectAddressService.updateByBo(bo));
    }

    /**
     * 删除乘客收藏地址
     *
     * @param ids 主键串
     */
    //@SaCheckPermission("member:passengerCollectAddress:remove")
    @Log(title = "乘客收藏地址", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mbrPassengerCollectAddressService.deleteWithValidByIds(List.of(ids), true));
    }
}
