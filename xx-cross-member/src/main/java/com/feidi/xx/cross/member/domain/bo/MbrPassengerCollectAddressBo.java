package com.feidi.xx.cross.member.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.member.domain.MbrPassengerCollectAddress;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 乘客收藏地址业务对象 mbr_passenger_collect_address
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MbrPassengerCollectAddress.class, reverseConvertGenerate = false)
public class MbrPassengerCollectAddressBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 乘客ID
     */
    //@NotNull(message = "乘客ID不能为空", groups = {AddGroup.class, EditGroup.class})
    //private Long passengerId;

    /**
     * 省ID
     */
    //@NotNull(message = "省ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long provinceId;

    /**
     * 省
     */
    //@NotBlank(message = "省不能为空", groups = {AddGroup.class, EditGroup.class})
    private String province;

    /**
     * 城市ID
     */
    //@NotNull(message = "城市ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long cityId;

    /**
     * 城市编码
     */
    //@NotBlank(message = "城市编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String cityCode;

    /**
     * 市
     */
    //@NotBlank(message = "市不能为空", groups = {AddGroup.class, EditGroup.class})
    private String city;

    /**
     * 区域ID
     */
    //@NotNull(message = "区域ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long districtId;

    /**
     * 区域编码
     */
    @NotBlank(message = "区域编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String adCode;

    /**
     * 区域
     */
    //@NotBlank(message = "区域不能为空", groups = {AddGroup.class, EditGroup.class})
    private String district;

    /**
     * 短地址
     */
    @NotBlank(message = "短地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String shortAddr;

    /**
     * 地址
     */
    @NotBlank(message = "地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String address;

    /**
     * 经度
     */
    @NotBlank(message = "经度不能为空", groups = {AddGroup.class, EditGroup.class})
    private String longitude;

    /**
     * 纬度
     */
    @NotBlank(message = "纬度不能为空", groups = {AddGroup.class, EditGroup.class})
    private String latitude;

    /**
     * 收藏类型[HOME，COMPANY，OTHER]
     */
    @NotBlank(message = "收藏类型[HOME，COMPANY，OTHER]不能为空", groups = {AddGroup.class, EditGroup.class})
    private String type;


}
