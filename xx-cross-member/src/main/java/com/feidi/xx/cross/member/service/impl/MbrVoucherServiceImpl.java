package com.feidi.xx.cross.member.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.member.domain.MbrVoucher;
import com.feidi.xx.cross.member.domain.bo.MbrVoucherBo;
import com.feidi.xx.cross.member.domain.vo.MbrVoucherVo;
import com.feidi.xx.cross.member.mapper.MbrVoucherMapper;
import com.feidi.xx.cross.member.service.IMbrVoucherService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 乘客凭证Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MbrVoucherServiceImpl implements IMbrVoucherService {

    private final MbrVoucherMapper baseMapper;

    /**
     * 查询乘客凭证
     */
    @Override
    public MbrVoucherVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询乘客凭证列表
     */
    @Override
    public TableDataInfo<MbrVoucherVo> queryPageList(MbrVoucherBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MbrVoucher> lqw = buildQueryWrapper(bo);
        Page<MbrVoucherVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询乘客凭证列表
     */
    @Override
    public List<MbrVoucherVo> queryList(MbrVoucherBo bo) {
        LambdaQueryWrapper<MbrVoucher> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MbrVoucher> buildQueryWrapper(MbrVoucherBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MbrVoucher> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPassengerId() != null, MbrVoucher::getPassengerId, bo.getPassengerId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), MbrVoucher::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getAppId()), MbrVoucher::getAppId, bo.getAppId());
        lqw.eq(StringUtils.isNotBlank(bo.getOpenId()), MbrVoucher::getOpenId, bo.getOpenId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), MbrVoucher::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增乘客凭证
     */
    @Override
    public Boolean insertByBo(MbrVoucherBo bo) {
        MbrVoucher add = MapstructUtils.convert(bo, MbrVoucher.class);
        MbrVoucher exist = exist(bo.getPassengerId(), bo.getType());
        if (exist == null) {
            boolean flag = baseMapper.insert(add) > 0;
            if (flag) {
                bo.setId(add.getId());
            }
            return flag;
        } else {
            // 更新openid之类的新值
            CopyOptions copyOptions = CopyOptions.create();
            copyOptions.setIgnoreNullValue(true);
            BeanUtil.copyProperties(add, exist, copyOptions);
            baseMapper.updateById(exist);
        }
        return true;
    }



    /**
     * 修改乘客凭证
     */
    @Override
    public Boolean updateByBo(MbrVoucherBo bo) {
        MbrVoucher update = MapstructUtils.convert(bo, MbrVoucher.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private boolean validEntityBeforeSave(MbrVoucher entity){
        return true;
    }

    /**
     * 批量删除乘客凭证
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public String getOpenId(Long passengerId) {
        LambdaQueryWrapper<MbrVoucher> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MbrVoucher::getPassengerId, passengerId)
                .isNotNull(MbrVoucher::getOpenId)
                //.orderByDesc(MbrVoucher::getUpdateTime)
                .last("LIMIT 1");
        MbrVoucher MbrVoucher = baseMapper.selectOne(queryWrapper);
        if (MbrVoucher == null) {
            return null;
        }
        return MbrVoucher.getOpenId();
    }

    @Override
    public MbrVoucher exist(Long passengerId, String loginType) {
        MbrVoucher voucher = baseMapper.selectOne(Wrappers.<MbrVoucher>lambdaQuery()
                .eq(MbrVoucher::getPassengerId, passengerId)
                .eq(MbrVoucher::getType, loginType));
        return voucher;
    }

}
