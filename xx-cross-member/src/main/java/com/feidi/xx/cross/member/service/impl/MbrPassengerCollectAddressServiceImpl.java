package com.feidi.xx.cross.member.service.impl;

import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.enums.passenger.PassengerCollectAddressEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerCollectAddressBo;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerCollectAddressVo;
import com.feidi.xx.cross.member.domain.MbrPassengerCollectAddress;
import com.feidi.xx.cross.member.mapper.MbrPassengerCollectAddressMapper;
import com.feidi.xx.cross.member.service.IMbrPassengerCollectAddressService;
import com.feidi.xx.common.core.utils.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 乘客收藏地址Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MbrPassengerCollectAddressServiceImpl implements IMbrPassengerCollectAddressService {

    private final MbrPassengerCollectAddressMapper baseMapper;

    /**
     * 查询乘客收藏地址
     *
     * @param id 主键
     * @return 乘客收藏地址
     */
    @Override
    public MbrPassengerCollectAddressVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询乘客收藏地址列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客收藏地址分页列表
     */
    @Override
    public TableDataInfo<MbrPassengerCollectAddressVo> queryPageList(MbrPassengerCollectAddressBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MbrPassengerCollectAddress> lqw = buildQueryWrapper(bo);
        Page<MbrPassengerCollectAddressVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的乘客收藏地址列表
     *
     * @param bo 查询条件
     * @return 乘客收藏地址列表
     */
    @Override
    public List<MbrPassengerCollectAddressVo> queryList(MbrPassengerCollectAddressBo bo) {
        LambdaQueryWrapper<MbrPassengerCollectAddress> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MbrPassengerCollectAddress> buildQueryWrapper(MbrPassengerCollectAddressBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MbrPassengerCollectAddress> lqw = Wrappers.lambdaQuery();
        //lqw.eq(bo.getPassengerId() != null, MbrPassengerCollectAddress::getPassengerId, bo.getPassengerId());
        lqw.eq(bo.getProvinceId() != null, MbrPassengerCollectAddress::getProvinceId, bo.getProvinceId());
        lqw.eq(StringUtils.isNotBlank(bo.getProvince()), MbrPassengerCollectAddress::getProvince, bo.getProvince());
        lqw.eq(bo.getCityId() != null, MbrPassengerCollectAddress::getCityId, bo.getCityId());
        lqw.eq(StringUtils.isNotBlank(bo.getCityCode()), MbrPassengerCollectAddress::getCityCode, bo.getCityCode());
        lqw.eq(StringUtils.isNotBlank(bo.getCity()), MbrPassengerCollectAddress::getCity, bo.getCity());
        lqw.eq(bo.getDistrictId() != null, MbrPassengerCollectAddress::getDistrictId, bo.getDistrictId());
        lqw.eq(StringUtils.isNotBlank(bo.getAdCode()), MbrPassengerCollectAddress::getAdCode, bo.getAdCode());
        lqw.eq(StringUtils.isNotBlank(bo.getDistrict()), MbrPassengerCollectAddress::getDistrict, bo.getDistrict());
        lqw.eq(StringUtils.isNotBlank(bo.getShortAddr()), MbrPassengerCollectAddress::getShortAddr, bo.getShortAddr());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), MbrPassengerCollectAddress::getAddress, bo.getAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getLongitude()), MbrPassengerCollectAddress::getLongitude, bo.getLongitude());
        lqw.eq(StringUtils.isNotBlank(bo.getLatitude()), MbrPassengerCollectAddress::getLatitude, bo.getLatitude());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), MbrPassengerCollectAddress::getType, bo.getType());
        return lqw;
    }

    /**
     * 新增乘客收藏地址
     *
     * @param bo 乘客收藏地址
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MbrPassengerCollectAddressBo bo) {
        MbrPassengerCollectAddress add = MapstructUtils.convert(bo, MbrPassengerCollectAddress.class);
        validEntityBeforeSave(add, true);
        add.setPassengerId(LoginHelper.getUserId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改乘客收藏地址
     *
     * @param bo 乘客收藏地址
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MbrPassengerCollectAddressBo bo) {
        MbrPassengerCollectAddress update = MapstructUtils.convert(bo, MbrPassengerCollectAddress.class);
        validEntityBeforeSave(update, false);
        update.setPassengerId(LoginHelper.getUserId());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MbrPassengerCollectAddress entity, boolean isInsert) {
        LambdaQueryWrapper<MbrPassengerCollectAddress> lambdaQuery = new LambdaQueryWrapper<>();
        lambdaQuery.eq(MbrPassengerCollectAddress::getPassengerId, LoginHelper.getUserId())
                .eq(MbrPassengerCollectAddress::getType, entity.getType());
        List<MbrPassengerCollectAddressVo> list = baseMapper.selectVoList(lambdaQuery);

        //短地址重复判断
        if(PassengerCollectAddressEnum.Other.getCode().equals(entity.getType())){
            boolean present = list.parallelStream()
                    .map(MbrPassengerCollectAddressVo::getShortAddr)
                    .anyMatch(i -> i.equals(entity.getShortAddr()));
            if (present){
                throw new ServiceException("这个地址你已经收藏过啦～");
            }
        }

        if (isInsert) {
            //数量校验
            if (PassengerCollectAddressEnum.Company.getCode().equals(entity.getType()) ||
                    PassengerCollectAddressEnum.Home.getCode().equals(entity.getType())) {
                if (!list.isEmpty()) {
                    throw new ServiceException("乘客已经收藏过" + list.get(0).getType() + "类型地址");
                }
            }

            if (PassengerCollectAddressEnum.Other.getCode().equals(entity.getType())) {
                if (list.size() >= 10) {
                    throw new ServiceException("最多只能设置10个收藏点");
                }
            }
        }

    }

    /**
     * 校验并批量删除乘客收藏地址信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<MbrPassengerCollectAddressVo> queryByPassengerId() {
        LambdaQueryWrapper<MbrPassengerCollectAddress> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MbrPassengerCollectAddress::getPassengerId, LoginHelper.getUserId())
                .eq(MbrPassengerCollectAddress::getDelFlag, 0)
                .orderByDesc(MbrPassengerCollectAddress::getCreateTime);
        log.info(String.valueOf(LoginHelper.getUserId()));
        return baseMapper.selectVoList(lqw);
    }
}
