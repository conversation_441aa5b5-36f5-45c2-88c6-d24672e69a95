package com.feidi.xx.cross.member.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.member.domain.vo.ExportVo;
import com.feidi.xx.cross.member.domain.bo.MbrVoucherBo;
import com.feidi.xx.cross.member.domain.vo.MbrVoucherVo;
import com.feidi.xx.cross.member.service.IMbrVoucherService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 总后台-乘客凭证
 * 前端访问路由地址为:/member/voucher
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/voucher")
public class VoucherController extends BaseController {

    private final IMbrVoucherService mbrVoucherService;

    /**
     * 查询乘客凭证列表
     */
    @SaCheckPermission("member:voucher:list")
    @GetMapping("/list")
    public TableDataInfo<MbrVoucherVo> list(MbrVoucherBo bo, PageQuery pageQuery) {
        return mbrVoucherService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出乘客凭证列表
     */
    @SaCheckPermission("member:voucher:export")
    @Log(title = "乘客凭证", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void  export(MbrVoucherBo bo, HttpServletResponse response) {
        List<MbrVoucherVo> list = mbrVoucherService.queryList(bo);
        ExcelUtil.exportExcel(list, "乘客凭证", MbrVoucherVo.class, response);
    }

    /**
     * 获取乘客凭证详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("member:voucher:query")
    @GetMapping("/{id}")
    public R<MbrVoucherVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long id) {
        return R.ok(mbrVoucherService.queryById(id));
    }

    /**
     * 新增乘客凭证
     */
    @SaCheckPermission("member:voucher:add")
    @Log(title = "乘客凭证", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MbrVoucherBo bo) {
        return toAjax(mbrVoucherService.insertByBo(bo));
    }

    /**
     * 修改乘客凭证
     */
    @SaCheckPermission("member:voucher:edit")
    @Log(title = "乘客凭证", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MbrVoucherBo bo) {
        return toAjax(mbrVoucherService.updateByBo(bo));
    }

    /**
     * 删除乘客凭证
     *
     * @param ids 主键串
     */
    @SaCheckPermission("member:voucher:remove")
    @Log(title = "乘客凭证", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mbrVoucherService.deleteWithValidByIds(List.of(ids), true));
    }
}
