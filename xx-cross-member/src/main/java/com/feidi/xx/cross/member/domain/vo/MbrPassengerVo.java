package com.feidi.xx.cross.member.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.common.sensitive.annotation.Sensitive;
import com.feidi.xx.common.sensitive.core.SensitiveStrategy;
import com.feidi.xx.cross.common.enums.passenger.PassengerSourceEnum;
import com.feidi.xx.cross.common.enums.passenger.PassengerStatusEnum;
import com.feidi.xx.cross.member.domain.MbrPassenger;
import com.feidi.xx.cross.member.domain.dto.Emergency;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 乘客视图对象 mt_passenger
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MbrPassenger.class)
public class MbrPassengerVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;


    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 主体名称
     */
    private String companyName;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 上级id
     */
    @ExcelProperty(value = "上级id")
    private Long parentId;

    /**
     * openid
     */
    @ExcelProperty(value = "openid")
    private String openId;

    /**
     * 手机号
     */
    @Sensitive(strategy = SensitiveStrategy.PHONE)
    @ExcelProperty(value = "手机号")
    private String phone;

    /**
     * 头像
     */
    @ExcelProperty(value = "头像")
    private String avatar;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String name;

    /**
     * 身份证号
     */
    @ExcelProperty(value = "身份证号")
    private String cardNo;

    /**
     * 生日
     */
    @ExcelProperty(value = "生日")
    private String birthday;

    /**
     * 性别
     */
    @ExcelProperty(value = "性别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_user_sex")
    private String sex;

    /**
     * 状态
     */
    @Enum2Text(enumClass = PassengerStatusEnum.class, fullName = "passengerStatusText")
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "PassengerStatusEnum")
    private String status;

    /**
     * 来源
     */
    @Enum2Text(enumClass = PassengerSourceEnum.class, fullName = "passengerSourceText")
    @ExcelProperty(value = "来源", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "PassengerSourceEnum")
    private String source;

    /**
     * 邀请码
     */
    @ExcelProperty(value = "邀请码")
    private String code;

    /**
     * 手机尾号
     */
    @ExcelProperty(value = "手机尾号")
    private String phoneEnd;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 下单数量
     */
    private Integer orderNum;

    /**
     * 完单数量
     */
    private Integer finishNum;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 紧急联系人
     */
    private Emergency emergency;

    /**
     * 登录地址
     */
    private String loginLocation;

    /**
     * 登录时间
     */
    private Date loginTime;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 是否同意服务告知函：0-不同意，1-同意
     * <p>
     * AgreementStatus
     */
    private String agreedToServiceNotice;

    /**
     * 服务告知函同意时间
     */
    private Date termsAgreedAt;

    /**
     * 是否黑名单
     *
     * @see com.feidi.xx.common.core.enums.IsYesEnum
     */
    private String blacklisted;

}
