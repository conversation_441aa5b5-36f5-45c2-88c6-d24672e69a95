package com.feidi.xx.cross.member.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerBlacklistBo;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerBo;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerVo;
import com.feidi.xx.cross.member.service.IMbrPassengerService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 总后台-乘客管理
 * 前端访问路由地址为:/member/passenger
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/passenger")
public class PassengerController extends BaseController {

    private final IMbrPassengerService mbrPassengerService;

    /**
     * 查询乘客列表
     */
    @Enum2TextAspect
    @SaCheckPermission("member:passenger:list")
    @GetMapping("/list")
    public TableDataInfo<MbrPassengerVo> list(MbrPassengerBo bo, PageQuery pageQuery) {
        return mbrPassengerService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出乘客列表
     */
    @SaCheckPermission("member:passenger:export")
    @Log(title = "乘客", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MbrPassengerBo bo, HttpServletResponse response) {
        List<MbrPassengerVo> list = mbrPassengerService.queryList(bo);
        ExcelUtil.exportExcel(list, "乘客", MbrPassengerVo.class, response);
    }

    /**
     * 获取乘客详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("member:passenger:query")
    @GetMapping("/{id}")
    public R<MbrPassengerVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(mbrPassengerService.queryById(id));
    }

    /**
     * 新增乘客
     */
    @SaCheckPermission("member:passenger:add")
    @Log(title = "乘客", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MbrPassengerBo bo) {
        return toAjax(mbrPassengerService.insertByBo(bo));
    }

    /**
     * 修改乘客
     */
    @SaCheckPermission("member:passenger:edit")
    @Log(title = "乘客", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MbrPassengerBo bo) {
        return toAjax(mbrPassengerService.updateByBo(bo));
    }

    /**
     * 删除乘客
     *
     * @param ids 主键串
     */
    @SaCheckPermission("member:passenger:remove")
    @Log(title = "乘客", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mbrPassengerService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 拉黑乘客
     *
     * @param bo 主键
     */
    @SaCheckPermission("member:passenger:block")
    @Log(title = "乘客", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/block")
    public R<Void> block(@Validated(AddGroup.class)
                         @RequestBody MbrPassengerBlacklistBo bo) {
        Assert.notNull(IsYesEnum.getByCode(bo.getBlacklisted()), " 拉黑状态不能为空");
        return toAjax(mbrPassengerService.block(bo));
    }
}
