package com.feidi.xx.cross.member.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.member.domain.MbrPassenger;
import com.feidi.xx.cross.member.domain.dto.Emergency;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 乘客业务对象 mt_passenger
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MbrPassenger.class, reverseConvertGenerate = false)
public class MbrPassengerBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;


    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 主体名称
     */
    private String companyName;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 司机名称
     */
    private String driverName;
    /**
     * 上级id
     */
    @NotNull(message = "上级id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long parentId;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phone;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String cardNo;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 性别
     */
    @NotBlank(message = "性别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sex;

    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 来源
     */
    @NotBlank(message = "来源不能为空", groups = { AddGroup.class, EditGroup.class })
    private String source;

    /**
     * 邀请码
     */
    @NotBlank(message = "邀请码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String code;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 紧急联系人
     */
    private Emergency emergency;

    /**
     * 最近登录地址
     */
    private String logLocation;

    /**
     * 是否黑名单
     *
     * @see com.feidi.xx.common.core.enums.IsYesEnum
     */
    private String blacklisted;
}
