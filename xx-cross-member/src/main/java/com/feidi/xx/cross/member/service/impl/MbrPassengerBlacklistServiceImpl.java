package com.feidi.xx.cross.member.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.member.domain.MbrPassengerBlacklist;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerBlacklistBo;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerBlacklistVo;
import com.feidi.xx.cross.member.mapper.MbrPassengerBlacklistMapper;
import com.feidi.xx.cross.member.service.IMbrPassengerBlacklistService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 乘客黑名单记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@RequiredArgsConstructor
@Service
public class MbrPassengerBlacklistServiceImpl implements IMbrPassengerBlacklistService {

    private final MbrPassengerBlacklistMapper baseMapper;

    /**
     * 查询乘客黑名单记录
     *
     * @param id 主键
     * @return 乘客黑名单记录
     */
    @Override
    public MbrPassengerBlacklistVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询乘客黑名单记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客黑名单记录分页列表
     */
    @Override
    public TableDataInfo<MbrPassengerBlacklistVo> queryPageList(MbrPassengerBlacklistBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MbrPassengerBlacklist> lqw = buildQueryWrapper(bo);
        Page<MbrPassengerBlacklistVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的乘客黑名单记录列表
     *
     * @param bo 查询条件
     * @return 乘客黑名单记录列表
     */
    @Override
    public List<MbrPassengerBlacklistVo> queryList(MbrPassengerBlacklistBo bo) {
        LambdaQueryWrapper<MbrPassengerBlacklist> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MbrPassengerBlacklist> buildQueryWrapper(MbrPassengerBlacklistBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MbrPassengerBlacklist> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPassengerId() != null, MbrPassengerBlacklist::getPassengerId, bo.getPassengerId());
        lqw.eq(StringUtils.isNotBlank(bo.getBlacklisted()), MbrPassengerBlacklist::getBlacklisted, bo.getBlacklisted());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), MbrPassengerBlacklist::getReason, bo.getReason());
        return lqw;
    }

    /**
     * 新增乘客黑名单记录
     *
     * @param bo 乘客黑名单记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MbrPassengerBlacklistBo bo) {
        MbrPassengerBlacklist add = MapstructUtils.convert(bo, MbrPassengerBlacklist.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改乘客黑名单记录
     *
     * @param bo 乘客黑名单记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MbrPassengerBlacklistBo bo) {
        MbrPassengerBlacklist update = MapstructUtils.convert(bo, MbrPassengerBlacklist.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MbrPassengerBlacklist entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除乘客黑名单记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public MbrPassengerBlacklistVo queryByPassengerId(Long passengerId) {
        var wrapper = Wrappers.<MbrPassengerBlacklist>lambdaQuery()
                .eq(MbrPassengerBlacklist::getBlacklisted, IsYesEnum.YES.getCode())
                .eq(MbrPassengerBlacklist::getPassengerId, passengerId);
        return baseMapper.selectVoOne(wrapper);
    }
}
