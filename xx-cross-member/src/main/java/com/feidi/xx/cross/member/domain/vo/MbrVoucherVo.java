package com.feidi.xx.cross.member.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.cross.member.domain.MbrVoucher;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 乘客凭证视图对象 mt_voucher
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MbrVoucher.class)
public class MbrVoucherVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 乘客id
     */
    @ExcelProperty(value = "乘客id")
    private Long passengerId;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "PtLoginTypeEnum")
    private String type;

    /**
     * app_id
     */
    @ExcelProperty(value = "app_id")
    private String appId;

    /**
     * open_id
     */
    @ExcelProperty(value = "open_id")
    private String openId;

    /**
     * union_id
     */
    @ExcelProperty(value = "union_id")
    private String unionId;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "StatusEnum")
    private String status;


}
