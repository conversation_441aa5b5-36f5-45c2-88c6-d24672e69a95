package com.feidi.xx.cross.member.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.member.domain.dto.Emergency;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 乘客对象 mbr_passenger
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "mbr_passenger", autoResultMap = true)
public class MbrPassenger extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 上级id
     */
    private Long parentId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String cardNo;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 性别（0男 1女 2未知）
     */
    private String sex;

    /**
     * 状态（0正常 1停用 2注销）
     */
    private String status;

    /**
     * 来源（0公众号 1微信小程序）
     */
    private String source;

    /**
     * 邀请码
     */
    private String code;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 紧急联系人
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Emergency emergency;


    /**
     * 是否同意服务告知函：0-不同意，1-同意
     * <p>
     * AgreementStatus
     */
    private String agreedToServiceNotice;

    /**
     * 服务告知函同意时间
     */
    private Date termsAgreedAt;

    /**
     * 是否黑名单
     *
     * @see com.feidi.xx.common.core.enums.IsYesEnum
     */
    private String blacklisted;
}
