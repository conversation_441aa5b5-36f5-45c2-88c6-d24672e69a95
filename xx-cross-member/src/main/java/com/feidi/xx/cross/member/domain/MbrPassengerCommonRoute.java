package com.feidi.xx.cross.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 乘客常用路线对象 mbr_passenger_common_route
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mbr_passenger_common_route")
public class MbrPassengerCommonRoute extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 乘客ID
     */
    private Long passengerId;

    /**
     * 线路名称
     */
    private String routeName;

    /**
     * 起点省ID
     */
    private Long startProvinceId;

    /**
     * 起点省
     */
    private String startProvince;

    /**
     * 起点城市ID
     */
    private Long startCityId;

    /**
     * 起点城市编码
     */
    private String startCityCode;

    /**
     * 起点市
     */
    private String startCity;

    /**
     * 起点区域ID
     */
    private Long startDistrictId;

    /**
     * 起点区域编码
     */
    private String startAdCode;

    /**
     * 起点区域
     */
    private String startDistrict;

    /**
     * 起点短地址
     */
    private String startShortAddr;

    /**
     * 起点地址
     */
    private String startAddress;

    /**
     * 起点经度
     */
    private String startLongitude;

    /**
     * 起点纬度
     */
    private String startLatitude;

    /**
     * 终点省ID
     */
    private Long endProvinceId;

    /**
     * 终点省
     */
    private String endProvince;

    /**
     * 终点城市ID
     */
    private Long endCityId;

    /**
     * 终点城市编码
     */
    private String endCityCode;

    /**
     * 终点市
     */
    private String endCity;

    /**
     * 终点区域ID
     */
    private Long endDistrictId;

    /**
     * 终点区域编码
     */
    private String endAdCode;

    /**
     * 终点区域
     */
    private String endDistrict;

    /**
     * 终点短地址
     */
    private String endShortAddr;

    /**
     * 终点地址
     */
    private String endAddress;

    /**
     * 终点经度
     */
    private String endLongitude;

    /**
     * 终点纬度
     */
    private String endLatitude;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
