package com.feidi.xx.cross.member.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.member.domain.MbrVoucher;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 乘客凭证业务对象 mt_voucher
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MbrVoucher.class, reverseConvertGenerate = false)
public class MbrVoucherBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 乘客id
     */
    @NotNull(message = "乘客id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long passengerId;

    /**
     * 类型
     */
    @NotBlank(message = "类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * app_id
     */
    private String appId;

    /**
     * open_id
     */
    private String openId;

    /**
     * union_id
     */
    private String unionId;

    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
