package com.feidi.xx.cross.member.domain.vo;

import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.cross.common.enums.passenger.PassengerCollectAddressEnum;
import com.feidi.xx.cross.common.enums.passenger.PassengerSourceEnum;
import com.feidi.xx.cross.member.domain.MbrPassengerCollectAddress;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 乘客收藏地址视图对象 mbr_passenger_collect_address
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MbrPassengerCollectAddress.class)
public class MbrPassengerCollectAddressVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 乘客ID
     */
    @ExcelProperty(value = "乘客ID")
    private Long passengerId;

    /**
     * 省ID
     */
    @ExcelProperty(value = "省ID")
    private Long provinceId;

    /**
     * 省
     */
    @ExcelProperty(value = "省")
    private String province;

    /**
     * 城市ID
     */
    @ExcelProperty(value = "城市ID")
    private Long cityId;

    /**
     * 城市编码
     */
    @ExcelProperty(value = "城市编码")
    private String cityCode;

    /**
     * 市
     */
    @ExcelProperty(value = "市")
    private String city;

    /**
     * 区域ID
     */
    @ExcelProperty(value = "区域ID")
    private Long districtId;

    /**
     * 区域编码
     */
    @ExcelProperty(value = "区域编码")
    private String adCode;

    /**
     * 区域
     */
    @ExcelProperty(value = "区域")
    private String district;

    /**
     * 短地址
     */
    @ExcelProperty(value = "短地址")
    private String shortAddr;

    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    private String address;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private String longitude;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private String latitude;

    @Enum2Text(enumClass = PassengerCollectAddressEnum.class, fullName = "passengerCollectAddressText")
    @ExcelProperty(value = "收藏类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "PassengerCollectAddressEnum")
    private String type;


}
