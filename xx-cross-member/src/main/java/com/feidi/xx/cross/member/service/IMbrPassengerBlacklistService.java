package com.feidi.xx.cross.member.service;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerBlacklistBo;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerBlacklistVo;
import jakarta.validation.constraints.NotNull;

import java.util.Collection;
import java.util.List;

/**
 * 乘客黑名单记录Service接口
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
public interface IMbrPassengerBlacklistService {

    /**
     * 查询乘客黑名单记录
     *
     * @param id 主键
     * @return 乘客黑名单记录
     */
    MbrPassengerBlacklistVo queryById(Long id);

    /**
     * 分页查询乘客黑名单记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客黑名单记录分页列表
     */
    TableDataInfo<MbrPassengerBlacklistVo> queryPageList(MbrPassengerBlacklistBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的乘客黑名单记录列表
     *
     * @param bo 查询条件
     * @return 乘客黑名单记录列表
     */
    List<MbrPassengerBlacklistVo> queryList(MbrPassengerBlacklistBo bo);

    /**
     * 新增乘客黑名单记录
     *
     * @param bo 乘客黑名单记录
     * @return 是否新增成功
     */
    Boolean insertByBo(MbrPassengerBlacklistBo bo);

    /**
     * 修改乘客黑名单记录
     *
     * @param bo 乘客黑名单记录
     * @return 是否修改成功
     */
    Boolean updateByBo(MbrPassengerBlacklistBo bo);

    /**
     * 校验并批量删除乘客黑名单记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据乘客ID查询乘客黑名单记录
     *
     * @param passengerId 乘客ID
     * @return 乘客黑名单记录
     */
    MbrPassengerBlacklistVo queryByPassengerId(Long passengerId);
}
