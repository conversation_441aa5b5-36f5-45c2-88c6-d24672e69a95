package com.feidi.xx.cross.member.service;

import com.feidi.xx.cross.member.domain.vo.MbrPassengerCollectAddressVo;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerCollectAddressBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import jakarta.validation.constraints.NotNull;

import java.util.Collection;
import java.util.List;

/**
 * 乘客收藏地址Service接口
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface IMbrPassengerCollectAddressService {

    /**
     * 查询乘客收藏地址
     *
     * @param id 主键
     * @return 乘客收藏地址
     */
    MbrPassengerCollectAddressVo queryById(Long id);

    /**
     * 分页查询乘客收藏地址列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客收藏地址分页列表
     */
    TableDataInfo<MbrPassengerCollectAddressVo> queryPageList(MbrPassengerCollectAddressBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的乘客收藏地址列表
     *
     * @param bo 查询条件
     * @return 乘客收藏地址列表
     */
    List<MbrPassengerCollectAddressVo> queryList(MbrPassengerCollectAddressBo bo);

    /**
     * 新增乘客收藏地址
     *
     * @param bo 乘客收藏地址
     * @return 是否新增成功
     */
    Boolean insertByBo(MbrPassengerCollectAddressBo bo);

    /**
     * 修改乘客收藏地址
     *
     * @param bo 乘客收藏地址
     * @return 是否修改成功
     */
    Boolean updateByBo(MbrPassengerCollectAddressBo bo);

    /**
     * 校验并批量删除乘客收藏地址信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<MbrPassengerCollectAddressVo> queryByPassengerId();
}
