package com.feidi.xx.cross.member.controller.driver;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerBo;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerVo;
import com.feidi.xx.cross.member.service.IMbrPassengerService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 司机端-乘客信息
 * 前端访问路由地址为:/member/passenger
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.DRIVER_ROUTE_PREFIX + "/passenger")
public class DrvPassengerController extends BaseController {

    private final IMbrPassengerService mbrPassengerService;


    /**
     * 查询乘客列表
     */
    @GetMapping("/list")
    public TableDataInfo<MbrPassengerVo> list(MbrPassengerBo bo, PageQuery pageQuery) {
        bo.setDriverId(LoginHelper.getUserId());
        return mbrPassengerService.queryPageList(bo, pageQuery);
    }
}
