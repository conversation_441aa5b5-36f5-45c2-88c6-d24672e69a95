package com.feidi.xx.cross.member.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.member.domain.MbrPassengerBlacklist;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 乘客黑名单记录视图对象 mbr_passenger_blacklist
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MbrPassengerBlacklist.class)
public class MbrPassengerBlacklistVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 乘客id
     */
    @ExcelProperty(value = "乘客id")
    private Long passengerId;

    /**
     * 是否，Y,N
     */
    @ExcelProperty(value = "是否，Y,N")
    private String blacklisted;

    /**
     *
     */
    @ExcelProperty(value = "原因")
    private String reason;

    /**
     * 创建部门
     */
    private Long createDept;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 租户编号
     */
    private String tenantId;

}
