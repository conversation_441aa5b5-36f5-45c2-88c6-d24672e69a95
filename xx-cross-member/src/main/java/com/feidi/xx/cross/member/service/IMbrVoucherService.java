package com.feidi.xx.cross.member.service;

import com.feidi.xx.common.core.enums.PtLoginTypeEnum;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.member.domain.MbrVoucher;
import com.feidi.xx.cross.member.domain.vo.MbrVoucherVo;
import com.feidi.xx.cross.member.domain.bo.MbrVoucherBo;

import java.util.Collection;
import java.util.List;

/**
 * 乘客凭证Service接口
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
public interface IMbrVoucherService {

    /**
     * 查询乘客凭证
     */
    MbrVoucherVo queryById(Long id);

    /**
     * 查询乘客凭证列表
     */
    TableDataInfo<MbrVoucherVo> queryPageList(MbrVoucherBo bo, PageQuery pageQuery);

    /**
     * 查询乘客凭证列表
     */
    List<MbrVoucherVo> queryList(MbrVoucherBo bo);

    /**
     * 新增乘客凭证
     */
    Boolean insertByBo(MbrVoucherBo bo);

    /**
     * 修改乘客凭证
     */
    Boolean updateByBo(MbrVoucherBo bo);

    /**
     * 校验并批量删除乘客凭证信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     *  获取用户的OpenId
     * @param passengerId
     * @return
     */
    String getOpenId(Long passengerId);

    /**
     * 是否存在
     * @param passengerId
     * @param loginType
     * @return
     */
    MbrVoucher exist(Long passengerId, String loginType);
}
