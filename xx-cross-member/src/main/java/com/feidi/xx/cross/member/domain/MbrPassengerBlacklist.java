package com.feidi.xx.cross.member.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 乘客黑名单记录对象 mbr_passenger_blacklist
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mbr_passenger_blacklist")
public class MbrPassengerBlacklist extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 是否，Y,N
     */
    private String blacklisted;

    /**
     *
     */
    private String reason;

    /**
     * 逻辑删除 0存在 2删除
     */
    @TableLogic
    private String delFlag;


}
