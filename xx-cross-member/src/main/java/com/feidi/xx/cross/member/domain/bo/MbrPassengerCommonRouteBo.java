package com.feidi.xx.cross.member.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.member.domain.MbrPassengerCommonRoute;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 乘客常用路线业务对象 mbr_passenger_common_route
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MbrPassengerCommonRoute.class, reverseConvertGenerate = false)
public class MbrPassengerCommonRouteBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 乘客ID
     */
    //@NotNull(message = "乘客ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long passengerId;

    /**
     * 线路名称
     */
    @NotBlank(message = "线路名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String routeName;

    /**
     * 起点省ID
     */
    //@NotNull(message = "起点省ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long startProvinceId;

    /**
     * 起点省
     */
    //@NotBlank(message = "起点省不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startProvince;

    /**
     * 起点城市ID
     */
    //@NotNull(message = "起点城市ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long startCityId;

    /**
     * 起点城市编码
     */
    //@NotBlank(message = "起点城市编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startCityCode;

    /**
     * 起点市
     */
    //@NotBlank(message = "起点市不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startCity;

    /**
     * 起点区域ID
     */
    //@NotNull(message = "起点区域ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long startDistrictId;

    /**
     * 起点区域编码
     */
    @NotBlank(message = "起点区域编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startAdCode;

    /**
     * 起点区域
     */
    //@NotBlank(message = "起点区域不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startDistrict;

    /**
     * 起点短地址
     */
    @NotBlank(message = "起点短地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startShortAddr;

    /**
     * 起点地址
     */
    @NotBlank(message = "起点地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startAddress;

    /**
     * 起点经度
     */
    @NotBlank(message = "起点经度不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startLongitude;

    /**
     * 起点纬度
     */
    @NotBlank(message = "起点纬度不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startLatitude;

    /**
     * 终点省ID
     */
    //@NotNull(message = "终点省ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long endProvinceId;

    /**
     * 终点省
     */
    //@NotBlank(message = "终点省不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endProvince;

    /**
     * 终点城市ID
     */
    //@NotNull(message = "终点城市ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long endCityId;

    /**
     * 终点城市编码
     */
    //@NotBlank(message = "终点城市编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endCityCode;

    /**
     * 终点市
     */
    //@NotBlank(message = "终点市不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endCity;

    /**
     * 终点区域ID
     */
    //@NotNull(message = "终点区域ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long endDistrictId;

    /**
     * 终点区域编码
     */
    @NotBlank(message = "终点区域编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endAdCode;

    /**
     * 终点区域
     */
    //@NotBlank(message = "终点区域不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endDistrict;

    /**
     * 终点短地址
     */
    @NotBlank(message = "终点短地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endShortAddr;

    /**
     * 终点地址
     */
    @NotBlank(message = "终点地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endAddress;

    /**
     * 终点经度
     */
    @NotBlank(message = "终点经度不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endLongitude;

    /**
     * 终点纬度
     */
    @NotBlank(message = "终点纬度不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endLatitude;


}
