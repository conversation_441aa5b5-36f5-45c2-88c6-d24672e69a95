package com.feidi.xx.cross.member.domain.bo;

import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.member.domain.MbrPassengerBlacklist;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 乘客黑名单记录业务对象 mbr_passenger_blacklist
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MbrPassengerBlacklist.class, reverseConvertGenerate = false)
public class MbrPassengerBlacklistBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 乘客id
     */
    @NotNull(message = "乘客id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long passengerId;

    /**
     * 是否，Y,N
     */
    @NotNull(message = "是否，Y,N不能为空", groups = {AddGroup.class, EditGroup.class})
    private String blacklisted = IsYesEnum.YES.getCode();

    /**
     * 原因
     */
    private String reason;


}
