package com.feidi.xx.cross.member.controller.member;

import java.util.List;

import com.feidi.xx.common.core.cache.system.DistrictCacheVo;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerCommonRouteBo;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerCommonRouteVo;
import com.feidi.xx.cross.member.domain.vo.MbrVoucherVo;
import com.feidi.xx.cross.member.service.IMbrPassengerCommonRouteService;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 乘客常用路线
 * 前端访问路由地址为:/settle/passengerCommonRoute
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.MEMBER_ROUTE_PREFIX + "/passengerCommonRoute")
public class MbrPassengerCommonRouteController extends BaseController {

    private final IMbrPassengerCommonRouteService mbrPassengerCommonRouteService;
    private final OprCacheManager oprCacheManager;

    /**
     * 查询乘客常用路线列表
     */
    /*@SaCheckPermission("settle:passengerCommonRoute:list")
    @GetMapping("/list")
    public TableDataInfo<MbrPassengerCommonRouteVo> list(MbrPassengerCommonRouteBo bo, PageQuery pageQuery) {
        return mbrPassengerCommonRouteService.queryPageList(bo, pageQuery);
    }*/

    /**
     * 导出乘客常用路线列表
     */
    /*@SaCheckPermission("settle:passengerCommonRoute:export")
    @Log(title = "乘客常用路线", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MbrPassengerCommonRouteBo bo, HttpServletResponse response) {
        List<MbrPassengerCommonRouteVo> list = mbrPassengerCommonRouteService.queryList(bo);
        ExcelUtil.exportExcel(list, "乘客常用路线", MbrPassengerCommonRouteVo.class, response);
    }*/

    /**
     * 获取乘客常用路线列表
     *
     */
    //@SaCheckPermission("settle:passengerCommonRoute:query")
    @GetMapping()
    public R<List<MbrPassengerCommonRouteVo>> getInfo() {
        MbrPassengerCommonRouteBo bo = new MbrPassengerCommonRouteBo();
        bo.setPassengerId(LoginHelper.getUserId());
        return R.ok(mbrPassengerCommonRouteService.queryList(bo));
    }

    /**
     * 获取乘客常用路线详细信息
     * @param id 主键
     */
    //@SaCheckPermission("settle:passengerCommonRoute:query")
    @GetMapping("/{id}")
    public R<MbrPassengerCommonRouteVo> getInfo2(@NotNull(message = "主键不能为空")
                                                          @PathVariable Long id) {
        return R.ok(mbrPassengerCommonRouteService.queryById(id));
    }

    /**
     * 新增乘客常用路线
     */
    //@SaCheckPermission("settle:passengerCommonRoute:add")
    @Log(title = "乘客常用路线", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MbrPassengerCommonRouteBo bo) {
        bo.setPassengerId(LoginHelper.getUserId());
        if(bo.getStartCityCode() == null){
            DistrictCacheVo vo = oprCacheManager.getDistrictCacheVByAdCode(bo.getStartAdCode());
            bo.setStartCityCode(vo != null ? vo.getCityCode() : null);
        }
        if(bo.getEndCityCode() == null){
            DistrictCacheVo vo = oprCacheManager.getDistrictCacheVByAdCode(bo.getEndAdCode());
            bo.setEndCityCode(vo != null ? vo.getCityCode() : null);
        }
        return toAjax(mbrPassengerCommonRouteService.insertByBo(bo));
    }

    /**
     * 修改乘客常用路线
     */
    //@SaCheckPermission("settle:passengerCommonRoute:edit")
    @Log(title = "乘客常用路线", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MbrPassengerCommonRouteBo bo) {
        if(bo.getStartCityCode() == null){
            DistrictCacheVo vo = oprCacheManager.getDistrictCacheVByAdCode(bo.getStartAdCode());
            bo.setStartCityCode(vo != null ? vo.getCityCode() : null);
        }
        if(bo.getEndCityCode() == null){
            DistrictCacheVo vo = oprCacheManager.getDistrictCacheVByAdCode(bo.getEndAdCode());
            bo.setEndCityCode(vo != null ? vo.getCityCode() : null);
        }
        return toAjax(mbrPassengerCommonRouteService.updateByBo(bo));
    }

    /**
     * 删除乘客常用路线
     *
     * @param ids 主键串
     */
    //@SaCheckPermission("settle:passengerCommonRoute:remove")
    @Log(title = "乘客常用路线", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mbrPassengerCommonRouteService.deleteWithValidByIds(List.of(ids), true));
    }
}
