package com.feidi.xx.cross.member.controller.member;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerBo;
import com.feidi.xx.cross.member.domain.dto.Emergency;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerVo;
import com.feidi.xx.cross.member.service.IMbrPassengerService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 会员-乘客信息
 * 前端访问路由地址为:/member/passenger
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.MEMBER_ROUTE_PREFIX + "/passenger")
public class MbrPassengerController extends BaseController {

    private final IMbrPassengerService mbrPassengerService;

    /**
     * 获取乘客详细信息
     */
    @GetMapping()
    public R<MbrPassengerVo> getInfo() {
        return R.ok(mbrPassengerService.queryById(LoginHelper.getUserId()));
    }

    /**
     * 修改乘客
     */
    @Log(title = "乘客", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MbrPassengerBo bo) {
        return toAjax(mbrPassengerService.updateByBo(bo));
    }

    /**
     * 根据司机code查询司机id
     */
    @Log(title = "乘客", businessType = BusinessType.OTHER)
    @GetMapping("/driver/{driverCode}")
    public R<Long> getDriverIdByDriverCode(@PathVariable String driverCode) {

        return R.ok(mbrPassengerService.getDriverIdByDriverCode(driverCode));
    }

    /**
     * 添加/修改紧急联系人
     */
    @Log(title = "乘客", businessType = BusinessType.UPDATE)
    @PostMapping("/emergency")
    public R<Void> emergency(@RequestBody MbrPassengerBo bo) {
        bo.setId(LoginHelper.getUserId());
        return toAjax(mbrPassengerService.emergency(bo));
    }

    /**
     * 查询紧急联系人
     */
    @GetMapping("/emergency")
    public R<Emergency> getEmergency() {
        return R.ok(mbrPassengerService.getEmergency(LoginHelper.getUserId()));
    }


    /**
     * 查询乘客邀请列表
     *
     * @return
     */
    @GetMapping("/queryInviteList")
    public R<List<MbrPassengerVo>> queryInviteList() {
        return R.ok(mbrPassengerService.queryInviteList());
    }

    /**
     * 同意服务告知函
     */
    @PostMapping("/agree-terms")
    @Log(title = "乘客", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    public R<Void> agreedServiceNotice() {
        mbrPassengerService.agreedServiceNotice(LoginHelper.getUserId());
        return R.ok();
    }
}
