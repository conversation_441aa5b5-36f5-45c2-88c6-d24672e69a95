package com.feidi.xx.cross.member.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 乘客收藏地址对象 mbr_passenger_collect_address
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mbr_passenger_collect_address")
public class MbrPassengerCollectAddress extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 乘客ID
     */
    private Long passengerId;

    /**
     * 省ID
     */
    private Long provinceId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 市
     */
    private String city;

    /**
     * 区域ID
     */
    private Long districtId;

    /**
     * 区域编码
     */
    private String adCode;

    /**
     * 区域
     */
    private String district;

    /**
     * 短地址
     */
    private String shortAddr;

    /**
     * 地址
     */
    private String address;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 收藏类型[HOME，COMPANY，OTHER]
     */
    private String type;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
