package com.feidi.xx.cross.member.service.impl;

import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.member.domain.MbrPassengerCommonRoute;
import com.feidi.xx.cross.member.domain.bo.MbrPassengerCommonRouteBo;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerCommonRouteVo;
import com.feidi.xx.cross.member.mapper.MbrPassengerCommonRouteMapper;
import com.feidi.xx.cross.member.service.IMbrPassengerCommonRouteService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.feidi.xx.common.core.utils.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 乘客常用路线Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@RequiredArgsConstructor
@Service
public class MbrPassengerCommonRouteServiceImpl implements IMbrPassengerCommonRouteService {

    private final MbrPassengerCommonRouteMapper baseMapper;

    /**
     * 查询乘客常用路线
     *
     * @param id 主键
     * @return 乘客常用路线
     */
    @Override
    public MbrPassengerCommonRouteVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询乘客常用路线列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客常用路线分页列表
     */
    @Override
    public TableDataInfo<MbrPassengerCommonRouteVo> queryPageList(MbrPassengerCommonRouteBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MbrPassengerCommonRoute> lqw = buildQueryWrapper(bo);
        Page<MbrPassengerCommonRouteVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的乘客常用路线列表
     *
     * @param bo 查询条件
     * @return 乘客常用路线列表
     */
    @Override
    public List<MbrPassengerCommonRouteVo> queryList(MbrPassengerCommonRouteBo bo) {
        LambdaQueryWrapper<MbrPassengerCommonRoute> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MbrPassengerCommonRoute> buildQueryWrapper(MbrPassengerCommonRouteBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MbrPassengerCommonRoute> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPassengerId() != null, MbrPassengerCommonRoute::getPassengerId, bo.getPassengerId());
        lqw.like(StringUtils.isNotBlank(bo.getRouteName()), MbrPassengerCommonRoute::getRouteName, bo.getRouteName());
        lqw.eq(bo.getStartProvinceId() != null, MbrPassengerCommonRoute::getStartProvinceId, bo.getStartProvinceId());
        lqw.eq(StringUtils.isNotBlank(bo.getStartProvince()), MbrPassengerCommonRoute::getStartProvince, bo.getStartProvince());
        lqw.eq(bo.getStartCityId() != null, MbrPassengerCommonRoute::getStartCityId, bo.getStartCityId());
        lqw.eq(StringUtils.isNotBlank(bo.getStartCityCode()), MbrPassengerCommonRoute::getStartCityCode, bo.getStartCityCode());
        lqw.eq(StringUtils.isNotBlank(bo.getStartCity()), MbrPassengerCommonRoute::getStartCity, bo.getStartCity());
        lqw.eq(bo.getStartDistrictId() != null, MbrPassengerCommonRoute::getStartDistrictId, bo.getStartDistrictId());
        lqw.eq(StringUtils.isNotBlank(bo.getStartAdCode()), MbrPassengerCommonRoute::getStartAdCode, bo.getStartAdCode());
        lqw.eq(StringUtils.isNotBlank(bo.getStartDistrict()), MbrPassengerCommonRoute::getStartDistrict, bo.getStartDistrict());
        lqw.eq(StringUtils.isNotBlank(bo.getStartShortAddr()), MbrPassengerCommonRoute::getStartShortAddr, bo.getStartShortAddr());
        lqw.eq(StringUtils.isNotBlank(bo.getStartAddress()), MbrPassengerCommonRoute::getStartAddress, bo.getStartAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getStartLongitude()), MbrPassengerCommonRoute::getStartLongitude, bo.getStartLongitude());
        lqw.eq(StringUtils.isNotBlank(bo.getStartLatitude()), MbrPassengerCommonRoute::getStartLatitude, bo.getStartLatitude());
        lqw.eq(bo.getEndProvinceId() != null, MbrPassengerCommonRoute::getEndProvinceId, bo.getEndProvinceId());
        lqw.eq(StringUtils.isNotBlank(bo.getEndProvince()), MbrPassengerCommonRoute::getEndProvince, bo.getEndProvince());
        lqw.eq(bo.getEndCityId() != null, MbrPassengerCommonRoute::getEndCityId, bo.getEndCityId());
        lqw.eq(StringUtils.isNotBlank(bo.getEndCityCode()), MbrPassengerCommonRoute::getEndCityCode, bo.getEndCityCode());
        lqw.eq(StringUtils.isNotBlank(bo.getEndCity()), MbrPassengerCommonRoute::getEndCity, bo.getEndCity());
        lqw.eq(bo.getEndDistrictId() != null, MbrPassengerCommonRoute::getEndDistrictId, bo.getEndDistrictId());
        lqw.eq(StringUtils.isNotBlank(bo.getEndAdCode()), MbrPassengerCommonRoute::getEndAdCode, bo.getEndAdCode());
        lqw.eq(StringUtils.isNotBlank(bo.getEndDistrict()), MbrPassengerCommonRoute::getEndDistrict, bo.getEndDistrict());
        lqw.eq(StringUtils.isNotBlank(bo.getEndShortAddr()), MbrPassengerCommonRoute::getEndShortAddr, bo.getEndShortAddr());
        lqw.eq(StringUtils.isNotBlank(bo.getEndAddress()), MbrPassengerCommonRoute::getEndAddress, bo.getEndAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getEndLongitude()), MbrPassengerCommonRoute::getEndLongitude, bo.getEndLongitude());
        lqw.eq(StringUtils.isNotBlank(bo.getEndLatitude()), MbrPassengerCommonRoute::getEndLatitude, bo.getEndLatitude());
        return lqw;
    }

    /**
     * 新增乘客常用路线
     *
     * @param bo 乘客常用路线
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MbrPassengerCommonRouteBo bo) {
        MbrPassengerCommonRoute add = MapstructUtils.convert(bo, MbrPassengerCommonRoute.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改乘客常用路线
     *
     * @param bo 乘客常用路线
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MbrPassengerCommonRouteBo bo) {
        MbrPassengerCommonRoute update = MapstructUtils.convert(bo, MbrPassengerCommonRoute.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MbrPassengerCommonRoute entity){
        //TODO 做一些数据校验,如唯一约束
        Long l = baseMapper.selectCount(new LambdaQueryWrapper<MbrPassengerCommonRoute>().eq(MbrPassengerCommonRoute::getPassengerId, entity.getPassengerId()));
        if (l >= 5){
            throw new ServiceException("每位乘客最多支持创建 5 条 常用线路");
        }
    }

    /**
     * 校验并批量删除乘客常用路线信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

}
