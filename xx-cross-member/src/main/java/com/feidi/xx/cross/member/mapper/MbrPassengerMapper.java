package com.feidi.xx.cross.member.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.member.domain.MbrPassenger;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerVo;

/**
 * 乘客Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface MbrPassengerMapper extends BaseMapperPlus<MbrPassenger, MbrPassengerVo> {

    default MbrPassenger getByCode(String code) {
        return selectOne(Wrappers.lambdaQuery(MbrPassenger.class)
                .eq(MbrPassenger::getCode, code));
    }

}
