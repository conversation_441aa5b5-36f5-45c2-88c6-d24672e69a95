package com.feidi.xx.cross.member.mapper;

import com.feidi.xx.cross.member.domain.MbrPassengerCollectAddress;
import com.feidi.xx.cross.member.domain.vo.MbrPassengerCollectAddressVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 乘客收藏地址Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface MbrPassengerCollectAddressMapper extends BaseMapperPlus<MbrPassengerCollectAddress, MbrPassengerCollectAddressVo> {

}
