<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.feidi.xx.cross.operate.mapper.OprLineDetailMapper">

    <delete id="deleteByLineId">
        DELETE
        FROM opr_line_detail
        WHERE line_id = #{lineId}
    </delete>

    <select id="matchLine" resultType="com.feidi.xx.cross.operate.domain.dto.OprLineMatchDTO">
        SELECT o1.line_id,
               o1.id,
               o1.direction AS route_type
        FROM opr_line_detail o1
        JOIN opr_line_detail o2 ON o1.line_id = o2.line_id
        AND o1.direction != o2.direction
        WHERE
            o1.ad_code = #{startAdCode}
        AND o2.ad_code = #{endAdCode}
        ORDER BY
            o1.direction DESC
            LIMIT 1
    </select>
    <select id="matchLineByFenceId" resultType="com.feidi.xx.cross.operate.domain.dto.OprLineMatchDTO">
        SELECT o1.line_id,
               o1.id,
               o1.direction AS route_type
        FROM opr_line_detail o1
        JOIN opr_line_detail o2 ON o1.line_id = o2.line_id
        JOIN opr_line ol ON o1.line_id = ol.id
        AND o1.direction != o2.direction
        WHERE
        o1.fence_id = #{startFenceId}
        AND o2.fence_id = #{endFenceId}
        AND ol.status = 0
        AND ol.del_flag = 0
        ORDER BY o1.direction DESC LIMIT 1
    </select>
    <select id="queryUsingLineByFenceId" resultType="com.feidi.xx.cross.operate.domain.dto.OprLineMatchDTO">
        SELECT DISTINCT line_id
        FROM opr_line_detail o
        WHERE o.fence_id = #{fenceId}
    </select>
</mapper>
