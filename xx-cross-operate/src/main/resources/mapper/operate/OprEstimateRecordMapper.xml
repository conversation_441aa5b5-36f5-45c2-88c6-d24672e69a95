<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.feidi.xx.cross.operate.mapper.OprEstimateRecordMapper">

    <delete id="doDeleteByIds">
        delete from opr_estimate_record where id in
        <foreach item="id" index="index" collection="ids"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
