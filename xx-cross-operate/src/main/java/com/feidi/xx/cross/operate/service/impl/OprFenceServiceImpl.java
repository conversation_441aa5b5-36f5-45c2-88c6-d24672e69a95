package com.feidi.xx.cross.operate.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.map.utils.GeometryUtil;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.operate.enums.OprCacheKeyEnum;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import com.feidi.xx.cross.operate.api.domain.city.vo.RemoteCityVo;
import com.feidi.xx.cross.operate.api.domain.fence.vo.RemoteFenceVo;
import com.feidi.xx.cross.operate.domain.OprFence;
import com.feidi.xx.cross.operate.domain.bo.OprFenceBo;
import com.feidi.xx.cross.operate.domain.dto.OprLineMatchDTO;
import com.feidi.xx.cross.operate.domain.vo.OprFenceVo;
import com.feidi.xx.cross.operate.mapper.OprFenceMapper;
import com.feidi.xx.cross.operate.mapper.OprLineDetailMapper;
import com.feidi.xx.cross.operate.service.IOprFenceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.MultiPolygon;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 电子围栏Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OprFenceServiceImpl implements IOprFenceService {

    private final OprFenceMapper baseMapper;

    private final ScheduledExecutorService scheduledExecutorService;

    private final OprLineDetailMapper lineDetailMapper;

    private final OprCacheManager cacheManager;

    /**
     * 查询电子围栏
     *
     * @param id 主键
     * @return 电子围栏
     */
    @Override
    public OprFenceVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询电子围栏列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 电子围栏分页列表
     */
    @Override
    public TableDataInfo<OprFenceVo> queryPageList(OprFenceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OprFence> lqw = buildQueryWrapper(bo);
        Page<OprFenceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        for (OprFenceVo record : result.getRecords()) {
            List<OprLineMatchDTO> usingLine = lineDetailMapper.queryUsingLineByFenceId(record.getId());
            record.setLineNum(usingLine.size());
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的电子围栏列表
     *
     * @param bo 查询条件
     * @return 电子围栏列表
     */
    @Override
    public List<OprFenceVo> queryList(OprFenceBo bo) {
        LambdaQueryWrapper<OprFence> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OprFence> buildQueryWrapper(OprFenceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OprFence> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), OprFence::getName, bo.getName());
        lqw.eq(bo.getProvinceId() != null, OprFence::getProvinceId, bo.getProvinceId());
        lqw.eq(bo.getCityId() != null, OprFence::getCityId, bo.getCityId());
        lqw.eq(StringUtils.isNotBlank(bo.getCityCode()), OprFence::getCityCode, bo.getCityCode());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), OprFence::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), OprFence::getStatus, bo.getStatus());
        lqw.ge(ObjUtil.isNotNull(params.get("startTime")), OprFence::getCreateTime, params.get("startTime"));
        lqw.le(ObjUtil.isNotNull(params.get("endTime")), OprFence::getCreateTime, params.get("endTime"));
        return lqw;
    }

    /**
     * 新增电子围栏
     *
     * @param bo 电子围栏
     * @return 是否新增成功
     */
    @Override
    public Long insertByBo(OprFenceBo bo) {
        OprFence add = MapstructUtils.convert(bo, OprFence.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            scheduledExecutorService.schedule(() -> addCache(add), 0, TimeUnit.SECONDS);
        }
        return add.getId();
    }

    /**
     * 修改电子围栏
     *
     * @param bo 电子围栏
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OprFenceBo bo) {
        OprFence update = MapstructUtils.convert(bo, OprFence.class);
        validEntityBeforeSave(update);
        boolean ret  = baseMapper.updateById(update) > 0;
        // 异步添加缓存
        scheduledExecutorService.schedule(() -> addCache(update), 0, TimeUnit.SECONDS);

        return ret;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OprFence entity){
        // 名称不能重复
        OprFence oprFence = baseMapper.queryByName(entity.getName());
        if (oprFence != null && !oprFence.getId().equals(entity.getId())) {
            throw new ServiceException("名称不能重复");
        }

    }

    /**
     * 校验并批量删除电子围栏信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        /**
         * 删除缓存
         */
        ids.forEach(id -> {
            OprFence fence = baseMapper.selectById(id);
            if (fence != null) {
                deleteCache(fence.getCityCode());
            }
        });
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据经纬度查询电子围栏信息
     * @param latitude 纬度
     * @param longitude 经度
     * @return
     */
    @Override
    public List<Long> queryByPoint(Double latitude, Double longitude) {
        List<OprFence> fences = baseMapper.selectList();
        List<Long> match = new ArrayList<>();
        for (OprFence fence : fences) {
            MultiPolygon multiPolygon = convert(fence.getPoints());
            Point point = GeometryUtil.FACTORY.createPoint(new Coordinate(longitude, latitude));
            if (multiPolygon.covers(point)) {
                match.add(fence.getId());
            }
        }
        return match;
    }


/*    public static void main(String[] args) {
         Double[] points= new Double[]{
                 47.218386,
                 130.369274,
                 47.551238,
                 129.993014,
                 47.795348,
                 130.531312,
                 47.572551,
                 131.411539,
                 47.366476,
                 131.138271,
                 47.218386,
                 130.369274
          };
        MultiPolygon multiPolygon = convert(points);
        Point point = GeometryUtil.FACTORY.createPoint(new Coordinate(47.728935, 128.926822));
        System.out.println(multiPolygon.covers(point));
    }*/
    /**
     * 根据经纬度和 adCode 查询电子围栏信息
     * @param cityCode 城市代码
     * @param latitude 纬度
     * @param longitude 经度
     * @return 匹配的电子围栏 ID（只返回一个匹配的围栏）
     */
    @Override
    public  List<Long> queryByPointAndCityCode(String cityCode, Double latitude, Double longitude) {
        List<Long> fenceIds = new ArrayList<>();
        List<RemoteFenceVo> fences;
        if (StringUtils.isBlank(cityCode)) {
            log.info("城市代码为空，查询所有电子围栏");
            List<OprFence> oprFences = baseMapper.selectList();
            fences = oprFences.stream().map(fence ->BeanUtils.copyProperties(fence, RemoteFenceVo.class)).collect(Collectors.toList());
        }else {
            log.info("城市代码不为空，查询城市{}电子围栏", cityCode);
            fences = cacheManager.getFenceInfoByCityCode(cityCode);

        }
        Point point = GeometryUtil.FACTORY.createPoint(new Coordinate(longitude, latitude));

        for (RemoteFenceVo fence : fences) {
            MultiPolygon multiPolygon = convert(fence.getPoints());
            if (multiPolygon.covers(point)) {
                log.info("匹配到电子围栏：{}", fence.getName());
                fenceIds.add(fence.getId()); // 匹配到就立即返回
            }
        }
        return fenceIds; // 如果没有匹配到，返回 null
    }


    // 转换方法
    public static MultiPolygon convert(Double[] points) {
        int length = points.length / 2;
        Coordinate[] coordinates = new Coordinate[length + 1];
        for (int i = 0; i < length; i++) {
            double lat = points[i * 2]; // 偶数索引为纬度
            double lng = points[i * 2 + 1]; // 奇数索引为经度
            coordinates[i] = new Coordinate(lng, lat);
        }
        // 补齐
        coordinates[length] = coordinates[0];
        Polygon polygon = GeometryUtil.FACTORY.createPolygon(coordinates);
        return GeometryUtil.FACTORY.createMultiPolygon(new Polygon[]{polygon});
    }
    /**
     * 添加缓存
     *
     * @param fence 电子围栏
     */
    private void addCache(OprFence fence) {
        if (fence == null) {
            return;
        }
        deleteCache(fence.getCityCode());
        // 缓存KEY
        cacheManager.getFenceInfoByCityCode(fence.getCityCode());
    }

    /**
     * 删除缓存
     *
     * @param cityCode
     */
    private void deleteCache(String cityCode) {
        RedisUtils.deleteObject(OprCacheKeyEnum.OPR_FENCE_INFO_KEY.create(cityCode));
    }

}
