package com.feidi.xx.cross.operate.domain.vo;

import com.feidi.xx.common.id2name.annotation.Id2Name;
import com.feidi.xx.cross.operate.domain.OprCity;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 城市视图对象 pow_city
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OprCity.class)
public class OprCityVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 价格模版
     */
    @ExcelProperty(value = "价格模版")
    private Long priceId;

    /**
     * 省ID
     */
    @ExcelProperty(value = "省ID")
    @Id2Name(fullName="province",index = "id")
    private Long provinceId;

    /**
     * 市ID
     */
    @ExcelProperty(value = "市ID")
    @Id2Name(fullName="city",index = "id")
    private Long cityId;

    /**
     * 市编码
     */
    @ExcelProperty(value = "市编码")
    private String cityCode;

    /**
     * 状态[StatusEnum]
     */
    @ExcelProperty(value = "状态[StatusEnum]")
    private String status;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;


    /**
     * 开始运营时间
     */
    private String startTime;
    /**
     * 结束运营时间
     */
    private String endTime;
    /**
     * 最大接单数量
     */
    private Integer maxNumber;

    /**
     * 计价模版名称
     */
    private String priceName;

    /**
     * 代理商list
     */
    private List<String> agentList;

    /**
     * 城市价格模版详情list
     */
    private List<OprCityPriceVo> oprCityPriceVoList;


}
