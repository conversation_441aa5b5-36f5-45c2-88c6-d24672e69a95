package com.feidi.xx.cross.operate.service;

import com.feidi.xx.cross.operate.domain.vo.OprFenceVo;
import com.feidi.xx.cross.operate.domain.bo.OprFenceBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 电子围栏Service接口
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
public interface IOprFenceService {

    /**
     * 查询电子围栏
     *
     * @param id 主键
     * @return 电子围栏
     */
    OprFenceVo queryById(Long id);

    /**
     * 分页查询电子围栏列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 电子围栏分页列表
     */
    TableDataInfo<OprFenceVo> queryPageList(OprFenceBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的电子围栏列表
     *
     * @param bo 查询条件
     * @return 电子围栏列表
     */
    List<OprFenceVo> queryList(OprFenceBo bo);

    /**
     * 新增电子围栏
     *
     * @param bo 电子围栏
     * @return 是否新增成功
     */
    Long insertByBo(OprFenceBo bo);

    /**
     * 修改电子围栏
     *
     * @param bo 电子围栏
     * @return 是否修改成功
     */
    Boolean updateByBo(OprFenceBo bo);

    /**
     * 校验并批量删除电子围栏信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据经纬度查询围栏信息
     * @param latitude
     * @param longitude
     * @return
     */
    List<Long> queryByPoint(Double latitude, Double longitude);


    /**
     * 根据经纬度和cityCode查询电子围栏信息
     * @param latitude 纬度
     * @param longitude 经度
     * @return
     */
    List<Long> queryByPointAndCityCode(String cityCode, Double latitude, Double longitude);

}
