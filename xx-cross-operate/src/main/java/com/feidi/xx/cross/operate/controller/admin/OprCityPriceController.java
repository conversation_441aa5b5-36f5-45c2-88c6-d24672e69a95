package com.feidi.xx.cross.operate.controller.admin;

import java.util.List;

import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.id2name.annotation.Id2NameAspect;
import com.feidi.xx.cross.operate.domain.bo.OprCityBo;
import com.feidi.xx.cross.operate.domain.vo.OprCityVo;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.cross.operate.domain.vo.OprCityPriceVo;
import com.feidi.xx.cross.operate.domain.bo.OprCityPriceBo;
import com.feidi.xx.cross.operate.service.IOprCityPriceService;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 城市价格关联
 * 前端访问路由地址为:/operate/cityPrice
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/cityPrice")
public class OprCityPriceController extends BaseController {

    private final IOprCityPriceService oprCityPriceService;

    /**
     * 查询城市价格关联列表
     */
    @SaCheckPermission("operate:cityPrice:list")
    @GetMapping("/list")
    public TableDataInfo<OprCityPriceVo> list(OprCityPriceBo bo, PageQuery pageQuery) {
        return oprCityPriceService.queryPageList(bo, pageQuery);
    }
    /**
     * 查询城市价格列表-所有
     */
    @SaCheckPermission("operate:cityPrice:list")
    @GetMapping("/list/all")
    @Id2NameAspect
    public R<List<OprCityPriceVo>> list(OprCityPriceBo bo) {
        return R.ok(oprCityPriceService.queryAllList(bo));
    }
    /**
     * 导出城市价格关联列表
     */
    @SaCheckPermission("operate:cityPrice:export")
    @Log(title = "城市价格关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OprCityPriceBo bo, HttpServletResponse response) {
        List<OprCityPriceVo> list = oprCityPriceService.queryList(bo);
        ExcelUtil.exportExcel(list, "城市价格关联", OprCityPriceVo.class, response);
    }

    /**
     * 获取城市价格关联详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("operate:cityPrice:query")
    @GetMapping("/{id}")
    public R<OprCityPriceVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(oprCityPriceService.queryById(id));
    }

    /**
     * 新增城市价格关联
     */
    @SaCheckPermission("operate:cityPrice:add")
    @Log(title = "城市价格关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OprCityPriceBo bo) {
        return toAjax(oprCityPriceService.insertByBo(bo));
    }

    /**
     * 修改城市价格关联
     */
    @SaCheckPermission("operate:cityPrice:edit")
    @Log(title = "城市价格关联", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OprCityPriceBo bo) {
        return toAjax(oprCityPriceService.updateByBo(bo));
    }

    /**
     * 删除城市价格关联
     *
     * @param ids 主键串
     */
    @SaCheckPermission("operate:cityPrice:remove")
    @Log(title = "城市价格关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(oprCityPriceService.deleteWithValidByIds(List.of(ids), true));
    }
}
