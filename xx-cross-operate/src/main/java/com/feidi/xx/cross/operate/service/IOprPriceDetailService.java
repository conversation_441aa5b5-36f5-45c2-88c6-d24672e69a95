package com.feidi.xx.cross.operate.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.operate.domain.OprPriceDetail;
import com.feidi.xx.cross.operate.domain.bo.OprPriceDetailBo;
import com.feidi.xx.cross.operate.domain.dto.EstimateRecordDTO;
import com.feidi.xx.cross.operate.domain.dto.OprCalculateDTO;
import com.feidi.xx.cross.operate.domain.vo.OprCalculateVo;
import com.feidi.xx.cross.operate.domain.vo.OprDrvCalculateVo;
import com.feidi.xx.cross.operate.domain.vo.OprPriceDetailVo;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.Collection;
import java.util.List;

/**
 * 定价Service接口
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface IOprPriceDetailService {

    /**
     * 分页查询定价列表
     *
     * @param bo        查询条件
     * @return 定价分页列表
     */
    TableDataInfo<OprPriceDetailVo> queryPageList(OprPriceDetailBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的定价列表
     *
     * @param bo 查询条件
     * @return 定价列表
     */
    List<OprPriceDetailVo> queryList(OprPriceDetailBo bo);


    /**
     * 查询价格根据城市编码和服务id
     */
    OprPriceDetail getPricingByCondition(OprCalculateDTO calculateDTO);

    /**
     * 校验并批量删除定价信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据条件计算价格
     * @param calculateDTO 计算条件
     * @return 价格结果
     */
    OprCalculateVo calculatePrice(OprCalculateDTO calculateDTO);

    /**
     * 批量新增定价信息
     *
     * @param boList 待新增的定价集合
     * @return 是否新增成功
     */
    Boolean insertByBoList(List<OprPriceDetailBo> boList);


    /**
     * 批量更新定价信息
     *
     * @param boList 待更新的定价集合
     * @return 是否更新成功
     */
    public Boolean updateBatch(List<OprPriceDetailBo> boList);


    /**
     * 根据价格编码查询价格信息
     *
     * @param priceId 价格模版id
     * @return 价格信息
     */
    List<OprPriceDetailVo> queryByPriceId(@NotNull(message = "主键不能为空") Long priceId);

    void deleteByPriceId(Long id);

    Boolean remove(@NotEmpty(message = "主键不能为空") Long[] ids);

    Boolean updateEstimateRecord(EstimateRecordDTO estimateRecord);

    /**
     * 根据价格编码查询价格信息
     *
     * @param estimateRecord 预估编码
     * @return 价格信息
     */
    OprDrvCalculateVo getPriceKeyByIncome(EstimateRecordDTO estimateRecord);

}
