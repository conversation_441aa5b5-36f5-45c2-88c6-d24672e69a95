package com.feidi.xx.cross.operate.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.operate.domain.vo.ExportVo;
import com.feidi.xx.cross.operate.domain.bo.OprHolidayBo;
import com.feidi.xx.cross.operate.domain.vo.OprHolidayVo;
import com.feidi.xx.cross.operate.service.IOprHolidayService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 节假日管理
 * 前端访问路由地址为:/operate/holiday
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/holiday")
public class OprHolidayController extends BaseController {

    private final IOprHolidayService holidayService;

    /**
     * 查询节假日列表
     */
    @SaCheckPermission("operate:holiday:list")
    @GetMapping("/list")
    public TableDataInfo<OprHolidayVo> list(OprHolidayBo bo, PageQuery pageQuery) {
        return holidayService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出节假日列表
     */
    @SaCheckPermission("operate:holiday:export")
    @Log(title = "节假日", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OprHolidayBo bo, HttpServletResponse response) {
        List<OprHolidayVo> list = holidayService.queryList(bo);
        ExcelUtil.exportExcel(list, "节假日", OprHolidayVo.class, response);
    }

    /**
     * 获取节假日详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("operate:holiday:query")
    @GetMapping("/{id}")
    public R<OprHolidayVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long id) {
        return R.ok(holidayService.queryById(id));
    }

    /**
     * 新增节假日
     */
    @SaCheckPermission("operate:holiday:add")
    @Log(title = "定价", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OprHolidayBo bo) {
        return toAjax(holidayService.insertByBo(bo));
    }

    /**
     * 修改节假日
     */
    @SaCheckPermission("operate:holiday:edit")
    @Log(title = "节假日", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OprHolidayBo bo) {
        return toAjax(holidayService.updateByBo(bo));
    }

    /**
     * 删除节假日
     *
     * @param ids 主键串
     */
    @SaCheckPermission("operate:holiday:remove")
    @Log(title = "节假日", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(holidayService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 同步节假日
     *
     * @param year 年份
     * @return
     */
    @SaCheckPermission("operate:holiday:syncHoliday")
    @Log(title = "同步节假日", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/syncHoliday")
    public R<Void> syncHoliday(@Validated(AddGroup.class) @RequestParam Integer year) {
        //为空获取本年的节假日
        if (year == null) {
            return toAjax(holidayService.syncHoliday());
        } else {
            return toAjax(holidayService.syncHoliday(year));
        }
    }


}
