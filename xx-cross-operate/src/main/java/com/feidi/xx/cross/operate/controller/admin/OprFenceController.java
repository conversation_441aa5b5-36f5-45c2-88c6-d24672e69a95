package com.feidi.xx.cross.operate.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.id2name.annotation.Id2NameAspect;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.operate.domain.bo.OprFenceBo;
import com.feidi.xx.cross.operate.domain.vo.OprFenceVo;
import com.feidi.xx.cross.operate.service.IOprFenceService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 电子围栏
 * 前端访问路由地址为:/operate/fence
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/fence")
public class OprFenceController extends BaseController {

    private final IOprFenceService oprFenceService;

    /**
     * 查询电子围栏列表
     */
    @Id2NameAspect
    //@SaCheckPermission("operate:fence:list")
    @GetMapping("/list")
    public TableDataInfo<OprFenceVo> list(OprFenceBo bo, PageQuery pageQuery) {
        return oprFenceService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出电子围栏列表
     */
    @SaCheckPermission("operate:fence:export")
    @Log(title = "电子围栏", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OprFenceBo bo, HttpServletResponse response) {
        List<OprFenceVo> list = oprFenceService.queryList(bo);
        ExcelUtil.exportExcel(list, "电子围栏", OprFenceVo.class, response);
    }

    /**
     * 获取电子围栏详细信息
     *
     * @param id 主键
     */
    @Id2NameAspect
    @SaCheckPermission("operate:fence:query")
    @GetMapping("/{id}")
    public R<OprFenceVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(oprFenceService.queryById(id));
    }

    /**
     * 新增电子围栏
     */
    @SaCheckPermission("operate:fence:add")
    @Log(title = "电子围栏", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Long> add(@Validated(AddGroup.class) @RequestBody OprFenceBo bo) {
        return R.ok(oprFenceService.insertByBo(bo));
    }

    /**
     * 修改电子围栏
     */
    @SaCheckPermission("operate:fence:edit")
    @Log(title = "电子围栏", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OprFenceBo bo) {
        return toAjax(oprFenceService.updateByBo(bo));
    }

    /**
     * 删除电子围栏
     *
     * @param ids 主键串
     */
    @SaCheckPermission("operate:fence:remove")
    @Log(title = "电子围栏", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(oprFenceService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 根据经纬度查询围栏信息
     */
    @GetMapping("/queryByPoint/{latitude},{longitude}")
    public R<List<Long>> queryByPoint(@PathVariable Double latitude, @PathVariable Double longitude) {
        return R.ok(oprFenceService.queryByPoint(latitude, longitude));
    }

}
