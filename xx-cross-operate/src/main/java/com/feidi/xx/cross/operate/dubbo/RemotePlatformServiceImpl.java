package com.feidi.xx.cross.operate.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.operate.api.RemotePlatformService;
import com.feidi.xx.cross.operate.api.domain.platform.vo.RemotePlatformVo;
import com.feidi.xx.cross.operate.domain.OprPlatform;
import com.feidi.xx.cross.operate.mapper.OprPlatformMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * 平台dubbo服务实现类
 *
 * <AUTHOR>
 * @date 2025/3/13
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemotePlatformServiceImpl implements RemotePlatformService {

    private final OprPlatformMapper platformMapper;

    @Override
    public RemotePlatformVo queryByCode(String code) {
        LambdaQueryWrapper<OprPlatform> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OprPlatform::getCode, code)
                .eq(OprPlatform::getStatus, StatusEnum.ENABLE.getCode())
                .last(Constants.LIMIT_ONE);

        OprPlatform oprPlatform = platformMapper.selectOne(queryWrapper);

        return BeanUtils.copyProperties(oprPlatform, RemotePlatformVo.class);
    }

    /**
     * 根据平台编码和appKey获取平台信息
     *
     * @param code   平台编码
     * @param appKey appKey
     * @return 平台信息
     */
    @Override
    public RemotePlatformVo queryByCodeAndAppKey(String code, String appKey) {
        LambdaQueryWrapper<OprPlatform> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OprPlatform::getCode, code)
                .eq(OprPlatform::getAppKey, appKey)
                .eq(OprPlatform::getStatus, StatusEnum.ENABLE.getCode())
                .last(Constants.LIMIT_ONE);

        OprPlatform oprPlatform = platformMapper.selectOne(queryWrapper);

        return BeanUtils.copyProperties(oprPlatform, RemotePlatformVo.class);
    }

}
