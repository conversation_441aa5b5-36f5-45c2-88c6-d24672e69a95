package com.feidi.xx.cross.operate.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.cache.annotation.Cacheable;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.List;

/**
 * 电子围栏对象 opr_fence
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "opr_fence", autoResultMap = true)
public class OprFence extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 省ID
     */
    private Long provinceId;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 区域编码
     */
    private String adCode;

    /**
     * 围栏坐标点
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Double[] points;

    /**
     * 类型
     */
    private String type;

    /**
     * 状态[StatusEnum]
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
