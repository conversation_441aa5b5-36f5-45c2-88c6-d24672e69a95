package com.feidi.xx.cross.operate.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 城市对象 pow_city
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("opr_city")
public class OprCity extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 价格模版
     */
    private Long priceId;

    /**
     * 省ID
     */
    private Long provinceId;

    /**
     * 市ID
     */
    private Long cityId;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 状态[StatusEnum]
     */
    private String status;


    /**
     * 开始运营时间
     */
    private String startTime;
    /**
     * 结束运营时间
     */
    private String endTime;
    /**
     * 最大接单数量
     */
    private Integer maxNumber;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
