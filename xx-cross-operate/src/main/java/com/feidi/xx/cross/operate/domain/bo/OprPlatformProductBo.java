package com.feidi.xx.cross.operate.domain.bo;

import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.operate.domain.OprPlatformProduct;
import com.feidi.xx.cross.operate.service.OprPlatProdHelper;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 平台产品业务对象 opr_platform_product
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OprPlatformProduct.class, reverseConvertGenerate = false)
public class OprPlatformProductBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 平台编码
     */
    @NotBlank(message = "平台编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platformCode;

    /**
     * 产品编码
     */
    @NotBlank(message = "产品编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productCode;

    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    public OprPlatformProductBo(String platformCode,String productCode) {
        this.platformCode = platformCode;
        this.productCode = productCode;
    }

    /**
     *
     * @param code 对应的code
     * @param type {@link OprPlatProdHelper}
     * @return
     */
    public static OprPlatformProductBo newInstance(String code, String type) {
        switch (type) {
            case OprPlatProdHelper.CONSTANTS.PLATFORM -> {
                return new OprPlatformProductBo( code, null);
            } case OprPlatProdHelper.CONSTANTS.PRODUCT -> {
                return new OprPlatformProductBo(null,code );
            } default -> throw new ServiceException("不支持的类型");
        }
    }

}
