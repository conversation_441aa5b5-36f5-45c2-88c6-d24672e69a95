package com.feidi.xx.cross.operate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.cache.system.SysDistrictCacheVo;
import com.feidi.xx.common.core.enums.StartEndEnum;
import com.feidi.xx.common.core.enums.SystemCacheKeyEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.*;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.operate.enums.OprCacheKeyEnum;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import com.feidi.xx.cross.common.cache.operate.vo.OprEstimateRecordCacheVo;
import com.feidi.xx.cross.common.cache.power.manager.PowCacheManager;
import com.feidi.xx.cross.common.constant.operate.OperateCacheConstants;
import com.feidi.xx.cross.common.enums.operate.HolidayTypeEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.common.utils.order.OrderUtils;
import com.feidi.xx.cross.operate.api.domain.city.vo.RemoteCityPriceVo;
import com.feidi.xx.cross.operate.api.domain.city.vo.RemoteCityVo;
import com.feidi.xx.cross.operate.api.domain.line.vo.RemoteLineVo;
import com.feidi.xx.cross.operate.api.domain.price.dto.PriceDto;
import com.feidi.xx.cross.operate.api.domain.price.vo.RemotePriceVo;
import com.feidi.xx.cross.operate.chain.CalculateChainContext;
import com.feidi.xx.cross.operate.domain.OprPriceDetail;
import com.feidi.xx.cross.operate.domain.bo.OprEstimateRecordBo;
import com.feidi.xx.cross.operate.domain.bo.OprPriceDetailBo;
import com.feidi.xx.cross.operate.domain.dto.EstimateRecordDTO;
import com.feidi.xx.cross.operate.domain.dto.OprCalculateDTO;
import com.feidi.xx.cross.operate.domain.dto.OprLineMatchDTO;
import com.feidi.xx.cross.operate.domain.vo.OprCalculateVo;
import com.feidi.xx.cross.operate.domain.vo.OprDrvCalculateVo;
import com.feidi.xx.cross.operate.domain.vo.OprPriceDetailVo;
import com.feidi.xx.cross.operate.mapper.OprLineDetailMapper;
import com.feidi.xx.cross.operate.mapper.OprPriceDetailMapper;
import com.feidi.xx.cross.operate.service.IOprEstimateRecordService;
import com.feidi.xx.cross.operate.service.IOprFenceService;
import com.feidi.xx.cross.operate.service.IOprHolidayService;
import com.feidi.xx.cross.operate.service.IOprPriceDetailService;
import com.feidi.xx.cross.operate.strategy.AbstractCalculateStrategy;
import com.feidi.xx.cross.operate.strategy.CalculateStrategy;
import com.feidi.xx.cross.operate.strategy.FixedCalculateStrategy;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.system.api.RemoteConfigService;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.feidi.xx.cross.common.enums.operate.ChainBizMarkEnum.CALCULATE_KEY;

/**
 * 定价Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class OprPriceDetailServiceImpl implements IOprPriceDetailService {

    private final OprPriceDetailMapper baseMapper;

    private final IOprHolidayService holidayService;

    private final CalculateChainContext calculateChainContext;

    private final OprLineDetailMapper lineDetailMapper;

    private final IOprFenceService fenceService;

    private final OprCacheManager cacheManager;

    private final PowCacheManager powCacheManager;

    @DubboReference
    private RemoteConfigService configService;


    /**
     * 分页查询定价列表
     *
     * @param bo 查询条件
     * @return 定价分页列表
     */
    @Override
    public TableDataInfo<OprPriceDetailVo> queryPageList(OprPriceDetailBo bo, PageQuery pageQuery) {
        //初始化
        if (ObjectUtils.isNull(pageQuery.getIsAsc())) {
            pageQuery.setIsAsc("desc");
        }
        if (ObjectUtils.isNull(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("create_time");
        }
        LambdaQueryWrapper<OprPriceDetail> lqw = buildQueryWrapper(bo);
        Page<OprPriceDetailVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    private LambdaQueryWrapper<OprPriceDetail> buildQueryWrapper(OprPriceDetailBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OprPriceDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPlatformCode()), OprPriceDetail::getPlatformCode, bo.getPlatformCode());
        lqw.eq(StringUtils.isNotBlank(bo.getHolidayType()), OprPriceDetail::getHolidayType, bo.getHolidayType());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), OprPriceDetail::getProductCode, bo.getProductCode());
        lqw.eq(bo.getPriceId() != null, OprPriceDetail::getPriceId, bo.getPriceId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), OprPriceDetail::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 查询符合条件的定价列表
     *
     * @param bo 查询条件
     * @return 定价列表
     */
    @Override
    public List<OprPriceDetailVo> queryList(OprPriceDetailBo bo) {
        LambdaQueryWrapper<OprPriceDetail> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }


    @Override
    public OprPriceDetail getPricingByCondition(OprCalculateDTO calculateDTO) {
        LambdaQueryWrapper<OprPriceDetail> lqw = new LambdaQueryWrapper<>();
        lqw.eq(OprPriceDetail::getPlatformCode, PlatformCodeEnum.TY.getCode())
                .eq(OprPriceDetail::getPriceId, calculateDTO.getPriceId())
                .eq(OprPriceDetail::getProductCode, calculateDTO.getProductCode())
                .eq(OprPriceDetail::getHolidayType, calculateDTO.getHolidayType())
                .orderByDesc(OprPriceDetail::getId)
                .last("limit 1");
        return baseMapper.selectOne(lqw);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OprPriceDetail entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除定价信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 计算价格
     *
     * @param calculateDTO 计算条件
     * @return
     **/
    @Override
    public OprCalculateVo calculatePrice(OprCalculateDTO calculateDTO) {
        log.info("计算价格参数1:{}", calculateDTO);
        // 通过责任链验证请求参数是否正确
        calculateChainContext.handler(CALCULATE_KEY.name(), calculateDTO);

        matchLineAndSetPrice(calculateDTO);

        log.info("计算价格参数2:{}", calculateDTO);

        //  判断当前时间是否为节假日
        Long startTime = calculateDTO.getStartTime();
        Date date = DateUtils.convertLongToDate(startTime);
        Boolean isHoliday = holidayService.isHoliday(date);
        if (isHoliday) {
            calculateDTO.setHolidayType(HolidayTypeEnum.HOLIDAY_PRICE.getCode());
        } else {
            calculateDTO.setHolidayType(HolidayTypeEnum.WEEKDAY_PRICE.getCode());
        }
        // 生成询价key
        if (calculateDTO.getEstimateKey() == null) {
            calculateDTO.setEstimateKey(OrderUtils.makeEstimateKey());
        }
        return CalculateStrategy.calculatePriceStrategy(calculateDTO);
    }

    /**
     * 匹配线路
     *
     * @param calculateDTO
     */
    private void matchLineAndSetPrice(OprCalculateDTO calculateDTO) {
        log.info("进入匹配线路-参数:{}", calculateDTO);
        //计算运行时间
        long startTime = DateUtils.getUnixTimeStamps();
        // 价格模板处理

        if (PlatformCodeEnum.SELF.getCode().equals(calculateDTO.getPlatformCode())) {


            if (ObjectUtils.isNull(calculateDTO.getPriceId())) {
                // 通过坐标匹配电子围栏，获取起点和终点的电子围栏 ID
                List<Long> startFenceIds;
                List<Long> endFenceIds;
                /// 优化 calculateDTO.getStartCityCode()=calculateDTO.getEndCityCode()
                //处理cityCode为空逻辑
                startFenceIds = fenceService.queryByPointAndCityCode(calculateDTO.getStartCityCode(), calculateDTO.getStartLatitude(), calculateDTO.getStartLongitude());
                endFenceIds = fenceService.queryByPointAndCityCode(calculateDTO.getEndCityCode(), calculateDTO.getEndLatitude(), calculateDTO.getEndLongitude());

                log.info("【电子围栏匹配】开始匹配，startFenceIds = {}, endFenceIds = {}", startFenceIds, endFenceIds);
                List<OprLineMatchDTO> oprLineMatchDTOS = List.of();
                OprLineMatchDTO oprLineMatchDTO;
                if (ObjectUtils.isNotNull(startFenceIds) && ObjectUtils.isNotNull(endFenceIds)) {
                    oprLineMatchDTOS = getOprLineMatchDTOS(startFenceIds, endFenceIds, oprLineMatchDTOS);
                }
                // 若起点或终点为空，或者未匹配到任何线路
                if (oprLineMatchDTOS.isEmpty()) {
                    log.info("进入区县匹配逻辑 未匹配到任何电子围栏");
                    // 若起始和终点的行政区划码不为空，则尝试基于行政区划匹配线路
                    if (calculateDTO.getStartAdCode() != null && calculateDTO.getEndAdCode() != null) {
                        List<OprLineMatchDTO> matchDTOS = lineDetailMapper.matchLine(calculateDTO.getStartAdCode(), calculateDTO.getEndAdCode());
                        // 若匹配到线路且当前 calculateDTO 未指定线路 ID
                        if (CollUtils.isNotEmpty(matchDTOS)) {
                            if (matchDTOS.size() > 1) {
                                // 优先选择 "往" 线路，其次选择 "返" 线路
                                Optional<OprLineMatchDTO> selectedRoute = matchDTOS.stream()
                                        .filter(dto -> StartEndEnum.START.getCode().equals(dto.getRouteType()))
                                        .findFirst()
                                        .or(() -> matchDTOS.stream()
                                                .filter(dto -> StartEndEnum.END.getCode().equals(dto.getRouteType()))
                                                .findFirst());
                                oprLineMatchDTO = selectedRoute.get();
                            } else {
                                oprLineMatchDTO = matchDTOS.get(0);
                            }
                            // 设置线路 ID，并查询对应的价格 ID
                            calculateDTO.setLineId(oprLineMatchDTO.getLineId());
                            RemoteLineVo oprLine = cacheManager.getLineInfoById(calculateDTO.getLineId());
                            if (StartEndEnum.START.getCode().equals(oprLineMatchDTO.getRouteType())) {
                                calculateDTO.setPriceId(oprLine.getStartPricingId());
                                calculateDTO.setRouteType(oprLineMatchDTO.getRouteType());
                            } else {
                                calculateDTO.setPriceId(oprLine.getEndPricingId());
                            }
                            calculateDTO.setRouteType(oprLineMatchDTO.getRouteType());
                        } else {
                            if (LoginHelper.getUserType().getUserType().equals(UserTypeEnum.PASSENGER_USER.getUserType())) {
                                saveNoSuchLineEstimateRecord(calculateDTO);
                                throw new ServiceException("当前路线暂未开通，请联系客服！");
                            }
                            //if (calculateDTO.getPlatformCode().equals(PlatformCodeEnum.SELF.getCode())) {
                            //    throw new ServiceException("当前路线暂未开通，请联系客服！");
                            //} else {
                            // 若无法匹配线路，则使用城市价格模板
                            String cityCode = calculateDTO.getStartCityCode();
                            if (ObjectUtils.isNull(cityCode)) {
                                saveNoSuchLineEstimateRecord(calculateDTO);
                                throw new ServiceException("当前路线暂未开通，请联系客服！");
                            }
                            RemoteCityPriceVo cityPriceInfoByCode = cacheManager.getCityPriceInfoByCode(cityCode, calculateDTO.getPlatformCode());
                            if (ObjectUtils.isNull(cityPriceInfoByCode)) {
                                saveNoSuchLineEstimateRecord(calculateDTO);
                                throw new ServiceException("当前路线暂未开通，请联系客服！");
                            }
                            calculateDTO.setPriceId(cityPriceInfoByCode.getPriceId());
                            //}
                        }
                    }
                } else {
                    log.info("进入电子围栏匹配逻辑 匹配到{}条线路", oprLineMatchDTOS.size());
                    // 电子围栏匹配成功，选择合适的线路
                    if (oprLineMatchDTOS.size() > 1) {
                        List<OprLineMatchDTO> finalOprLineMatchDTOS = oprLineMatchDTOS;
                        Optional<OprLineMatchDTO> selectedRoute = oprLineMatchDTOS.stream()
                                .filter(dto -> StartEndEnum.START.getCode().equals(dto.getRouteType()))
                                .findFirst()
                                .or(() -> finalOprLineMatchDTOS.stream()
                                        .filter(dto -> StartEndEnum.END.getCode().equals(dto.getRouteType()))
                                        .findFirst());
                        oprLineMatchDTO = selectedRoute.get();
                        log.info("选择线路：{}", oprLineMatchDTO);
                    } else {
                        oprLineMatchDTO = oprLineMatchDTOS.get(0);
                    }
                    // 设置线路 ID 并查询价格 ID
                    calculateDTO.setLineId(oprLineMatchDTO.getLineId());
                    RemoteLineVo oprLine = cacheManager.getLineInfoById(calculateDTO.getLineId());
                    if (StartEndEnum.START.getCode().equals(oprLineMatchDTO.getRouteType())) {
                        calculateDTO.setPriceId(oprLine.getStartPricingId());
                    } else {
                        calculateDTO.setPriceId(oprLine.getEndPricingId());
                    }
                    calculateDTO.setRouteType(oprLineMatchDTO.getRouteType());
                }
                // 若仍未获取价格 ID，且当前用户为乘客，则抛出异常
                if (calculateDTO.getPriceId() == null) {
                    if (UserTypeEnum.PASSENGER_USER.equals(LoginHelper.getUserType())) {
                        saveNoSuchLineEstimateRecord(calculateDTO);
                        throw new ServiceException("抱歉，暂未开通此线路");
                    }
                }
            }
        } else {
            String cityCode = calculateDTO.getStartCityCode();
            if (ObjectUtils.isNull(cityCode)) {
                saveNoSuchLineEstimateRecord(calculateDTO);
                throw new ServiceException("当前路线暂未开通，请联系客服！");
            }
            RemoteCityPriceVo cityPriceInfoByCode = cacheManager.getCityPriceInfoByCode(cityCode, calculateDTO.getPlatformCode());
            if (ObjectUtils.isNull(cityPriceInfoByCode)) {
                saveNoSuchLineEstimateRecord(calculateDTO);
                throw new ServiceException("当前路线暂未开通，请联系客服！");
            }
            calculateDTO.setPriceId(cityPriceInfoByCode.getPriceId());
        }

        // 价格类型处理，若价格类型为空，则查询价格类型并赋值
        if (ObjectUtils.isNull(calculateDTO.getPriceType())) {
            if (ObjectUtils.isNull(calculateDTO.getPriceId())) {
                saveNoSuchLineEstimateRecord(calculateDTO);
                throw new ServiceException("抱歉，暂未开通此线路");
            }
            RemotePriceVo oprPrice = cacheManager.getPriceInfoByPriceId(calculateDTO.getPriceId());
            calculateDTO.setPriceType(oprPrice.getPricingType());
        }

        long endTime = DateUtils.getUnixTimeStamps();

        if (log.isInfoEnabled()) {
            log.info("计算价格 -  耗时：【{}】", ArithUtils.sub(endTime, startTime));
        }
    }

    /**
     * 保存《暂未开通此线路》询价记录
     * @param calculateDTO dto
     */
    public void saveNoSuchLineEstimateRecord(OprCalculateDTO calculateDTO) {
        calculateDTO.setReason("当前路线暂未开通，请联系客服！");
        AbstractCalculateStrategy bean = SpringUtils.getBean(FixedCalculateStrategy.class);
        bean.saveEstimateRecord(calculateDTO, new OprCalculateVo());
    }

    private List<OprLineMatchDTO> getOprLineMatchDTOS(List<Long> startFenceIds, List<Long> endFenceIds, List<OprLineMatchDTO> oprLineMatchDTOS) {
        for (Long startFenceId : startFenceIds) {
            for (Long endFenceId : endFenceIds) {
                oprLineMatchDTOS = lineDetailMapper.matchLineByFenceId(startFenceId, endFenceId);
                if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(oprLineMatchDTOS)) {
                    return oprLineMatchDTOS;
                }
            }
        }
        return oprLineMatchDTOS;
    }

    /**
     * 根据adCode获取cityCode
     */
    private static String getCityCodeByAdCode(String adCode) {
        SysDistrictCacheVo value = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_AD_CODE_KEY.create(), String.valueOf(adCode));
        if (com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotNull(value)) {
            return value.getCityCode();
        }
        return null;
    }

    /**
     * 批量新增定价
     *
     * @param boList pricing集合
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBoList(List<OprPriceDetailBo> boList) {
        List<OprPriceDetail> pricingList = boList.stream()
                .map(bo -> {
                    OprPriceDetail pricing = MapstructUtils.convert(bo, OprPriceDetail.class);
                    return pricing;
                })
                .collect(Collectors.toList());
        return baseMapper.insertBatch(pricingList);
    }

    /**
     * 批量更新价模版详情
     *
     * @param boList pricing集合
     * @return 是否新增成功
     */
    @Override
    public Boolean updateBatch(List<OprPriceDetailBo> boList) {
        List<OprPriceDetail> pricingList = boList.stream()
                .map(bo -> {
                    OprPriceDetail pricing = MapstructUtils.convert(bo, OprPriceDetail.class);
                    return pricing;
                })
                .collect(Collectors.toList());
        return baseMapper.insertOrUpdateBatch(pricingList);
    }

    @Override
    public List<OprPriceDetailVo> queryByPriceId(Long priceId) {
        LambdaQueryWrapper<OprPriceDetail> lqw = new LambdaQueryWrapper<>();
        lqw.eq(OprPriceDetail::getPriceId, priceId);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public void deleteByPriceId(Long id) {
        LambdaQueryWrapper<OprPriceDetail> oprPriceDetailLambdaQueryWrapper = new LambdaQueryWrapper<>();
        oprPriceDetailLambdaQueryWrapper.eq(OprPriceDetail::getPriceId, id);
        baseMapper.delete(oprPriceDetailLambdaQueryWrapper);
    }

    @Override
    public Boolean remove(@NotEmpty(message = "主键不能为空") Long[] ids) {
        // 删除缓存信息需要查询是否有价格被关联 todo需要优化

        return this.deleteWithValidByIds(Arrays.asList(ids), true);
    }

    @Override
    public Boolean updateEstimateRecord(EstimateRecordDTO estimateRecord) {
        // 根据估价key获取缓存的key
        String key = OprCacheKeyEnum.OPR_ESTIMATE_RECORD_KEY.create(estimateRecord.getEstimateKey());

        // 如果缓存中没有该key，则抛出异常
        if (!RedisUtils.hasKey(key)) {
            throw new ServiceException("估价key异常!");
        }

        // 获取价格波动率配置
        String inviteReward = configService.selectValueByKey(OperateCacheConstants.PROXY_ORDER_PRICE_FLUCTUATION_RATE);

        // 如果配置值存在，进行价格浮动计算
        if (StringUtils.isNotBlank(inviteReward)) {
            // 将价格波动率转换为浮动系数
            Double div = ArithUtils.div(Double.parseDouble(inviteReward), 100);

            // 从缓存中获取估价记录
            OprEstimateRecordCacheVo cacheVo = RedisUtils.getCacheObject(key);

            // 根据产品Code修改价格
            Optional<PriceDto> matchedPrice = Optional.ofNullable(cacheVo.getPrice())
                    .orElse(Collections.emptyList()) // 避免空指针异常
                    .stream()
                    .filter(p -> p.getProductCode().equals(estimateRecord.getProductCode()))
                    .findFirst();

            // 如果没有找到匹配的价格，抛出异常
            if (!matchedPrice.isPresent()) {
                throw new ServiceException("未找到匹配的产品价格!");
            }

            // 获取匹配的价格信息
            PriceDto priceDto = matchedPrice.get();
            Long totalPrice = priceDto.getTotalPrice();

            // 计算价格浮动范围
            Double minPrice = ArithUtils.mul(totalPrice, 1 - div); // 最低价格
            Double maxPrice = ArithUtils.mul(totalPrice, 1 + div); // 最高价格

            // 获取更新后的价格
            Long price = estimateRecord.getPrice();

            // 校验价格是否在浮动范围内
            if (price < minPrice || price > maxPrice) {
                throw new ServiceException("价格浮动范围超出范围!");
            }

            // 更新缓存中的价格
            cacheVo.getPrice().stream()
                    .filter(p -> p.getProductCode().equals(estimateRecord.getProductCode()))
                    .findFirst()
                    .ifPresent(p -> p.setCalculatePrice(estimateRecord.getPrice()));

            // 将更新后的缓存数据重新写回Redis
            RedisUtils.setCacheObject(key, cacheVo);
        } else {
            // 如果价格浮动率配置不存在，抛出异常
            throw new ServiceException("修改价格异常!");
        }
        return true;
    }

    @Override
    public OprDrvCalculateVo getPriceKeyByIncome(EstimateRecordDTO estimateRecord) {
        if (estimateRecord != null) {
            // 根据估价key获取缓存的key
            String key = OprCacheKeyEnum.OPR_ESTIMATE_RECORD_KEY.create(estimateRecord.getEstimateKey());

            // 如果缓存中没有该key，则抛出异常
            if (!RedisUtils.hasKey(key)) {
                throw new ServiceException("估价key异常!");
            }

            RemoteDriverVo driverInfoById = powCacheManager.getDriverInfoById(LoginHelper.getUserId());
            if (ObjectUtils.isNull(driverInfoById)) {
                throw new ServiceException("司机信息异常!");
            }
            RemoteAgentVo agentInfoById = powCacheManager.getAgentInfoById(driverInfoById.getAgentId());
            if (ObjectUtils.isNull(agentInfoById)) {
                throw new ServiceException("代理商信息异常!");
            }
            BigDecimal technicalFeeRatio = agentInfoById.getTechnicalFeeRatio();


            if (ObjectUtils.isNotNull(technicalFeeRatio)) {
                BigDecimal subtract = new BigDecimal(100).subtract(technicalFeeRatio);
                Double div = ArithUtils.div(subtract.doubleValue(), 100);
                // 从缓存中获取估价记录
                OprEstimateRecordCacheVo cacheVo = RedisUtils.getCacheObject(key);

                // 根据产品Code修改价格
                Optional<PriceDto> matchedPrice = Optional.ofNullable(cacheVo.getPrice())
                        .orElse(Collections.emptyList()) // 避免空指针异常
                        .stream()
                        .filter(p -> p.getProductCode().equals(estimateRecord.getProductCode()))
                        .findFirst();

                // 如果没有找到匹配的价格，抛出异常
                if (!matchedPrice.isPresent()) {
                    throw new ServiceException("未找到匹配的产品价格!");
                }

                // 获取匹配的价格信息
                PriceDto priceDto = matchedPrice.get();
                Long calculatePrice = priceDto.getCalculatePrice();
                Double income = ArithUtils.mul(calculatePrice, div);
                Long incomeLong = income.longValue();
                Long techServiceFee = ArithUtils.sub(calculatePrice, incomeLong);
                //订单价格
                priceDto.setTotalPrice(calculatePrice);
                //司机收益
                priceDto.setCalculatePrice(incomeLong);
                //技术服务费
                priceDto.setAddPrice(techServiceFee);
                OprDrvCalculateVo oprDrvCalculateVo = new OprDrvCalculateVo();
                oprDrvCalculateVo.setPriceDto(priceDto);
                oprDrvCalculateVo.setEstimateKey(estimateRecord.getEstimateKey());
                return oprDrvCalculateVo;
            } else {
                // 如果价格浮动率配置不存在，抛出异常
                throw new ServiceException("查询司机收益价格异常!");
            }

        }
        return null;
    }

}
