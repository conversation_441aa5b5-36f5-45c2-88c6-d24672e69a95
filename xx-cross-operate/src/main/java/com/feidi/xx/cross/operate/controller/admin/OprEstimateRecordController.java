package com.feidi.xx.cross.operate.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.StopWatch;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.operate.domain.bo.OprEstimateRecordBo;
import com.feidi.xx.cross.operate.domain.vo.OprEstimateRecordVo;
import com.feidi.xx.cross.operate.service.IOprEstimateRecordService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 后台 - 询价记录
 * 前端访问路由地址为:/operate/estimateRecord
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/estimateRecord")
public class OprEstimateRecordController extends BaseController {

    private final IOprEstimateRecordService oprEstimateRecordService;

    /**
     * 查询询价记录列表
     */
    @SaCheckPermission("operate:estimateRecord:list")
    @GetMapping("/list")
    public TableDataInfo<OprEstimateRecordVo> list(OprEstimateRecordBo bo, PageQuery pageQuery) {
        return oprEstimateRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出询价记录列表
     */
    @SaCheckPermission("operate:estimateRecord:export")
    @Log(title = "询价记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @Download(name = "无效估价记录", module = ModuleConstants.OPERATE, mode = "no")
    public Object export(@RequestBody OprEstimateRecordBo bo, HttpServletResponse response) {
        bo.setPlatformCode(PlatformCodeEnum.SELF.getCode());
        StopWatch stopWatch = new StopWatch();

        stopWatch.start("查询数据");
        List<OprEstimateRecordVo> list = oprEstimateRecordService.queryList(bo);
        log.debug( "数据记录总数 {}", list.size());
        stopWatch.stop();

        stopWatch.start("导出Excel");
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(list, "询价记录", OprEstimateRecordVo.class, outputStream);
        stopWatch.stop();

        log.info("导出询价记录耗时统计：\n{}", stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        return outputStream.toByteArray();
    }
    /**
     * 获取询价记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("operate:estimateRecord:query")
    @GetMapping("/{id}")
    public R<OprEstimateRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(oprEstimateRecordService.queryById(id));
    }

    /**
     * 新增询价记录
     */
    @SaCheckPermission("operate:estimateRecord:add")
    @Log(title = "询价记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OprEstimateRecordBo bo) {
        return toAjax(oprEstimateRecordService.insertByBo(bo));
    }

    /**
     * 修改询价记录
     */
    @SaCheckPermission("operate:estimateRecord:edit")
    @Log(title = "询价记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OprEstimateRecordBo bo) {
        return toAjax(oprEstimateRecordService.updateByBo(bo));
    }

    /**
     * 删除询价记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("operate:estimateRecord:remove")
    @Log(title = "询价记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(oprEstimateRecordService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 删除询价记录
     */
    @RepeatSubmit(interval = 30000)
    @SaCheckPermission("operate:estimateRecord:remove")
    @GetMapping("/deleteRecord")
    public R<Void> deleteRecord(@RequestParam String deleteDate) {
        return toAjax(oprEstimateRecordService.deleteRecord(deleteDate));
    }
}
