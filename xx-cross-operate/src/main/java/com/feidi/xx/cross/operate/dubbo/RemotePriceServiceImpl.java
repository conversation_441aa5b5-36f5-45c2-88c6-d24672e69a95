package com.feidi.xx.cross.operate.dubbo;

import cn.hutool.core.bean.BeanUtil;

import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.operate.api.RemotePriceService;
import com.feidi.xx.cross.operate.api.domain.price.bo.RemoteCalculateBo;
import com.feidi.xx.cross.operate.api.domain.price.vo.RemoteCalculateVo;
import com.feidi.xx.cross.operate.api.domain.price.vo.RemotePriceVo;
import com.feidi.xx.cross.operate.domain.OprPrice;
import com.feidi.xx.cross.operate.domain.dto.OprCalculateDTO;
import com.feidi.xx.cross.operate.domain.vo.OprCalculateVo;
import com.feidi.xx.cross.operate.mapper.OprPriceMapper;
import com.feidi.xx.cross.operate.service.IOprPriceDetailService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 市内定价服务接口实现类
 *
 * <AUTHOR>
 * @date 2024/9/7
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemotePriceServiceImpl implements RemotePriceService {

    private final IOprPriceDetailService pricingService;

    private final OprPriceMapper priceMapper;
    /**
     * 计算价格
     *
     * @param remoteCalculateBo 计算价格参数
     * @return
     */
    @Override
    public  RemoteCalculateVo calculatePrice(RemoteCalculateBo remoteCalculateBo) {
        OprCalculateDTO calculateDTO = BeanUtil.copyProperties(remoteCalculateBo, OprCalculateDTO.class);
        OprCalculateVo oprCalculateVo = pricingService.calculatePrice(calculateDTO);

        RemoteCalculateVo remoteCalculateVo = new RemoteCalculateVo();
        remoteCalculateVo.setPriceDtoList(oprCalculateVo.getPriceDtoList());
        remoteCalculateVo.setEstimateKey(oprCalculateVo.getEstimateKey());
        return remoteCalculateVo;
    }

    @Override
    public RemotePriceVo getPriceInfoByPriceId(Long priceId) {
        OprPrice oprPrice = priceMapper.selectById(priceId);
        return BeanUtils.copyProperties(oprPrice, RemotePriceVo.class);
    }
}
