package com.feidi.xx.cross.operate.controller.agent;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.operate.domain.bo.OprPlatformBo;
import com.feidi.xx.cross.operate.domain.vo.OprPlatformVo;
import com.feidi.xx.cross.operate.service.IOprPlatformService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 后台 - 平台
 * 前端访问路由地址为:/operate/agt/platform
 *
 * <AUTHOR>
 * @date 2023-03-04
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/platform")
public class AgtPlatformController extends BaseController {

    private final IOprPlatformService platformService;

    /**
     * 查询平台列表-分页
     */
    @Log(title = "价格模版", businessType = BusinessType.OTHER)
    @GetMapping("/list")
    public TableDataInfo<OprPlatformVo> list(OprPlatformBo bo, PageQuery pageQuery) {
        return platformService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询平台列表-全部
     */
    @Log(title = "价格模版", businessType = BusinessType.OTHER)
    @GetMapping("/list/all")
    public R<List<OprPlatformVo>> list(OprPlatformBo bo) {
        return R.ok(platformService.queryList(bo, OprPlatformVo.class));
    }

}
