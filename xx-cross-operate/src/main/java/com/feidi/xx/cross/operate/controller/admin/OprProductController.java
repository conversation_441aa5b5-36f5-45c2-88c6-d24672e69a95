package com.feidi.xx.cross.operate.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.operate.domain.vo.ExportVo;
import com.feidi.xx.cross.operate.domain.bo.OprProductBo;
import com.feidi.xx.cross.operate.domain.vo.OprProductVo;
import com.feidi.xx.cross.operate.service.IOprProductService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 产品
 * 前端访问路由地址为:/operate/product
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/product")
public class OprProductController extends BaseController {

    private final IOprProductService oprProductService;

    /**
     * 查询产品列表
     */
    @SaCheckPermission("operate:product:list")
    @GetMapping("/list")
    public TableDataInfo<OprProductVo> list(OprProductBo bo, PageQuery pageQuery) {
        return oprProductService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询产品列表-全部
     */
    @SaCheckPermission("operate:product:list")
    @GetMapping("/list/all")
    public R<List<OprProductVo>> list(OprProductBo bo) {
        return R.ok(oprProductService.queryList(bo));
    }

    /**
     * 导出产品列表
     */
    @SaCheckPermission("operate:product:export")
    @Log(title = "产品", businessType = BusinessType.EXPORT)
    @Download(name="产品",module = ModuleConstants.OPERATE)
    @PostMapping("/export")
    public void export(OprProductBo bo, HttpServletResponse response) {
        List<OprProductVo> list = oprProductService.queryList(bo);
        ExcelUtil.exportExcel(list, "产品", OprProductVo.class, response);
    }

    /**
     * 获取产品详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("operate:product:query")
    @GetMapping("/{id}")
    public R<OprProductVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(oprProductService.queryById(id));
    }

    /**
     * 新增产品
     */
    @SaCheckPermission("operate:product:add")
    @Log(title = "产品", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OprProductBo bo) {
        return toAjax(oprProductService.insertByBo(bo));
    }

    /**
     * 修改产品
     */
    @SaCheckPermission("operate:product:edit")
    @Log(title = "产品", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OprProductBo bo) {
        return toAjax(oprProductService.updateByBo(bo));
    }

    /**
     * 删除产品
     *
     * @param ids 主键串
     */
    @SaCheckPermission("operate:product:remove")
    @Log(title = "产品", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(oprProductService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 修改产品状态
     */
    @SaCheckPermission("operate:business:edit")
    @Log(title = "产品", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @GetMapping("/{id}/{status}")
    public R<Void> editStatus(@PathVariable Long id, @PathVariable String status) {
        return toAjax(oprProductService.updateStatus(id, status));
    }
}
