package com.feidi.xx.cross.operate.service;

import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.cross.operate.domain.OprPlatform;
import com.feidi.xx.cross.operate.domain.bo.OprPlatformBo;
import com.feidi.xx.cross.operate.domain.vo.OprPlatformVo;

import java.util.Collection;
import java.util.List;

/**
 * 平台Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IOprPlatformService {

    /**
     * 查询平台
     *
     * @param id 主键
     * @return 平台
     */
    OprPlatformVo queryById(Long id);

    /**
     * 分页查询平台列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 平台分页列表
     */
    TableDataInfo<OprPlatformVo> queryPageList(OprPlatformBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的平台列表
     *
     * @param bo 查询条件
     * @return 平台列表
     */
    <T> List<T> queryList(OprPlatformBo bo, Class<T> clazz);

    /**
     * 新增平台
     *
     * @param bo 平台
     * @return 是否新增成功
     */
    Boolean insertByBo(OprPlatformBo bo);

    /**
     * 修改平台
     *
     * @param bo 平台
     * @return 是否修改成功
     */
    Boolean updateByBo(OprPlatformBo bo);

    /**
     * 校验并批量删除平台信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据编码查询平台信息
     *
     * @param code 平台编码
     * @return 平台信息
     */
    OprPlatform queryByCode(String code);

    /**
     * 根据平台编码获取平台信息
     *
     * @return
     */
    OprPlatformVo queryPlatformByCode(String platformCode);
}
