package com.feidi.xx.cross.operate.service;

import com.feidi.xx.cross.operate.domain.OprChannel;
import com.feidi.xx.cross.operate.domain.vo.OprChannelVo;
import com.feidi.xx.cross.operate.domain.bo.OprChannelBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 渠道Service接口
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
public interface IOprChannelService {

    /**
     * 查询渠道
     *
     * @param id 主键
     * @return 渠道
     */
    OprChannelVo queryById(String id);

    /**
     * 分页查询渠道列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 渠道分页列表
     */
    TableDataInfo<OprChannelVo> queryPageList(OprChannelBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的渠道列表
     *
     * @param bo 查询条件
     * @return 渠道列表
     */
    List<OprChannelVo> queryList(OprChannelBo bo);

    /**
     * 新增渠道
     *
     * @param bo 渠道
     * @return 是否新增成功
     */
    Boolean insertByBo(OprChannelBo bo);

    /**
     * 修改渠道
     *
     * @param bo 渠道
     * @return 是否修改成功
     */
    Boolean updateByBo(OprChannelBo bo);

    /**
     * 校验并批量删除渠道信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
