package com.feidi.xx.cross.operate.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.cross.operate.domain.OprLineDetail;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 线路详情视图对象 opr_line_detail
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OprLineDetail.class)
public class OprLineDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 围栏ID
     */
    private Long fenceId;

    /**
     * 线路
     */
    @ExcelProperty(value = "线路")
    private Long lineId;

    /**
     * 省
     */
    @ExcelProperty(value = "省")
    private Long provinceId;

    /**
     * 城市
     */
    @ExcelProperty(value = "城市")
    private Long cityId;

    /**
     * 区域
     */
    @ExcelProperty(value = "区域")
    private Long districtId;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "StartEndEnum")
    private String direction;


}
