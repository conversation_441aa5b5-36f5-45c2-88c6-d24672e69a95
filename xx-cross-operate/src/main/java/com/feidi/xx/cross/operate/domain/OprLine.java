package com.feidi.xx.cross.operate.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 线路对象 opr_line
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("opr_line")
public class OprLine extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 起点省
     */
    private Long startProvinceId;

    /**
     * 起点城市
     */
    private Long startCityId;

    /**
     * 起点城市编码
     */
    private String startCityCode;

    /**
     * 起点定价
     */
    private Long startPricingId;

    /**
     * 终点省
     */
    private Long endProvinceId;

    /**
     * 终点城市
     */
    private Long endCityId;

    /**
     * 终点城市编码
     */
    private String endCityCode;

    /**
     * 终点定价
     */
    private Long endPricingId;

    /**
     * 类型
     */
    private String type;

    /**
     * 状态
     */
    private String status;

    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
