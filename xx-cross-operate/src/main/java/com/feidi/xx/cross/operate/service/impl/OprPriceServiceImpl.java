package com.feidi.xx.cross.operate.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import com.feidi.xx.cross.operate.domain.bo.OprPriceDetailBo;
import com.feidi.xx.cross.operate.domain.vo.OprPriceDetailVo;
import com.feidi.xx.cross.operate.service.IOprCityService;
import com.feidi.xx.cross.operate.service.IOprLineService;
import com.feidi.xx.cross.operate.service.IOprPriceDetailService;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.feidi.xx.cross.operate.domain.bo.OprPriceBo;
import com.feidi.xx.cross.operate.domain.vo.OprPriceVo;
import com.feidi.xx.cross.operate.domain.OprPrice;
import com.feidi.xx.cross.operate.mapper.OprPriceMapper;
import com.feidi.xx.cross.operate.service.IOprPriceService;
import com.feidi.xx.common.core.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 价格模版Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-15
 */
@RequiredArgsConstructor
@Service
public class OprPriceServiceImpl implements IOprPriceService {

    private final OprPriceMapper baseMapper;

    private final IOprPriceDetailService oprPriceDetailService;

    private final IOprCityService oprCityService;

    private final IOprLineService oprLineService;

    private final OprCacheManager cacheManager;

    /**
     * 查询价格模版
     *
     * @param id 主键
     * @return 价格模版
     */
    @Override
    public OprPriceVo queryById(Long id){
        OprPriceVo oprPriceVo = baseMapper.selectVoById(id);
        //根据价格模版id查询价格详情
        oprPriceVo.setOprPriceDetailVoList(oprPriceDetailService.queryByPriceId(oprPriceVo.getId()));
        return oprPriceVo;
    }

    /**
     * 分页查询价格模版列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 价格模版分页列表
     */
    @Override
    public TableDataInfo<OprPriceVo> queryPageList(OprPriceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OprPrice> lqw = buildQueryWrapper(bo);
        Page<OprPriceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        //todo 统计关联路线数量
        List<OprPriceVo> records = result.getRecords();
        if (records != null) {
            records.forEach(item -> {
                //TODO 做一些数据处理
                //根据价格模版id查询价格详情
                item.setCityNumber(oprCityService.countByPriceId(item.getId()));
                item.setLineNumber(oprLineService.countByPriceId(item.getId()));
                item.setOprPriceDetailVoList(oprPriceDetailService.queryByPriceId(item.getId()));
            });
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的价格模版列表
     *
     * @param bo 查询条件
     * @return 价格模版列表
     */
    @Override
    public List<OprPriceVo> queryList(OprPriceBo bo) {
        LambdaQueryWrapper<OprPrice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OprPrice> buildQueryWrapper(OprPriceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OprPrice> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), OprPrice::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getPricingType()), OprPrice::getPricingType, bo.getPricingType());
        lqw.eq(StringUtils.isNotBlank(bo.getHolidayTemplate()), OprPrice::getHolidayTemplate, bo.getHolidayTemplate());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), OprPrice::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getTemplate()), OprPrice::getTemplate, bo.getTemplate());
        //创建时间
        lqw.ge(bo.getStartCreateTime() != null, OprPrice::getCreateTime, bo.getStartCreateTime());
        lqw.le(bo.getEndCreateTime() != null, OprPrice::getCreateTime, bo.getEndCreateTime());
        return lqw;
    }

    /**
     * 新增价格模版
     *
     * @param bo 价格模版
     * @return 是否新增成功
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean insertByBo(OprPriceBo bo) {
        OprPrice add = MapstructUtils.convert(bo, OprPrice.class);
        validEntityBeforeSave(add);
        //
        boolean flag = baseMapper.insert(add) > 0;
        //将价格Id放入详情
        bo.getOprPriceDetailBoList().forEach(oprPriceDetailBo -> {
            oprPriceDetailBo.setPriceId(add.getId());
        });
        oprPriceDetailService.insertByBoList(bo.getOprPriceDetailBoList());
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改价格模版
     *
     * @param bo 价格模版
     * @return 是否修改成功
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean updateByBo(OprPriceBo bo) {
        OprPrice update = MapstructUtils.convert(bo, OprPrice.class);
        validEntityBeforeSave(update);

        /**
         * 删除价格详情
         *
         */
        oprPriceDetailService.deleteByPriceId(update.getId());
        List<OprPriceDetailBo> oprPriceDetailBoList = bo.getOprPriceDetailBoList();
        oprPriceDetailBoList.stream()
                .forEach(detail -> detail.setPriceId(update.getId()));
        oprPriceDetailService.insertByBoList(bo.getOprPriceDetailBoList());
        cacheManager.deletePriceCache(update.getId());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OprPrice entity){
// 判断是否需要校验唯一性（新增或名称发生变化时）
        boolean needCheckUnique = ObjectUtil.isNull(entity.getId());
        if (!needCheckUnique) {
            OprPriceVo oprPriceVo = this.queryById(entity.getId());
            needCheckUnique = !oprPriceVo.getName().equals(entity.getName());
        }
        if (needCheckUnique) {
            // 校验名称唯一性
            boolean exists = baseMapper.selectCount(
                    Wrappers.lambdaQuery(OprPrice.class)
                            .eq(OprPrice::getName, entity.getName())
            ) > 0;
            if (exists) {
                throw new ServiceException("价格模版名称重复");
            }
        }


    }

    /**
     * 校验并批量删除价格模版信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
            ids.forEach(id -> {
                //校验价格模版是否关联路线 或者城市
                if(oprCityService.countByPriceId(id) > 0 || oprLineService.countByPriceId(id) > 0){
                    throw new ServiceException("价格模版关联路线或者城市，无法删除");
                }
            });
        }
        ids.forEach(
                id -> {
                    //删除价格详情
                    cacheManager.deletePriceCache(id);
                    oprPriceDetailService.deleteByPriceId(id);
                }
        );

        return baseMapper.deleteByIds(ids) > 0;
    }
}
