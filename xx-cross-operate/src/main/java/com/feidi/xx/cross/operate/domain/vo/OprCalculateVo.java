package com.feidi.xx.cross.operate.domain.vo;

import com.feidi.xx.cross.market.api.domain.couponGrant.RemoteCouponGrantVo;
import com.feidi.xx.cross.operate.api.domain.price.dto.PriceDto;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 询价返回值
 */
@Data
public class OprCalculateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 询价结果
     */
    private List<PriceDto> priceDtoList;

    /**
     * 询价KEY
     */
    private String estimateKey;

}
