package com.feidi.xx.cross.operate.timer;

import cn.hutool.core.date.DateUtil;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.cross.operate.service.IOprEstimateRecordService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 询价记录删除定时任务
 *
 * <AUTHOR>
 * @date 2025/8/8
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EstimateRecordDeleteJob {

    private final IOprEstimateRecordService oprEstimateRecordService;

    @XxlJob("estimateRecordDeleteJob")
    public void estimateRecordDeleteJob() {
        if (log.isInfoEnabled()) {
            log.info("============== 询价记录删除定时器开始执行 ==============");
        }

        // 删除的限制时间，默认45天
        Date limitDate = DateUtil.offsetDay(DateUtils.getEndDayOfYesterDay(), -45);

        String jobParam = XxlJobHelper.getJobParam();

        String deleteDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, limitDate);
        if (StringUtils.isNotBlank(jobParam)) {
            deleteDate = jobParam + " 23:59:59";
        }
        log.info("删除时间：{}", deleteDate);

        if (DateUtils.parseDate(deleteDate).after(limitDate)) {
            log.info("删除时间：【{}】不能早于：【{}】", deleteDate, DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, limitDate));
            return;
        }

        oprEstimateRecordService.deleteEstimateRecord(deleteDate);

        if (log.isInfoEnabled()) {
            log.info("============== 询价记录删除定时器执行结束 ==============");
        }
    }
}
