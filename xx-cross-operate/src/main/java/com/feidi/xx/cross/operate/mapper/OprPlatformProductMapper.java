package com.feidi.xx.cross.operate.mapper;

import com.feidi.xx.cross.operate.domain.OprPlatformProduct;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.operate.domain.vo.OprPlatformProductVo;
import org.apache.ibatis.annotations.Param;

/**
 * 平台产品Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface OprPlatformProductMapper extends BaseMapperPlus<OprPlatformProduct, OprPlatformProductVo> {

    /**
     * 根据平台编码删除产品信息
     *
     * @param tenantId 租户id
     * @param platformCode 平台编码
     * @return  删除数量
     */
    int deleteByPlatformCode(@Param("tenantId") String tenantId, @Param("platformCode") String platformCode);
}
