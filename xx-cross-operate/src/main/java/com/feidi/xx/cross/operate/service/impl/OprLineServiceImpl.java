package com.feidi.xx.cross.operate.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.cache.system.SysDistrictCacheVo;
import com.feidi.xx.common.core.enums.*;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.operate.enums.OprCacheKeyEnum;
import com.feidi.xx.cross.common.enums.operate.LabelTypeEnum;
import com.feidi.xx.cross.common.enums.operate.LineTypeEnum;
import com.feidi.xx.cross.operate.api.domain.line.vo.RemoteLineVo;
import com.feidi.xx.cross.operate.domain.OprLabel;
import com.feidi.xx.cross.operate.domain.OprLine;
import com.feidi.xx.cross.operate.domain.OprLineDetail;
import com.feidi.xx.cross.operate.domain.bo.OprLineBo;
import com.feidi.xx.cross.operate.domain.vo.OprFenceVo;
import com.feidi.xx.cross.operate.domain.vo.OprFilterVo;
import com.feidi.xx.cross.operate.domain.vo.OprLineVo;
import com.feidi.xx.cross.operate.mapper.OprLabelMapper;
import com.feidi.xx.cross.operate.mapper.OprLineDetailMapper;
import com.feidi.xx.cross.operate.mapper.OprLineMapper;
import com.feidi.xx.cross.operate.service.IOprFenceService;
import com.feidi.xx.cross.operate.service.IOprFilterService;
import com.feidi.xx.cross.operate.service.IOprLineService;
import com.feidi.xx.cross.power.api.RemoteAgentLineService;
import com.feidi.xx.cross.power.api.domain.agent.vo.RemoteAgentLineVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 线路Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@RequiredArgsConstructor
@Service
public class OprLineServiceImpl implements IOprLineService {

    private final OprLineMapper baseMapper;
    private final OprLabelMapper labelMapper;
    private final OprLineDetailMapper detailMapper;
    private final IOprFilterService oprFilterService;
    private final IOprFenceService oprFenceService;

    @DubboReference
    private final RemoteAgentLineService remoteAgentLineService;


    private final ScheduledExecutorService scheduledExecutorService;

    /**
     * 查询线路
     *
     * @param id 主键
     * @return 线路
     */
    @Override
    public OprLineVo queryById(Long id) {
        OprLineVo oprLineVo = baseMapper.selectVoById(id);
        // 绑定过滤规则  //todo 过滤规则相关json
        this.bindLineFilter(oprLineVo);
        return oprLineVo;
    }

    /**
     * 绑定线路过滤数据
     *
     * @param lineVo
     */
    private void bindLineFilter(OprLineVo lineVo) {
        OprFilterVo oprFilterVo = oprFilterService.selectByLineId(lineVo.getId());
    }

    /**
     * 分页查询线路列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 线路分页列表
     */
    @Override
    public TableDataInfo<OprLineVo> queryPageList(OprLineBo bo, PageQuery pageQuery) {
        //初始化
        if (ObjectUtils.isNull(pageQuery.getIsAsc())) {
            pageQuery.setIsAsc("desc");
        }
        if (ObjectUtils.isNull(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("create_time");
        }
        LambdaQueryWrapper<OprLine> lqw = buildQueryWrapper(bo);
        Page<OprLineVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<OprLineVo> records = result.getRecords();
        if (records != null) {
            for (OprLineVo record : records) {
                LambdaQueryWrapper<OprLineDetail> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(OprLineDetail::getLineId, record.getId());
                List<OprLineDetail> oprLineDetails = detailMapper.selectList(wrapper);
                processDistricts(record, oprLineDetails);
            }
        }
        // 补充分配信息
        return TableDataInfo.build(result);
    }

    private void processDistricts(OprLineVo record, List<OprLineDetail> oprLineDetails) {
        if (LineTypeEnum.REGION.getCode().equals(record.getType())) {
            record.setStartDistricts(
                    oprLineDetails.stream()
                            .filter(detail -> StartEndEnum.START.getCode().equals(detail.getDirection()))
                            .filter(detail -> detail.getAdCode() != null)
                            .map(OprLineDetail::getAdCode)
                            .map(adCode -> (SysDistrictCacheVo) RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_AD_CODE_KEY.create(), adCode))
                            .filter(vo -> vo != null && vo.getName() != null)
                            .map(SysDistrictCacheVo::getName)
                            .collect(Collectors.toList())
            );
            record.setEndDistricts(
                    oprLineDetails.stream()
                            .filter(detail -> StartEndEnum.END.getCode().equals(detail.getDirection()))
                            .filter(detail -> detail.getAdCode() != null)
                            .map(OprLineDetail::getAdCode)
                            .map(adCode -> (SysDistrictCacheVo) RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_AD_CODE_KEY.create(), adCode))
                            .filter(vo -> vo != null && vo.getName() != null)
                            .map(SysDistrictCacheVo::getName)
                            .collect(Collectors.toList())
            );
        } else {
            record.setStartDistricts(
                    oprLineDetails.stream()
                            .filter(detail -> StartEndEnum.START.getCode().equals(detail.getDirection()))
                            .map(OprLineDetail::getFenceId)
                            .map(oprFenceService::queryById)
                            .filter(fence -> fence != null && fence.getName() != null)
                            .map(OprFenceVo::getName)
                            .collect(Collectors.toList())
            );

            record.setEndDistricts(
                    oprLineDetails.stream()
                            .filter(detail -> StartEndEnum.END.getCode().equals(detail.getDirection()))
                            .map(OprLineDetail::getFenceId)
                            .map(oprFenceService::queryById)
                            .filter(fence -> fence != null && fence.getName() != null)
                            .map(OprFenceVo::getName)
                            .collect(Collectors.toList())
            );
        }
    }

    /**
     * 查询符合条件的线路列表
     *
     * @param bo 查询条件
     * @return 线路列表
     */
    @Override
    public List<OprLineVo> queryList(OprLineBo bo) {
        LambdaQueryWrapper<OprLine> lqw = buildQueryWrapper(bo);
        List<OprLineVo> lineVos = baseMapper.selectVoList(lqw);
        // 补充分配信息
        fillAssignInfo(lineVos);
        return lineVos;
    }

    /**
     * 获取线路分配信息
     * @param lineVos
     */
    private void fillAssignInfo(List<OprLineVo> lineVos) {
        UserTypeEnum userType = LoginHelper.getUserType();
        Long userId = LoginHelper.getAgentId();
        if (CollUtil.isNotEmpty(lineVos)) {
            List<RemoteAgentLineVo> agentLines = remoteAgentLineService.getLineByAgentIds(null);
            if (UserTypeEnum.AGENT_USER.equals(userType)) {
                // 如果是代理商，不展示相关信息
                agentLines = agentLines.stream().filter(e -> !e.getAgentId().equals(userId)).toList();
            }
            Map<Long, RemoteAgentLineVo> map = agentLines.stream()
                    .collect(Collectors.toMap(RemoteAgentLineVo::getLineId, Function.identity(), (a, b) -> a));
            for (OprLineVo lineVo : lineVos) {
                RemoteAgentLineVo agentLineVo = map.get(lineVo.getId());
                if (agentLineVo != null) {
                    lineVo.setAssignInfo(new OprLineVo.AssignInfo(
                            agentLineVo.getAgentId(),
                            agentLineVo.getParentId()
                    ));
                }
            }
        }
    }

    private LambdaQueryWrapper<OprLine> buildQueryWrapper(OprLineBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OprLine> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), OprLine::getName, bo.getName());
        lqw.eq(bo.getStartProvinceId() != null, OprLine::getStartProvinceId, bo.getStartProvinceId());
        lqw.eq(bo.getStartCityId() != null, OprLine::getStartCityId, bo.getStartCityId());
        lqw.eq(StringUtils.isNotBlank(bo.getStartCityCode()), OprLine::getStartCityCode, bo.getStartCityCode());
        lqw.eq(bo.getStartPricingId() != null, OprLine::getStartPricingId, bo.getStartPricingId());
        lqw.eq(bo.getEndProvinceId() != null, OprLine::getEndProvinceId, bo.getEndProvinceId());
        lqw.eq(bo.getEndCityId() != null, OprLine::getEndCityId, bo.getEndCityId());
        lqw.eq(StringUtils.isNotBlank(bo.getEndCityCode()), OprLine::getEndCityCode, bo.getEndCityCode());
        lqw.eq(bo.getEndPricingId() != null, OprLine::getEndPricingId, bo.getEndPricingId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), OprLine::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), OprLine::getStatus, bo.getStatus());
        lqw.in(CollUtils.isNotEmpty(bo.getIds()), OprLine::getId, bo.getIds());
        return lqw;
    }

    /**
     * 新增线路
     *
     * @param bo 线路
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OprLineBo bo) {
        OprLine add = MapstructUtils.convert(bo, OprLine.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            saveLabels(bo.getLabels());

            // 添加缓存
            scheduledExecutorService.schedule(() -> addCache(add), 0, TimeUnit.SECONDS);
        }
        return flag;
    }

    /**
     * 修改线路
     *
     * @param bo 线路
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(OprLineBo bo) {
        OprLine update = MapstructUtils.convert(bo, OprLine.class);
        validEntityBeforeSave(update);
        // 先判断状态是否更新
        boolean ret = updateStatus(bo.getId(), bo.getStatus());

        ret &= baseMapper.updateById(update) > 0;
        saveLabels(bo.getLabels());

        // 添加缓存
        scheduledExecutorService.schedule(() -> addCache(update), 0, TimeUnit.SECONDS);
        return ret;
    }

    @Override
    public Boolean updateStatus(Long id, String status) {
        OprLine oxLine = baseMapper.selectById(id);
        if (!oxLine.getStatus().equals(status) && StatusEnum.getInfoByCode(status) != null) {
            oxLine.setStatus(status);
            boolean ret = baseMapper.updateById(oxLine) > 0;

            if (ret && StatusEnum.DISABLE.getCode().equals(status)) {
                // 禁用线路抢单
                // TODO-NEW 稍后完成
                //remoteRobService.disableLineRob(id);
            }
            // 添加缓存
            scheduledExecutorService.schedule(() -> addCache(oxLine), 0, TimeUnit.SECONDS);
            return ret;
        }
        return true;
    }

    @Override
    public Integer countByPriceId(Long priceId) {
        LambdaQueryWrapper<OprLine> startWrapper = new LambdaQueryWrapper<>();
        startWrapper.eq(OprLine::getStartPricingId, priceId);
        Long startCount = baseMapper.selectCount(startWrapper);
        LambdaQueryWrapper<OprLine> endWrapper = new LambdaQueryWrapper<>();
        endWrapper.eq(OprLine::getEndPricingId, priceId);
        Long EndCount = baseMapper.selectCount(endWrapper);
        Long add = ArithUtils.add(startCount, EndCount);
        return add.intValue();
    }

    @Override
    public List<OprLineVo> queryByPriceId(Long priceId) {
        //根据id去重 返回   List<OprLineVo>
        List<OprLineVo> oprLineVos = baseMapper.selectVoList(Wrappers.<OprLine>lambdaQuery()
                .eq(OprLine::getStartPricingId, priceId)
        );
        oprLineVos.forEach(oprLineVo -> {
            oprLineVo.setDirection(StartEndEnum.START.getCode());
        });
        List<OprLineVo> lineVos = baseMapper.selectVoList(Wrappers.<OprLine>lambdaQuery()
                .eq(OprLine::getEndPricingId, priceId));
        lineVos.forEach(oprLineVo -> {
            oprLineVo.setDirection(StartEndEnum.END.getCode());
        });
        oprLineVos.addAll(lineVos);
        return oprLineVos;
    }

    @Override
    public List<OprLineVo> listByFenceId(Long fenceId) {
        List<OprLineDetail> oprLineDetails = detailMapper.selectList(
                Wrappers.<OprLineDetail>lambdaQuery().eq(OprLineDetail::getFenceId, fenceId)
        );
        // 分组获取 START 和 END 的 lineId
        Map<String, List<Long>> lineIdMap = oprLineDetails.stream()
                .collect(Collectors.groupingBy(
                        OprLineDetail::getDirection,
                        Collectors.mapping(OprLineDetail::getLineId, Collectors.toList())
                ));

        // 获取所有 lineId，避免多次数据库查询
        List<Long> allLineIds = new ArrayList<>();
        allLineIds.addAll(lineIdMap.getOrDefault(StartEndEnum.START.getCode(), Collections.emptyList()));
        allLineIds.addAll(lineIdMap.getOrDefault(StartEndEnum.END.getCode(), Collections.emptyList()));

        if (allLineIds.isEmpty()) {
            return Collections.emptyList(); // 如果没有数据，直接返回，避免无效查询
        }

        // 一次查询所有 lineId
        List<OprLineVo> oprLineVos = baseMapper.selectVoBatchIds(allLineIds);
        // 设置方向
        oprLineVos.forEach(line -> {
            if (lineIdMap.getOrDefault(StartEndEnum.START.getCode(), Collections.emptyList()).contains(line.getId())) {
                line.setDirection(StartEndEnum.START.getCode());
            } else {
                line.setDirection(StartEndEnum.END.getCode());
            }
        });

        return oprLineVos;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OprLine entity) {
        if (entity.getStartPricingId() == null) {
            entity.setStartPricingId(0L);
        }
        if (entity.getEndPricingId() == null) {
            entity.setEndPricingId(0L);
        }
    }

    /**
     * 保存标签
     *
     * @param names
     */
    public void saveLabels(List<String> names) {
        if (CollUtil.isNotEmpty(names)) {
            // 线路标签类型都是订单类型，给订单携带使用
            String type = LabelTypeEnum.ORDER.getCode();
            List<OprLabel> oxLabels = labelMapper.selectList(Wrappers.<OprLabel>lambdaQuery()
                    .in(CollUtil.isNotEmpty(names), OprLabel::getName, names)
                    .eq(StrUtil.isNotBlank(type), OprLabel::getType, type)
            );
            Set<String> origins = oxLabels.stream().map(OprLabel::getName).collect(Collectors.toSet());
            Set<String> newNames = new HashSet<>(names);
            newNames.removeAll(origins);
            if (!newNames.isEmpty()) {
                List<OprLabel> newLabels = newNames.stream().map(name -> new OprLabel(name, type)).collect(Collectors.toList());
                labelMapper.insertBatch(newLabels);
            }
        }
    }

    /**
     * 校验并批量删除线路信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        /*if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;*/

        throw new ServiceException(ResultCode.UNSUPPORTED.getMessage());
    }

    /**
     * 添加缓存
     *
     * @param line 路线信息
     */
    public void addCache(OprLine line) {
        if (ObjectUtils.isNull(line)) {
            return;
        }

        // 缓存KEY
        String cacheCodeKey = OprCacheKeyEnum.OPR_LINE_INFO_KEY.create(line.getId());

        RedisUtils.setCacheObject(cacheCodeKey, BeanUtils.copyProperties(line, RemoteLineVo.class),
                OprCacheKeyEnum.OPR_LINE_INFO_KEY.getDuration());

    }

    /**
     * 删除缓存
     *
     * @param code
     */
    public void deleteCache(String code) {
        RedisUtils.deleteObject(OprCacheKeyEnum.OPR_LINE_INFO_KEY.create(code));
    }
}
