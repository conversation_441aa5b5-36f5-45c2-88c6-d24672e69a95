package com.feidi.xx.cross.operate.service.impl;

import com.feidi.xx.common.core.cache.system.SysDistrictCacheVo;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.enums.SystemCacheKeyEnum;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.operate.api.domain.price.vo.RemotePriceVo;
import com.feidi.xx.cross.operate.domain.OprCity;
import com.feidi.xx.cross.operate.domain.OprPrice;
import com.feidi.xx.cross.operate.domain.bo.OprCityBo;
import com.feidi.xx.cross.operate.domain.vo.OprCityVo;
import com.feidi.xx.cross.operate.mapper.OprCityMapper;
import com.feidi.xx.cross.operate.mapper.OprPriceMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import com.feidi.xx.cross.operate.domain.bo.OprCityPriceBo;
import com.feidi.xx.cross.operate.domain.vo.OprCityPriceVo;
import com.feidi.xx.cross.operate.domain.OprCityPrice;
import com.feidi.xx.cross.operate.mapper.OprCityPriceMapper;
import com.feidi.xx.cross.operate.service.IOprCityPriceService;
import com.feidi.xx.common.core.utils.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 城市价格关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@RequiredArgsConstructor
@Service
public class OprCityPriceServiceImpl implements IOprCityPriceService {

    private final OprCityPriceMapper baseMapper;


    private final OprCityMapper oprCityMapper;

    private final OprCacheManager oprCacheManager;

    /**
     * 查询城市价格关联
     *
     * @param id 主键
     * @return 城市价格关联
     */
    @Override
    public OprCityPriceVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询城市价格关联列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 城市价格关联分页列表
     */
    @Override
    public TableDataInfo<OprCityPriceVo> queryPageList(OprCityPriceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OprCityPrice> lqw = buildQueryWrapper(bo);
        Page<OprCityPriceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的城市价格关联列表
     *
     * @param bo 查询条件
     * @return 城市价格关联列表
     */
    @Override
    public List<OprCityPriceVo> queryList(OprCityPriceBo bo) {
        LambdaQueryWrapper<OprCityPrice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OprCityPrice> buildQueryWrapper(OprCityPriceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OprCityPrice> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCityId() != null, OprCityPrice::getCityId, bo.getCityId());
        lqw.eq(StringUtils.isNotBlank(bo.getCityCode()), OprCityPrice::getCityCode, bo.getCityCode());
        lqw.eq(bo.getPriceId() != null, OprCityPrice::getPriceId, bo.getPriceId());
        return lqw;
    }

    /**
     * 新增城市价格关联
     *
     * @param bo 城市价格关联
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OprCityPriceBo bo) {
        OprCityPrice add = MapstructUtils.convert(bo, OprCityPrice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改城市价格关联
     *
     * @param bo 城市价格关联
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OprCityPriceBo bo) {
        OprCityPrice update = MapstructUtils.convert(bo, OprCityPrice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OprCityPrice entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除城市价格关联信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<OprCityPriceVo> queryAllList(OprCityPriceBo bo) {
        LambdaQueryWrapper<OprCityPrice> lqw = buildQueryWrapper(bo);
        List<OprCityPriceVo> cityPriceVos = baseMapper.selectVoList(lqw);
        for (OprCityPriceVo cityPriceVo : cityPriceVos) {
            OprCity oprCity = oprCityMapper.selectById(cityPriceVo.getCityId());
            if (ObjectUtils.isNotEmpty(oprCity)){
                String s = SystemCacheKeyEnum.SYS_ID_KEY.create();
                String s1 = String.valueOf(oprCity.getProvinceId());
                SysDistrictCacheVo provinceValue = RedisUtils.getCacheMapValue(s, s1);
                if (ObjectUtils.isNotEmpty(provinceValue)){
                    if (provinceValue.getIsDirect().equals("Y")) {
                        cityPriceVo.setCity(provinceValue.getName());
                    }else {
                        SysDistrictCacheVo cacheMapValue = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_CITY_CODE_KEY.create(), oprCity.getCityCode());
                        cityPriceVo.setCity(cacheMapValue.getName());
                    }
                }
                cityPriceVo.setCityStatus(oprCity.getStatus());
                cityPriceVo.setProvinceId(oprCity.getProvinceId());
                cityPriceVo.setCityStatusName(StatusEnum.getInfoByCode(cityPriceVo.getCityStatus()));
                cityPriceVo.setPlatformName(PlatformCodeEnum.getInfoByCode(cityPriceVo.getPlatformCode()));
            }else {
                cityPriceVos.remove(cityPriceVo);
            }
        }
        return cityPriceVos;
    }
}
