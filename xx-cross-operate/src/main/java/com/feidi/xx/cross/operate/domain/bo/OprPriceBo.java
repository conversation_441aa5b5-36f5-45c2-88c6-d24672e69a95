package com.feidi.xx.cross.operate.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.operate.domain.OprPrice;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 价格模版业务对象 opr_price
 *
 * <AUTHOR>
 * @date 2025-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OprPrice.class, reverseConvertGenerate = false)
public class OprPriceBo extends BaseEntity {

    /**
     * 价格模版主表
     */
    private Long id;

    /**
     * 价格模版名称
     */
    @NotBlank(message = "价格模版名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 价格类型[PriceTypeEnum]
     */
    @NotBlank(message = "价格类型[PriceTypeEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pricingType;

    /**
     * 是否有节假日模板
     */
    private String holidayTemplate;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否模板
     */
    private String template;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开始创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date startCreateTime;
    /**
     * 结束创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date endCreateTime;
    /**
     * 价格详情
     */
    private List<OprPriceDetailBo> oprPriceDetailBoList;
}
