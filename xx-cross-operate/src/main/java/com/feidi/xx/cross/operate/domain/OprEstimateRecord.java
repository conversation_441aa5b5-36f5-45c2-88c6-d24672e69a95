package com.feidi.xx.cross.operate.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.common.cache.operate.vo.OprEstimateRecordCacheVo;
import com.feidi.xx.cross.common.mybatis.typehandler.PriceDtoTypeHandler;
import com.feidi.xx.cross.operate.api.domain.price.dto.PriceDto;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * 询价记录对象 opr_estimate_record
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OprEstimateRecordCacheVo.class, reverseConvertGenerate = false)
@TableName(value = "opr_estimate_record", autoResultMap = true)
public class OprEstimateRecord extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 线路id
     */
    private Long lineId;

    /**
     * 计价模板id
     */
    private Long priceId;

    /**
     * 起点省
     */
    private String startProvinceName;

    /**
     * 起点市
     */
    private String startCityName;

    /**
     * 起点市编码
     */
    private String startCityCode;

    /**
     * 起点区
     */
    private String startDistrictName;

    /**
     * 起点区编码
     */
    private String startAdCode;

    /**
     * 起点位置（长地址）
     */
    private String startAddress;

    /**
     * 起点位置（短地址）
     */
    private String startShortAddress;

    /**
     * 起点经度
     */
    private Double startLongitude;

    /**
     * 起点纬度
     */
    private Double startLatitude;

    /**
     * 终点省
     */
    private String endProvinceName;

    /**
     * 终点市
     */
    private String endCityName;

    /**
     * 终点市编码
     */
    private String endCityCode;

    /**
     * 终点区
     */
    private String endDistrictName;

    /**
     * 终点区编码
     */
    private String endAdCode;

    /**
     * 终点位置（长地址）
     */
    private String endAddress;

    /**
     * 终点位置（短地址）
     */
    private String endShortAddress;

    /**
     * 终点经度
     */
    private Double endLongitude;

    /**
     * 终点纬度
     */
    private Double endLatitude;

    /**
     * 最早出发时间
     */
    private Date earliestTime;

    /**
     * 最晚出发时间
     */
    private Date latestTime;

    /**
     * 里程
     */
    private Integer mileage;

    /**
     * 预计时长(单位：秒)
     */
    private Integer expectDuration;

    /**
     * 产品类型[productCodeEnum]
     */
    private String productCode;

    /**
     * 乘客数量
     */
    private Integer passengerNum;

    /**
     * 乘客电话
     */
    private String passengerPhone;

    /**
     * 乘客创建时间
     */
    private Date passengerCreateTime;

    /**
     * 返回的价格
     */
    @TableField(typeHandler = PriceDtoTypeHandler.class)
    private List<PriceDto> price;

    /**
     * 计价KEY
     */
    private String estimateKey;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 失败原因
     */
    private String reason;

    /**
     * 订单id
     */
    private Long orderId;
}
