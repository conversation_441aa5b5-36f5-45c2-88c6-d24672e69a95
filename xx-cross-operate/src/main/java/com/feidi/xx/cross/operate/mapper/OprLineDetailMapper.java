package com.feidi.xx.cross.operate.mapper;

import com.feidi.xx.cross.operate.domain.OprLineDetail;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.operate.domain.dto.OprLineMatchDTO;
import com.feidi.xx.cross.operate.domain.vo.OprLineDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 线路详情Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface OprLineDetailMapper extends BaseMapperPlus<OprLineDetail, OprLineDetailVo> {

    int deleteByLineId(@Param("lineId") Long lineId);

    /**
     * 匹配线路
     *
     * @param startAdCode
     * @param endAdCode
     * @return
     */
    List<OprLineMatchDTO> matchLine(@Param("startAdCode") String startAdCode, @Param("endAdCode") String endAdCode);

    /**
     * 通过围栏id匹配线路
     *
     * @param startFenceId
     * @param endFenceId
     * @return
     */
    List<OprLineMatchDTO> matchLineByFenceId(@Param("startFenceId") Long startFenceId, @Param("endFenceId") Long endFenceId);

    /**
     * 使用围栏id的线路
     *
     * @param fenceId
     * @return
     */
    List<OprLineMatchDTO> queryUsingLineByFenceId(@Param("fenceId") Long fenceId);




}
