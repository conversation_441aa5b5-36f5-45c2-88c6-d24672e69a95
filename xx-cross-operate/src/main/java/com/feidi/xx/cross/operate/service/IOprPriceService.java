package com.feidi.xx.cross.operate.service;

import com.feidi.xx.cross.operate.domain.OprPrice;
import com.feidi.xx.cross.operate.domain.vo.OprPriceVo;
import com.feidi.xx.cross.operate.domain.bo.OprPriceBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 价格模版Service接口
 *
 * <AUTHOR>
 * @date 2025-03-15
 */
public interface IOprPriceService {

    /**
     * 查询价格模版
     *
     * @param id 主键
     * @return 价格模版
     */
    OprPriceVo queryById(Long id);

    /**
     * 分页查询价格模版列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 价格模版分页列表
     */
    TableDataInfo<OprPriceVo> queryPageList(OprPriceBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的价格模版列表
     *
     * @param bo 查询条件
     * @return 价格模版列表
     */
    List<OprPriceVo> queryList(OprPriceBo bo);

    /**
     * 新增价格模版
     *
     * @param bo 价格模版
     * @return 是否新增成功
     */
    Boolean insertByBo(OprPriceBo bo);

    /**
     * 修改价格模版
     *
     * @param bo 价格模版
     * @return 是否修改成功
     */
    Boolean updateByBo(OprPriceBo bo);

    /**
     * 校验并批量删除价格模版信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
