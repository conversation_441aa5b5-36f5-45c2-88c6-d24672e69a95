package com.feidi.xx.cross.operate.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.cross.operate.domain.OprPlatformProduct;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 平台产品视图对象 opr_platform_product
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OprPlatformProduct.class)
public class OprPlatformProductVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 平台编码
     */
    @ExcelProperty(value = "平台编码", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "PlatformEnum")
    private String platformCode;

    /**
     * 产品编码
     */
    @ExcelProperty(value = "产品编码", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ProductCodeEnum")
    private String productCode;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "StatusEnum")
    private String status;


}
