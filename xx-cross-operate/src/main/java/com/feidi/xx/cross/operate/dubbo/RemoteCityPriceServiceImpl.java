package com.feidi.xx.cross.operate.dubbo;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.feidi.xx.common.core.cache.system.SysDistrictCacheVo;
import com.feidi.xx.common.core.enums.SystemCacheKeyEnum;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.operate.api.RemoteCityPriceService;
import com.feidi.xx.cross.operate.api.RemoteCityService;
import com.feidi.xx.cross.operate.api.domain.city.vo.RemoteCityPriceVo;
import com.feidi.xx.cross.operate.api.domain.city.vo.RemoteCityVo;
import com.feidi.xx.cross.operate.domain.OprCity;
import com.feidi.xx.cross.operate.domain.OprCityPrice;
import com.feidi.xx.cross.operate.domain.vo.OprCityPriceVo;
import com.feidi.xx.cross.operate.domain.vo.OprCityVo;
import com.feidi.xx.cross.operate.mapper.OprCityMapper;
import com.feidi.xx.cross.operate.mapper.OprCityPriceMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 城市dubbo服务实现类
 *
 * <AUTHOR>
 * @date 2025/3/14
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteCityPriceServiceImpl implements RemoteCityPriceService {

    private final OprCityPriceMapper cityMapper;

    /**
     * 根据cityCode查询城市信息
     */
    @Override
    public RemoteCityPriceVo queryByCityCode(String cityCode,  String platformCode) {
        OprCityPriceVo oprCityPriceVo = cityMapper.selectVoOne(new LambdaQueryWrapper<OprCityPrice>().eq(OprCityPrice::getCityCode, cityCode).eq(OprCityPrice::getPlatformCode, platformCode));
        return BeanUtils.copyProperties(oprCityPriceVo, RemoteCityPriceVo.class);
    }

}
