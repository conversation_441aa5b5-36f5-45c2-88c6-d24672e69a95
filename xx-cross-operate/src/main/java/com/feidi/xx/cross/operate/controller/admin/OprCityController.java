package com.feidi.xx.cross.operate.controller.admin;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.id2name.annotation.Id2NameAspect;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.cross.operate.domain.vo.ExportVo;
import com.feidi.xx.cross.operate.domain.bo.OprCityBo;
import com.feidi.xx.cross.operate.domain.vo.OprCityVo;
import com.feidi.xx.cross.operate.service.IOprCityService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;

import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 城市
 * 前端访问路由地址为:/power/city
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/city")
public class OprCityController extends BaseController {

    private final IOprCityService oprCityService;

    /**
     * 查询城市列表
     */
    @SaCheckPermission("operate:city:list")
    @GetMapping("/list")
    @Id2NameAspect
    public TableDataInfo<OprCityVo> list(OprCityBo bo, PageQuery pageQuery) {
        return oprCityService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询城市列表-所有
     */
    @SaCheckPermission("operate:city:list")
    @GetMapping("/list/all")
    @Id2NameAspect
    public R<List<OprCityVo>> list(OprCityBo bo) {
        return R.ok(oprCityService.queryAllList(bo));
    }

    /**
     * 导出城市列表
     */
    @SaCheckPermission("operate:city:export")
    @Log(title = "城市", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OprCityBo bo, HttpServletResponse response, PageQuery pageQuery) {
        List<OprCityVo> list = oprCityService.queryList(bo,pageQuery);
        ExcelUtil.exportExcel(list, "城市", OprCityVo.class, response);
    }

    /**
     * 获取城市详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("operate:city:query")
    @GetMapping("/{id}")
    public R<OprCityVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(oprCityService.queryById(id));
    }

    /**
     * 新增城市
     */
    @SaCheckPermission("operate:city:add")
    @Log(title = "城市", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OprCityBo bo) {
        return toAjax(oprCityService.insertByBo(bo));
    }

    /**
     * 修改城市
     */
    @SaCheckPermission("operate:city:edit")
    @Log(title = "城市", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OprCityBo bo) {
        return toAjax(oprCityService.updateByBo(bo));
    }
    /**
     * 删除城市
     *
     * @param ids 主键串
     */
    @SaCheckPermission("operate:city:remove")
    @Log(title = "城市", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(oprCityService.deleteWithValidByIds(List.of(ids), true));
    }


    /**
     * 老数据批量修复
     */
    @PutMapping("/repair")
    public R<Void> repair() {
        oprCityService.repair();
        return R.ok();
    }
}
