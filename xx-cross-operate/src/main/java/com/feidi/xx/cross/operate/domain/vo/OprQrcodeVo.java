package com.feidi.xx.cross.operate.domain.vo;

import com.feidi.xx.cross.operate.domain.OprQrcode;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 推广码视图对象 opr_qrcode
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OprQrcode.class)
public class OprQrcodeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private String id;

    /**
     * 推广码名称
     */
    @ExcelProperty(value = "推广码名称")
    private String name;

    /**
     * 渠道ID
     */
    @ExcelProperty(value = "渠道ID")
    private Long channelId;

    /**
     * 邀请码
     */
    @ExcelProperty(value = "邀请码")
    private String code;

    /**
     * 二维码链接
     */
    @ExcelProperty(value = "二维码链接")
    private String qrcodeUrl;

    /**
     * 广告图链接
     */
    @ExcelProperty(value = "广告图链接")
    private String adUrl;

    /**
     * Logo链接
     */
    @ExcelProperty(value = "Logo链接")
    private String logoUrl;


}
