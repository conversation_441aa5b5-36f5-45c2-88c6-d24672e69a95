package com.feidi.xx.cross.operate.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.operate.api.RemoteHolidayService;
import com.feidi.xx.cross.operate.api.domain.holiday.vo.RemoteHolidayVo;
import com.feidi.xx.cross.operate.domain.OprHoliday;
import com.feidi.xx.cross.operate.domain.vo.OprHolidayVo;
import com.feidi.xx.cross.operate.mapper.OprHolidayMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * 节假日dubbo服务实现类
 *
 * <AUTHOR>
 * @date 2025/3/14
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteHolidayServiceImpl implements RemoteHolidayService {

    private final OprHolidayMapper oprHolidayMapper;

    /**
     * 根据日期查询节假日信息
     * @param date
     * @return
     */
    @Override
    public RemoteHolidayVo queryByDate(String date) {
        OprHolidayVo oprHolidayVo = oprHolidayMapper.selectVoOne(new LambdaQueryWrapper<OprHoliday>().eq(OprHoliday::getDate, date));
        return BeanUtils.copyProperties(oprHolidayVo, RemoteHolidayVo.class);
    }
}
