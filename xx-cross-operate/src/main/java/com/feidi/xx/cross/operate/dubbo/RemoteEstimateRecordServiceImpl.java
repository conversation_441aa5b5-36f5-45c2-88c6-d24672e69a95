package com.feidi.xx.cross.operate.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.operate.api.RemoteEstimateRecordService;
import com.feidi.xx.cross.operate.api.domain.estimate.vo.RemoteEstimateRecordVo;
import com.feidi.xx.cross.operate.domain.OprEstimateRecord;
import com.feidi.xx.cross.operate.mapper.OprEstimateRecordMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * 市内定价服务接口实现类
 *
 * <AUTHOR>
 * @date 2024/9/7
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteEstimateRecordServiceImpl implements RemoteEstimateRecordService {

    private final OprEstimateRecordMapper baseMapper;

    @Override
    public RemoteEstimateRecordVo getEstimateRecordByEstimateKey(String estimateKey) {
        OprEstimateRecord oprEstimateRecord = baseMapper.selectOne(new QueryWrapper<OprEstimateRecord>().eq("estimate_key", estimateKey));
        return BeanUtils.copyProperties(oprEstimateRecord, RemoteEstimateRecordVo.class);
    }

    @Override
    public void updateEstimateRecordById(Long estimateId, Long orderId) {
        OprEstimateRecord oprEstimateRecord = baseMapper.selectById(estimateId);
        oprEstimateRecord.setOrderId(orderId);
        baseMapper.updateById(oprEstimateRecord);
    }
}
