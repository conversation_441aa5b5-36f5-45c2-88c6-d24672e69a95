package com.feidi.xx.cross.operate.domain.bo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.feidi.xx.common.core.serializer.DoubleArraySerializer;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.operate.domain.OprFence;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 电子围栏业务对象 opr_fence
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OprFence.class, reverseConvertGenerate = false)
public class OprFenceBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 省ID
     */
    @NotNull(message = "省ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long provinceId;

    /**
     * 城市ID
     */
    @NotNull(message = "城市ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long cityId;

    /**
     * 市编码
     */
    @NotBlank(message = "市编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cityCode;

    /**
     * 区域编码
     */
    @NotBlank(message = "区域编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String adCode;

    /**
     * 围栏坐标点
     */
    @JsonSerialize(using = DoubleArraySerializer.class)
    @NotNull(message = "围栏坐标点不能为空", groups = { AddGroup.class, EditGroup.class })
    private Double[] points;

    /**
     * 类型
     */
    @NotBlank(message = "类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 状态[StatusEnum]
     */
    @NotBlank(message = "状态[StatusEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
