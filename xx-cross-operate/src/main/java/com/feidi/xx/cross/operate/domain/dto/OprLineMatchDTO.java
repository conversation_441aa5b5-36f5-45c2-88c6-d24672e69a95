package com.feidi.xx.cross.operate.domain.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 线路匹配对象
 */
@Data
public class OprLineMatchDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 线路ID
     */
    private Long lineId;

    /**
     * 路线类型 {@link com.feidi.xx.common.core.enums.StartEndEnum}
     */
    private String routeType;


}
