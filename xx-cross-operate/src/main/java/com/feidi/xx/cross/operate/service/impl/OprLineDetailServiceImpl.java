package com.feidi.xx.cross.operate.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.enums.StartEndEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.tenant.helper.TenantHelper;
import com.feidi.xx.cross.operate.domain.OprLine;
import com.feidi.xx.cross.operate.domain.OprLineDetail;
import com.feidi.xx.cross.operate.domain.bo.OprLineDetailBo;
import com.feidi.xx.cross.operate.domain.bo.OprLineDetailForm;
import com.feidi.xx.cross.operate.domain.dto.OprLineMatchDTO;
import com.feidi.xx.cross.operate.mapper.OprLineDetailMapper;
import com.feidi.xx.cross.operate.mapper.OprLineMapper;
import com.feidi.xx.cross.operate.service.IOprLineDetailService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 线路详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Service
@RequiredArgsConstructor
public class OprLineDetailServiceImpl implements IOprLineDetailService {

    private final OprLineMapper lineMapper;
    private final OprLineDetailMapper baseMapper;

    /**
     * 根据线路id获取线路详情
     *
     * @param lineId 线路id
     * @return 线路详情
     */
    @Override
    public List<OprLineDetail> queryByLineId(Long lineId) {
        return baseMapper.selectList(Wrappers.<OprLineDetail>lambdaQuery().eq(OprLineDetail::getLineId, lineId));
    }

    /**
     * 新增线路详情
     *
     * @param form 线路详情
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(OprLineDetailForm form) {
        Long lineId = form.getLineId();
        OprLine line = lineMapper.selectById(lineId);
        Assert.notNull(line, "线路不存在");
        // 清空该线路的详情
        baseMapper.deleteByLineId(lineId);

        List<OprLineDetail> saveDetails = new ArrayList<>();
        StringBuilder msg = new StringBuilder();
        List<OprLineDetailBo> startList = form.getStart();
        List<OprLineDetailBo> endList = form.getEnd();
        if (CollUtil.isNotEmpty(startList) && CollUtil.isNotEmpty(endList)) {
            for (int i = 0; i < startList.size(); i++) {
                OprLineDetailBo startBo = startList.get(i);
                OprLineDetail start = MapstructUtils.convert(startBo, OprLineDetail.class);
                start.init(line, StartEndEnum.START);
                saveDetails.add(start);
                for (OprLineDetailBo endBo : endList) {
                    OprLineDetail end = MapstructUtils.convert(endBo, OprLineDetail.class);
                    // 是否存在
                    if (matchLine(start.getAdCode(), end.getAdCode()) != null) {
                        startList.stream().filter(bo -> Objects.equals(bo.getAdCode(), start.getAdCode()))
                                        .findFirst().ifPresent(e -> msg.append(e.getDistrict()).append("到"));
                        endList.stream().filter(bo -> Objects.equals(bo.getAdCode(), end.getAdCode()))
                                .findFirst().ifPresent(e -> msg.append(e.getDistrict()).append(";"));
                        continue;
                    }
                    if (i == 0) {
                        end.init(line, StartEndEnum.END);
                        saveDetails.add(end);
                    }
                }
            }
        }
        if (StrUtil.isNotBlank(msg.toString())) {
            throw new ServiceException("当前" + msg + " 线路已存在");
        }
        // 注入字段
        TenantHelper.insertFill(saveDetails);
        return baseMapper.insertBatchSomeColumn(saveDetails) > 0;
    }

    /**
     * 匹配线路
     *
     * @param startAdCode
     * @param endAdCode
     * @return
     */
    @Override
    public OprLineMatchDTO matchLine(String startAdCode, String endAdCode) {
        // 匹配到的线路
        List<OprLineMatchDTO> matchedLine = baseMapper.matchLine(startAdCode, endAdCode);
        if (!matchedLine.isEmpty()) {
            return matchedLine.get(0);
        }
        return null;
    }
}
