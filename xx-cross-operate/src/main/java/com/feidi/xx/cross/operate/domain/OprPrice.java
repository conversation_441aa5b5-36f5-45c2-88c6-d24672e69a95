package com.feidi.xx.cross.operate.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import java.io.Serial;

/**
 * 价格模版对象 opr_price
 *
 * <AUTHOR>
 * @date 2025-03-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("opr_price")
public class OprPrice extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 价格模版主表
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 价格模版名称
     */
    private String name;

    /**
     * 价格类型[PriceTypeEnum]
     */
    private String pricingType;

    /**
     * 是否节假日模板
     */
    private String holidayTemplate;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否模板
     */
    private String template;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
