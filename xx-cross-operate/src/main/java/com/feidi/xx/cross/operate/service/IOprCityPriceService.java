package com.feidi.xx.cross.operate.service;

import com.feidi.xx.cross.operate.domain.OprCityPrice;
import com.feidi.xx.cross.operate.domain.bo.OprCityBo;
import com.feidi.xx.cross.operate.domain.vo.OprCityPriceVo;
import com.feidi.xx.cross.operate.domain.bo.OprCityPriceBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.cross.operate.domain.vo.OprCityVo;

import java.util.Collection;
import java.util.List;

/**
 * 城市价格关联Service接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface IOprCityPriceService {

    /**
     * 查询城市价格关联
     *
     * @param id 主键
     * @return 城市价格关联
     */
    OprCityPriceVo queryById(Long id);

    /**
     * 分页查询城市价格关联列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 城市价格关联分页列表
     */
    TableDataInfo<OprCityPriceVo> queryPageList(OprCityPriceBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的城市价格关联列表
     *
     * @param bo 查询条件
     * @return 城市价格关联列表
     */
    List<OprCityPriceVo> queryList(OprCityPriceBo bo);

    /**
     * 新增城市价格关联
     *
     * @param bo 城市价格关联
     * @return 是否新增成功
     */
    Boolean insertByBo(OprCityPriceBo bo);

    /**
     * 修改城市价格关联
     *
     * @param bo 城市价格关联
     * @return 是否修改成功
     */
    Boolean updateByBo(OprCityPriceBo bo);

    /**
     * 校验并批量删除城市价格关联信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询城市价格关联列表
     * @param bo
     * @return
     */
    List<OprCityPriceVo> queryAllList(OprCityPriceBo bo);
}
