package com.feidi.xx.cross.operate.controller.agent;

import cn.hutool.core.collection.CollUtil;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.id2name.annotation.Id2NameAspect;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.operate.domain.bo.OprLineBo;
import com.feidi.xx.cross.operate.domain.vo.OprLineVo;
import com.feidi.xx.cross.operate.service.IOprLineService;
import com.feidi.xx.cross.power.api.RemoteAgentLineService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 代理商 - 线路
 * 前端访问路由地址为:/operate/line
 *
 * <AUTHOR>
 * @date 2024-08-29
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/line")
public class AgtLineController extends BaseController {

    private final IOprLineService lineService;

    @DubboReference
    private final RemoteAgentLineService remoteAgentLineService;

    /**
     * 查询线路列表-分页
     */
    @Id2NameAspect
    @GetMapping("/list")
    @Log(title = "价格模版", businessType = BusinessType.OTHER)
    public TableDataInfo<OprLineVo> list(OprLineBo bo, PageQuery pageQuery) {
        List<Long> agentLine = remoteAgentLineService.getAgentLine(LoginHelper.getAgentId());
        if (CollUtil.isEmpty(agentLine)) {
            return TableDataInfo.build();
        }

        bo.setIds(agentLine);
        return lineService.queryPageList(bo, pageQuery);
    }


    /**
     * 查询线路列表-全部
     */
    @Log(title = "价格模版", businessType = BusinessType.OTHER)
    @GetMapping("/list/all")
    public R<List<OprLineVo>> list(OprLineBo bo) {
        List<Long> agentLine = remoteAgentLineService.getAgentLine(LoginHelper.getAgentId());
        if (CollUtil.isEmpty(agentLine)) {
            return R.ok();
        }

        bo.setIds(agentLine);

        return R.ok(lineService.queryList(bo));
    }

    /**
     * 获取线路详细信息
     *
     * @param id 主键
     */
    @Log(title = "价格模版", businessType = BusinessType.OTHER)
    @GetMapping("/{id}")
    public R<OprLineVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(lineService.queryById(id));
    }

}
