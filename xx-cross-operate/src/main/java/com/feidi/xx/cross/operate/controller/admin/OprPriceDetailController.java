package com.feidi.xx.cross.operate.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.web.core.BaseController;

import com.feidi.xx.cross.operate.domain.vo.ExportVo;
import com.feidi.xx.cross.operate.domain.bo.OprPriceDetailBo;
import com.feidi.xx.cross.operate.domain.vo.OprPriceDetailVo;
import com.feidi.xx.cross.operate.service.IOprPriceDetailService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 定价详情
 * 前端访问路由地址为:/operate/pricing
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/priceDetail")
public class OprPriceDetailController extends BaseController {

    private final IOprPriceDetailService pricingService;


    /**
     * 导出定价详情列表
     */
    @SaCheckPermission("operate:priceDetail:export")
    @Log(title = "定价", businessType = BusinessType.EXPORT)
    @Download(name="定价",module = ModuleConstants.OPERATE)
    @PostMapping("/export")
    public void export(OprPriceDetailBo bo, HttpServletResponse response) {
        List<OprPriceDetailVo> list = pricingService.queryList(bo);
        ExcelUtil.exportExcel(list, "定价", OprPriceDetailVo.class, response);
    }

    /**
     * 删除定价详情
     *
     * @param ids 主键串
     */
    @SaCheckPermission("operate:priceDetail:remove")
    @Log(title = "定价", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(pricingService.remove(ids));
    }

    /**
     * 查询定价详情列表
     */
    @SaCheckPermission("operate:priceDetail:list")
    @GetMapping("/list")
    public TableDataInfo<OprPriceDetailVo> list(OprPriceDetailBo bo, PageQuery pageQuery) {
        return pricingService.queryPageList(bo, pageQuery);
    }
}
