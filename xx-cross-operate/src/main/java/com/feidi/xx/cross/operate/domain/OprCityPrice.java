package com.feidi.xx.cross.operate.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import java.io.Serial;

/**
 * 城市价格关联对象 opr_city_price
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("opr_city_price")
public class OprCityPrice extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 城市表id
     */
    private Long cityId;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 价格id
     */
    private Long priceId;

    /**
     * {@link PlatformCodeEnum}
     */
    private String platformCode;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
