package com.feidi.xx.cross.operate.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.id2name.annotation.Id2NameAspect;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.operate.domain.vo.ExportVo;
import com.feidi.xx.cross.operate.domain.bo.OprLineBo;
import com.feidi.xx.cross.operate.domain.vo.OprLineVo;
import com.feidi.xx.cross.operate.service.IOprLineService;
import com.feidi.xx.cross.power.api.RemoteAgentLineService;
import com.feidi.xx.cross.power.api.domain.line.bo.RemoteLineAgentBo;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 线路
 * 前端访问路由地址为:/operate/line
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/line")
public class OprLineController extends BaseController {

    private final IOprLineService oprLineService;

    @DubboReference
    private final RemoteAgentLineService remoteAgentLineService;

    /**
     * 查询线路列表
     */
    @SaCheckPermission("operate:line:list")
    @GetMapping("/list")
    @Id2NameAspect
    public TableDataInfo<OprLineVo> list(OprLineBo bo, PageQuery pageQuery) {
        return oprLineService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询线路列表-全部
     */
    @SaCheckPermission("operate:line:list")
    @GetMapping("/list/all")
    public R<List<OprLineVo>> list(OprLineBo bo) {
        return R.ok(oprLineService.queryList(bo));
    }

    /**
     * 根据价格模版查询线路列表
     */
    @SaCheckPermission("operate:line:list")
    @GetMapping("/list/byPriceId/{priceId}")
    public R<List<OprLineVo>> listByPriceId(@NotNull(message = "价格模版id不能为空")
                                            @PathVariable Long priceId) {
        return R.ok(oprLineService.queryByPriceId(priceId));
    }

    /**
     * 根据电子围栏id 查询线路列表
     */
    @SaCheckPermission("operate:line:list")
    @GetMapping("/list/byFenceId/{fenceId}")
    public R<List<OprLineVo>> listByFenceId(@NotNull(message = "电子围栏id不能为空")
                                            @PathVariable Long fenceId) {
        return R.ok(oprLineService.listByFenceId(fenceId));
    }
    /**
     * 导出线路列表
     */
    @SaCheckPermission("operate:line:export")
    @Log(title = "线路", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OprLineBo bo, HttpServletResponse response) {
        List<OprLineVo> list = oprLineService.queryList(bo);
        ExcelUtil.exportExcel(list, "线路", OprLineVo.class, response);
    }

    /**
     * 获取线路详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("operate:line:query")
    @GetMapping("/{id}")
    public R<OprLineVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(oprLineService.queryById(id));
    }

    /**
     * 获取线路下的代理商
     */
    @RepeatSubmit()
    @SaCheckPermission("operate:line:query")
    @GetMapping("/agents/{id}")
    public R<List<Long>> getAgents(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(remoteAgentLineService.getLineAgent(id));
    }

    /**
     * 新增线路
     */
    @SaCheckPermission("operate:line:add")
    @Log(title = "线路", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OprLineBo bo) {
        return toAjax(oprLineService.insertByBo(bo));
    }

    /**
     * 修改线路
     */
    @SaCheckPermission("operate:line:edit")
    @Log(title = "线路", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OprLineBo bo) {
        return toAjax(oprLineService.updateByBo(bo));
    }

    /**
     * 修改线路状态
     */
    @SaCheckPermission("operate:line:edit")
    @Log(title = "线路", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @GetMapping("/{id}/{status}")
    public R<Void> editStatus(@PathVariable Long id, @PathVariable String status) {
        return toAjax(oprLineService.updateStatus(id, status));
    }

    /**
     * 删除线路
     *
     * @param ids 主键串
     */
    @SaCheckPermission("operate:line:remove")
    @Log(title = "线路", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(oprLineService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 分配代理商
     * @param bo
     * @return
     */
    @SaCheckPermission("operate:line:assignAgent")
    @Log(title = "线路", businessType = BusinessType.INSERT)
    @PostMapping("/assignAgent")
    public R<Boolean> assignAgent(@Validated @RequestBody RemoteLineAgentBo bo) {
        return R.ok(remoteAgentLineService.assignAgent(bo));
    }
}
