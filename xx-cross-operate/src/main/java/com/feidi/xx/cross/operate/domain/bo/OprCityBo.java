package com.feidi.xx.cross.operate.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.common.core.domain.StartEndTime;
import com.feidi.xx.cross.operate.domain.OprCity;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 城市业务对象 pow_city
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OprCity.class, reverseConvertGenerate = false)
public class OprCityBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 价格模版
     */
    private Long priceId;

    /**
     * 代理商名称
     */
    private String companyName;

    /**
     * 省ID
     */
    private Long provinceId;

    /**
     * 市ID
     */
    private Long cityId;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 状态[StatusEnum]
     */
    private String status;

    /**
     * 代理商ID列表
     */
    List<Long> cityAgentIdList;

    /**
     * 城市价格
     */
    List<OprCityPriceBo> cityPriceBoList;

    /**
     * 开始运营时间
     */
    private String startTime;
    /**
     * 结束运营时间
     */
    private String endTime;
    /**
     * 最大接单数量
     */
    private Integer maxNumber;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date startCreateTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date  endCreateTime;


}
