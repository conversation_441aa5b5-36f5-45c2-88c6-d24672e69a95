package com.feidi.xx.cross.operate.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.operate.domain.OprChannel;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 渠道业务对象 opr_channel
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OprChannel.class, reverseConvertGenerate = false)
public class OprChannelBo extends BaseEntity {

    /**
     * 主键
     */
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 渠道名称
     */
    @NotBlank(message = "渠道名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 邀请码
     */
    @NotBlank(message = "邀请码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String code;

    /**
     * 渠道类型
     */
    @NotBlank(message = "渠道类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;


}
