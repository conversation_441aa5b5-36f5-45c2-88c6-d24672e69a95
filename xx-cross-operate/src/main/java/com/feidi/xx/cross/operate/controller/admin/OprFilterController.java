package com.feidi.xx.cross.operate.controller.admin;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.cross.operate.domain.vo.ExportVo;
import com.feidi.xx.cross.operate.domain.bo.OprFilterBo;
import com.feidi.xx.cross.operate.domain.vo.OprFilterVo;
import com.feidi.xx.cross.operate.service.IOprFilterService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 过滤规则
 * 前端访问路由地址为:/operate/filter
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/filter")
public class OprFilterController extends BaseController {

    private final IOprFilterService oprFilterService;

    /**
     * 查询过滤规则列表
     */
    @SaCheckPermission("operate:filter:list")
    @GetMapping("/list")
    public TableDataInfo<OprFilterVo> list(OprFilterBo bo, PageQuery pageQuery) {
        return oprFilterService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出过滤规则列表
     */
    @SaCheckPermission("operate:filter:export")
    @Log(title = "过滤规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OprFilterBo bo, HttpServletResponse response) {
        List<OprFilterVo> list = oprFilterService.queryList(bo);
        ExcelUtil.exportExcel(list, "过滤规则", OprFilterVo.class, response);
    }

    /**
     * 获取过滤规则详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("operate:filter:query")
    @GetMapping("/{id}")
    public R<OprFilterVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(oprFilterService.queryById(id));
    }

    /**
     * 新增过滤规则
     */
    @SaCheckPermission("operate:filter:add")
    @Log(title = "过滤规则", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OprFilterBo bo) {
        return toAjax(oprFilterService.insertByBo(bo));
    }

    /**
     * 修改过滤规则
     */
    @SaCheckPermission("operate:filter:edit")
    @Log(title = "过滤规则", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OprFilterBo bo) {
        return toAjax(oprFilterService.updateByBo(bo));
    }

    /**
     * 删除过滤规则
     *
     * @param ids 主键串
     */
    @SaCheckPermission("operate:filter:remove")
    @Log(title = "过滤规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(oprFilterService.deleteWithValidByIds(List.of(ids), true));
    }
}
