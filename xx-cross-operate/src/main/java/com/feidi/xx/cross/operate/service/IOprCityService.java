package com.feidi.xx.cross.operate.service;


import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.operate.domain.bo.OprCityBo;
import com.feidi.xx.cross.operate.domain.vo.OprCityVo;

import java.util.Collection;
import java.util.List;

/**
 * 城市Service接口
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface IOprCityService {

    /**
     * 查询城市
     *
     * @param id 主键
     * @return 城市
     */
    OprCityVo queryById(Long id);

    /**
     * 分页查询城市列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 城市分页列表
     */
    TableDataInfo<OprCityVo> queryPageList(OprCityBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的城市列表
     *
     * @param bo 查询条件
     * @return 城市列表
     */
    List<OprCityVo> queryList(OprCityBo bo, PageQuery pageQuery);

    /**
     * 新增城市
     *
     * @param bo 城市
     * @return 是否新增成功
     */
    Boolean insertByBo(OprCityBo bo);

    /**
     * 修改城市
     *
     * @param bo 城市
     * @return 是否修改成功
     */
    Boolean updateByBo(OprCityBo bo);

    /**
     * 校验并批量删除城市信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据价格模版id查询城市数量
     *
     * @param priceId 价格模版id
     * @return 城市数量
     */
    Integer countByPriceId(Long priceId);

    /**
     * 查询城市列表
     * @param bo
     * @return
     */
    List<OprCityVo> queryAllList(OprCityBo bo);

    OprCityVo getByCityCode(String adcode, String longitude, String latitude);

    /**
     * 修复数据
     */
    void repair();

}
