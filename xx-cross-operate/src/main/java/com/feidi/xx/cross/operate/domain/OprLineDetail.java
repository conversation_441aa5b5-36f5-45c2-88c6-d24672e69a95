package com.feidi.xx.cross.operate.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.core.enums.StartEndEnum;
import com.feidi.xx.common.id2name.annotation.Id2Name;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 线路详情对象 opr_line_detail
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("opr_line_detail")
public class OprLineDetail extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 围栏ID
     */
    private Long fenceId;

    /**
     * 线路
     */
    private Long lineId;

    /**
     * 省
     */
    @Id2Name(fullName="province",index = "id")
    private Long provinceId;

    /**
     * 城市
     */
    @Id2Name(fullName="city",index = "id")
    private Long cityId;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 区域
     */
    @Id2Name(fullName="district",index = "id")
    private Long districtId;

    /**
     * 区域编码
     */
    private String adCode;

    /**
     * 类型
     */
    private String direction;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


    public void init(OprLine line, StartEndEnum direction) {
        this.delFlag = "0";
        this.lineId = line.getId();
        this.direction = direction.getCode();
        // 线路没有省市的名称，所以详情只能接受前端传的
        if (StartEndEnum.START.equals(direction)) {
            this.provinceId = line.getStartProvinceId();
            this.cityId = line.getStartCityId();
            this.cityCode = line.getStartCityCode();
        } else if (StartEndEnum.END.equals(direction)) {
            this.provinceId = line.getEndProvinceId();
            this.cityId = line.getEndCityId();
            this.cityCode = line.getEndCityCode();
        }
    }

}
