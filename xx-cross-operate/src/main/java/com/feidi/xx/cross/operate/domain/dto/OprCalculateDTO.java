package com.feidi.xx.cross.operate.domain.dto;

import com.feidi.xx.cross.common.enums.operate.HolidayTypeEnum;
import com.feidi.xx.cross.common.enums.operate.PriceTypeEnum;
import com.feidi.xx.cross.common.enums.operate.ProductCodeEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.common.validate.MemberGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 询价参数
 * @Description TODO
 */
@Data
public class OprCalculateDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 最早出发时间 时间戳 秒级
     */
    @NotNull(message = "最早出发时间不能为空")
    private Long startTime;

    /**
     * 最晚出发时间 时间戳 秒级
     */
    @NotNull(message = "最晚出发时间不能为空")
    private Long endTime;

    /**
     * 平台code {@link PlatformCodeEnum}
     */
    private String platformCode;

    /**
     * 价格类型 {@link PriceTypeEnum}
     */
    private String priceType;
    /**
     * 价格模版id
     */
//    @NotNull(message = "价格模版id不能为空")
    private Long priceId;


    /**
     * 路线类型 {@link com.feidi.xx.common.core.enums.StartEndEnum}
     */
    private String routeType;
    /**
     * 乘客数量
     */
    @NotNull(message = "乘客数量不能为空")
    private Integer passengerCount;

    /**
     * 乘客详情  {"person":1,"child":2}
     */
    private String passengerDetail;

    /**
     * 产品code {@link ProductCodeEnum}
     */
    private String productCode;

    /**
     * 日期类型 {@link HolidayTypeEnum}
     */
//    @NotBlank(message = "日期类型不能为空")
    private String holidayType;
    /**
     * 里程（单位：米）
     */
    private Integer mileage;

    /**
     * 预计时长(单位：秒)
     */
    private Integer expectDuration;

    //预估key
//    @NotBlank(message = "预估key不能为空")
    private String estimateKey;

    //线路id
    private Long lineId;

    //出发地 起点区编码
    @NotBlank(message = "起点AdCode不能为空", groups = MemberGroup.class)
    private String startAdCode;

    //出发地 起点城市编码
//    @NotBlank(message = "起点CityCode不能为空", groups = MemberGroup.class)
    private String startCityCode;

    //出发地 详细地址
    private String startAddress;

    //出发地 短地址
    private String startShortAddress;

    //起点经度
    @NotNull(message = "起点经度不能为空")
    private Double startLongitude;

    //起点纬度
    @NotNull(message = "起点纬度不能为空")
    private Double startLatitude;

    //目的地 终点区编码
    @NotBlank(message = "终点AdCode不能为空", groups = MemberGroup.class)
    private String endAdCode;

    //出发地 起点城市编码
//    @NotBlank(message = "起点CityCode不能为空", groups = MemberGroup.class)
    private String endCityCode;

    //目的地 详细地址
    private String endAddress;

    //目的地 短地址
    private String endShortAddress;

    //终点经度
    @NotNull(message = "终点经度不能为空")
    private Double endLongitude;

    //终点纬度
    @NotNull(message = "终点经度不能为空")
    private Double endLatitude;

    //乘客id
    private Long passengerId;

    /**
     * 运营时间是否校验标识 Y/N
     */
    private String checkOperateTime="Y";

    private String reason;

}
