package com.feidi.xx.cross.operate.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.common.enums.operate.*;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 定价对象 fin_pricing
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("opr_price_detail")
public class OprPriceDetail extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     *  价格模版id
     */
    private Long priceId;

    /**
     * 平台code {@link PlatformCodeEnum}
     */
    private String platformCode;

    /**
     * 日期类型 {@link HolidayTypeEnum}
     */
    private String holidayType;

    /**
     * 产品code {@link ProductCodeEnum}
     */
    private String productCode;

    /**
     * 起步价格模板
     */
    private String initiateData;

    /**
     * 里程价格模板
     */
    private String mileageData;

    /**
     * 状态 {@link  PricingStatusEnum}
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
