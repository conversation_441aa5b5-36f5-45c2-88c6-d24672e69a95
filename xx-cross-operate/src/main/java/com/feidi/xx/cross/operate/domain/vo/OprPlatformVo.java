package com.feidi.xx.cross.operate.domain.vo;

import java.math.BigDecimal;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.cross.operate.domain.OprPlatform;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 平台视图对象 opr_platform
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OprPlatform.class)
public class OprPlatformVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 编码
     */
    @ExcelProperty(value = "编码", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "PlatformEnum")
    private String code;

    /**
     * 应用
     */
    @ExcelProperty(value = "应用")
    private String appId;

    /**
     * 接口KEY
     */
    @ExcelProperty(value = "接口KEY")
    private String appKey;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "StatusEnum")
    private String status;

    /**
     * 平台抽成比例
     */
    @ExcelProperty(value = "平台抽成比例")
    private BigDecimal rate;

    /**
     * 接口密钥
     */
    @ExcelProperty(value = "接口密钥")
    private String appSecret;

    /**
     * 接口私钥
     */
    @ExcelProperty(value = "接口私钥")
    private String appPrivateSecret;

    /**
     * 更新时间
     */
    private Date updateTime;

}
