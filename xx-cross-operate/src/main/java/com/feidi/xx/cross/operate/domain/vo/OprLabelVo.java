package com.feidi.xx.cross.operate.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.cross.operate.domain.OprLabel;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 平台-产品视图对象 opr_label
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OprLabel.class)
public class OprLabelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "LabelTypeEnum")
    private String type;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

}
