package com.feidi.xx.cross.operate.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import java.io.Serial;
import java.util.Date;

/**
 * 平台对象 opr_platform
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("opr_platform")
public class OprPlatform extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 应用
     */
    private String appId;

    /**
     * 接口KEY
     */
    private String appKey;

    /**
     * 接口密钥
     */
    private String appSecret;

    /**
     * 接口私钥
     */
    private String appPrivateSecret;

    /**
     * 状态
     */
    private String status;

    /**
     * 平台抽成比例
     */
    private BigDecimal rate;

    /**
     * 更新时间
     */
    private Date updateTime;

}
