package com.feidi.xx.cross.operate.domain.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 预估价格dto
 */
@Data
public class EstimateRecordDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 修改价格
     */
    private Long price;

    /**
     * 预估价格key
     */
    private String estimateKey;
    /**
     * 产品编码
     */
    private String  productCode;
}
