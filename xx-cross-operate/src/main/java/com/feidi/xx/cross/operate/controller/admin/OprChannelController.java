package com.feidi.xx.cross.operate.controller.admin;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.cross.operate.domain.vo.ExportVo;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.cross.operate.domain.vo.OprChannelVo;
import com.feidi.xx.cross.operate.domain.bo.OprChannelBo;
import com.feidi.xx.cross.operate.service.IOprChannelService;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 渠道
 * 前端访问路由地址为:/operate/channel
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/channel")
public class OprChannelController extends BaseController {

    private final IOprChannelService oprChannelService;

    /**
     * 查询渠道列表
     */
    @SaCheckPermission("operate:channel:list")
    @GetMapping("/list")
    @Log(title = "渠道", businessType = BusinessType.OTHER)
    public TableDataInfo<OprChannelVo> list(OprChannelBo bo, PageQuery pageQuery) {
        return oprChannelService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出渠道列表
     */
    @SaCheckPermission("operate:channel:export")
    @Log(title = "渠道", businessType = BusinessType.EXPORT)
    @Download(name="渠道",module = ModuleConstants.OPERATE,mode="no")
    @PostMapping("/export")
    public Object export(OprChannelBo bo,HttpServletResponse response) {
        List<OprChannelVo> list = oprChannelService.queryList(bo);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(list, "渠道", OprChannelVo.class, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 获取渠道详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("operate:channel:query")
    @GetMapping("/{id}")
    public R<OprChannelVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable String id) {
        return R.ok(oprChannelService.queryById(id));
    }

    /**
     * 新增渠道
     */
    @SaCheckPermission("operate:channel:add")
    @Log(title = "渠道", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OprChannelBo bo) {
        return toAjax(oprChannelService.insertByBo(bo));
    }

    /**
     * 修改渠道
     */
    @SaCheckPermission("operate:channel:edit")
    @Log(title = "渠道", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OprChannelBo bo) {
        return toAjax(oprChannelService.updateByBo(bo));
    }

    /**
     * 删除渠道
     *
     * @param ids 主键串
     */
    @SaCheckPermission("operate:channel:remove")
    @Log(title = "渠道", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(oprChannelService.deleteWithValidByIds(List.of(ids), true));
    }
}
