package com.feidi.xx.cross.operate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.SpringUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.operate.enums.OprCacheKeyEnum;
import com.feidi.xx.cross.operate.api.domain.product.vo.RemoteProductVo;
import com.feidi.xx.cross.operate.domain.OprProduct;
import com.feidi.xx.cross.operate.domain.bo.OprPlatformProductBo;
import com.feidi.xx.cross.operate.domain.bo.OprProductBo;
import com.feidi.xx.cross.operate.domain.vo.OprProductVo;
import com.feidi.xx.cross.operate.mapper.OprProductMapper;
import com.feidi.xx.cross.operate.service.IOprPlatformProductService;
import com.feidi.xx.cross.operate.service.IOprProductService;
import com.feidi.xx.cross.operate.service.OprPlatProdHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 产品Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@RequiredArgsConstructor
@Service
public class OprProductServiceImpl implements IOprProductService {

    private final ScheduledExecutorService scheduledExecutorService;

    private final IOprPlatformProductService platformProductService;

    private final OprProductMapper baseMapper;

    private final OprPlatProdHelper platProdHelper;


    /**
     * 查询产品
     *
     * @param id 主键
     * @return 产品
     */
    @Override
    public OprProductVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询产品列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品分页列表
     */
    @Override
    public TableDataInfo<OprProductVo> queryPageList(OprProductBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OprProduct> lqw = buildQueryWrapper(bo);
        Page<OprProductVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的产品列表
     *
     * @param bo 查询条件
     * @return 产品列表
     */
    @Override
    public List<OprProductVo> queryList(OprProductBo bo) {
        LambdaQueryWrapper<OprProduct> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OprProduct> buildQueryWrapper(OprProductBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OprProduct> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), OprProduct::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), OprProduct::getCode, bo.getCode());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), OprProduct::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增产品
     *
     * @param bo 产品
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OprProductBo bo) {
        OprProduct add = MapstructUtils.convert(bo, OprProduct.class);
        validEntityBeforeSave(add);
        add.setUpdateTime(DateUtils.getNowDate());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());

            // 异步添加缓存
            scheduledExecutorService.schedule(() -> addCache(add), 0, TimeUnit.SECONDS);
        }
        return flag;
    }

    /**
     * 修改产品
     *
     * @param bo 产品
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OprProductBo bo) {
        OprProduct update = MapstructUtils.convert(bo, OprProduct.class);
        validEntityBeforeSave(update);
        update.setUpdateTime(DateUtils.getNowDate());
        // 先判断状态是否更新
        IOprProductService productService = SpringUtils.getBean(IOprProductService.class);
        boolean ret = productService.updateStatus(bo.getId(), bo.getStatus());
        ret &= baseMapper.updateById(update) > 0;
        // 异步添加缓存
        scheduledExecutorService.schedule(() -> addCache(update), 0, TimeUnit.SECONDS);

        return ret;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OprProduct entity) {
        LambdaQueryWrapper<OprProduct> lqw = Wrappers.lambdaQuery();
        lqw.eq(OprProduct::getCode, entity.getCode())
                .ne(entity.getId() != null, OprProduct::getId, entity.getId())
                .last(Constants.LIMIT_ONE);

        OprProduct product = baseMapper.selectOne(lqw);

        if (product != null) {
            throw new ServiceException("当前编码已存在");
        }
    }

    /**
     * 校验并批量删除产品信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        Assert.isTrue(ids.size() == 1, "不支持批量删除");
        // 根据ids查询出产品列表拿出code
        List<OprProduct> oprProducts = baseMapper.selectBatchIds(ids);
        List<String> codeList = oprProducts.stream().map(OprProduct::getCode).toList();
        if (isValid) {
            // TODO-NEW 删除产品平台关联数据
            List<OprPlatformProductBo> boList = codeList.stream().map(code -> OprPlatformProductBo.newInstance(code, OprPlatProdHelper.CONSTANTS.PRODUCT)).collect(Collectors.toList());
            boolean exists = platformProductService.existsByBoList(boList);
            Assert.isTrue(!exists, "当前产品已使用，无法删除");
        }
        boolean deleteFlag = baseMapper.deleteByIds(ids) > 0;
        if (deleteFlag) {
            // 删除缓存
            scheduledExecutorService.schedule(() -> oprProducts.forEach(product -> deleteCache(product.getCode())), 0, TimeUnit.SECONDS);
        }
        return deleteFlag;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateStatus(Long id, String status) {
        OprProduct product = baseMapper.selectById(id);
        if (!product.getStatus().equals(status) && StatusEnum.getInfoByCode(status) != null) {
            product.setStatus(status);
            boolean ret = baseMapper.updateById(product) > 0;
            if (ret && StatusEnum.DISABLE.getCode().equals(status)) {
                // TODO-NEW 删除产品平台关联数据
                platProdHelper.removeByCodeAndType(product.getCode(), OprPlatProdHelper.CONSTANTS.PRODUCT);
            }
        }
        scheduledExecutorService.schedule(() -> deleteCache(product.getCode()), 0, TimeUnit.SECONDS);
        return true;
    }

    /**
     * 根据code获取产品信息
     *
     * @param code 产品code
     * @return 产品信息
     */
    @Override
    public OprProduct queryByCode(String code) {
        LambdaQueryWrapper<OprProduct> lqw = Wrappers.lambdaQuery();
        lqw.eq(OprProduct::getCode, code)
                .last(Constants.LIMIT_ONE);

        OprProduct product = baseMapper.selectOne(lqw);

        // 异步添加缓存
        scheduledExecutorService.schedule(() -> addCache(product), 0, TimeUnit.SECONDS);

        return product;
    }

    /**
     * 添加缓存
     *
     * @param product 产品信息
     */
    private void addCache(OprProduct product) {
        if (product == null) {
            return;
        }
        // 缓存KEY
        String cacheCodeKey = OprCacheKeyEnum.OPR_PRODUCT_INFO_KEY.create(product.getCode());
        RedisUtils.setCacheObject(cacheCodeKey, BeanUtils.copyProperties(product, RemoteProductVo.class),
                OprCacheKeyEnum.OPR_PRODUCT_INFO_KEY.getDuration());
    }

    /**
     * 删除缓存
     *
     * @param code
     */
    private void deleteCache(String code) {
        RedisUtils.deleteObject(OprCacheKeyEnum.OPR_PRODUCT_INFO_KEY.create(code));
    }
}
