package com.feidi.xx.cross.operate.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.operate.domain.vo.ExportVo;
import com.feidi.xx.cross.operate.domain.bo.OprPlatformBo;
import com.feidi.xx.cross.operate.domain.vo.OprPlatformVo;
import com.feidi.xx.cross.operate.service.IOprPlatformService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 平台
 * 前端访问路由地址为:/operate/platform
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/platform")
public class OprPlatformController extends BaseController {

    private final IOprPlatformService oprPlatformService;

    /**
     * 查询平台列表
     */
    @SaCheckPermission("operate:platform:list")
    @GetMapping("/list")
    public TableDataInfo<OprPlatformVo> list(OprPlatformBo bo, PageQuery pageQuery) {
        return oprPlatformService.queryPageList(bo, pageQuery);
    }
    /**
     * 查询平台列表-全部
     */
    @SaCheckPermission("operate:platform:list")
    @GetMapping("/list/all")
    public R<List<OprPlatformVo>> list(OprPlatformBo bo) {
        return R.ok(oprPlatformService.queryList(bo, OprPlatformVo.class));
    }

    /**
     * 导出平台列表
     */
    @SaCheckPermission("operate:platform:export")
    @Log(title = "平台", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OprPlatformBo bo, HttpServletResponse response) {
        List<OprPlatformVo> list = oprPlatformService.queryList(bo, OprPlatformVo.class);
        ExcelUtil.exportExcel(list, "平台", OprPlatformVo.class, response);
    }

    /**
     * 获取平台详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("operate:platform:query")
    @GetMapping("/{id}")
    public R<OprPlatformVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(oprPlatformService.queryById(id));
    }

    /**
     * 新增平台
     */
    @SaCheckPermission("operate:platform:add")
    @Log(title = "平台", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OprPlatformBo bo) {
        return toAjax(oprPlatformService.insertByBo(bo));
    }

    /**
     * 修改平台
     */
    @SaCheckPermission("operate:platform:edit")
    @Log(title = "平台", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OprPlatformBo bo) {
        return toAjax(oprPlatformService.updateByBo(bo));
    }

    /**
     * 删除平台
     *
     * @param ids 主键串
     */
    @SaCheckPermission("operate:platform:remove")
    @Log(title = "平台", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(oprPlatformService.deleteWithValidByIds(List.of(ids), true));
    }
}
