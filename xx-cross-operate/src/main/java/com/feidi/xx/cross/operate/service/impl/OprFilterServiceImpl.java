package com.feidi.xx.cross.operate.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.operate.domain.OprFilter;
import com.feidi.xx.cross.operate.domain.bo.OprFilterBo;
import com.feidi.xx.cross.operate.domain.vo.OprFilterVo;
import com.feidi.xx.cross.operate.mapper.OprFilterMapper;
import com.feidi.xx.cross.operate.service.IOprFilterService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.feidi.xx.common.core.utils.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 过滤规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@RequiredArgsConstructor
@Service
public class OprFilterServiceImpl implements IOprFilterService {

    private final OprFilterMapper baseMapper;

    /**
     * 查询过滤规则
     *
     * @param id 主键
     * @return 过滤规则
     */
    @Override
    public OprFilterVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询过滤规则列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 过滤规则分页列表
     */
    @Override
    public TableDataInfo<OprFilterVo> queryPageList(OprFilterBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OprFilter> lqw = buildQueryWrapper(bo);
        Page<OprFilterVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的过滤规则列表
     *
     * @param bo 查询条件
     * @return 过滤规则列表
     */
    @Override
    public List<OprFilterVo> queryList(OprFilterBo bo) {
        LambdaQueryWrapper<OprFilter> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OprFilter> buildQueryWrapper(OprFilterBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OprFilter> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPlatformCode()), OprFilter::getPlatformCode, bo.getPlatformCode());
        lqw.eq(bo.getLineId() != null, OprFilter::getLineId, bo.getLineId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), OprFilter::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增过滤规则
     *
     * @param bo 过滤规则
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OprFilterBo bo) {
        OprFilter add = MapstructUtils.convert(bo, OprFilter.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改过滤规则
     *
     * @param bo 过滤规则
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OprFilterBo bo) {
        OprFilter update = MapstructUtils.convert(bo, OprFilter.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OprFilter entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除过滤规则信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据线路id查询过滤规则
     *
     * @param lineId
     * @return
     */
    @Override
    public OprFilterVo selectByLineId(Long lineId) {
        List<OprFilterVo> oxLineFilterVos = selectByLineIds(Collections.singletonList(lineId));
        return CollUtil.isEmpty(oxLineFilterVos) ? null :oxLineFilterVos.get(0);
    }
    /**
     * 根据线路id查询过滤规则
     *
     * @param lineIds
     * @return
     */
    @Override
    public List<OprFilterVo> selectByLineIds(List<Long> lineIds) {

        if (CollUtil.isEmpty(lineIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OprFilter> lqw = Wrappers.lambdaQuery();
        lqw.in(OprFilter::getLineId, lineIds);
        return baseMapper.selectVoList(lqw);
    }
}
