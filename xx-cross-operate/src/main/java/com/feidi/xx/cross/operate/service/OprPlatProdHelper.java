package com.feidi.xx.cross.operate.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.constant.operate.OperateCacheConstants;
import com.feidi.xx.cross.operate.domain.OprPlatformProduct;
import com.feidi.xx.cross.operate.domain.bo.OprPlatformProductBo;
import com.feidi.xx.cross.operate.mapper.OprPlatformProductMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 专门处理获取平台、产品相关信息的助手类
 */
@Slf4j
@Component
@AllArgsConstructor
public class OprPlatProdHelper {

    /**
     * 缓存key
     */
    private static final String KEY = OperateCacheConstants.PLAT_PROD_KEY;

    public static class CONSTANTS {
        public static final String PLATFORM = "PLATFORM";
        public static final String PRODUCT = "PRODUCT";
    }

    private final OprPlatformProductMapper baseMapper;

    private List<OprPlatformProductBo> queryListInDB() {
        List<OprPlatformProduct> list = baseMapper.selectList();
        return BeanUtils.copyToList(list, OprPlatformProductBo.class);
    }

    public List<OprPlatformProductBo> queryList() {
        List<OprPlatformProductBo> cache = RedisUtils.getCacheObject(KEY);
        if (CollUtil.isEmpty(cache)) {
            cache = queryListInDB();
            RedisUtils.setCacheObject(KEY, cache);
        }
        return cache;
    }

    /**
     * 根据code和type删除相关的数据
     * @param code
     * @param type PLATFORM/PRODUCT
     * @return
     */
    public boolean removeByCodeAndType(String code, String type) {
        OprPlatformProductBo bo = OprPlatformProductBo.newInstance(code, type);
        boolean result = baseMapper.delete(buildQueryWrapper(bo)) > 0;
        removeCache();
        return result;
    }

    /**
     * 所有修改到缓存内相关数据的都需要清除
     */
    public static void removeCache() {
        RedisUtils.deleteObject(KEY);
    }

    public static LambdaQueryWrapper<OprPlatformProduct> buildQueryWrapper(OprPlatformProductBo bo) {
        LambdaQueryWrapper<OprPlatformProduct> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPlatformCode() != null, OprPlatformProduct::getPlatformCode, bo.getPlatformCode());
        lqw.eq(bo.getProductCode() != null, OprPlatformProduct::getProductCode, bo.getProductCode());
        return lqw;
    }

}
