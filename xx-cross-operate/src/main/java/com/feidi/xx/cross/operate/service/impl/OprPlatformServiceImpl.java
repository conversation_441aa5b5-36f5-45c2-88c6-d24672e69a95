package com.feidi.xx.cross.operate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.operate.enums.OprCacheKeyEnum;
import com.feidi.xx.cross.operate.api.domain.platform.vo.RemotePlatformVo;
import com.feidi.xx.cross.operate.domain.OprPlatform;
import com.feidi.xx.cross.operate.domain.bo.OprPlatformBo;
import com.feidi.xx.cross.operate.domain.vo.OprPlatformVo;
import com.feidi.xx.cross.operate.mapper.OprPlatformMapper;
import com.feidi.xx.cross.operate.service.IOprPlatformService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 平台Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@RequiredArgsConstructor
@Service
public class OprPlatformServiceImpl implements IOprPlatformService {

    private final ScheduledExecutorService scheduledExecutorService;
    private final OprPlatformMapper baseMapper;

    /**
     * 查询平台
     *
     * @param id 主键
     * @return 平台
     */
    @Override
    public OprPlatformVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询平台列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 平台分页列表
     */
    @Override
    public TableDataInfo<OprPlatformVo> queryPageList(OprPlatformBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OprPlatform> lqw = buildQueryWrapper(bo);
        Page<OprPlatformVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的平台列表
     *
     * @param bo 查询条件
     * @return 平台列表
     */
    @Override
    public <T> List<T> queryList(OprPlatformBo bo, Class<T> clazz) {
        LambdaQueryWrapper<OprPlatform> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw, clazz);
    }

    private LambdaQueryWrapper<OprPlatform> buildQueryWrapper(OprPlatformBo bo) {
        LambdaQueryWrapper<OprPlatform> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), OprPlatform::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), OprPlatform::getCode, bo.getCode());
        lqw.eq(StringUtils.isNotBlank(bo.getAppId()), OprPlatform::getAppId, bo.getAppId());
        lqw.eq(StringUtils.isNotBlank(bo.getAppKey()), OprPlatform::getAppKey, bo.getAppKey());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), OprPlatform::getStatus, bo.getStatus());
        lqw.eq(bo.getRate() != null, OprPlatform::getRate, bo.getRate());
        return lqw;
    }

    /**
     * 新增平台
     *
     * @param bo 平台
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OprPlatformBo bo) {
        OprPlatform add = MapstructUtils.convert(bo, OprPlatform.class);
        validEntityBeforeSave(add);
        add.setUpdateTime(DateUtils.getNowDate());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());

            // 添加缓存
            scheduledExecutorService.schedule(() -> addCache(add), 0, TimeUnit.SECONDS);
        }
        return flag;
    }

    /**
     * 修改平台
     *
     * @param bo 平台
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OprPlatformBo bo) {
        OprPlatform update = MapstructUtils.convert(bo, OprPlatform.class);
        validEntityBeforeSave(update);
        update.setUpdateTime(DateUtils.getNowDate());
        boolean updateFlag = baseMapper.updateById(update) > 0;

        if (updateFlag) {
            // 添加缓存
            scheduledExecutorService.schedule(() -> addCache(update), 0, TimeUnit.SECONDS);
        }

        return updateFlag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OprPlatform entity) {
        OprPlatform oprPlatform = queryByCode(entity.getCode());
        if (entity.getId() == null) {
            Assert.isNull(oprPlatform, "当前编码已存在");
        } else {
            if (oprPlatform != null && !oprPlatform.getId().equals(entity.getId())) {
                throw new ServiceException("当前编码已存在");
            }
        }
    }

    @Override
    public OprPlatform queryByCode(String code) {
        LambdaQueryWrapper<OprPlatform> lqw = Wrappers.lambdaQuery();
        lqw.eq(OprPlatform::getCode, code);
        OprPlatform platform = baseMapper.selectOne(lqw);

        // 添加缓存
        scheduledExecutorService.schedule(() -> addCache(platform), 0, TimeUnit.SECONDS);

        return platform;
    }

    /**
     * 校验并批量删除平台信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }

        // 删除缓存
        scheduledExecutorService.schedule(() -> baseMapper.selectBatchIds(ids).forEach(platform -> deleteCache(platform.getCode())),
                0, TimeUnit.SECONDS);

        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据平台编码获取平台信息
     *
     * @return 平台信息
     */
    @Override
    public OprPlatformVo queryPlatformByCode(String platformCode) {
        // 结果集
        OprPlatformVo ptPlatformVo;

        // 缓存KEY
        String cacheCodeKey = OprCacheKeyEnum.OPR_PLATFORM_INFO_KEY.create(platformCode);

        if (RedisUtils.hasKey(cacheCodeKey)) {
            ptPlatformVo = BeanUtils.copyProperties(RedisUtils.getCacheObject(cacheCodeKey), OprPlatformVo.class);
        } else {
            LambdaQueryWrapper<OprPlatform> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OprPlatform::getCode, platformCode);
            ptPlatformVo = baseMapper.selectVoOne(queryWrapper);
        }

        return ptPlatformVo;
    }

    /**
     * 添加缓存
     *
     * @param platform 平台信息
     */
    public void addCache(OprPlatform platform) {
        // 缓存KEY - CODE
        String cacheCodeKey = OprCacheKeyEnum.OPR_PLATFORM_INFO_KEY.create(platform.getCode());

        RedisUtils.setCacheObject(cacheCodeKey, BeanUtils.copyProperties(platform, RemotePlatformVo.class),
                OprCacheKeyEnum.OPR_PLATFORM_INFO_KEY.getDuration());

    }

    /**
     * 删除缓存
     *
     * @param code
     */
    public void deleteCache(String code) {
        RedisUtils.deleteObject(OprCacheKeyEnum.OPR_PLATFORM_INFO_KEY.create(code));
    }
}
