package com.feidi.xx.cross.operate.controller.admin;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.cross.operate.domain.vo.ExportVo;
import com.feidi.xx.cross.operate.domain.bo.OprPlatformProductBo;
import com.feidi.xx.cross.operate.domain.vo.OprPlatformProductVo;
import com.feidi.xx.cross.operate.service.IOprPlatformProductService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 平台产品
 * 前端访问路由地址为:/operate/platformProduct
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platformProduct")
public class OprPlatformProductController extends BaseController {

    private final IOprPlatformProductService oprPlatformProductService;

    /**
     * 查询平台产品列表
     */
    @SaCheckPermission("operate:platformProduct:list")
    @GetMapping("/list")
    public TableDataInfo<OprPlatformProductVo> list(OprPlatformProductBo bo, PageQuery pageQuery) {
        return oprPlatformProductService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出平台产品列表
     */
    @SaCheckPermission("operate:platformProduct:export")
    @Log(title = "平台产品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OprPlatformProductBo bo, HttpServletResponse response) {
        List<OprPlatformProductVo> list = oprPlatformProductService.queryList(bo);
        ExcelUtil.exportExcel(list, "平台产品", OprPlatformProductVo.class, response);
    }

    /**
     * 获取平台产品详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("operate:platformProduct:query")
    @GetMapping("/{id}")
    public R<OprPlatformProductVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(oprPlatformProductService.queryById(id));
    }

    /**
     * 新增平台产品
     */
    @SaCheckPermission("operate:platformProduct:add")
    @Log(title = "平台产品", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OprPlatformProductBo bo) {
        return toAjax(oprPlatformProductService.insertByBo(bo));
    }

    /**
     * 修改平台产品
     */
    @SaCheckPermission("operate:platformProduct:edit")
    @Log(title = "平台产品", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OprPlatformProductBo bo) {
        return toAjax(oprPlatformProductService.updateByBo(bo));
    }

    /**
     * 删除平台产品
     *
     * @param ids 主键串
     */
    @SaCheckPermission("operate:platformProduct:remove")
    @Log(title = "平台产品", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(oprPlatformProductService.deleteWithValidByIds(List.of(ids), true));
    }
}
