package com.feidi.xx.cross.operate.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.operate.domain.OprFence;
import com.feidi.xx.cross.operate.domain.vo.OprFenceVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 电子围栏Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
public interface OprFenceMapper extends BaseMapperPlus<OprFence, OprFenceVo> {

    // 根据名称查询
    default OprFence queryByName(String name) {
        return selectOne(Wrappers.<OprFence>lambdaQuery().eq(OprFence::getName, name));
    }

}
