package com.feidi.xx.cross.operate.service;

import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.cross.operate.domain.bo.OprFilterBo;
import com.feidi.xx.cross.operate.domain.vo.OprFilterVo;

import java.util.Collection;
import java.util.List;

/**
 * 过滤规则Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IOprFilterService {

    /**
     * 查询过滤规则
     *
     * @param id 主键
     * @return 过滤规则
     */
    OprFilterVo queryById(Long id);

    /**
     * 分页查询过滤规则列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 过滤规则分页列表
     */
    TableDataInfo<OprFilterVo> queryPageList(OprFilterBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的过滤规则列表
     *
     * @param bo 查询条件
     * @return 过滤规则列表
     */
    List<OprFilterVo> queryList(OprFilterBo bo);

    /**
     * 新增过滤规则
     *
     * @param bo 过滤规则
     * @return 是否新增成功
     */
    Boolean insertByBo(OprFilterBo bo);

    /**
     * 修改过滤规则
     *
     * @param bo 过滤规则
     * @return 是否修改成功
     */
    Boolean updateByBo(OprFilterBo bo);

    /**
     * 校验并批量删除过滤规则信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据线路id查询过滤规则
     *
     * @param lineId
     * @return
     */
    OprFilterVo selectByLineId(Long lineId);

    /**
     * 根据线路id查询过滤规则
     *
     * @param lineIds
     * @return
     */
    List<OprFilterVo> selectByLineIds(List<Long> lineIds);
}
