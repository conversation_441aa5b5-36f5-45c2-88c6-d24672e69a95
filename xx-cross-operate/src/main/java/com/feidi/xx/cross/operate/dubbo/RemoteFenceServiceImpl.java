package com.feidi.xx.cross.operate.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.operate.api.RemoteFenceService;
import com.feidi.xx.cross.operate.api.domain.fence.vo.RemoteFenceVo;
import com.feidi.xx.cross.operate.domain.OprFence;
import com.feidi.xx.cross.operate.domain.vo.OprFenceVo;
import com.feidi.xx.cross.operate.mapper.OprFenceMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 电子围栏dubbo服务实现类
 *
 * <AUTHOR>
 * @date 2025/3/14
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteFenceServiceImpl implements RemoteFenceService {

    private final OprFenceMapper fenceMapper;

    /**
     * 根据cityCode获取电子围栏信息
     * @param cityCode
     * @return
     */
    @Override
    public List<RemoteFenceVo> queryByCityCode(String cityCode) {
        List<OprFenceVo> oprFenceVos = fenceMapper.selectVoList(new LambdaQueryWrapper<OprFence>()
                .eq(OprFence::getCityCode, cityCode));
        return BeanUtils.copyToList(oprFenceVos, RemoteFenceVo.class);    }
}
