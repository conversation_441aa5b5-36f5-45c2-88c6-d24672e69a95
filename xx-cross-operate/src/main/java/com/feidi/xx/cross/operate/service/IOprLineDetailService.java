package com.feidi.xx.cross.operate.service;

import com.feidi.xx.cross.operate.domain.OprLineDetail;
import com.feidi.xx.cross.operate.domain.bo.OprLineDetailForm;
import com.feidi.xx.cross.operate.domain.dto.OprLineMatchDTO;

import java.util.List;

/**
 * 线路详情Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IOprLineDetailService {

    /**
     * 根据线路id获取线路详情
     *
     * @param lineId 线路id
     * @return 线路详情
     */
    List<OprLineDetail> queryByLineId(Long lineId);

    /**
     * 新增线路详情
     *
     * @param bo 线路详情
     * @return 是否新增成功
     */
    Boolean insertByBo(OprLineDetailForm bo);

    /**
     * 匹配线路
     *
     * @param startAdCode
     * @param endAdCode
     * @return
     */
    OprLineMatchDTO matchLine(String startAdCode, String endAdCode);
}
