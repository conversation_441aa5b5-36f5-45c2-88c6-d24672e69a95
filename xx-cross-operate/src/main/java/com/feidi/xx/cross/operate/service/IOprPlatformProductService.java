package com.feidi.xx.cross.operate.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.operate.domain.bo.OprPlatformProductBo;
import com.feidi.xx.cross.operate.domain.vo.OprPlatformProductVo;

import java.util.Collection;
import java.util.List;

/**
 * 平台产品Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IOprPlatformProductService {

    /**
     * 查询平台产品
     *
     * @param id 主键
     * @return 平台产品
     */
    OprPlatformProductVo queryById(Long id);

    /**
     * 分页查询平台产品列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 平台产品分页列表
     */
    TableDataInfo<OprPlatformProductVo> queryPageList(OprPlatformProductBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的平台产品列表
     *
     * @param bo 查询条件
     * @return 平台产品列表
     */
    List<OprPlatformProductVo> queryList(OprPlatformProductBo bo);

    /**
     * 新增平台产品
     *
     * @param bo 平台产品
     * @return 是否新增成功
     */
    Boolean insertByBo(OprPlatformProductBo bo);

    /**
     * 修改平台产品
     *
     * @param bo 平台产品
     * @return 是否修改成功
     */
    Boolean updateByBo(OprPlatformProductBo bo);

    /**
     * 校验并批量删除平台产品信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据平台编码删除平台产品
     *
     * @param platformCode 平台编码
     * @return 是否删除成功
     */
    Boolean deleteByPlatformCode(String platformCode);

    /**
     * 是否存在相关记录
     * @param bo
     * @return
     */
    boolean existsByBo(OprPlatformProductBo bo);

    /**
     * 是否存在相关记录
     * @param boList
     * @return
     */
    boolean existsByBoList(List<OprPlatformProductBo> boList);

}
