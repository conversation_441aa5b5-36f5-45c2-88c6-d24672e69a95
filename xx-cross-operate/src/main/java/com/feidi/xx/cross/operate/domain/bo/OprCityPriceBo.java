package com.feidi.xx.cross.operate.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.operate.domain.OprCityPrice;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 城市价格关联业务对象 opr_city_price
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OprCityPrice.class, reverseConvertGenerate = false)
public class OprCityPriceBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 城市表id
     */
    @NotNull(message = "城市表id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long cityId;

    /**
     * 城市code
     */
    @NotBlank(message = "城市code不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cityCode;

    /**
     * 价格id
     */
    private Long priceId;

    /**
     * {@link PlatformCodeEnum}
     */
    private String platformCode;


}
