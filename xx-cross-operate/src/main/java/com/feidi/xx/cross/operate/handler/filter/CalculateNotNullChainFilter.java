

package com.feidi.xx.cross.operate.handler.filter;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.feidi.xx.common.core.enums.UserTypeEnum;

import com.feidi.xx.common.core.utils.ValidatorUtils;
import com.feidi.xx.common.map.model.tencent.route.TxMapRoute;
import com.feidi.xx.common.map.utils.TxMapUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.validate.MemberGroup;
import com.feidi.xx.cross.operate.chain.CalculateAbstractChainHandler;
import com.feidi.xx.cross.operate.domain.dto.OprCalculateDTO;
import org.springframework.stereotype.Component;

import static com.feidi.xx.cross.common.enums.operate.ChainBizMarkEnum.CALCULATE_KEY;


/**
 * 验证计算价格参数是否正确责任链｜验证必填参数是否为空或空的字符串
 *
 * <AUTHOR>
 */
@Component
public class CalculateNotNullChainFilter implements CalculateAbstractChainHandler<OprCalculateDTO> {

    @Override
    public void handler(OprCalculateDTO requestParam) {
        ValidatorUtils.validate(requestParam);
        if (StpUtil.isLogin()) {
            if (UserTypeEnum.PASSENGER_USER.equals(LoginHelper.getUserType())) {
                ValidatorUtils.validate(requestParam, MemberGroup.class);
            }
        }
        if (ObjectUtil.isNull(requestParam.getMileage()) || requestParam.getMileage()==0
                || ObjectUtil.isNull(requestParam.getExpectDuration()) || requestParam.getExpectDuration()==0) {
            // 根据经纬度计算距离及所需时间（默认返回第一种路线方案）
            TxMapRoute.Route route = TxMapUtils.route(String.valueOf(requestParam.getStartLongitude()), String.valueOf(requestParam.getStartLatitude()),
                    String.valueOf(requestParam.getEndLongitude()), String.valueOf(requestParam.getEndLatitude()));
            if (ObjectUtil.isNull(requestParam.getMileage()) || requestParam.getMileage()==0) {
                int distance = route.getDistance();
                requestParam.setMileage(distance);
            }
            // 预估时间
            if (ObjectUtil.isNull(requestParam.getExpectDuration()) || requestParam.getExpectDuration()==0) {
                int duration = route.getDuration();
                requestParam.setExpectDuration(duration * 60);
            }
        }



    }

    @Override
    public String mark() {
        return CALCULATE_KEY.name();
    }

    @Override
    public int getOrder() {
        return 0;
    }
}
