package com.feidi.xx.cross.operate.domain.bo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.common.enums.operate.HolidayTypeEnum;
import com.feidi.xx.cross.common.enums.operate.PricingStatusEnum;
import com.feidi.xx.cross.common.enums.operate.ProductCodeEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.operate.domain.OprPriceDetail;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 定价业务对象 uf_pricing
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OprPriceDetail.class, reverseConvertGenerate = false)
public class OprPriceDetailBo extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 价格模版id
     */
    private Long priceId;

    /**
     * 平台code {@link PlatformCodeEnum}
     */
    private String platformCode;

    /**
     * 日期类型 {@link HolidayTypeEnum}
     */
    private String holidayType;

    /**
     * 产品code {@link ProductCodeEnum}
     */
    private String productCode;

    /**
     * 起步价格模板
     */
    private String initiateData;

    /**
     * 里程价格模板
     */
    private String mileageData;

    /**
     * 状态 {@link  PricingStatusEnum}
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

}
