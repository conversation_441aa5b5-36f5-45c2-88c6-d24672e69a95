package com.feidi.xx.cross.operate.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import java.io.Serial;

/**
 * 渠道对象 opr_channel
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("opr_channel")
public class OprChannel extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;

    /**
     * 渠道名称
     */
    private String name;

    /**
     * 邀请码
     */
    private String code;

    /**
     * 渠道类型
     */
    private String type;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
