

package com.feidi.xx.cross.operate.handler.filter;

import cn.hutool.core.util.ObjectUtil;
import com.feidi.xx.common.core.cache.system.DistrictCacheVo;
import com.feidi.xx.common.core.cache.system.SysDistrictCacheVo;
import com.feidi.xx.common.core.enums.SystemCacheKeyEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.SpringUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import com.feidi.xx.cross.common.constant.operate.OperateCacheConstants;
import com.feidi.xx.cross.operate.api.domain.city.vo.RemoteCityVo;
import com.feidi.xx.cross.operate.chain.CalculateAbstractChainHandler;
import com.feidi.xx.cross.operate.domain.dto.OprCalculateDTO;
import com.feidi.xx.cross.operate.domain.vo.OprCalculateVo;
import com.feidi.xx.cross.operate.strategy.AbstractCalculateStrategy;
import com.feidi.xx.cross.operate.strategy.FixedCalculateStrategy;
import com.feidi.xx.cross.power.api.RemoteAgentCityService;
import com.feidi.xx.cross.power.api.domain.agent.vo.RemoteAgentCityVo;
import com.feidi.xx.system.api.RemoteConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalTime;
import java.time.ZoneId;

import static com.feidi.xx.cross.common.enums.operate.ChainBizMarkEnum.CALCULATE_KEY;


/**
 * 验证计算几个参数是否正确责任链｜验证参数基本数据关系是否正确
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CalculateBaseVerifyChainFilter implements CalculateAbstractChainHandler<OprCalculateDTO> {


    @Autowired
    private OprCacheManager oprCacheManager;

    @Autowired
    private RemoteAgentCityService remoteAgentCityService;
    @DubboReference
    private final RemoteConfigService remoteConfigService;

    @Override
    public void handler(OprCalculateDTO requestParam) {


        requestParam.getStartTime();
        //秒级时间戳校验 最晚出发时间是否小于当前时间
        Long startTime = requestParam.getEndTime(); // 秒级时间戳
        Long currentTimestamp = System.currentTimeMillis() / 1000; // 转换为秒级
        Long timestampMinus3Min = currentTimestamp - 3 * 60; // 减去3分钟
        if (startTime < timestampMinus3Min) {
            throw new ServiceException("最晚出发时间不能小于当前时间");
        }
        //校验起点与终点不可一致
        if (requestParam.getStartAddress() != null &&
                requestParam.getStartAddress().equals(requestParam.getEndAddress())){
            throw new ServiceException("起点和终点不能相同，请重新选择终点");
        }
        if (requestParam.getStartShortAddress() != null &&
                requestParam.getStartShortAddress().equals(requestParam.getEndShortAddress())){
            throw new ServiceException("起点和终点不能相同，请重新选择终点");
        }

//        if (requestParam.getPlatformCode().equals(PlatformCodeEnum.SELF.getCode())){
//            //校验人数
//            if (requestParam.getPassengerCount() > 4) {
//                throw new ServiceException("人数不能大于4人");
//            }
//        }
        UserTypeEnum userType = LoginHelper.getUserType();
        if (ObjectUtils.isNotEmpty(userType)) {
            if (UserTypeEnum.AGENT_USER.getUserType().equals(userType.getUserType())||  UserTypeEnum.DRIVER_USER.getUserType().equals(userType.getUserType())) {
                String startAdCode = requestParam.getStartAdCode();
                SysDistrictCacheVo value = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_AD_CODE_KEY.create(), startAdCode);
                if (ObjectUtils.isEmpty(value)) {
                    throw new ServiceException("当前线络不在您的运营范围，暂无法提供服务");
                }
                String cityCode = value.getCityCode();
                // 根据城市code查询城市信息
                RemoteCityVo cityInfoByCode = oprCacheManager.getCityInfoByCode(cityCode);
                if (ObjectUtils.isEmpty(cityInfoByCode)) {
                    throw new ServiceException("当前线络不在您的运营范围，暂无法提供服务");
                }
                Long cityInfoByCodeId = cityInfoByCode.getId();
                RemoteAgentCityVo agentCityInfoByAgentIdAndCityId = remoteAgentCityService.getAgentCityInfoByAgentIdAndCityId(LoginHelper.getAgentId(), cityInfoByCodeId);
                if (ObjectUtils.isEmpty(agentCityInfoByAgentIdAndCityId)) {
                    throw new ServiceException("当前线络不在您的运营范围，暂无法提供服务");
                }
            }
            //乘客端校验起点与终点最小距离限制
            if (UserTypeEnum.PASSENGER_USER.getUserType().equals(userType.getUserType())) {
                String minDistance = remoteConfigService.selectValueByKey(OperateCacheConstants.MIN_DISTANCE);
                if (StringUtils.isEmpty(minDistance)) {
                    minDistance = "3";
                }
                int minDistanceInt;
                try {
                    minDistanceInt = new BigDecimal(minDistance).movePointRight(2).intValue() * 10;
                }catch (NumberFormatException e){
                    throw new ServiceException("参数设置-最小下单距离-参数格式错误");
                }

                minDistanceInt = Math.max(minDistanceInt, 3000);
                if (ObjectUtil.isNull(requestParam.getMileage()) || requestParam.getMileage() == 0) {
                    throw new ServiceException("起点终点距离过近，无法下单");
                }
                if (requestParam.getMileage() < minDistanceInt){
                    throw new ServiceException("起点终点距离过近，无法下单");
                }
            }
        }
        //如果如果前端代码未传startCityCode，则根据startAdCode获取startCityCode
        if (ObjectUtils.isEmpty(requestParam.getStartCityCode())) {
            DistrictCacheVo districtCacheVByAdCode = oprCacheManager.getDistrictCacheVByAdCode(requestParam.getStartAdCode());
            requestParam.setStartCityCode(districtCacheVByAdCode.getCityCode());
            log.info("startCityCode1:{}", requestParam.getStartCityCode());
        }

        if (ObjectUtils.isEmpty(requestParam.getEndCityCode())) {
            DistrictCacheVo districtCacheVByAdCode = oprCacheManager.getDistrictCacheVByAdCode(requestParam.getEndAdCode());
            requestParam.setEndCityCode(districtCacheVByAdCode.getCityCode());
            log.info("endCityCode1:{}", requestParam.getEndCityCode());
        }

        RemoteCityVo cityInfoByCode = oprCacheManager.getCityInfoByCode(requestParam.getStartCityCode());
        if (ObjectUtils.isNotEmpty(cityInfoByCode) && ObjectUtils.isNotEmpty(requestParam.getEndCityCode())) {
            if (ObjectUtils.isNotEmpty(cityInfoByCode) && ObjectUtils.isNotEmpty(cityInfoByCode.getStartTime()) && ObjectUtils.isNotEmpty(cityInfoByCode.getEndTime())) {
                // 获取出发时间并转换为 LocalTime
                long startSeconds = requestParam.getStartTime();
                LocalTime requestTime = Instant.ofEpochSecond(startSeconds)
                        .atZone(ZoneId.systemDefault())
                        .toLocalTime();

                // 获取城市运营开始和结束时间
                LocalTime cityStartTime = LocalTime.parse(cityInfoByCode.getStartTime());
                String endTimeStr = cityInfoByCode.getEndTime();

                // 特殊处理 "24:00" -> 第二天 00:00（逻辑上延后一天）
                LocalTime cityEndTime;
                boolean crossMidnight = false;

                if ("24:00".equals(endTimeStr)) {
                    cityEndTime = LocalTime.MIDNIGHT; // 即 00:00
                    crossMidnight = true; // 标记为跨天
                } else {
                    cityEndTime = LocalTime.parse(endTimeStr);
                    // 判断是否跨天，例如 "22:00" 到 "06:00"
                    crossMidnight = cityEndTime.isBefore(cityStartTime);
                }
                if (requestParam.getCheckOperateTime().equals("Y")) {
                    boolean inBusinessHours;
                    if (crossMidnight) {
                        // 跨天时，例如 22:00 ~ 次日 06:00，逻辑为：时间 >= 开始 || 时间 < 结束
                        inBusinessHours = !requestTime.isBefore(cityStartTime) || requestTime.isBefore(cityEndTime);
                    } else {
                        // 非跨天时：时间在开始和结束之间
                        inBusinessHours = !requestTime.isBefore(cityStartTime) && requestTime.isBefore(cityEndTime);
                    }
                    if (!inBusinessHours) {
                        requestParam.setReason("出发时间不在该城市运营时间范围内");
                        AbstractCalculateStrategy bean = SpringUtils.getBean(FixedCalculateStrategy.class);
                        bean.saveEstimateRecord(requestParam, new OprCalculateVo());
                        throw new ServiceException("出发时间不在该城市运营时间范围内");
                    }
                }
            }

        }
    }

    @Override
    public String mark() {
        return CALCULATE_KEY.name();
    }

    @Override
    public int getOrder() {
        return 2;
    }
}
