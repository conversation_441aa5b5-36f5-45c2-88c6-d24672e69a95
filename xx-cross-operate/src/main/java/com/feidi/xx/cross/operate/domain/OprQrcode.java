package com.feidi.xx.cross.operate.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import java.io.Serial;

/**
 * 推广码对象 opr_qrcode
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("opr_qrcode")
public class OprQrcode extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;

    /**
     * 推广码名称
     */
    private String name;

    /**
     * 渠道ID
     */
    private Long channelId;

    /**
     * 邀请码
     */
    private String code;

    /**
     * 二维码链接
     */
    private String qrcodeUrl;

    /**
     * 广告图链接
     */
    private String adUrl;

    /**
     * Logo链接
     */
    private String logoUrl;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
