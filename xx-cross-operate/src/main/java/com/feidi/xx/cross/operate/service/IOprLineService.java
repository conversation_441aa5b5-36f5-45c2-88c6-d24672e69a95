package com.feidi.xx.cross.operate.service;

import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.cross.operate.domain.bo.OprLineBo;
import com.feidi.xx.cross.operate.domain.vo.OprLineVo;
import jakarta.validation.constraints.NotNull;

import java.util.Collection;
import java.util.List;

/**
 * 线路Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IOprLineService {

    /**
     * 查询线路
     *
     * @param id 主键
     * @return 线路
     */
    OprLineVo queryById(Long id);

    /**
     * 分页查询线路列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 线路分页列表
     */
    TableDataInfo<OprLineVo> queryPageList(OprLineBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的线路列表
     *
     * @param bo 查询条件
     * @return 线路列表
     */
    List<OprLineVo> queryList(OprLineBo bo);

    /**
     * 新增线路
     *
     * @param bo 线路
     * @return 是否新增成功
     */
    Boolean insertByBo(OprLineBo bo);

    /**
     * 修改线路
     *
     * @param bo 线路
     * @return 是否修改成功
     */
    Boolean updateByBo(OprLineBo bo);

    /**
     * 校验并批量删除线路信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 修改线路
     * @param id
     * @param status
     * @return
     */
    Boolean updateStatus(Long id, String status);


    /**
     * 根据价格模版id查询关联线路
     *
     * @param priceId 价格模版id
     * @return 城市数量
     */
    Integer countByPriceId(Long priceId);

    /**
     * 根据价格模版
     * @param priceId 价格模版id
     * */
    List<OprLineVo> queryByPriceId(@NotNull(message = "价格模版id不能为空") Long priceId);

    List<OprLineVo> listByFenceId(@NotNull(message = "电子围栏id不能为空") Long fenceId);

}
