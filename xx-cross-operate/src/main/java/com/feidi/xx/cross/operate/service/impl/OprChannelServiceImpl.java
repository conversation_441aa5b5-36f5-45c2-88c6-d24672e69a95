package com.feidi.xx.cross.operate.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.operate.domain.OprQrcode;
import com.feidi.xx.cross.operate.mapper.OprQrcodeMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.feidi.xx.cross.operate.domain.bo.OprChannelBo;
import com.feidi.xx.cross.operate.domain.vo.OprChannelVo;
import com.feidi.xx.cross.operate.domain.OprChannel;
import com.feidi.xx.cross.operate.mapper.OprChannelMapper;
import com.feidi.xx.cross.operate.service.IOprChannelService;
import com.feidi.xx.common.core.utils.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 渠道Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@RequiredArgsConstructor
@Service
public class OprChannelServiceImpl implements IOprChannelService {

    private final OprChannelMapper baseMapper;
    private final OprQrcodeMapper qrcodeMapper;

    /**
     * 查询渠道
     *
     * @param id 主键
     * @return 渠道
     */
    @Override
    public OprChannelVo queryById(String id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询渠道列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 渠道分页列表
     */
    @Override
    public TableDataInfo<OprChannelVo> queryPageList(OprChannelBo bo, PageQuery pageQuery) {

        LambdaQueryWrapper<OprChannel> lqw = buildQueryWrapper(bo);
        Page<OprChannel> result = baseMapper.selectPage(pageQuery.build(), lqw);
        List<OprChannelVo> records = MapstructUtils.convert(result.getRecords(), OprChannelVo.class);
        if (CollUtil.isNotEmpty(records)) {
            //推广码数量
            Map<Long, Long> qrcodeMap = getQrcodeMap(records.stream().map(OprChannelVo::getId).toList());
            records.forEach(item -> item.setNum(qrcodeMap.get(item.getId())));
        }
        return TableDataInfo.build(records);
    }

    private Map<Long, Long> getQrcodeMap(List<Long> ids) {
        Map<Long, Long> qrcodeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(ids)) {
            List<OprQrcode> oprQrcodes = qrcodeMapper.queryByChannelIds(ids);
            for (OprQrcode oprQrcode : oprQrcodes) {
                Long l = qrcodeMapper.selectCount(new LambdaQueryWrapper<OprQrcode>()
                        .eq(OprQrcode::getChannelId, oprQrcode.getChannelId()));
                qrcodeMap.put(oprQrcode.getChannelId(), l);
            }
        }
        return qrcodeMap;
    }

    /**
     * 查询符合条件的渠道列表
     *
     * @param bo 查询条件
     * @return 渠道列表
     */
    @Override
    public List<OprChannelVo> queryList(OprChannelBo bo) {
        LambdaQueryWrapper<OprChannel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OprChannel> buildQueryWrapper(OprChannelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OprChannel> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), OprChannel::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), OprChannel::getCode, bo.getCode());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), OprChannel::getType, bo.getType());
        return lqw;
    }

    /**
     * 新增渠道
     *
     * @param bo 渠道
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OprChannelBo bo) {
        OprChannel add = MapstructUtils.convert(bo, OprChannel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改渠道
     *
     * @param bo 渠道
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OprChannelBo bo) {
        OprChannel update = MapstructUtils.convert(bo, OprChannel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OprChannel entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除渠道信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
