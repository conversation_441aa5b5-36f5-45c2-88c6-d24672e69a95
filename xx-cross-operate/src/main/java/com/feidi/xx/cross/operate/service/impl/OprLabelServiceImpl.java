package com.feidi.xx.cross.operate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.operate.domain.OprLabel;
import com.feidi.xx.cross.operate.domain.bo.OprLabelBo;
import com.feidi.xx.cross.operate.domain.vo.OprLabelVo;
import com.feidi.xx.cross.operate.mapper.OprLabelMapper;
import com.feidi.xx.cross.operate.service.IOprLabelService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 平台-产品Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@RequiredArgsConstructor
@Service
public class OprLabelServiceImpl implements IOprLabelService {

    private final OprLabelMapper baseMapper;

    /**
     * 查询标签
     *
     * @param id 主键
     * @return 标签
     */
    @Override
    public OprLabelVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询标签列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 标签分页列表
     */
    @Override
    public TableDataInfo<OprLabelVo> queryPageList(OprLabelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OprLabel> lqw = buildQueryWrapper(bo);
        Page<OprLabelVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的平台-产品列表
     *
     * @param bo 查询条件
     * @return 平台-产品列表
     */
    @Override
    public <T> List<T> queryList(OprLabelBo bo, Class<T> clazz) {
        LambdaQueryWrapper<OprLabel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw, clazz);
    }

    private LambdaQueryWrapper<OprLabel> buildQueryWrapper(OprLabelBo bo) {
        LambdaQueryWrapper<OprLabel> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), OprLabel::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), OprLabel::getType, bo.getType());
        return lqw;
    }

    /**
     * 新增平台-产品
     *
     * @param bo 平台-产品
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OprLabelBo bo) {
        OprLabel add = MapstructUtils.convert(bo, OprLabel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改标签
     *
     * @param bo 标签
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OprLabelBo bo) {
        OprLabel update = MapstructUtils.convert(bo, OprLabel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OprLabel entity){
        LambdaQueryWrapper<OprLabel> queryWrapper = Wrappers.<OprLabel>lambdaQuery()
                .eq(OprLabel::getName, entity.getName())
                .eq(OprLabel::getType, entity.getType())
                .ne(entity.getId() != null, OprLabel::getId, entity.getId())
                .last(Constants.LIMIT_ONE);

        OprLabel label = baseMapper.selectOne(queryWrapper);

        if (ObjectUtils.isNotNull(label)) {
            throw new ServiceException("当前标签已存在");
        }
    }

    /**
     * 校验并批量删除标签信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return baseMapper.deleteByIds(ids) > 0;
    }
}
