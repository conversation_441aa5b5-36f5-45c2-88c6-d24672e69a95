package com.feidi.xx.cross.operate.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.operate.domain.OprLine;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.List;

/**
 * 线路业务对象 opr_line
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OprLine.class, reverseConvertGenerate = false)
public class OprLineBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 起点省
     */
    @NotNull(message = "起点省不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long startProvinceId;

    /**
     * 起点城市
     */
    @NotNull(message = "起点城市不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long startCityId;

    /**
     * 起点城市编码
     */
    private String startCityCode;

    /**
     * 起点定价
     */
    private Long startPricingId;

    /**
     * 终点省
     */
    @NotNull(message = "终点省不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long endProvinceId;

    /**
     * 终点城市
     */
    @NotNull(message = "终点城市不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long endCityId;

    /**
     * 终点城市编码
     */
    private String endCityCode;

    /**
     * 终点定价
     */
    private Long endPricingId;

    /**
     * 类型
     */
    private String type;

    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 标签
     */
    private List<String> labels;

    /**
     * 线路列表
     */
    private List<Long> ids;

}
