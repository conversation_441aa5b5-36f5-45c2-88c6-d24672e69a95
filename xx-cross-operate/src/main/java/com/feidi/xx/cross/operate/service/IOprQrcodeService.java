package com.feidi.xx.cross.operate.service;

import com.feidi.xx.cross.operate.domain.OprQrcode;
import com.feidi.xx.cross.operate.domain.vo.OprQrcodeVo;
import com.feidi.xx.cross.operate.domain.bo.OprQrcodeBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 推广码Service接口
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
public interface IOprQrcodeService {

    /**
     * 查询推广码
     *
     * @param id 主键
     * @return 推广码
     */
    OprQrcodeVo queryById(String id);

    /**
     * 分页查询推广码列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 推广码分页列表
     */
    TableDataInfo<OprQrcodeVo> queryPageList(OprQrcodeBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的推广码列表
     *
     * @param bo 查询条件
     * @return 推广码列表
     */
    List<OprQrcodeVo> queryList(OprQrcodeBo bo);

    /**
     * 新增推广码
     *
     * @param bo 推广码
     * @return 是否新增成功
     */
    Boolean insertByBo(OprQrcodeBo bo);

    /**
     * 修改推广码
     *
     * @param bo 推广码
     * @return 是否修改成功
     */
    Boolean updateByBo(OprQrcodeBo bo);

    /**
     * 校验并批量删除推广码信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
