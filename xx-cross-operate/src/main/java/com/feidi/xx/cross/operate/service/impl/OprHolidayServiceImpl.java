package com.feidi.xx.cross.operate.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.tenant.helper.TenantHelper;

import com.feidi.xx.cross.common.constant.operate.OperateCacheConstants;
import com.feidi.xx.cross.common.cache.operate.enums.OprCacheKeyEnum;
import com.feidi.xx.cross.operate.api.domain.holiday.vo.RemoteHolidayVo;
import com.feidi.xx.cross.operate.domain.OprHoliday;
import com.feidi.xx.cross.operate.domain.bo.OprHolidayBo;
import com.feidi.xx.cross.operate.domain.vo.OprHolidayVo;
import com.feidi.xx.cross.operate.mapper.OprHolidayMapper;
import com.feidi.xx.cross.operate.service.IOprHolidayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 定价Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OprHolidayServiceImpl implements IOprHolidayService {

    private final OprHolidayMapper baseMapper;

    private final ScheduledExecutorService scheduledExecutorService;


    /**
     * 查询定价
     *
     * @param id 主键
     * @return 定价
     */
    @Override
    public OprHolidayVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询定价列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 定价分页列表
     */
    @Override
    public TableDataInfo<OprHolidayVo> queryPageList(OprHolidayBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OprHoliday> lqw = buildQueryWrapper(bo);
        Page<OprHolidayVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的定价列表
     *
     * @param bo 查询条件
     * @return 定价列表
     */
    @Override
    public List<OprHolidayVo> queryList(OprHolidayBo bo) {
        LambdaQueryWrapper<OprHoliday> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OprHoliday> buildQueryWrapper(OprHolidayBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OprHoliday> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getId() != null, OprHoliday::getId, bo.getId());
        return lqw;
    }

    /**
     * 新增定价
     *
     * @param bo 定价
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OprHolidayBo bo) {
        OprHoliday add = MapstructUtils.convert(bo, OprHoliday.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            scheduledExecutorService.schedule(() -> addCache(add), 0, TimeUnit.SECONDS);
        }
        return flag;
    }

    /**
     * 修改定价
     *
     * @param bo 定价
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OprHolidayBo bo) {
        OprHoliday update = MapstructUtils.convert(bo, OprHoliday.class);
        validEntityBeforeSave(update);
        boolean ret = baseMapper.updateById(update) > 0;
        // 异步添加缓存
        scheduledExecutorService.schedule(() -> addCache(update), 0, TimeUnit.SECONDS);

        return ret;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OprHoliday entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除定价信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 同步节假日数据
     *
     * @return
     */
    @Override
    public Boolean syncHoliday() {
        return syncHoliday(DateUtils.getNowYear());
    }

    @Override
    public Boolean syncHoliday(Integer year) {
        LambdaQueryWrapper<OprHoliday> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OprHoliday::getYear, year);
        TenantHelper.ignore(() ->{
            baseMapper.delete(queryWrapper);
        });
        /// 调接口获取该年份的节假日数据
        return syncHolidayApi(year);
    }

    /**
     * 同步节假日数据
     * <p>
     * 【就一个接口、暂时就放在这里】
     *
     * @param year
     * @return
     */

    public Boolean syncHolidayApi(Integer year) {
        try {
            JSONObject holiday = JSON.parseObject(DateUtils.getHolidayData(year));
            List<OprHoliday> OprHolidays = new ArrayList<>();
            for (String key : holiday.keySet()) {
                OprHoliday OprHoliday = new OprHoliday();
                JSONObject data = holiday.getJSONObject(key);
                //获取是否是节假日
                Boolean aBoolean = data.getBoolean(OperateCacheConstants.HOLIDAY);
                OprHoliday.setHoliday(aBoolean ? IsYesEnum.YES.getCode() :IsYesEnum.NO.getCode());
                OprHoliday.setYear(year);
                //获取名称
                String name = data.getString(OperateCacheConstants.NAME);
                OprHoliday.setName(name);
                //获取日期
                String date = data.getString(OperateCacheConstants.DATE);
                OprHoliday.setDate(date);
                OprHolidays.add(OprHoliday);
                DateUtils.parseDate(date, date);
            }
            TenantHelper.ignore(() -> {
                baseMapper.insert(OprHolidays);
                    });

        } catch (Exception e) {
            log.error("节假日获取失败!请联系管理人员 获取年份:" + year, e.getMessage());
            return false;
        }
        return true;
    }

    @Override
    public Boolean isHoliday(Date holidayDate) {
        /**
         * 1.先查询缓存是否存在如果存在直接返回
         * 2.如果不存在去数据库查询并做相应的逻辑校验
         * 3.返回然后缓存中放一份 缓存时间为24个小时
         */
        String dateTime = DateUtils.dateTime(holidayDate);
        String holidayKey = OprCacheKeyEnum.OPR_HOLIDAY_INFO_KEY.create(dateTime);
        if (RedisUtils.hasKey(holidayKey)){
            return RedisUtils.getCacheObject(holidayKey);
        }
        Boolean isHoliday=false;
        LambdaQueryWrapper<OprHoliday> lqw = new LambdaQueryWrapper<>();
        lqw.eq(OprHoliday::getDate, dateTime);
            OprHolidayVo OprHolidayVo = baseMapper.selectVoOne(lqw);
        if (OprHolidayVo != null){
            isHoliday = IsYesEnum.YES.getCode().equals(OprHolidayVo.getHoliday());

        }else {
            isHoliday = DateUtils.isWeekendDay(holidayDate);
        }
        RedisUtils.setCacheObject(holidayKey,isHoliday, OprCacheKeyEnum.OPR_HOLIDAY_INFO_KEY.getDuration());
        return isHoliday;


    }

    /**
     * 根据日期查询节假日信息
     *
     * @param holidayDate yyyy-MM-dd
     * @return [true.是|false.否]
     */
    private Boolean verifyHoliday(String holidayDate) {
        LambdaQueryWrapper<OprHoliday> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OprHoliday::getDate, holidayDate);
        OprHolidayVo oprHolidayVo = baseMapper.selectVoOne(queryWrapper);
        if (oprHolidayVo == null) {
            return false;
        } else {
            if (oprHolidayVo.getHoliday().equals(IsYesEnum.YES.getCode())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 添加缓存
     *
     * @param holiday 节假日
     */
    private void addCache(OprHoliday holiday) {
        if (holiday == null) {
            return;
        }
        // 缓存KEY
        String cacheCodeKey = OprCacheKeyEnum.OPR_HOLIDAY_INFO_KEY.create(holiday.getDate());
        RedisUtils.setCacheObject(cacheCodeKey, BeanUtils.copyProperties(holiday, RemoteHolidayVo.class),
                OprCacheKeyEnum.OPR_HOLIDAY_INFO_KEY.getDuration());
    }

    /**
     * 删除缓存
     *
     * @param date
     */
    private void deleteCache(String date) {
        RedisUtils.deleteObject(OprCacheKeyEnum.OPR_HOLIDAY_INFO_KEY.create(date));
    }
}
