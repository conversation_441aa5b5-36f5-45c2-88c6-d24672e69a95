package com.feidi.xx.cross.operate.domain.vo;

import com.feidi.xx.cross.operate.domain.OprPrice;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.cross.operate.domain.bo.OprPriceDetailBo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 价格模版视图对象 opr_price
 *
 * <AUTHOR>
 * @date 2025-03-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OprPrice.class)
public class OprPriceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 价格模版主表
     */
    @ExcelProperty(value = "价格模版主表")
    private Long id;

    /**
     * 价格模版名称
     */
    @ExcelProperty(value = "价格模版名称")
    private String name;

    /**
     * 价格类型[PriceTypeEnum]
     */
    @ExcelProperty(value = "价格类型[PriceTypeEnum]")
    private String pricingType;

    /**
     * 是否节假日模板
     */
    @ExcelProperty(value = "是否节假日模板")
    private String holidayTemplate;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 是否模板
     */
    @ExcelProperty(value = "是否模板")
    private String template;

    /**
     * 关联线路数量
     */
    private Integer lineNumber;

    /**
     * 关联城市数量
     */
    private Integer cityNumber;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 价格详情
     */
    private List<OprPriceDetailVo> oprPriceDetailVoList;

}
