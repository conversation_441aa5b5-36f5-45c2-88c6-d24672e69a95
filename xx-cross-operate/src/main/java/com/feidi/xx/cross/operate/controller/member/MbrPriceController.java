package com.feidi.xx.cross.operate.controller.member;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.operate.domain.dto.OprCalculateDTO;
import com.feidi.xx.cross.operate.domain.vo.OprCalculateVo;
import com.feidi.xx.cross.operate.service.IOprPriceDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 乘客端-定价
 * 前端访问路由地址为:/finance/pricing
 *
 * <AUTHOR>
 * @date 2024-08-06
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@SaCheckRole(UserTypeEnum.UserType.PASSENGER_USER)
@RequestMapping(WebConstants.MEMBER_ROUTE_PREFIX)
public class MbrPriceController extends BaseController {

    private final IOprPriceDetailService pricingService;

    /**
     * 询价
     * @param calculateDTO 计算价格条件
     * @return 计算价格结果
     */
    @Log(title = "价格模版", businessType = BusinessType.OTHER)
    @PostMapping("/estimate")
    public R<OprCalculateVo> estimate(@RequestBody OprCalculateDTO calculateDTO) {
        calculateDTO.setPassengerId(LoginHelper.getUserId());
        return R.ok(pricingService.calculatePrice(calculateDTO));
    }

}
