package com.feidi.xx.cross.operate.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.operate.domain.OprQrcode;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 推广码业务对象 opr_qrcode
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OprQrcode.class, reverseConvertGenerate = false)
public class OprQrcodeBo extends BaseEntity {

    /**
     * 主键
     */
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 推广码名称
     */
    @NotBlank(message = "推广码名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 渠道ID
     */
    @NotNull(message = "渠道ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long channelId;

    /**
     * 邀请码
     */
    private String code;

    /**
     * 二维码链接
     */
    private String qrcodeUrl;

    /**
     * 广告图链接
     */
    private String adUrl;

    /**
     * Logo链接
     */
    private String logoUrl;

    /**
     * 生成数量
     */
    @NotNull(message = "生成数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer num;
}
