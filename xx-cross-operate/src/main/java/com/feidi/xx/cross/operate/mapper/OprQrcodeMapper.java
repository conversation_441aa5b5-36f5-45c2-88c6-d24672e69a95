package com.feidi.xx.cross.operate.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.feidi.xx.cross.operate.domain.OprQrcode;
import com.feidi.xx.cross.operate.domain.vo.OprQrcodeVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 推广码Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
public interface OprQrcodeMapper extends BaseMapperPlus<OprQrcode, OprQrcodeVo> {

    default List<OprQrcode> queryByChannelIds(List<Long> ids) {
        return selectList(new LambdaUpdateWrapper<OprQrcode>()
                .in(OprQrcode::getChannelId, ids));
    }

}
