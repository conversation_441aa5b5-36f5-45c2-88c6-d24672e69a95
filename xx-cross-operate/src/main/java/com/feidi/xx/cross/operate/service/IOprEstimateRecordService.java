package com.feidi.xx.cross.operate.service;

import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.cross.operate.domain.bo.OprEstimateRecordBo;
import com.feidi.xx.cross.operate.domain.vo.OprEstimateRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 询价记录Service接口
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
public interface IOprEstimateRecordService {

    /**
     * 查询询价记录
     *
     * @param id 主键
     * @return 询价记录
     */
    OprEstimateRecordVo queryById(Long id);

    /**
     * 分页查询询价记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 询价记录分页列表
     */
    TableDataInfo<OprEstimateRecordVo> queryPageList(OprEstimateRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的询价记录列表
     *
     * @param bo 查询条件
     * @return 询价记录列表
     */
    List<OprEstimateRecordVo> queryList(OprEstimateRecordBo bo);

    /**
     * 新增询价记录
     *
     * @param bo 询价记录
     * @return 是否新增成功
     */
    Boolean insertByBo(OprEstimateRecordBo bo);

    /**
     * 修改询价记录
     *
     * @param bo 询价记录
     * @return 是否修改成功
     */
    Boolean updateByBo(OprEstimateRecordBo bo);

    /**
     * 校验并批量删除询价记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    void saveEstimateRecord(OprEstimateRecordBo oprEstimateRecordBo);

    /**
     * 根据删除时间删除询价记录
     *
     * @param deleteDate 删除时间
     */
    void deleteEstimateRecord(String deleteDate);

    /**
     * 删除询价记录
     *
     * @param deleteDate 删除时间
     * @return
     */
    Boolean deleteRecord(String deleteDate);
}
