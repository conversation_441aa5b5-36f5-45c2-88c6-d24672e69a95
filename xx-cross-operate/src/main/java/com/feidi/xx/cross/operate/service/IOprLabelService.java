package com.feidi.xx.cross.operate.service;

import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.cross.operate.domain.bo.OprLabelBo;
import com.feidi.xx.cross.operate.domain.vo.OprLabelVo;

import java.util.Collection;
import java.util.List;

/**
 * 平台-产品Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IOprLabelService {

    /**
     * 查询标签
     *
     * @param id 主键
     * @return 平台-产品
     */
    OprLabelVo queryById(Long id);

    /**
     * 分页查询标签列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 标签分页列表
     */
    TableDataInfo<OprLabelVo> queryPageList(OprLabelBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的标签列表
     */
    <T> List<T> queryList(OprLabelBo bo, Class<T> clazz);

    /**
     * 新增标签
     *
     * @param bo 标签
     * @return 是否新增成功
     */
    Boolean insertByBo(OprLabelBo bo);

    /**
     * 修改标签
     *
     * @param bo 标签
     * @return 是否修改成功
     */
    Boolean updateByBo(OprLabelBo bo);

    /**
     * 校验并批量删除标签信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
