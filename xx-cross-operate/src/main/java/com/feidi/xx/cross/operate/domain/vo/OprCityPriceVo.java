package com.feidi.xx.cross.operate.domain.vo;

import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.common.id2name.annotation.Id2Name;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.operate.domain.OprCityPrice;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 城市价格关联视图对象 opr_city_price
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OprCityPrice.class)
public class OprCityPriceVo {


    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 省份id
     */
    @ExcelProperty(value = "省份id")
    @Id2Name(fullName="province",index = "id")
    private Long provinceId;
    /**
     * 城市表id
     */
    @ExcelProperty(value = "城市表id")
    private Long cityId;

    /**
     * 城市code
     */
    @ExcelProperty(value = "城市code")
    private String cityCode;

    /**
     * 城市code
     */
    @ExcelProperty(value = "城市")
    private String city;

    /**
     * 城市状态
     */
    @ExcelProperty(value = "城市状态")
    @Enum2Text(enumClass = StatusEnum.class)
    private String cityStatus;
    private String cityStatusName;

    /**
     * 价格id
     */
    @ExcelProperty(value = "价格id")
    private Long priceId;
    /**
     * 价格模版名称
     */
    @ExcelProperty(value = "价格模版名称")
    private String priceName;

    /**
     * {@link PlatformCodeEnum}
     */
    @Enum2Text(enumClass = PlatformCodeEnum.class)
    private String platformCode;

    private String platformName;


}
