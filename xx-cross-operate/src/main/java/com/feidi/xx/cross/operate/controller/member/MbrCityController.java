package com.feidi.xx.cross.operate.controller.member;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.operate.domain.vo.OprCityVo;
import com.feidi.xx.cross.operate.service.IOprCityService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 城市
 * 前端访问路由地址为:/power/city
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@SaCheckRole(UserTypeEnum.UserType.PASSENGER_USER)
@RequestMapping(WebConstants.MEMBER_ROUTE_PREFIX+"/city")
public class MbrCityController extends BaseController {

    private final IOprCityService oprCityService;


    /**
     * 根据adcode  查询城市信息
     */
    @Log(title = "乘客端-查询城市", businessType = BusinessType.UPDATE)
    @GetMapping("/{adcode}")
    public R<OprCityVo> getByAdCode(@PathVariable String adcode, @RequestParam(required = false) String longitude, @RequestParam(required = false) String latitude) {
        OprCityVo oprCityVo = oprCityService.getByCityCode(adcode, longitude, latitude);
        // 未开城-默认运行时间是0:00 - 24:00
        if (oprCityVo == null) {
            oprCityVo = new OprCityVo();
            oprCityVo.setStartTime("0:00");
            oprCityVo.setEndTime("24:00");
        }
        if (StringUtils.isBlank(oprCityVo.getStartTime())) {
            oprCityVo.setStartTime("0:00");
        }
        if (StringUtils.isBlank(oprCityVo.getEndTime())) {
            oprCityVo.setEndTime("24:00");
        }
        return R.ok(oprCityVo);
    }
}
