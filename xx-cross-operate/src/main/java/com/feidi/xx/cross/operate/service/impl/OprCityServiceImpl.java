package com.feidi.xx.cross.operate.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.cache.system.DistrictCacheVo;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.operate.enums.OprCacheKeyEnum;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import com.feidi.xx.cross.common.constant.operate.OperateCacheConstants;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.operate.api.domain.city.vo.RemoteCityPriceVo;
import com.feidi.xx.cross.operate.api.domain.city.vo.RemoteCityVo;
import com.feidi.xx.cross.operate.domain.OprCity;
import com.feidi.xx.cross.operate.domain.OprCityPrice;
import com.feidi.xx.cross.operate.domain.OprPrice;
import com.feidi.xx.cross.operate.domain.bo.OprCityBo;
import com.feidi.xx.cross.operate.domain.bo.OprCityPriceBo;
import com.feidi.xx.cross.operate.domain.vo.OprCityPriceVo;
import com.feidi.xx.cross.operate.domain.vo.OprCityVo;
import com.feidi.xx.cross.operate.mapper.OprCityMapper;
import com.feidi.xx.cross.operate.mapper.OprCityPriceMapper;
import com.feidi.xx.cross.operate.mapper.OprPriceMapper;
import com.feidi.xx.cross.operate.service.IOprCityService;
import com.feidi.xx.cross.operate.service.IOprFenceService;
import com.feidi.xx.cross.power.api.RemoteAgentCityService;
import com.feidi.xx.system.api.RemoteConfigService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 城市Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@RequiredArgsConstructor
@Service
public class OprCityServiceImpl implements IOprCityService {

    private final IOprFenceService fenceService;

    private final OprCacheManager oprCacheManager;

    private final OprCityMapper baseMapper;

    private final ScheduledExecutorService scheduledExecutorService;

    private final OprCityPriceMapper oprCityPriceMapper;

    private final OprPriceMapper oprPriceMapper;

    @DubboReference
    private RemoteAgentCityService remoteAgentCityService;
    @DubboReference
    private final RemoteConfigService remoteConfigService;

    /**
     * 查询城市
     *
     * @param id 主键
     * @return 城市
     */
    @Override
    public OprCityVo queryById(Long id) {
        OprCityVo oprCityVo = baseMapper.selectVoById(id);
        if (oprCityVo != null) {
            LambdaQueryWrapper<OprCityPrice> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OprCityPrice::getCityId, oprCityVo.getCityId());
            List<OprCityPriceVo> cityPriceVos = oprCityPriceMapper.selectVoList(queryWrapper);
            for (OprCityPriceVo cityPriceVo : cityPriceVos) {
                cityPriceVo.setPriceName(oprPriceMapper.selectById(cityPriceVo.getPriceId()).getName());
            }
            oprCityVo.setOprCityPriceVoList(cityPriceVos);
        }
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询城市列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 城市分页列表
     */
    @Override
    public TableDataInfo<OprCityVo> queryPageList(OprCityBo bo, PageQuery pageQuery) {
        //初始化
        if (ObjectUtils.isNull(pageQuery.getIsAsc())) {
            pageQuery.setIsAsc("desc");
        }
        if (ObjectUtils.isNull(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("create_time");
        }
        //先判断代理商名称是否为空
        if (StringUtils.isNotBlank(bo.getCompanyName())) {
            //根据代理商名称查询城市信息
            List<Long> cityList = remoteAgentCityService.getCityIdByCompanyName(bo.getCompanyName());
            bo.setCityAgentIdList(cityList);
        }
        LambdaQueryWrapper<OprCity> lqw = buildQueryWrapper(bo);
        Page<OprCityVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<OprCityVo> records = result.getRecords();
        if (records != null) {
            records.forEach(item -> {
                LambdaQueryWrapper<OprCityPrice> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(OprCityPrice::getCityId, item.getId());
                List<OprCityPriceVo> cityPriceVos = oprCityPriceMapper.selectVoList(queryWrapper);
                for (OprCityPriceVo cityPriceVo : cityPriceVos) {
                    OprPrice oprPrice = oprPriceMapper.selectById(cityPriceVo.getPriceId());
                    if (oprPrice != null){

                        cityPriceVo.setPriceName(oprPrice.getName());
                    }
                }
                item.setOprCityPriceVoList(cityPriceVos);
                //根据城市id查询代理商名称
                if (item.getId() != null) {
                    //根据城市id查询代理商名称
                    List<String> cityList = remoteAgentCityService.getCompanyNameByCityId(item.getId());
                    if (cityList != null && cityList.size() > 0) {
                        item.setAgentList(cityList);
                    }
                }
            });
        }
        return TableDataInfo.build(result);
    }

    @Override
    public List<OprCityVo> queryAllList(OprCityBo bo) {
        LambdaQueryWrapper<OprCity> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public OprCityVo getByCityCode(String adcode, String longitude, String latitude) {
        OprCityVo oprCityVo = new OprCityVo();
        DistrictCacheVo districtCacheVo = oprCacheManager.getDistrictCacheVByAdCode(adcode);

        // 电子围栏id集合
        List<Long> fenceIds = fenceService.queryByPointAndCityCode(districtCacheVo.getCityCode(), Double.parseDouble(latitude), Double.parseDouble(longitude));

        List<Long> startFenceIds = JsonUtils.parseArray(remoteConfigService.selectValueByKey(OperateCacheConstants.FENCE_FZ_FQ_IDS), Long.class);
        if (CollUtils.isNotEmpty(startFenceIds) && new HashSet<>(startFenceIds).containsAll(fenceIds)) {
            String time = remoteConfigService.selectValueByKey(OperateCacheConstants.FENCE_FZ_FQ_TIME);
            if (StringUtils.isNotBlank(time)) {
                String[] times = time.split("-");
                oprCityVo.setStartTime(times[0]);
                oprCityVo.setEndTime(times[1]);
                return oprCityVo;
            }
        } else {
            List<Long> endFenceIds = JsonUtils.parseArray(remoteConfigService.selectValueByKey(OperateCacheConstants.FENCE_FQ_FZ_IDS), Long.class);
            if (CollUtils.isNotEmpty(endFenceIds) && new HashSet<>(endFenceIds).containsAll(fenceIds)) {
                String time = remoteConfigService.selectValueByKey(OperateCacheConstants.FENCE_FQ_FZ_TIME);
                if (StringUtils.isNotBlank(time)) {
                    String[] times = time.split("-");
                    oprCityVo.setStartTime(times[0]);
                    oprCityVo.setEndTime(times[1]);
                    return oprCityVo;
                }
            }
        }
        oprCityVo = baseMapper.selectVoOne(Wrappers.lambdaQuery(OprCity.class).eq(OprCity::getCityCode, districtCacheVo.getCityCode()));
        return oprCityVo;
    }

    @Override
    public void repair() {
        List<OprCity> oprCities = baseMapper.selectList();
        oprCities.forEach(item -> {
            OprCityPrice oprCityPrice = new OprCityPrice();
            oprCityPrice.setCityCode(item.getCityCode());
            oprCityPrice.setCityId(item.getId());
            oprCityPrice.setPriceId(item.getPriceId());
            oprCityPrice.setCreateTime(item.getCreateTime());
            oprCityPrice.setPlatformCode(PlatformCodeEnum.SELF.getCode());
            oprCityPriceMapper.insert(oprCityPrice);
            oprCityPrice.setPlatformCode(PlatformCodeEnum.MT.getCode());
            oprCityPrice.setId(null);
            oprCityPriceMapper.insert(oprCityPrice);
        });
    }

    /**
     * 查询符合条件的城市列表
     *
     * @param bo 查询条件
     * @return 城市列表
     */
    @Override
    public List<OprCityVo> queryList(OprCityBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OprCity> lqw = buildQueryWrapper(bo);
        Page<OprCityVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<OprCityVo> records = result.getRecords();
        return records;
    }

    private LambdaQueryWrapper<OprCity> buildQueryWrapper(OprCityBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OprCity> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPriceId() != null, OprCity::getPriceId, bo.getPriceId());
        lqw.eq(bo.getProvinceId() != null, OprCity::getProvinceId, bo.getProvinceId());
        lqw.eq(bo.getCityId() != null, OprCity::getCityId, bo.getCityId());
        lqw.eq(StringUtils.isNotBlank(bo.getCityCode()), OprCity::getCityCode, bo.getCityCode());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), OprCity::getStatus, bo.getStatus());
        if (ObjectUtils.isNotNull(bo.getStartCreateTime()) && ObjectUtils.isNotNull(bo.getEndCreateTime())) {
            lqw.between(OprCity::getCreateTime, bo.getStartCreateTime(), bo.getEndCreateTime());
        }
        if (bo.getCityAgentIdList() != null) {
            lqw.in(OprCity::getId, bo.getCityAgentIdList());
        }
        return lqw;
    }

    /**
     * 新增城市
     *
     * @param bo 城市
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(OprCityBo bo) {
        OprCity add = MapstructUtils.convert(bo, OprCity.class);
        validEntityBeforeSave(add);
        add.setStatus(StatusEnum.ENABLE.getCode());
        boolean flag = baseMapper.insert(add) > 0;
        List<OprCityPriceBo> cityPriceBoList = bo.getCityPriceBoList();
        if (CollUtils.isNotEmpty(cityPriceBoList)) {
            cityPriceBoList.forEach(cityPriceBo -> {
                if (ObjectUtils.isNull(cityPriceBo.getPlatformCode())||ObjectUtils.isNull(cityPriceBo.getPriceId())) {
                    //价格id和平台编码不能为空
                    throw new ServiceException("价格id或者平台编码为空");
                }
                cityPriceBo.setCityId(add.getId());
                cityPriceBo.setCityCode(add.getCityCode());
                OprCityPrice oprCityPrice = BeanUtils.copyProperties(cityPriceBo, OprCityPrice.class);
                oprCityPriceMapper.insert(oprCityPrice);
            });

        }
        if (flag) {
            bo.setId(add.getId());
            // 异步添加缓存
            scheduledExecutorService.schedule(() -> addCache(add), 0, TimeUnit.SECONDS);
        }
        return flag;
    }

    /**
     * 修改城市
     *
     * @param bo 城市
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OprCityBo bo) {
        LambdaUpdateWrapper<OprCity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OprCity::getId, bo.getId())
                .set(OprCity::getPriceId, bo.getPriceId())
                .set(OprCity::getProvinceId, bo.getProvinceId())
                .set(OprCity::getCityId, bo.getCityId())
                .set(OprCity::getCityCode, bo.getCityCode())
                .set(OprCity::getStatus, bo.getStatus())
                .set(OprCity::getStartTime, bo.getStartTime())
                .set(OprCity::getEndTime, bo.getEndTime())
                .set(OprCity::getMaxNumber, bo.getMaxNumber());
        OprCityVo oprCityVo = baseMapper.selectVoById(bo.getId());
        for (OprCityPriceBo oprCityPriceBo : bo.getCityPriceBoList()) {
            if (ObjectUtils.isNull(oprCityPriceBo.getPlatformCode())||ObjectUtils.isNull(oprCityPriceBo.getPriceId())) {
                //价格id和平台编码不能为空
                throw new ServiceException("价格id或者平台编码为空");
            }
            OprCityPrice oprCityPrice = BeanUtils.copyProperties(oprCityPriceBo, OprCityPrice.class);
            oprCityPriceMapper.updateById(oprCityPrice);
        }

        if (oprCityVo != null) {
            // 异步删除缓存
            scheduledExecutorService.schedule(() -> deleteCache(oprCityVo.getCityCode(), oprCityVo.getId()), 0, TimeUnit.SECONDS);
        }
        return baseMapper.update(wrapper) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OprCity entity) {
        //TODO 做一些数据校验,如唯一约束
        Assert.isTrue(StrUtil.isAllNotBlank(entity.getCityCode()), "城市code不能为空");
        //校验城市code
        if (baseMapper.exists(new LambdaQueryWrapper<OprCity>().eq(OprCity::getCityCode, entity.getCityCode()))) {
            throw new ServiceException("该城市已开通，请重新选择");
        }
    }

    /**
     * 校验并批量删除城市信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
            ids.forEach(item -> {
                //根据城市id查询代理商名称
                List<String> cityList = remoteAgentCityService.getCompanyNameByCityId(item);
                if (cityList != null && cityList.size() > 0) {
                    throw new ServiceException("该城市已绑定代理商，不允许删除!");
                }
            });
        }
        List<OprCity> oprCities = baseMapper.selectBatchIds(ids);
        oprCities.forEach(item -> {
            deleteCache(item.getCityCode(),  item.getId());
            oprCityPriceMapper.delete(new LambdaQueryWrapper<OprCityPrice>().eq(OprCityPrice::getCityId, item.getId()));
        });
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public Integer countByPriceId(Long priceId) {
        Long totalCount = oprCityPriceMapper.selectCount(
                new LambdaQueryWrapper<OprCityPrice>().eq(OprCityPrice::getPriceId, priceId)
        );
        return totalCount.intValue();

    }

    /**
     * 添加缓存
     *
     * @param city 产品信息
     */
    private void addCache(OprCity city) {
        if (city == null) {
            return;
        }
        // 缓存KEY
        String cacheCodeKey = OprCacheKeyEnum.OPR_CITY_INFO_KEY.create(city.getCityCode());
        RedisUtils.setCacheObject(cacheCodeKey, BeanUtils.copyProperties(city, RemoteCityVo.class),
                OprCacheKeyEnum.OPR_CITY_INFO_KEY.getDuration());
        LambdaQueryWrapper<OprCityPrice> cityPriceLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cityPriceLambdaQueryWrapper.eq(OprCityPrice::getCityId, city.getId());
        List<OprCityPrice> oprCityPrices = oprCityPriceMapper.selectList(cityPriceLambdaQueryWrapper);
        oprCityPrices.forEach(item -> {
            String cachePriceKey = OprCacheKeyEnum.OPR_CITY_PRICE_INFO_KEY.create( item.getCityCode(),item.getPlatformCode());
            RedisUtils.setCacheObject(cachePriceKey, BeanUtils.copyProperties(item, RemoteCityPriceVo.class),
                    OprCacheKeyEnum.OPR_CITY_PRICE_INFO_KEY.getDuration());
        });
    }

    /**
     * 删除缓存
     *
     * @param cityCode
     */
    private void deleteCache(String cityCode,  Long cityId) {
        RedisUtils.deleteObject(OprCacheKeyEnum.OPR_CITY_INFO_KEY.create(cityCode));
        LambdaQueryWrapper<OprCityPrice> cityPriceLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cityPriceLambdaQueryWrapper.eq(OprCityPrice::getCityId,cityId);
        List<OprCityPrice> oprCityPrices = oprCityPriceMapper.selectList(cityPriceLambdaQueryWrapper);
        oprCityPrices.forEach(item -> {
            RedisUtils.deleteObject(OprCacheKeyEnum.OPR_CITY_PRICE_INFO_KEY.create(item.getCityCode(),item.getPlatformCode()));
        });
    }
}
