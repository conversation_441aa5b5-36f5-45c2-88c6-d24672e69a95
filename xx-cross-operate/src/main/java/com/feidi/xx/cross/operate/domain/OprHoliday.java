package com.feidi.xx.cross.operate.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 定价对象 Fin_holiday
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("opr_holiday")
public class OprHoliday extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 时间
     */
    private String date;

    /**
     * 是否为节假日
     */
    private String holiday;

    /**
     * 备注
     */
    private String remark;

}
