package com.feidi.xx.cross.operate.domain.vo;

import com.feidi.xx.cross.operate.domain.OprChannel;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 渠道视图对象 opr_channel
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OprChannel.class)
public class OprChannelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 渠道名称
     */
    @ExcelProperty(value = "渠道名称")
    private String name;

    /**
     * 邀请码
     */
    @ExcelProperty(value = "邀请码")
    private String code;

    /**
     * 渠道类型
     */
    @ExcelProperty(value = "渠道类型")
    private String type;

    /**
     * 推广码数量
     */
    private Long num;


}
