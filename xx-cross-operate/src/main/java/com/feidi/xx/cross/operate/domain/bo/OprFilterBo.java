package com.feidi.xx.cross.operate.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.operate.domain.OprFilter;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 过滤规则业务对象 opr_filter
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OprFilter.class, reverseConvertGenerate = false)
public class OprFilterBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 平台编码
     */
    @NotBlank(message = "平台编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platformCode;

    /**
     * 线路
     */
    @NotNull(message = "线路不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long lineId;

    /**
     * 过滤规则
     */
    private String filterRule;

    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
