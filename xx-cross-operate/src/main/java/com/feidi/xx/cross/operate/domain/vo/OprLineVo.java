package com.feidi.xx.cross.operate.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.common.id2name.annotation.Id2Name;
import com.feidi.xx.cross.common.enums.operate.LineTypeEnum;
import com.feidi.xx.cross.operate.domain.OprLine;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 线路视图对象 opr_line
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OprLine.class)
public class OprLineVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 起点省
     */
    @ExcelProperty(value = "起点省")
    private Long startProvinceId;

    /**
     * 起点城市
     */
    @ExcelProperty(value = "起点城市")
    @Id2Name(fullName="startCity",index = "id")
    private Long startCityId;

    /**
     * 起点区
     */
    @ExcelProperty(value = "起点区")
    List<String> startDistricts;

    /**
     * 起点定价
     */
    @ExcelProperty(value = "起点定价")
    private Long startPricingId;

    /**
     * 终点省
     */
    @ExcelProperty(value = "终点省")
    private Long endProvinceId;

    /**
     * 终点城市
     */
    @ExcelProperty(value = "终点城市")
    @Id2Name(fullName="endCity",index = "id")
    private Long endCityId;

    /**
     * 终点区
     */
    private List<String> endDistricts;

    /**
     * 终点定价
     */
    @ExcelProperty(value = "终点定价")
    private Long endPricingId;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "LineTypeEnum")
    @Enum2Text(enumClass = LineTypeEnum.class)
    private String type;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "StatusEnum")
    @Enum2Text(enumClass = StatusEnum.class)
    private String status;



    /**
     * 标签
     */
    private List<String> labels;
    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间", converter = ExcelDictConvert.class)
    private Date createTime;

    /**
     * 往返 StartEndEnum
     */
    private String direction;

    /**
     * 备注
     */
    private String remark;

    /**
     * 分配信息
     */
    private AssignInfo assignInfo;

    /**
     * 分配信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AssignInfo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        private Long id;


        private Long parentId;


    }

}
