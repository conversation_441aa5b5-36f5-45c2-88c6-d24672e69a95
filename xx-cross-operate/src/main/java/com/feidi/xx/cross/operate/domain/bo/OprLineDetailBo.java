package com.feidi.xx.cross.operate.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.operate.domain.OprLineDetail;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 线路详情业务对象 opr_line_detail
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OprLineDetail.class, reverseConvertGenerate = false)
public class OprLineDetailBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 围栏ID
     */
    private Long fenceId;

    /**
     * 线路
     */
    @NotNull(message = "线路不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long lineId;

    /**
     * 省
     */
    @NotNull(message = "省不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long provinceId;

    /**
     * 城市
     */
    @NotNull(message = "城市不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long cityId;

    /**
     * 城市编码
     */
    @NotBlank(message = "城市编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cityCode;

    /**
     * 区域
     */
    @NotNull(message = "区域不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long districtId;

    /**
     * 区域编码
     */
    @NotBlank(message = "区域编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String adCode;

    /**
     * 类型
     */
    private String direction;

    /**
     * 区域名称
     */
    private String district;


}
