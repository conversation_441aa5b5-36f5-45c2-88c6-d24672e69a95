package com.feidi.xx.cross.operate.controller.agent;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.id2name.annotation.Id2NameAspect;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.operate.domain.OprLineDetail;
import com.feidi.xx.cross.operate.service.IOprLineDetailService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 后台 - 线路详情
 * 前端访问路由地址为:/operate/agt/lineDetail
 *
 * <AUTHOR>
 * @date 2024-08-29
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/lineDetail")
public class AgtLineDetailController extends BaseController {

    private final IOprLineDetailService lineDetailService;

    /**
     * 获取线路详情列表信息
     *
     * @param lineId 线路id
     */
    @Id2NameAspect
    @GetMapping("/{lineId}")
    @Log(title = "价格模版", businessType = BusinessType.OTHER)
    public R<List<OprLineDetail>> getInfo(@NotNull(message = "线路不能为空") @PathVariable Long lineId) {
        return R.ok(lineDetailService.queryByLineId(lineId));
    }

}
