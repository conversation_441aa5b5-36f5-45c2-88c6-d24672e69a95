package com.feidi.xx.cross.operate.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.operate.domain.OprHoliday;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 定价对象 Fin_holiday
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OprHoliday.class, reverseConvertGenerate = false)
public class OprHolidayBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 年份
     */
    @NotNull(message = "年份不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer year;

    /**
     * 时间
     */
    @NotBlank(message = "时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String date;

    /**
     * 是否为节假日
     */
    @NotBlank(message = "是否为节假日不能为空", groups = { AddGroup.class, EditGroup.class })
    private String holiday;

    /**
     * 备注
     */
    private String remark;

}
