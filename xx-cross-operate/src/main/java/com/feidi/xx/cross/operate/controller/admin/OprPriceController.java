package com.feidi.xx.cross.operate.controller.admin;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.cross.operate.domain.dto.OprCalculateDTO;
import com.feidi.xx.cross.operate.domain.vo.ExportVo;
import com.feidi.xx.cross.operate.domain.vo.OprCalculateVo;
import com.feidi.xx.cross.operate.service.IOprPriceDetailService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.cross.operate.domain.vo.OprPriceVo;
import com.feidi.xx.cross.operate.domain.bo.OprPriceBo;
import com.feidi.xx.cross.operate.service.IOprPriceService;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 价格模版
 * 前端访问路由地址为:/operate/price
 *
 * <AUTHOR>
 * @date 2025-03-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/price")
public class OprPriceController extends BaseController {

    private final IOprPriceService oprPriceService;

    /**
     * 查询价格模版列表
     */
    @SaCheckPermission("operate:price:list")
    @GetMapping("/list")
    public TableDataInfo<OprPriceVo> list(OprPriceBo bo, PageQuery pageQuery) {
        return oprPriceService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询定价列表-全部
     */
    @SaCheckPermission("operate:price:list")
    @GetMapping("/list/all")
    public  R<List<OprPriceVo>> list(OprPriceBo bo) {
        return R.ok(oprPriceService.queryList(bo));
    }
    /**
     * 导出价格模版列表
     */
    @SaCheckPermission("operate:price:export")
    @Log(title = "价格模版", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OprPriceBo bo, HttpServletResponse response) {
        List<OprPriceVo> list = oprPriceService.queryList(bo);
        ExcelUtil.exportExcel(list, "价格模版", OprPriceVo.class, response);
    }

    /**
     * 获取价格模版详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("operate:price:query")
    @GetMapping("/{id}")
    public R<OprPriceVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(oprPriceService.queryById(id));
    }

    /**
     * 新增价格模版
     */
    @SaCheckPermission("operate:price:add")
    @Log(title = "价格模版", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OprPriceBo bo) {
        return toAjax(oprPriceService.insertByBo(bo));
    }

    /**
     * 修改价格模版
     */
    @SaCheckPermission("operate:price:edit")
    @Log(title = "价格模版", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OprPriceBo bo) {
        return toAjax(oprPriceService.updateByBo(bo));
    }

    /**
     * 删除价格模版
     *
     * @param ids 主键串
     */
    @SaCheckPermission("operate:price:remove")
    @Log(title = "价格模版", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(oprPriceService.deleteWithValidByIds(List.of(ids), true));
    }


    private final IOprPriceDetailService pricingService;

    /**
     * 询价
     * @param calculateDTO 计算价格条件
     * @return 计算价格结果
     */
    @PostMapping("/estimate")
    public R<OprCalculateVo> estimate(@RequestBody OprCalculateDTO calculateDTO) {
        return R.ok(pricingService.calculatePrice(calculateDTO));
    }


}
