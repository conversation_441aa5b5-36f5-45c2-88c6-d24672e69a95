package com.feidi.xx.cross.operate.service.impl;

import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.operate.domain.OprPlatformProduct;
import com.feidi.xx.cross.operate.domain.bo.OprPlatformProductBo;
import com.feidi.xx.cross.operate.domain.vo.OprPlatformProductVo;
import com.feidi.xx.cross.operate.mapper.OprPlatformProductMapper;
import com.feidi.xx.cross.operate.service.IOprPlatformProductService;
import com.feidi.xx.cross.operate.service.OprPlatProdHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.feidi.xx.common.core.utils.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 平台产品Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@RequiredArgsConstructor
@Service
public class OprPlatformProductServiceImpl implements IOprPlatformProductService {

    private final OprPlatformProductMapper baseMapper;

    /**
     * 查询平台产品
     *
     * @param id 主键
     * @return 平台产品
     */
    @Override
    public OprPlatformProductVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询平台产品列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 平台产品分页列表
     */
    @Override
    public TableDataInfo<OprPlatformProductVo> queryPageList(OprPlatformProductBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OprPlatformProduct> lqw = buildQueryWrapper(bo);
        Page<OprPlatformProductVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的平台产品列表
     *
     * @param bo 查询条件
     * @return 平台产品列表
     */
    @Override
    public List<OprPlatformProductVo> queryList(OprPlatformProductBo bo) {
        LambdaQueryWrapper<OprPlatformProduct> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OprPlatformProduct> buildQueryWrapper(OprPlatformProductBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OprPlatformProduct> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPlatformCode()), OprPlatformProduct::getPlatformCode, bo.getPlatformCode());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), OprPlatformProduct::getProductCode, bo.getProductCode());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), OprPlatformProduct::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增平台产品
     *
     * @param bo 平台产品
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OprPlatformProductBo bo) {
        OprPlatformProduct add = MapstructUtils.convert(bo, OprPlatformProduct.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改平台产品
     *
     * @param bo 平台产品
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OprPlatformProductBo bo) {
        OprPlatformProduct update = MapstructUtils.convert(bo, OprPlatformProduct.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OprPlatformProduct entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除平台产品信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据平台编码删除平台产品
     *
     * @param platformCode 平台编码
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteByPlatformCode(String platformCode) {
        if (StringUtils.isBlank(platformCode)) {
            return false;
        }

        return baseMapper.deleteByPlatformCode(LoginHelper.getTenantId(), platformCode) > 0;
    }

    @Override
    public boolean existsByBo(OprPlatformProductBo bo) {
        LambdaQueryWrapper<OprPlatformProduct> lqw = OprPlatProdHelper.buildQueryWrapper(bo);
        return baseMapper.exists(lqw);
    }

    @Override
    public boolean existsByBoList(List<OprPlatformProductBo> boList) {
        List<String> productCodes = boList.stream().map(OprPlatformProductBo::getProductCode).collect(Collectors.toList());
        LambdaQueryWrapper<OprPlatformProduct> lqw = Wrappers.lambdaQuery();
        lqw.in(OprPlatformProduct::getProductCode, productCodes);
        return baseMapper.selectCount(lqw) > 0;
    }
}
