package com.feidi.xx.cross.operate.controller.agent;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.operate.domain.bo.OprProductBo;
import com.feidi.xx.cross.operate.domain.vo.OprProductVo;
import com.feidi.xx.cross.operate.service.IOprProductService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 代理商 - 产品
 * 前端访问路由地址为:/operate/agt/product
 *
 * <AUTHOR>
 * @date 2023-03-04
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/product")
public class AgtProductController extends BaseController {

    private final IOprProductService productService;

    /**
     * 查询产品列表-分页
     */
    @Log(title = "价格模版", businessType = BusinessType.OTHER)
    @GetMapping("/list")
    public TableDataInfo<OprProductVo> list(OprProductBo bo, PageQuery pageQuery) {
        return productService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询产品列表-全部
     */
    @Log(title = "价格模版", businessType = BusinessType.OTHER)
    @GetMapping("/list/all")
    public R<List<OprProductVo>> list(OprProductBo bo) {
        return R.ok(productService.queryList(bo));
    }
}
