package com.feidi.xx.cross.operate.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.operate.domain.bo.OprHolidayBo;
import com.feidi.xx.cross.operate.domain.vo.OprHolidayVo;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 节假日Service接口
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
public interface IOprHolidayService {

    /**
     * 查询定价
     *
     * @param id 主键
     * @return 定价
     */
    OprHolidayVo queryById(Long id);

    /**
     * 分页查询定价列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 定价分页列表
     */
    TableDataInfo<OprHolidayVo> queryPageList(OprHolidayBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的定价列表
     *
     * @param bo 查询条件
     * @return 定价列表
     */
    List<OprHolidayVo> queryList(OprHolidayBo bo);

    /**
     * 新增定价
     *
     * @param bo 定价
     * @return 是否新增成功
     */
    Boolean insertByBo(OprHolidayBo bo);

    /**
     * 修改定价
     *
     * @param bo 定价
     * @return 是否修改成功
     */
    Boolean updateByBo(OprHolidayBo bo);

    /**
     * 校验并批量删除定价信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 同步节假日
     * @return
     */
    Boolean syncHoliday();

    /**
     * 同步节假日
     * @param year
     * @return
     */
    Boolean syncHoliday(Integer year);


    /**
     * 是否为节假日
     * @param holidayDate
     * @return
     */
    Boolean isHoliday(Date holidayDate);

}
