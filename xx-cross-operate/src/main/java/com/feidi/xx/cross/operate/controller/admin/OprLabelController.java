package com.feidi.xx.cross.operate.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.operate.domain.vo.ExportVo;
import com.feidi.xx.cross.operate.domain.bo.OprLabelBo;
import com.feidi.xx.cross.operate.domain.vo.OprLabelVo;
import com.feidi.xx.cross.operate.service.IOprLabelService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 平台-产品
 * 前端访问路由地址为:/operate/label
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/label")
public class OprLabelController extends BaseController {

    private final IOprLabelService oprLabelService;

    /**
     * 查询标签-产品列表
     */
    @SaCheckPermission("operate:label:list")
    @GetMapping("/list")
    public TableDataInfo<OprLabelVo> list(OprLabelBo bo, PageQuery pageQuery) {
        return oprLabelService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询标签列表-全部
     */
    @SaCheckPermission("operate:label:list")
    @GetMapping("/list/all")
    public R<List<OprLabelVo>> list(OprLabelBo bo) {
        return R.ok(oprLabelService.queryList(bo, OprLabelVo.class));
    }

    /**
     * 导出标签列表
     */
    @SaCheckPermission("operate:label:export")
    @Log(title = "标签", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OprLabelBo bo, HttpServletResponse response) {
        List<OprLabelVo> list = oprLabelService.queryList(bo, OprLabelVo.class);
        ExcelUtil.exportExcel(list, "标签", OprLabelVo.class, response);
    }

    /**
     * 获取标签详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("operate:label:query")
    @GetMapping("/{id}")
    public R<OprLabelVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(oprLabelService.queryById(id));
    }

    /**
     * 新增标签
     */
    @SaCheckPermission("operate:label:add")
    @Log(title = "标签", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OprLabelBo bo) {
        return toAjax(oprLabelService.insertByBo(bo));
    }

    /**
     * 修改标签
     */
    @SaCheckPermission("operate:label:edit")
    @Log(title = "标签", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OprLabelBo bo) {
        return toAjax(oprLabelService.updateByBo(bo));
    }

    /**
     * 删除标签
     *
     * @param ids 主键串
     */
    @SaCheckPermission("operate:label:remove")
    @Log(title = "标签", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(oprLabelService.deleteWithValidByIds(List.of(ids), true));
    }
}
