package com.feidi.xx.cross.operate.dubbo;

import cn.hutool.core.collection.CollUtil;
import com.feidi.xx.common.core.cache.system.DistrictCacheVo;
import com.feidi.xx.common.core.cache.system.SysDistrictCacheVo;
import com.feidi.xx.common.core.enums.StartEndEnum;
import com.feidi.xx.common.core.enums.SystemCacheKeyEnum;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import com.feidi.xx.cross.operate.api.RemoteLineService;
import com.feidi.xx.cross.operate.api.domain.line.vo.RemoteLineVo;
import com.feidi.xx.cross.operate.domain.dto.OprLineMatchDTO;
import com.feidi.xx.cross.operate.domain.vo.OprLineVo;
import com.feidi.xx.cross.operate.mapper.OprLineDetailMapper;
import com.feidi.xx.cross.operate.mapper.OprLineMapper;
import com.feidi.xx.cross.operate.service.IOprFenceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 路线dubbo服务实现类
 *
 * <AUTHOR>
 * @date 2025/3/14
 */
@Slf4j
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteLineServiceImpl implements RemoteLineService {

    private final OprCacheManager cacheManager;
    private final OprLineMapper lineMapper;
    private final OprLineDetailMapper lineDetailMapper;

    private final IOprFenceService fenceService;

    /**
     * 根据id查询路线信息
     *
     * @param id 路线id
     * @return 路线信息
     */
    @Override
    public RemoteLineVo queryByLineId(Long id) {
        List<RemoteLineVo> lineVos = queryByLineIds(Collections.singletonList(id));
        if (CollUtil.isNotEmpty(lineVos)) {
            return lineVos.get(0);
        }
        return null;
    }

    /**
     * 根据id集合查询路线信息
     *
     * @param ids 路线id集合
     * @return 路线信息
     */
    @Override
    public List<RemoteLineVo> queryByLineIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<OprLineVo> lineVos = lineMapper.selectVoBatchIds(ids);

        return BeanUtils.copyToList(lineVos, RemoteLineVo.class);
    }

    /**
     * 根据起点AdCode和终点AdCode查询路线id
     *
     * @param startAdCode 起点AdCode
     * @param endAdCode   终点AdCode
     * @return 路线id
     */
    @Override
    public Long matchLine(String startAdCode, String endAdCode) {
        List<OprLineMatchDTO> lineDetails = lineDetailMapper.matchLine(startAdCode, endAdCode);
        if (CollUtils.isNotEmpty(lineDetails)) {
            return lineDetails.get(0).getLineId();
        }
        return 0L;
    }

    @Override
    public Long matchLine(String startAdCode, String endAdCode, Double startLatitudem, Double startLongitudem, Double endLatitudem, Double endLongitudem) {
        log.info("常用路线线路匹配");
        log.info("matchLine:{},{},{},{},{},{}", startAdCode, endAdCode, startLatitudem, startLongitudem, endLatitudem, endLongitudem);
        // 通过坐标匹配电子围栏，获取起点和终点的电子围栏 ID
        Long startFenceId;
        Long endFenceId;
        /// 优化 calculateDTO.getStartCityCode()=calculateDTO.getEndCityCode()

        DistrictCacheVo startDistrictCacheByAdCode = cacheManager.getDistrictCacheVByAdCode(startAdCode);
        DistrictCacheVo endDiStrictCacheByAdCode = cacheManager.getDistrictCacheVByAdCode(endAdCode);
        SysDistrictCacheVo startCityValue = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_ID_KEY.create(), String.valueOf(startDistrictCacheByAdCode.getCityId()));
        SysDistrictCacheVo endCityValue = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_ID_KEY.create(), String.valueOf(endDiStrictCacheByAdCode.getCityId()));

        List<Long> startFenceIds = fenceService.queryByPointAndCityCode(startCityValue.getCityCode(), startLatitudem, startLongitudem);
        List<Long> endFenceIds = fenceService.queryByPointAndCityCode(endCityValue.getCityCode(), endLatitudem, endLongitudem);
        log.info("startFenceIds:{},endFenceIds:{}", startFenceIds, endFenceIds);
        List<OprLineMatchDTO> oprLineMatchDTOS = List.of();
        OprLineMatchDTO oprLineMatchDTO;
        if (ObjectUtils.isNotNull(startFenceIds) && ObjectUtils.isNotNull(endFenceIds)) {
            oprLineMatchDTOS =  getOprLineMatchDTOS(startFenceIds,endFenceIds);
        }
        if (oprLineMatchDTOS.isEmpty()) {

            List<OprLineMatchDTO> matchDTOS = lineDetailMapper.matchLine(startAdCode, endAdCode);
            if (CollUtils.isNotEmpty(matchDTOS)) {
                if (matchDTOS.size() > 1) {
                    // 优先选择 "往" 线路，其次选择 "返" 线路
                    Optional<OprLineMatchDTO> selectedRoute = matchDTOS.stream()
                            .filter(dto -> StartEndEnum.START.getCode().equals(dto.getRouteType()))
                            .findFirst()
                            .or(() -> matchDTOS.stream()
                                    .filter(dto -> StartEndEnum.END.getCode().equals(dto.getRouteType()))
                                    .findFirst());
                    oprLineMatchDTO = selectedRoute.get();
                } else {
                    oprLineMatchDTO = matchDTOS.get(0);
                }
                if (ObjectUtils.isNotNull(oprLineMatchDTO)) {
                    // 设置线路 ID，并查询对应的价格 ID
                    return oprLineMatchDTO.getLineId();
                }
            }
        } else {
            // 电子围栏匹配成功，选择合适的线路
            if (oprLineMatchDTOS.size() > 1) {
                List<OprLineMatchDTO> finalOprLineMatchDTOS = oprLineMatchDTOS;
                Optional<OprLineMatchDTO> selectedRoute = oprLineMatchDTOS.stream()
                        .filter(dto -> StartEndEnum.START.getCode().equals(dto.getRouteType()))
                        .findFirst()
                        .or(() -> finalOprLineMatchDTOS.stream()
                                .filter(dto -> StartEndEnum.END.getCode().equals(dto.getRouteType()))
                                .findFirst());
                oprLineMatchDTO = selectedRoute.get();
            } else {
                oprLineMatchDTO = oprLineMatchDTOS.get(0);
            }
            return oprLineMatchDTO.getLineId();
        }
        return null;
    }

    /**
     * 获取全部路线
     *
     * @return 路线集合
     */
    @Override
    public List<RemoteLineVo> getAll() {
        return BeanUtils.copyToList(lineMapper.selectList(), RemoteLineVo.class);
    }


    private List<OprLineMatchDTO> getOprLineMatchDTOS(List<Long> startFenceIds, List<Long> endFenceIds) {
        List<OprLineMatchDTO> oprLineMatchDTOS = new ArrayList<>();
        for (Long startFenceId : startFenceIds) {
            for (Long endFenceId : endFenceIds) {
                oprLineMatchDTOS = lineDetailMapper.matchLineByFenceId(startFenceId, endFenceId);
                if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(oprLineMatchDTOS)) {
                    return oprLineMatchDTOS;
                }
            }
        }
        return oprLineMatchDTOS;
    }

}
