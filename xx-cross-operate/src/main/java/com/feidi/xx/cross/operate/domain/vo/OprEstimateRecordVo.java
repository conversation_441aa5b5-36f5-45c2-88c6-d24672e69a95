package com.feidi.xx.cross.operate.domain.vo;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.operate.api.domain.price.dto.PriceDto;
import com.feidi.xx.cross.operate.domain.OprEstimateRecord;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 询价记录视图对象 opr_estimate_record
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OprEstimateRecord.class)
public class OprEstimateRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
//    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 平台编码
     */
    @ExcelProperty(value = "渠道", converter = ExcelEnumConvert.class, index = 5)
    @ExcelEnumFormat(enumClass = PlatformCodeEnum.class)
    private String platformCode;

    /**
     * 线路id
     */
//    @ExcelProperty(value = "线路id")
    private Long lineId;

    /**
     * 计价模板id
     */
//    @ExcelProperty(value = "计价模板id")
    private Long priceId;

    /**
     * 起点省
     */
//    @ExcelProperty(value = "起点省")
    private String startProvinceName;

    /**
     * 起点市
     */
    @ExcelProperty(value = "起点城市", index = 1)
    private String startCityName;

    /**
     * 起点市编码
     */
//    @ExcelProperty(value = "起点市编码")
    private String startCityCode;

    /**
     * 起点区
     */
//    @ExcelProperty(value = "起点区")
    private String startDistrictName;

    /**
     * 起点区编码
     */
//    @ExcelProperty(value = "起点区编码")
    private String startAdCode;

    /**
     * 起点位置（长地址）
     */
//    @ExcelProperty(value = "起点位置（长地址）")
    private String startAddress;

    /**
     * 起点位置（短地址）
     */
    @ExcelProperty(value = "起点地址", index = 2)
    private String startShortAddress;

    /**
     * 起点经度
     */
//    @ExcelProperty(value = "起点经度")
    private Double startLongitude;

    /**
     * 起点纬度
     */
//    @ExcelProperty(value = "起点纬度")
    private Double startLatitude;

    /**
     * 终点省
     */
//    @ExcelProperty(value = "终点省")
    private String endProvinceName;

    /**
     * 终点市
     */
    @ExcelProperty(value = "终点城市", index = 3)
    private String endCityName;

    /**
     * 终点市编码
     */
//    @ExcelProperty(value = "终点市编码")
    private String endCityCode;

    /**
     * 终点区id
     */
//    @ExcelProperty(value = "终点区Name")
    private String endDistrictName;

    /**
     * 终点区编码
     */
//    @ExcelProperty(value = "终点区编码")
    private String endAdCode;

    /**
     * 终点位置（长地址）
     */
//    @ExcelProperty(value = "终点位置（长地址）")
    private String endAddress;

    /**
     * 终点位置（短地址）
     */
    @ExcelProperty(value = "终点地址", index = 4)
    private String endShortAddress;

    /**
     * 终点经度
     */
//    @ExcelProperty(value = "终点经度")
    private Double endLongitude;

    /**
     * 终点纬度
     */
//    @ExcelProperty(value = "终点纬度")
    private Double endLatitude;

    /**
     * 最早出发时间
     */
//    @ExcelProperty(value = "最早出发时间")
    private Date earliestTime;

    /**
     * 最晚出发时间
     */
//    @ExcelProperty(value = "最晚出发时间")
    private Date latestTime;

    /**
     * 里程
     */
//    @ExcelProperty(value = "里程")
    private Integer mileage;

    /**
     * 产品类型[productCodeEnum]
     */
//    @ExcelProperty(value = "产品类型[productCodeEnum]")
    private String productCode;

    /**
     * 乘客数量
     */
//    @ExcelProperty(value = "乘客数量")
    private Integer passengerNum;

    /**
     * 返回的价格
     */
//    @ExcelProperty(value = "返回的价格")
    private List<PriceDto> price;

    /**
     * 计价KEY
     */
//    @ExcelProperty(value = "计价KEY")
    private String estimateKey;

    /**
     * 乘客电话
     */
    @ExcelProperty(value = "乘客手机号", index = 0)
    private String passengerPhone;

    /**
     * 乘客创建时间
     */
    @ExcelProperty(value = "乘客注册时间", index = 7)
    @DateTimeFormat("yyyy/MM/dd HH:mm:ss")
    private Date passengerCreateTime;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "询价时间", index = 6)
    @DateTimeFormat("yyyy/MM/dd HH:mm:ss")
    private Date createTime;

    private String reason;

    private Long orderId;
}
