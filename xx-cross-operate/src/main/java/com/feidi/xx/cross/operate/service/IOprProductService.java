package com.feidi.xx.cross.operate.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.operate.domain.OprProduct;
import com.feidi.xx.cross.operate.domain.bo.OprProductBo;
import com.feidi.xx.cross.operate.domain.vo.OprProductVo;

import java.util.Collection;
import java.util.List;

/**
 * 产品Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IOprProductService {

    /**
     * 查询产品
     *
     * @param id 主键
     * @return 产品
     */
    OprProductVo queryById(Long id);

    /**
     * 分页查询产品列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品分页列表
     */
    TableDataInfo<OprProductVo> queryPageList(OprProductBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的产品列表
     *
     * @param bo 查询条件
     * @return 产品列表
     */
    List<OprProductVo> queryList(OprProductBo bo);

    /**
     * 新增产品
     *
     * @param bo 产品
     * @return 是否新增成功
     */
    Boolean insertByBo(OprProductBo bo);

    /**
     * 修改产品
     *
     * @param bo 产品
     * @return 是否修改成功
     */
    Boolean updateByBo(OprProductBo bo);

    /**
     * 校验并批量删除产品信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 修改产品状态
     * @param id
     * @param status
     * @return
     */
    Boolean updateStatus(Long id, String status);

    /**
     * 根据code获取产品信息
     *
     * @param code 产品code
     * @return 产品信息
     */
    OprProduct queryByCode(String code);
}
