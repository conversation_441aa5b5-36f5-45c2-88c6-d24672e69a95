package com.feidi.xx.cross.operate;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 运营模块
 *
 * <AUTHOR>
 */
@EnableDubbo
@MapperScan("com.feidi.xx.**.mapper")
@SpringBootApplication(scanBasePackages = {"com.feidi.xx.**"})
@EnableAsync
public class XXCrossOperateApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(XXCrossOperateApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  运营模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
