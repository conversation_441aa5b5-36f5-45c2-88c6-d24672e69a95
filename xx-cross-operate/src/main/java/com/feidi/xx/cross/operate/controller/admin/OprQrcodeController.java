package com.feidi.xx.cross.operate.controller.admin;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.cross.operate.domain.vo.ExportVo;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.cross.operate.domain.vo.OprQrcodeVo;
import com.feidi.xx.cross.operate.domain.bo.OprQrcodeBo;
import com.feidi.xx.cross.operate.service.IOprQrcodeService;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 推广码
 * 前端访问路由地址为:/operate/qrcode
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/qrcode")
public class OprQrcodeController extends BaseController {

    private final IOprQrcodeService oprQrcodeService;

    /**
     * 查询推广码列表
     */
    @SaCheckPermission("operate:qrcode:list")
    @GetMapping("/list")
    public TableDataInfo<OprQrcodeVo> list(OprQrcodeBo bo, PageQuery pageQuery) {
        return oprQrcodeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出推广码列表
     */
    @SaCheckPermission("operate:qrcode:export")
    @Log(title = "推广码", businessType = BusinessType.EXPORT)
    @Download(name="推广码",module = ModuleConstants.OPERATE)
    @PostMapping("/export")
    public void export(OprQrcodeBo bo, HttpServletResponse response) {
        List<OprQrcodeVo> list = oprQrcodeService.queryList(bo);
        ExcelUtil.exportExcel(list, "推广码", OprQrcodeVo.class, response);
    }

    /**
     * 获取推广码详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("operate:qrcode:query")
    @GetMapping("/{id}")
    public R<OprQrcodeVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable String id) {
        return R.ok(oprQrcodeService.queryById(id));
    }

    /**
     * 新增推广码
     */
    @SaCheckPermission("operate:qrcode:add")
    @Log(title = "推广码", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OprQrcodeBo bo) {
        return toAjax(oprQrcodeService.insertByBo(bo));
    }

    /**
     * 修改推广码
     */
    @SaCheckPermission("operate:qrcode:edit")
    @Log(title = "推广码", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OprQrcodeBo bo) {
        return toAjax(oprQrcodeService.updateByBo(bo));
    }

    /**
     * 删除推广码
     *
     * @param ids 主键串
     */
    @SaCheckPermission("operate:qrcode:remove")
    @Log(title = "推广码", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(oprQrcodeService.deleteWithValidByIds(List.of(ids), true));
    }
}
