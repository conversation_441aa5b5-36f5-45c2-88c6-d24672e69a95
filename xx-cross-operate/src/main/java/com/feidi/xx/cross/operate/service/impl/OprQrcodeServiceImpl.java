package com.feidi.xx.cross.operate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.feidi.xx.cross.operate.domain.bo.OprQrcodeBo;
import com.feidi.xx.cross.operate.domain.vo.OprQrcodeVo;
import com.feidi.xx.cross.operate.domain.OprQrcode;
import com.feidi.xx.cross.operate.mapper.OprQrcodeMapper;
import com.feidi.xx.cross.operate.service.IOprQrcodeService;
import com.feidi.xx.common.core.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 推广码Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-21
 */
@RequiredArgsConstructor
@Service
public class OprQrcodeServiceImpl implements IOprQrcodeService {

    private final OprQrcodeMapper baseMapper;

    /**
     * 查询推广码
     *
     * @param id 主键
     * @return 推广码
     */
    @Override
    public OprQrcodeVo queryById(String id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询推广码列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 推广码分页列表
     */
    @Override
    public TableDataInfo<OprQrcodeVo> queryPageList(OprQrcodeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OprQrcode> lqw = buildQueryWrapper(bo);
        Page<OprQrcodeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的推广码列表
     *
     * @param bo 查询条件
     * @return 推广码列表
     */
    @Override
    public List<OprQrcodeVo> queryList(OprQrcodeBo bo) {
        LambdaQueryWrapper<OprQrcode> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OprQrcode> buildQueryWrapper(OprQrcodeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OprQrcode> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), OprQrcode::getName, bo.getName());
        lqw.eq(bo.getChannelId() != null, OprQrcode::getChannelId, bo.getChannelId());
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), OprQrcode::getCode, bo.getCode());
        return lqw;
    }

    /**
     * 新增推广码
     *
     * @param bo 推广码
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OprQrcodeBo bo) {
        OprQrcode add = MapstructUtils.convert(bo, OprQrcode.class);
        validEntityBeforeSave(add);

        Integer num = bo.getNum();

        if (num != null && num > 0) {
            List<OprQrcode> addList = new ArrayList<>();

            for (int i = 0; i < num; i++) {
                OprQrcode qrcode = BeanUtils.copyProperties(bo, OprQrcode.class);
                String code = makeQrcode(bo.getChannelId());
                qrcode.setCode(code);
                qrcode.setQrcodeUrl(bo.getQrcodeUrl() + code);
                addList.add(qrcode);
            }
            return baseMapper.insertBatch(addList);
        }
        return false;
    }

    private String makeQrcode(Long channelId) {

        String code = RandomUtil.randomString(8);

        if (ObjectUtils.isNotNull(getByCode(channelId, code))) {
            return makeQrcode(channelId);
        }
        return code;
    }

    /**
     * 根据渠道ID和二维码获取推广码
     * @param channelId
     * @param code
     * @return
     */
    private OprQrcode getByCode(Long channelId, String code) {
        LambdaQueryWrapper<OprQrcode> lqw = new LambdaQueryWrapper<>();
        lqw.eq(OprQrcode::getChannelId, channelId);
        lqw.eq(OprQrcode::getCode, code);
        return baseMapper.selectOne(lqw, false);
    }

    /**
     * 修改推广码
     *
     * @param bo 推广码
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OprQrcodeBo bo) {
        OprQrcode update = MapstructUtils.convert(bo, OprQrcode.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OprQrcode entity){
        // 补充唯一性校验
        if (ObjectUtils.isNotNull(getByCode(entity.getChannelId(), entity.getCode()))) {
            throw new ServiceException("推广码已存在");
        }
    }


    /**
     * 校验并批量删除推广码信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
