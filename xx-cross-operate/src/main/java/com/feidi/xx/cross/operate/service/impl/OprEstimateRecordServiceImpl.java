package com.feidi.xx.cross.operate.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.cache.system.DistrictCacheVo;
import com.feidi.xx.common.core.utils.*;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.operate.enums.OprCacheKeyEnum;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import com.feidi.xx.cross.common.cache.operate.vo.OprEstimateRecordCacheVo;
import com.feidi.xx.cross.operate.domain.OprEstimateRecord;
import com.feidi.xx.cross.operate.domain.bo.OprEstimateRecordBo;
import com.feidi.xx.cross.operate.domain.vo.OprEstimateRecordVo;
import com.feidi.xx.cross.operate.mapper.OprEstimateRecordMapper;
import com.feidi.xx.cross.operate.service.IOprEstimateRecordService;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ScheduledExecutorService;

/**
 * 询价记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OprEstimateRecordServiceImpl implements IOprEstimateRecordService {

    private final OprEstimateRecordMapper baseMapper;

    private final ScheduledExecutorService scheduledExecutorService;

    private final OprCacheManager oprCacheManager;

    @DubboReference
    private final RemotePassengerService remotePassengerService;

    /**
     * 查询询价记录
     *
     * @param id 主键
     * @return 询价记录
     */
    @Override
    public OprEstimateRecordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询询价记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 询价记录分页列表
     */
    @Override
    public TableDataInfo<OprEstimateRecordVo> queryPageList(OprEstimateRecordBo bo, PageQuery pageQuery) {
        //初始化
        if (ObjectUtils.isNull(pageQuery.getIsAsc())) {
            pageQuery.setIsAsc("desc");
        }
        if (ObjectUtils.isNull(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("create_time");
        }
        LambdaQueryWrapper<OprEstimateRecord> lqw = buildQueryWrapper(bo);
        Page<OprEstimateRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的询价记录列表
     *
     * @param bo 查询条件
     * @return 询价记录列表
     */
    @Override
    public List<OprEstimateRecordVo> queryList(OprEstimateRecordBo bo) {
        LambdaQueryWrapper<OprEstimateRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OprEstimateRecord> buildQueryWrapper(OprEstimateRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OprEstimateRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPlatformCode()), OprEstimateRecord::getPlatformCode, bo.getPlatformCode());
        lqw.eq(bo.getLineId() != null, OprEstimateRecord::getLineId, bo.getLineId());
        lqw.eq(bo.getPriceId() != null, OprEstimateRecord::getPriceId, bo.getPriceId());
        lqw.eq(bo.getStartProvinceName() != null, OprEstimateRecord::getStartProvinceName, bo.getStartProvinceName());
        lqw.eq(bo.getStartCityName() != null, OprEstimateRecord::getStartCityName, bo.getStartCityName());
        lqw.eq(StringUtils.isNotBlank(bo.getStartCityCode()), OprEstimateRecord::getStartCityCode, bo.getStartCityCode());
        lqw.eq(bo.getStartDistrictName() != null, OprEstimateRecord::getStartDistrictName, bo.getStartDistrictName());
        lqw.eq(StringUtils.isNotBlank(bo.getStartAdCode()), OprEstimateRecord::getStartAdCode, bo.getStartAdCode());
        lqw.eq(StringUtils.isNotBlank(bo.getStartAddress()), OprEstimateRecord::getStartAddress, bo.getStartAddress());
        lqw.eq(ObjectUtil.isNotNull(bo.getStartLongitude()), OprEstimateRecord::getStartLongitude, bo.getStartLongitude());
        lqw.eq(ObjectUtil.isNotNull(bo.getStartLatitude()), OprEstimateRecord::getStartLatitude, bo.getStartLatitude());
        lqw.eq(bo.getEndProvinceName() != null, OprEstimateRecord::getEndProvinceName, bo.getEndProvinceName());
        lqw.eq(bo.getEndCityName() != null, OprEstimateRecord::getEndCityName, bo.getEndCityName());
        lqw.eq(StringUtils.isNotBlank(bo.getEndCityCode()), OprEstimateRecord::getEndCityCode, bo.getEndCityCode());
        lqw.eq(bo.getEndDistrictName() != null, OprEstimateRecord::getEndDistrictName, bo.getEndDistrictName());
        lqw.eq(StringUtils.isNotBlank(bo.getEndAdCode()), OprEstimateRecord::getEndAdCode, bo.getEndAdCode());
        lqw.eq(StringUtils.isNotBlank(bo.getEndAddress()), OprEstimateRecord::getEndAddress, bo.getEndAddress());
        lqw.eq(ObjectUtil.isNotNull(bo.getEndLongitude()), OprEstimateRecord::getEndLongitude, bo.getEndLongitude());
        lqw.eq(ObjectUtil.isNotNull(bo.getEndLatitude()), OprEstimateRecord::getEndLatitude, bo.getEndLatitude());
        lqw.eq(bo.getEarliestTime() != null, OprEstimateRecord::getEarliestTime, bo.getEarliestTime());
        lqw.eq(bo.getMileage() != null, OprEstimateRecord::getMileage, bo.getMileage());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), OprEstimateRecord::getProductCode, bo.getProductCode());
        lqw.eq(bo.getPassengerNum() != null, OprEstimateRecord::getPassengerNum, bo.getPassengerNum());
        lqw.eq(bo.getPrice() != null, OprEstimateRecord::getPrice, bo.getPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getEstimateKey()), OprEstimateRecord::getEstimateKey, bo.getEstimateKey());

        lqw.ge(bo.getStartCreateTime() != null, OprEstimateRecord::getCreateTime, bo.getStartCreateTime());
        lqw.le(bo.getEndCreateTime() != null, OprEstimateRecord::getCreateTime, bo.getEndCreateTime());
        if (bo.getSuccess() != null) {
            if (bo.getSuccess()) {
                lqw.isNotNull(OprEstimateRecord::getEstimateKey);
            } else {
                lqw.isNull(OprEstimateRecord::getEstimateKey);
            }
        }
        lqw.orderByDesc(OprEstimateRecord::getCreateTime);

        return lqw;
    }

    /**
     * 新增询价记录
     *
     * @param bo 询价记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OprEstimateRecordBo bo) {
        OprEstimateRecord add = MapstructUtils.convert(bo, OprEstimateRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }


    public OprEstimateRecordBo getByAdCode(OprEstimateRecordBo bo) {
        if (Objects.equals(bo.getStartAdCode(), bo.getEndAdCode())) {
            DistrictCacheVo districtCacheVo = oprCacheManager.getDistrictCacheVByAdCode(bo.getStartAdCode());
            bo.setStartProvinceName(districtCacheVo.getProvince());
            bo.setStartCityName(districtCacheVo.getCity());
            bo.setStartDistrictName(districtCacheVo.getDistrict());
            bo.setEndProvinceName(districtCacheVo.getProvince());
            bo.setEndCityName(districtCacheVo.getCity());
            bo.setEndDistrictName(districtCacheVo.getDistrict());
            return bo;
        } else {
            DistrictCacheVo startDistrictCacheVo = oprCacheManager.getDistrictCacheVByAdCode(bo.getStartAdCode());
            DistrictCacheVo endDistrictCacheVo = oprCacheManager.getDistrictCacheVByAdCode(bo.getEndAdCode());
            bo.setStartProvinceName(startDistrictCacheVo.getProvince());
            bo.setStartCityName(startDistrictCacheVo.getCity());
            bo.setStartDistrictName(startDistrictCacheVo.getDistrict());
            bo.setEndProvinceName(endDistrictCacheVo.getProvince());
            bo.setEndCityName(endDistrictCacheVo.getCity());
            bo.setEndDistrictName(endDistrictCacheVo.getDistrict());
            return bo;
        }
    }

    /**
     * 修改询价记录
     *
     * @param bo 询价记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OprEstimateRecordBo bo) {
        OprEstimateRecord update = MapstructUtils.convert(bo, OprEstimateRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OprEstimateRecord entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除询价记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * update since 20250716
     * 询价失败也保存
     *
     * @param oprEstimateRecordBo bo
     */
    @Override
    public void saveEstimateRecord(OprEstimateRecordBo oprEstimateRecordBo) {
        oprEstimateRecordBo = getByAdCode(oprEstimateRecordBo);
        OprEstimateRecord add = MapstructUtils.convert(oprEstimateRecordBo, OprEstimateRecord.class);
        //full passenger info
        if (oprEstimateRecordBo.getPassengerId() != null) {
            RemotePassengerVo passengerInfo = remotePassengerService.getPassengerInfo(oprEstimateRecordBo.getPassengerId());
            add.setPassengerPhone(passengerInfo.getPhone());
            add.setPassengerCreateTime(DateUtil.parse(passengerInfo.getCreateTime()));
        }
        validEntityBeforeSave(add);

        // 缓存 失败记录 不做缓存
        if (oprEstimateRecordBo.getEstimateKey() != null) {
            String key = OprCacheKeyEnum.OPR_ESTIMATE_RECORD_KEY.create(oprEstimateRecordBo.getEstimateKey());
            RedisUtils.setCacheObject(key, MapstructUtils.convert(oprEstimateRecordBo, OprEstimateRecordCacheVo.class), OprCacheKeyEnum.OPR_ESTIMATE_RECORD_KEY.getDuration());
        }
        scheduledExecutorService.submit(() -> baseMapper.insert(add));
    }

    /**
     * 根据删除时间删除询价记录
     *
     * @param deleteDate 删除时间
     */
    @Override
    public void deleteEstimateRecord(String deleteDate) {

        // 每批次删除的记录数
        int batchSize = 10000;
        List<Long> ids;
        // 重复次数
        int repeatCount = 1;
        int deleteCount = 0;

        // 分批查询并删除
        do {
            repeatCount++;
            LambdaQueryWrapper<OprEstimateRecord> lqw = new LambdaQueryWrapper<>();
            lqw.select(OprEstimateRecord::getId)
                    .le(OprEstimateRecord::getCreateTime, deleteDate)
                    .isNotNull(OprEstimateRecord::getEstimateKey)
                    // 分页查询
                    .last("limit " + batchSize);

            ids = baseMapper.selectObjs(lqw);

            if (ids != null && !ids.isEmpty()) {

                // 不使用逻辑删除
                baseMapper.doDeleteByIds(ids);

                deleteCount += ids.size();
                log.info("Deleted {} records with createTime less than {}", deleteCount, deleteDate);
            }
        } while (repeatCount <= 20 && CollUtils.isNotEmpty(ids));
    }

    /**
     * 删除询价记录
     *
     * @param deleteDate 删除时间
     * @return
     */
    @Override
    public Boolean deleteRecord(String deleteDate) {
        Date limitDate = DateUtil.offsetDay(DateUtils.getEndDayOfYesterDay(), -20);

        deleteDate = deleteDate + " 23:59:59";

        log.info("手动删除订单询价记录，删除时间：{}", deleteDate);

        if (DateUtils.parseDate(deleteDate).after(limitDate)) {
            log.info("删除时间：【{}】不能早于：【{}】", deleteDate, DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, limitDate));
            return false;
        }
        this.deleteEstimateRecord(deleteDate);
        return true;
    }
}
