package com.feidi.xx.cross.operate.mapper;

import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.operate.domain.OprEstimateRecord;
import com.feidi.xx.cross.operate.domain.vo.OprEstimateRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 询价记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
public interface OprEstimateRecordMapper extends BaseMapperPlus<OprEstimateRecord, OprEstimateRecordVo> {

    /**
     * 批量删除
     * @param ids
     */
    void doDeleteByIds(@Param("ids") List<Long> ids);
}
