package com.feidi.xx.cross.operate.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.id2name.annotation.Id2NameAspect;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.operate.domain.OprLineDetail;
import com.feidi.xx.cross.operate.domain.bo.OprLineDetailForm;
import com.feidi.xx.cross.operate.service.IOprLineDetailService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 后台 - 线路详情
 * 前端访问路由地址为:/operate/lineDetail
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/lineDetail")
public class OprLineDetailController extends BaseController {

    private final IOprLineDetailService oprLineDetailService;

    /**
     * 获取线路详情列表信息
     *
     * @param lineId 主键
     */
    @SaCheckPermission("operate:lineDetail:query")
    @GetMapping("/{lineId}")
    @Id2NameAspect
    public R<List<OprLineDetail>> getInfo(@NotNull(message = "线路不能为空")
                                         @PathVariable Long lineId) {
        return R.ok(oprLineDetailService.queryByLineId(lineId));
    }

    /**
     * 新增线路详情
     */
    @SaCheckPermission("operate:lineDetail:add")
    @Log(title = "线路详情", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated @RequestBody OprLineDetailForm form) {
        return toAjax(oprLineDetailService.insertByBo(form));
    }
}
