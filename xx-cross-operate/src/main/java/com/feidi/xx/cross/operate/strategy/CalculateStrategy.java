package com.feidi.xx.cross.operate.strategy;

import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.SpringUtils;
import com.feidi.xx.cross.operate.domain.dto.OprCalculateDTO;
import com.feidi.xx.cross.operate.domain.vo.OprCalculateVo;
import org.springframework.beans.factory.InitializingBean;

/**
 * 计算价钱策略
 *
 * <AUTHOR>
 */
public interface CalculateStrategy {

    String BASE_NAME = "CalculateStrategy";

    /**
     * 计算价格
     * @param calculateDTO  计算条件
     * @return
     */
    static OprCalculateVo calculatePriceStrategy(OprCalculateDTO calculateDTO) {

        String beanName = calculateDTO.getPriceType() + BASE_NAME;
        if (!SpringUtils.containsBean(beanName)) {
            throw new ServiceException("模板类型不正确!");
        }
        CalculateStrategy instance = SpringUtils.getBean(beanName);
        return instance.calculatePrice(calculateDTO);
    }

    /**
     * 计算价格
     *
     * @param calculateDTO 计算价格条件
     * @return
     */
    OprCalculateVo calculatePrice(OprCalculateDTO calculateDTO);
}
