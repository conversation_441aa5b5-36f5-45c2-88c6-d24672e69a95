package com.feidi.xx.cross.operate.strategy;

import cn.hutool.core.util.ObjectUtil;
import com.feidi.xx.common.core.exception.ServiceException;

import com.feidi.xx.cross.common.cache.operate.vo.OprPriceCacheVo;
import com.feidi.xx.cross.common.enums.operate.ProductCodeEnum;
import com.feidi.xx.cross.operate.api.domain.price.dto.PriceDto;
import com.feidi.xx.cross.operate.domain.dto.OprCalculateDTO;
import com.feidi.xx.cross.operate.domain.vo.OprCalculateVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 阶梯价格策略
 */
@Slf4j
@Service("step" + CalculateStrategy.BASE_NAME)
public class StepCalculateStrategy extends AbstractCalculateStrategy {

    @Override
    public OprCalculateVo calculatePrice(OprCalculateDTO calculateDTO) {

        // 获取拼车价格模板
        OprCalculateVo oprCalculateVo = new OprCalculateVo();
        List<PriceDto> priceDtoList = new ArrayList<>();
        calculateDTO.setProductCode(ProductCodeEnum.FIT.getCode());
        OprPriceCacheVo pricingCacheVo = getRelatedPricingTemplate(calculateDTO);
        if (ObjectUtil.isNull(pricingCacheVo)) {
            throw  new ServiceException("价格模板未配置!");
        }
        PriceDto priceDto = calculateMileagePrices(calculateDTO, pricingCacheVo);
        priceDtoList.add(priceDto);
        // 获取价格独享
        calculateDTO.setProductCode(ProductCodeEnum.RENT.getCode());
        OprPriceCacheVo alonePricingCacheVo = getRelatedPricingTemplate(calculateDTO);
        if (ObjectUtil.isNull(pricingCacheVo)) {
            throw  new ServiceException("价格模板未配置!");
        }
        PriceDto alonePriceDto = calculateMileagePrices(calculateDTO, alonePricingCacheVo);
        priceDtoList.add(alonePriceDto);
        oprCalculateVo.setPriceDtoList(priceDtoList);
        oprCalculateVo.setEstimateKey(calculateDTO.getEstimateKey());
        //保存预估记录
        saveEstimateRecord(calculateDTO, oprCalculateVo);
        discountCoupon(calculateDTO, oprCalculateVo);
        return oprCalculateVo;
    }
}
