package com.feidi.xx.cross.operate.dubbo;

import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.operate.api.RemoteProductService;
import com.feidi.xx.cross.operate.api.domain.product.vo.RemoteProductVo;
import com.feidi.xx.cross.operate.domain.OprProduct;
import com.feidi.xx.cross.operate.domain.vo.OprProductVo;
import com.feidi.xx.cross.operate.service.IOprProductService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/10/18
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteProductServiceImpl implements RemoteProductService {

    private final IOprProductService productService;

    /**
     * 根据id获取产品信息
     *
     * @param id
     * @return
     */
    @Override
    public RemoteProductVo queryById(Long id) {
        OprProductVo oprProductVo = productService.queryById(id);
        return BeanUtils.copyProperties(oprProductVo, RemoteProductVo.class);
    }

    /**
     * 根据code获取产品信息
     *
     * @param code
     * @return
     */
    @Override
    public RemoteProductVo queryByCode(String code) {
        OprProduct oprProduct = productService.queryByCode(code);
        return BeanUtils.copyProperties(oprProduct, RemoteProductVo.class);
    }


}
