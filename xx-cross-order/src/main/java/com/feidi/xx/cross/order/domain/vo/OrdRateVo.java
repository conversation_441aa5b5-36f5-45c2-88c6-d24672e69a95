package com.feidi.xx.cross.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.cross.order.domain.OrdRate;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 订单账单视图对象 ord_rate
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrdRate.class)
public class OrdRateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private String tenantId;

    /**
     * 订单id
     */
    @ExcelProperty(value = "订单id")
    private Long orderId;

    /**
     * 账单类型[BillTypeEnum]
     */
    @ExcelProperty(value = "账单类型[BillTypeEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "BillTypeEnum")
    private String rateType;

    /**
     * 总价
     */
    @ExcelProperty(value = "总价")
    private Long total;

    /**
     * 分佣比例
     */
    @ExcelProperty(value = "分佣比例")
    private BigDecimal rate;

    /**
     * 分佣金额
     */
    @ExcelProperty(value = "分佣金额")
    private Long amount;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 用户名称
     */
    @ExcelProperty(value = "用户名称")
    private String userName;

    /**
     * 用户类型[UserTypeEnum]
     */
    @ExcelProperty(value = "用户类型[UserTypeEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "UserTypeEnum")
    private String userType;

    /**
     * 状态[StatusEnum]
     */
    @ExcelProperty(value = "状态[StatusEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "StatusEnum")
    private String status;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Integer sort;

    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段")
    private String remark;


}
