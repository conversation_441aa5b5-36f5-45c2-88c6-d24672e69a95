package com.feidi.xx.cross.order.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.order.domain.bo.OrdOperateBo;
import com.feidi.xx.cross.order.domain.vo.OrdOperateVo;

import java.util.Collection;
import java.util.List;

/**
 * 订单操作Service接口
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface IOrdOperateService {

    /**
     * 查询订单操作
     *
     * @param id 主键
     * @return 订单操作
     */
    OrdOperateVo queryById(Long id);

    /**
     * 分页查询订单操作列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单操作分页列表
     */
    TableDataInfo<OrdOperateVo> queryPageList(OrdOperateBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的订单操作列表
     *
     * @param bo 查询条件
     * @return 订单操作列表
     */
    List<OrdOperateVo> queryList(OrdOperateBo bo);

    /**
     * 新增订单操作
     *
     * @param bo 订单操作
     * @return 是否新增成功
     */
    Boolean insertByBo(OrdOperateBo bo);

    /**
     * 修改订单操作
     *
     * @param bo 订单操作
     * @return 是否修改成功
     */
    Boolean updateByBo(OrdOperateBo bo);

    /**
     * 校验并批量删除订单操作信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据订单id查询订单操作记录
     *
     * @param orderId 订单id
     * @return 订单操作记录
     */
    List<OrdOperateVo> queryByOrderId(Long orderId);
}
