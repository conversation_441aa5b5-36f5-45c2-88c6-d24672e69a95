package com.feidi.xx.cross.order.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.StrUtil;
import com.feidi.xx.common.core.domain.platform.PlatformApiResponseVo;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.StartEndEnum;
import com.feidi.xx.common.core.enums.SuccessFailEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.insure.AcInsureHelper;
import com.feidi.xx.common.insure.constant.AcInsureConstants;
import com.feidi.xx.common.insure.enums.FileTypeEnum;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.order.enums.OrdCacheKeyEnum;
import com.feidi.xx.cross.common.enums.order.InsureStatusEnum;
import com.feidi.xx.cross.common.enums.order.OperateTypeEnum;
import com.feidi.xx.cross.common.helper.OrdOrderOperateHelper;
import com.feidi.xx.cross.common.mq.event.OrdOrderOperateEvent;
import com.feidi.xx.cross.common.mq.producer.OrdOrderOperateProducer;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderHandleBaseBo;
import com.feidi.xx.cross.order.domain.vo.OrdPositionVo;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.service.IOrdOrderInsureService;
import com.feidi.xx.cross.order.service.IOrdPositionService;
import com.feidi.xx.system.api.RemoteConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单投保服务实现类
 *
 * <AUTHOR>
 * @date 2025/3/16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrdOrderInsureServiceImpl implements IOrdOrderInsureService {

    private final IOrdPositionService ordPositionService;
    private final OrdOrderMapper ordOrderMapper;
    private final OrdOrderOperateProducer ordOrderOperateProducer;
    @DubboReference
    private final RemoteConfigService remoteConfigService;

    @Override
    public Boolean insure(OrdOrderHandleBaseBo bo) {
        OrdOrder order = ordOrderMapper.selectById(bo.getOrderId());
        if (order != null && Objects.equals(order.getInsureStatus(), InsureStatusEnum.INSURED.getCode())) {
            log.error("已经为该司机进行投保，订单id: 【{}】", bo.getOrderId());
            throw new ServiceException("已经为该司机进行投保，请勿重复操作");
        }
        // 为司机投保
        this.handleOrderInsure(order, FileTypeEnum.INSURE.getCode(), bo);
        return Boolean.TRUE;
    }

    @Override
    public void handleOrderInsure(OrdOrder order, String fileType, OrdOrderHandleBaseBo bo) {
        // 校验订单是否已经投保
        if (Objects.equals(fileType, FileTypeEnum.INSURE.getCode()) &&
                Objects.equals(order.getInsureStatus(), InsureStatusEnum.INSURED.getCode())) {
            log.info("订单已投保，无需再次投保，订单号：{}", order.getOrderNo());
            return;
        }

        String remark = "投保中";
        String operateType = Objects.equals(fileType, FileTypeEnum.INSURE.getCode()) ? OperateTypeEnum.INSURE.getCode() : OperateTypeEnum.CANCEL_INSURE.getCode();
        // 投保日志
        OrdOrderOperateEvent insureRecordEvent = OrdOrderOperateHelper.buildInsureRecordEvent(order.getTenantId(), order.getId(), operateType, JsonUtils.toJsonString(bo));

        try {
            if (log.isInfoEnabled()) {
                log.info("开始投保，订单id: 【{}】", order.getId());
            }

            String insureSwitch = remoteConfigService.selectValueByKey(AcInsureConstants.ORDER_INSURE_SWITCH);
            if (!Objects.equals(insureSwitch, IsYesEnum.YES.getCode())) {
                if (log.isInfoEnabled()) {
                    log.info("投保开关未开启，跳过投保环节");
                }
                remark = "投保开关未开启，暂不投保";
                return;
            }

            DateTime now = DateUtil.date();

            String fileTypeInfo = "投保";
            StrBuilder insureInfo = StrBuilder.create();
            if (Objects.equals(FileTypeEnum.INSURE.getCode(), fileType)) {
                // 获取订单出发城市和终点城市
                List<OrdPositionVo> positionVos = ordPositionService.queryByOrderIds(Collections.singletonList(order.getId()));
                Map<String, OrdPositionVo> type2CityMap = positionVos.stream().collect(Collectors.toMap(OrdPositionVo::getType, Function.identity()));

                OrdPositionVo startPositionVo = type2CityMap.get(StartEndEnum.START.getCode());
                OrdPositionVo endPositionVo = type2CityMap.get(StartEndEnum.END.getCode());

                // 出发地-目的地
                String positionCty = startPositionVo.getCity() + " - " + endPositionVo.getCity();
                // 投保信息
                insureInfo.append(order.getId()).append("|")
                        .append(now.toString(DatePattern.NORM_DATETIME_PATTERN)).append("|")
                        .append(now.toString(DatePattern.NORM_DATETIME_PATTERN)).append("|")
                        .append(DateUtil.offsetDay(now, 1).toString(DatePattern.NORM_DATETIME_PATTERN)).append("|")
                        .append(positionCty).append("|");
                // 非拼车时，不用传
                //.append(ShareFlagEnum.CROSS_POOLING_SUCCESS.getCode());
            } else if (Objects.equals(FileTypeEnum.CANCEL_INSURE.getCode(), fileType)) {
                // 退保信息
                remark = "退保中";
                fileTypeInfo = "退保";
                insureInfo.append(order.getId()).append("|")
                        .append(order.getInsureNo()).append("|")
                        .append(now.toString(DatePattern.NORM_DATETIME_PATTERN));
            }

            if (log.isInfoEnabled()) {
                log.info("{}信息: 【{}】", fileTypeInfo, insureInfo);
            }

            // 投保/退保 完成后，文件下载路径
            String filePath = AcInsureHelper.createFileUploadPath(order.getId().toString(), fileType);
            if (log.isInfoEnabled()) {
                log.info("{}完成后，文件下载路径: 【{}】", fileTypeInfo, filePath);
            }

            // 投保/退保 完成后，文件下载路径，保存到redis，在投保回调方法中使用
            String key = OrdCacheKeyEnum.ORD_ORDER_INSURANCE_INFO_KEY.create(order.getId());
            RedisUtils.setCacheObject(key, filePath, Duration.ofDays(AcInsureConstants.ORDER_INSURE_TIME));

            // 投保/退保 文件上传路径
            String fileUploadPath = filePath + fileType + "/";
            if (log.isInfoEnabled()) {
                log.info("{}文件上传路径: 【{}】", fileTypeInfo, fileUploadPath);
            }

            // 创建txt文件，并压缩成zip包
            long zipSize = AcInsureHelper.createAndZipFile(insureInfo.toString(), fileType, order.getId().toString());
            if (log.isInfoEnabled()) {
                log.info("{}压缩文件size: 【{}】", fileTypeInfo, zipSize);
            }

            // 将压缩的文件上传文件到 SFTP服务器
            AcInsureHelper.sftpUploadFile(fileUploadPath, fileType, order.getId().toString());

            // 生成请求报文并加签
            String xmlSignStr = AcInsureHelper.createRequestMessage(order.getId().toString(), fileType, zipSize);
            if (log.isInfoEnabled()) {
                log.info("{}请求报文: 【{}】", fileTypeInfo, xmlSignStr);
            }

            // 发送请求
            PlatformApiResponseVo platformApiResponseVo = AcInsureHelper.sendMessage(xmlSignStr);

            // 投保日志
            insureRecordEvent.setResponseJson(JsonUtils.toJsonString(platformApiResponseVo));
            insureRecordEvent.setStatus(SuccessFailEnum.SUCCESS.getCode());
        } catch (Exception e) {
            log.error("订单投保异常，订单id: 【{}】", order.getId(), e);
            insureRecordEvent.setRemark("投保失败");
            String failIds = RedisUtils.getCacheObject(AcInsureConstants.ORDER_INSURE_FAIL_IDS);
            String failIdStr = StrUtil.isNotBlank(failIds) ? (failIds + "," + order.getId().toString()) : order.getId().toString();
            if (failIdStr.split(",").length >= AcInsureConstants.ORDER_INSURE_FAIL_SIZE) {
                // 发送邮件
                AcInsureHelper.sendEmailForInsureFail(failIdStr);
                RedisUtils.deleteObject(AcInsureConstants.ORDER_INSURE_FAIL_IDS);
            } else {
                RedisUtils.setCacheObject(AcInsureConstants.ORDER_INSURE_FAIL_IDS, failIdStr, Duration.ofMinutes(AcInsureConstants.ORDER_INSURE_FAIL_TIME));
            }
        } finally {
            insureRecordEvent.setRemark(remark);
            ordOrderOperateProducer.sendMessage(insureRecordEvent);
        }
    }
}
