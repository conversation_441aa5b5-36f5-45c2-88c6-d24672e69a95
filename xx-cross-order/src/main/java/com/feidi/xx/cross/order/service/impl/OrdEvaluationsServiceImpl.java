package com.feidi.xx.cross.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.common.utils.ExceptionUtil;
import com.feidi.xx.cross.order.api.domain.RemoteOrderDriverService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDriverVo;
import com.feidi.xx.cross.order.domain.OrdDriverEvaluation;
import com.feidi.xx.cross.order.domain.OrdEvaluations;
import com.feidi.xx.cross.order.domain.bo.OrdEvaluationsBo;
import com.feidi.xx.cross.order.domain.bo.OrdOrderCommentBo;
import com.feidi.xx.cross.order.domain.vo.OrdEvaluationsVo;
import com.feidi.xx.cross.order.mapper.OrdDriverEvaluationMapper;
import com.feidi.xx.cross.order.mapper.OrdEvaluationsMapper;
import com.feidi.xx.cross.order.service.IOrdEvaluationsService;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 行程评价Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OrdEvaluationsServiceImpl implements IOrdEvaluationsService {

    private final OrdEvaluationsMapper baseMapper;
    private final OrdDriverEvaluationMapper driverEvaluationMapper;

    @DubboReference
    private final RemotePassengerService remotePassengerService;
    private final RemoteOrderDriverService remoteOrderDriverService;
    private final ScheduledExecutorService scheduledExecutorService;


    /**
     * 查询行程评价
     *
     * @param id 主键
     * @return 行程评价
     */
    @Override
    public OrdEvaluationsVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询行程评价列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 行程评价分页列表
     */
    @Override
    public TableDataInfo<OrdEvaluationsVo> queryPageList(OrdEvaluationsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrdEvaluations> lqw = buildQueryWrapper(bo);
        Page<OrdEvaluationsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的行程评价列表
     *
     * @param bo 查询条件
     * @return 行程评价列表
     */
    @Override
    public List<OrdEvaluationsVo> queryList(OrdEvaluationsBo bo) {
        LambdaQueryWrapper<OrdEvaluations> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrdEvaluations> buildQueryWrapper(OrdEvaluationsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrdEvaluations> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getAgentId() != null, OrdEvaluations::getAgentId, bo.getAgentId());
        lqw.eq(bo.getOrderId() != null, OrdEvaluations::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), OrdEvaluations::getOrderNo, bo.getOrderNo());
        lqw.eq(bo.getDriverId() != null, OrdEvaluations::getDriverId, bo.getDriverId());
        lqw.eq(bo.getPassengerId() != null, OrdEvaluations::getPassengerId, bo.getPassengerId());
        lqw.eq(StringUtils.isNotBlank(bo.getPassengerPhone()), OrdEvaluations::getPassengerPhone, bo.getPassengerPhone());
        lqw.like(StringUtils.isNotBlank(bo.getPassengerName()), OrdEvaluations::getPassengerName, bo.getPassengerName());
        lqw.eq(bo.getRating() != null, OrdEvaluations::getRating, bo.getRating());
        lqw.eq(bo.getIsAnonymous() != null, OrdEvaluations::getIsAnonymous, bo.getIsAnonymous());
        lqw.eq(StringUtils.isNotBlank(bo.getComment()), OrdEvaluations::getComment, bo.getComment());
        List<String> searchTags = bo.getTag().stream().map(URLUtil::decode).toList();
        lqw.apply(CollUtil.isNotEmpty(bo.getTag()), "JSON_OVERLAPS(tags, {0})", JsonUtils.toJsonString(searchTags));
        //时间筛选
        lqw.ge(bo.getStartCreateTime() != null, OrdEvaluations::getCreateTime, bo.getStartCreateTime());
        lqw.le(bo.getEndCreateTime() != null, OrdEvaluations::getCreateTime, bo.getEndCreateTime());
        lqw.ge(bo.getStartUpdateTime() != null, OrdEvaluations::getUpdateTime, bo.getStartUpdateTime());
        lqw.le(bo.getEndUpdateTime() != null, OrdEvaluations::getUpdateTime, bo.getEndUpdateTime());
        return lqw;
    }

    /**
     * 新增行程评价
     *
     * @param bo 行程评价
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OrdEvaluationsBo bo) {
        OrdEvaluations add = MapstructUtils.convert(bo, OrdEvaluations.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改行程评价
     *
     * @param bo 行程评价
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OrdEvaluationsBo bo) {
        OrdEvaluations update = MapstructUtils.convert(bo, OrdEvaluations.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrdEvaluations entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除行程评价信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public OrdEvaluationsVo queryByOrderId(Long orderId) {
        if (orderId == null) {
            throw new ServiceException("订单ID不能为空");
        }
        OrdEvaluationsBo bo = new OrdEvaluationsBo();
        bo.setOrderId(orderId);
        var lqw = Wrappers.<OrdEvaluations>lambdaQuery().eq(OrdEvaluations::getOrderId, orderId);
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void comment(OrdOrderCommentBo bo) {
        //判断是否已经评价过
        var lqw = Wrappers.<OrdEvaluations>lambdaQuery().eq(OrdEvaluations::getOrderId, bo.getOrderId());
        if (baseMapper.selectCount(lqw) > 0) {
            throw new ServiceException("已经评价过");
        }
        OrdEvaluations ordEvaluations = OrdEvaluations.create(bo);
        RemotePassengerVo passengerInfo = remotePassengerService.getPassengerInfo(bo.getPassengerId());
        ordEvaluations.setPassengerPhone(passengerInfo.getPhone());
        ordEvaluations.setPassengerName(passengerInfo.getName());
        RemoteOrderDriverVo remoteOrderDriverVo = remoteOrderDriverService.queryByOrderId(bo.getOrderId());
        Assert.notNull(remoteOrderDriverVo, "司机信息不存在");
        ordEvaluations.setDriverId(remoteOrderDriverVo.getDriverId());
        ordEvaluations.setAgentId(remoteOrderDriverVo.getAgentId());
        int i = baseMapper.insert(ordEvaluations);
        log.info("行程评价插入结果:{}", i);
        OrdDriverEvaluation driverEvaluation = driverEvaluationMapper.queryByDriverId(remoteOrderDriverVo.getDriverId());
        if (driverEvaluation == null) {
            driverEvaluation = new OrdDriverEvaluation();
            driverEvaluation.setAgentId(ordEvaluations.getAgentId());
            driverEvaluation.setDriverId(ordEvaluations.getDriverId());
            driverEvaluation.setDriverPhone(remoteOrderDriverVo.getDriverPhone());
            driverEvaluation.setDriverName(remoteOrderDriverVo.getDriverName());
            driverEvaluation.setAgentId(remoteOrderDriverVo.getAgentId());
            driverEvaluationMapper.insert(driverEvaluation);
        }
        var w = Wrappers.<OrdDriverEvaluation>lambdaUpdate().set(OrdDriverEvaluation::getUpdateTime, new Date()).eq(OrdDriverEvaluation::getId, driverEvaluation.getId());
        driverEvaluationMapper.update(w);

        Long driverEvaluationId = driverEvaluation.getId();
        Long driverId = ordEvaluations.getDriverId();
        ExceptionUtil.ignoreEx(() -> {
            scheduledExecutorService.schedule(() -> {
                statistics(driverEvaluationId, driverId);
            }, 1, TimeUnit.SECONDS);
        });
    }

    /**
     * 统计数量
     */
    public void statistics(Long driverEvaluationId, Long driverId) {
        log.debug("统计数量开始 deid: {} drvid {}", driverEvaluationId, driverId);
        //统计数量
        Long l = baseMapper.selectCount(Wrappers.<OrdEvaluations>lambdaQuery().eq(OrdEvaluations::getDriverId, driverId));
        Long score = baseMapper.sumRatingByDriverId(driverId);
        BigDecimal decimal = NumberUtil.div(score, l, 1);

        int i = driverEvaluationMapper.update(Wrappers.<OrdDriverEvaluation>lambdaUpdate()
                .eq(OrdDriverEvaluation::getId, driverEvaluationId)
                .set(OrdDriverEvaluation::getEvaluationCount, l)
                .set(OrdDriverEvaluation::getTotalScore, decimal));

        log.debug("行程评价统计结果:{}", i);
    }
}
