package com.feidi.xx.cross.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.cross.order.domain.OrdStatistic;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 订单统计视图对象 ord_statistic
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrdStatistic.class)
public class OrdStatisticVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 平台编号 
     */
    private String platformCode;

    /**
     * 统计时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date statisticDate;

    /**
     * 总发单量
     */
    private Long volumeNumber = 0L;

    /**
     * 下单数（发单量）
     */
    private Long placeNumber = 0L;

    /**
     * 总发单量日环比
     */
    private BigDecimal volumeNumberDayRate = new BigDecimal("0.00");

    /**
     * 总发单量周环比
     */
    private BigDecimal volumeNumberWeekRate = new BigDecimal("0.00");

    /**
     * 接单数
     */
    private Long acceptNumber = 0L;

    /**
     * 待接单数量
     */
    private Long createNumber = 0L;

    /**
     * 待出发数量
     */
    private Long receiveNumber = 0L;

    /**
     * 前往上车点数量
     */
    private Long pickNumber = 0L;

    /**
     * 到达上车点数量
     */
    private Long pickStartNumber = 0L;

    /**
     * 行程中数量
     */
    private Long ingNumber = 0L;

    /**
     * 完单数量
     */
    private Long finishNumber = 0L;

    /**
     * 完单数日环比
     */
    private BigDecimal finishNumberDayRate = new BigDecimal("0.00");

    /**
     * 完单数周环比
     */
    private BigDecimal finishNumberWeekRate = new BigDecimal("0.00");

    /**
     * 已取消数量
     */
    private Long cancelNumber = 0L;

    /**
     * 取消数日环比
     */
    private BigDecimal cancelNumberDayRate = new BigDecimal("0.00");

    /**
     * 取消数周环比
     */
    private BigDecimal cancelNumberWeekRate = new BigDecimal("0.00");

    /**
     * 接完率
     */
    private BigDecimal receiveFinishRate = new BigDecimal("0.00");

    /**
     * 接完率日环比
     */
    private BigDecimal receiveFinishDayRate = new BigDecimal("0.00");

    /**
     * 接完率周环比
     */
    private BigDecimal receiveFinishWeekRate = new BigDecimal("0.00");

    /**
     * 完单金额
     */
    private Long finishAmount = 0L;

    /**
     * 完单金额日环比
     */
    private BigDecimal finishAmountDayRate = new BigDecimal("0.00");

    /**
     * 完单金额周环比
     */
    private BigDecimal finishAmountWeekRate = new BigDecimal("0.00");

    /**
     * 支付金额
     */
    private Long payAmount = 0L;

    /**
     * 支付金额日环比
     */
    private BigDecimal payAmountDayRate = new BigDecimal("0.00");

    /**
     * 支付金额周环比
     */
    private BigDecimal payAmountWeekRate = new BigDecimal("0.00");

    /**
     * 支付订单数
     */
    private Long payNumber = 0L;

    /**
     * 支付订单数日环比
     */
    private BigDecimal payNumberDayRate = new BigDecimal("0.00");

    /**
     * 支付订单数周环比
     */
    private BigDecimal payNumberWeekRate = new BigDecimal("0.00");

    /**
     * 新增乘客注册数
     */
    private Long passengerRegisterNumber = 0L;

    /**
     * 新增乘客注册数日环比
     */
    private BigDecimal registerNumberDayRate = new BigDecimal("0.00");

    /**
     * 新增乘客注册数周环比
     */
    private BigDecimal registerNumberWeekRate = new BigDecimal("0.00");

    /**
     * 乘客访问数量
     */
    //private Long passengerVisitNumber = 0L;

    /**
     * 乘客访问数量日环比
     */
    //private BigDecimal VisitNumberDayRate = new BigDecimal("0.00");

    /**
     * 乘客访问数量周环比
     */
    //private BigDecimal VisitNumberWeekRate = new BigDecimal("0.00");

}
