package com.feidi.xx.cross.order.chain.common;

/**
 * 责任链线程上下文工具
 */
public  class ChainContextUtil {

    /**
     * 上下文
     */
    private static final ThreadLocal<BaseChainContext> CONTEXT = ThreadLocal.withInitial(BaseChainContext::new);

    /**
     * 结果
     */
    private static final ThreadLocal<BaseChainResult> RESULT = ThreadLocal.withInitial(BaseChainResult::new);

    public static BaseChainContext getContext() {
        return CONTEXT.get();
    }

    public static void setContext(BaseChainContext context) {
        CONTEXT.set(context);
    }

    public static void clearContext() {
        CONTEXT.remove();
    }

    public static BaseChainResult getResult() {
        return RESULT.get();
    }

    public static void setResult(BaseChainResult result) {
        RESULT.set(result);
    }

    public static void clearResult() {
        RESULT.remove();
    }

}
