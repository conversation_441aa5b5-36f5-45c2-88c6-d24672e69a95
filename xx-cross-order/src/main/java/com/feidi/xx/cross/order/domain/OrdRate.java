package com.feidi.xx.cross.order.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 订单账单对象 ord_rate
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ord_rate")
public class OrdRate extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 账单类型[BillTypeEnum]
     */
    private String rateType;

    /**
     * 总价
     */
    private Long total;

    /**
     * 分佣比例
     */
    private BigDecimal rate;

    /**
     * 分佣金额
     */
    private Long amount;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户类型[UserTypeEnum]
     */
    private String userType;

    /**
     * 状态[StatusEnum]
     */
    private String status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 扩展字段
     */
    private String remark;

    /**
     * 逻辑删除 0存在 2删除
     */
    @TableLogic
    private String delFlag;


}
