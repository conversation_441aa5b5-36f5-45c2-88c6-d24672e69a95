package com.feidi.xx.cross.order.helper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.cache.power.manager.PowCacheManager;
import com.feidi.xx.cross.common.enums.market.InviteTypeEnum;
import com.feidi.xx.cross.common.enums.market.RewardTypeEnum;
import com.feidi.xx.cross.common.enums.order.CreateModelEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.enums.order.RateTypeEnum;
import com.feidi.xx.cross.common.enums.order.RebateStatusEnum;
import com.feidi.xx.cross.common.enums.platform.MtRefundTypeEnum;
import com.feidi.xx.cross.finance.api.RemoteDrvWalletService;
import com.feidi.xx.cross.finance.api.domain.bo.RemoteRebateBo;
import com.feidi.xx.cross.market.api.RemoteInviteRecordService;
import com.feidi.xx.cross.market.api.domain.RemoteInviteRecordVo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderCancelBo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDriverVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderRateVo;
import com.feidi.xx.cross.order.components.PlatformOrderApiComponent;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.OrdOrderInfo;
import com.feidi.xx.cross.order.mapper.OrdOrderInfoMapper;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.service.IOrdRateService;
import com.feidi.xx.cross.platform.api.hbk.domain.hbk.vo.RemotePlatformApiResponseVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 订单客诉辅助类
 *
 * <AUTHOR>
 * @date 2025/3/22
 */
@Slf4j
@Component
@AllArgsConstructor
public class OrdOrderComplainHelper {

    private final PowCacheManager powCacheManager;
    private final OrdCacheManager ordCacheManager;
    private final IOrdRateService ordRateService;
    private final OrdOrderMapper ordOrderMapper;
    private final PlatformOrderApiComponent platformOrderApiComponent;
    private final OrdOrderInfoMapper ordOrderInfoMapper;
    @DubboReference
    private final RemoteDrvWalletService remoteDrvWalletService;
    @DubboReference
    private final RemoteInviteRecordService remoteInviteRecordService;

    /**
     * 客诉
     *
     * @param cancelBo 客诉参数
     * @param order    订单信息
     * @return 是否客诉成功
     */
    @Transactional(rollbackFor = Exception.class)
    //@GlobalTransactional(rollbackFor = Exception.class)
    public Boolean complain(RemoteOrderCancelBo cancelBo, OrdOrder order) {
        Assert.notNull(order, "数据不存在");
        Assert.isTrue(Objects.equals(order.getStatus(), OrderStatusEnum.FINISH.getCode()), "当前订单未完成，无法发起客诉");

        // 获取司机收益
        RemoteOrderRateVo driverProfitVo = ordCacheManager.getOrderRateInfoByOrderIdAndRateType(order.getId(), RateTypeEnum.DRIVER.getCode());
        // 获取邀请司机收益
        RemoteOrderRateVo inviteDriverProfitVo = ordCacheManager.getOrderRateInfoByOrderIdAndRateType(order.getId(), RateTypeEnum.INVITE_DRIVER.getCode());

        // 订单收益数据状态更新为不可用
        this.updateOrderProfitAfterComplain(order.getId(), StatusEnum.DISABLE.getCode());

        LambdaUpdateWrapper<OrdOrderInfo> infoUpdateWrapper = new LambdaUpdateWrapper<>();
        infoUpdateWrapper.eq(OrdOrderInfo::getOrderId, order.getId())
                .set(OrdOrderInfo::getComplainTime, DateUtils.getNowDate())
                .set(OrdOrderInfo::getComplainType, cancelBo.getComplainType())
                .set(OrdOrderInfo::getComplainRemark, cancelBo.getComplainRemark())
                .set(OrdOrderInfo::getComplainPrice, cancelBo.getComplainPrice())
                .set(OrdOrderInfo::getUpdateTime, DateUtils.getNowDate());
        ordOrderInfoMapper.update(infoUpdateWrapper);

        // 处理客诉
        LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrdOrder::getId, order.getId())
                .set(OrdOrder::getComplain, IsYesEnum.YES.getCode())
                // 返利状态
                .set(OrdOrder::getRebateStatus, RebateStatusEnum.COMPLAIN.getCode());
        Boolean flag = ordOrderMapper.update(updateWrapper) > 0;
        if (flag) {
            // 记录资金流水
            RemoteRebateBo rebateBo = this.createRemoteRebateVo(order, driverProfitVo, inviteDriverProfitVo);
            rebateBo.setComplainPrice(order.getOrderPrice());
            rebateBo.setResellDriverId(order.getResellDriverId());
            flag = remoteDrvWalletService.complain(rebateBo);
            if (Objects.equals(order.getCreateModel(), CreateModelEnum.RESELL_ORDER.getCode())
                    && order.getResellDriverId() != null && order.getResellDriverId() > 0) {
                RemoteDriverVo driverVo = powCacheManager.getDriverInfoById(order.getResellDriverId());
                // 卖单司机收益（订单金额 - 转卖后司机接单金额 - 订单金额 * 订单转卖服务费比例）
                long resellDriverProfit = order.getOrderPrice() -  order.getResellDriverPrice() - ArithUtils.profitUseBigDecimal(order.getOrderPrice(), driverVo.getResellServiceRate());
                rebateBo.setResellDriverProfit(resellDriverProfit);
                rebateBo.setResellDriverName(driverVo.getName());
                rebateBo.setResellDriverPhone(driverVo.getPhone());
                flag = remoteDrvWalletService.resellComplain(rebateBo);
            }
        }

        // 获取邀请司机收益
        RemoteOrderRateVo inviteAgentProfitVo = ordCacheManager.getOrderRateInfoByOrderIdAndRateType(order.getId(), RateTypeEnum.INVITE_AGENT.getCode());
        if (ObjectUtils.isNotNull(inviteAgentProfitVo)) {
            //代理商客诉
            String inviteType;
            if (ObjectUtils.isNotNull(order.getInviteDriverId())) {
                inviteType = InviteTypeEnum.DRIVER_INVITE.getCode();
            } else {
                inviteType = InviteTypeEnum.AGENT_INVITE.getCode();
            }
            RemoteInviteRecordVo build = RemoteInviteRecordVo.builder()
                    .tenantId(order.getTenantId())
                    .passengerId(order.getPassengerId())
                    .agentId(inviteAgentProfitVo.getUserId())
                    .driverId(order.getDriverId())
                    .orderId(inviteAgentProfitVo.getOrderId())
                    .inviteType(inviteType)
                    .rewardType(RewardTypeEnum.REWARD_REFUND.getCode())
                    .rewardRate(inviteAgentProfitVo.getRate())
                    .rewardPrice(inviteAgentProfitVo.getAmount())
                    .build();
            remoteInviteRecordService.insertByBo(build);
        }

        return flag;
    }

    /**
     * 计算客诉扣款后的订单收益
     *
     * 目前客诉是全部扣款，不支持部分扣款
     *
     * 当前逻辑是把订单收益数据状态全部更新为不可用
     */
    public Boolean updateOrderProfitAfterComplain(Long orderId, String rateStatus) {
        return ordRateService.updateRateStatusByOrderId(orderId, rateStatus);
    }

    /**
     * 订单退款
     *
     * @param order 订单信息
     */
    public RemotePlatformApiResponseVo applyRefund(OrdOrder order) {
        if (log.isInfoEnabled()) {
            log.info("后台管理员-发起订单退款，订单信息【{}】", JsonUtils.toJsonString(order));
        }

        RemotePlatformApiResponseVo platformApiResponseVo = platformOrderApiComponent.applyRefund(order.getOrderPrice(), 0L, MtRefundTypeEnum.FULL.getCode(), "客服取消退款", order);

        // TODO-NEW 记录日志MQ
        //SpringUtils.context().publishEvent(orderOperateEvent);

        return platformApiResponseVo;
    }

    /**
     * 创建客诉返利对象
     *
     * @param order 订单信息
     * @param driverRate 司机收益
     * @param inviteRate 邀请司机收益
     * @return 客诉返利对象
     */
    public RemoteRebateBo createRemoteRebateVo(OrdOrder order, RemoteOrderRateVo driverRate, RemoteOrderRateVo inviteRate) {
        // 订单司机信息
        RemoteOrderDriverVo orderDriverVo = ordCacheManager.getOrderDriverInfoByOrderId(order.getId());
        return new RemoteRebateBo()
                .setTenantId(order.getTenantId())
                .setOrderId(order.getId())
                .setOrderNo(order.getOrderNo())
                .setAgentId(order.getAgentId())
                .setDriverId(order.getDriverId())
                .setDriverName(orderDriverVo.getDriverName())
                .setDriverPhone(orderDriverVo.getDriverPhone())
                .setOrderPrice(order.getOrderPrice())
                .setDriverRate(driverRate.getRate())
                .setDriverComplainPrice(driverRate.getAmount())
                .setRebateStatus(order.getRebateStatus())
                .setInviteComplainPrice(ObjectUtils.isNotNull(inviteRate) ? inviteRate.getAmount() : 0L)
                .setInviteDriverId(order.getInviteDriverId())
                .setInviteAgentId(order.getInviteAgentId());

    }
}
