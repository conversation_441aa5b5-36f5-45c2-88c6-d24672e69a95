package com.feidi.xx.cross.order.chain.common;

import cn.hutool.core.util.StrUtil;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.constant.order.OrderLockKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.util.Assert;

import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 订单下单链
 */
@Slf4j
public abstract class BaseChain<T extends BaseChainContext, R extends BaseChainResult> {

    protected final List<AbstractChainHandler<T, R>> handlers = new LinkedList<>();

    public R execute(T context) {
        String lockKey = getLockKey(context);
        Assert.isTrue(StrUtil.isNotBlank(lockKey), "lockKey不能为空");
        RedissonClient client = RedisUtils.getClient();
        RLock lock = client.getLock(lockKey);
        try {
            boolean isLock = lock.tryLock(OrderLockKeyConstants.LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
            if (isLock) {
                handlers.forEach(handler -> handler.handle(context));
                return (R) ChainContextUtil.getResult();
            }
            throw new ServiceException("系统异常，请重试");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("订单链执行异常", e);
            throw new ServiceException(e.getMessage());
        } finally {
            ChainContextUtil.clearContext();
            ChainContextUtil.clearResult();
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public abstract String getLockKey(T context);

}
