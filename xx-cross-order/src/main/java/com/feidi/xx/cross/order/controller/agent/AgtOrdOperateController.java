package com.feidi.xx.cross.order.controller.agent;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.order.domain.vo.OrdOperateVo;
import com.feidi.xx.cross.order.service.IOrdOperateService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 后台 - 订单操作
 * 前端访问路由地址为:/order/operate
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/operate")
public class AgtOrdOperateController extends BaseController {

    private final IOrdOperateService ordOperateService;

    /**
     * 查询订单操作列表
     */
    @Enum2TextAspect
    @GetMapping("/list/{orderId}")
    public R<List<OrdOperateVo>> list(@PathVariable Long orderId) {
        return R.ok(ordOperateService.queryByOrderId(orderId));
    }
}
