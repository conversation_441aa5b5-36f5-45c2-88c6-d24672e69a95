package com.feidi.xx.cross.order.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单统计对象 ord_statistic
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ord_statistic")
public class OrdStatistic extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 平台编号 
     */
    private String platformCode;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 线路id
     */
    private Long lineId;

    /**
     * 线路名称
     */
    private String lineName;

    /**
     * 统计时间
     */
    private Date statisticDate;

    /**
     * 总发单量
     */
    private Long volumeNumber;

    /**
     * 下单数（发单量）
     */
    @TableField(exist = false)
    private Long placeNumber = 0L;

    /**
     * 当日出发订单
     */
    private Long departNumber;

    /**
     * 接单数
     */
    private Long acceptNumber;

    /**
     * 待接单数量
     */
    private Long createNumber;

    /**
     * 待出发数量
     */
    private Long receiveNumber;

    /**
     * 前往上车点数量
     */
    private Long pickNumber;

    /**
     * 到达上车点数量
     */
    private Long pickStartNumber;

    /**
     * 行程中数量
     */
    private Long ingNumber;

    /**
     * 已完成数量
     */
    private Long finishNumber;

    /**
     * 已取消数量
     */
    private Long cancelNumber;

    /**
     * 未支付取消数量
     */
    private Long unpayCancelNumber;

    /**
     * 已支付取消数量
     */
    private Long payCancelNumber;

    /**
     * 完单汇总
     */
    private Long finishSummary;

    /**
     * 接完率
     */
    private BigDecimal receiveFinishRate;

    /**
     * 完单金额
     */
    private Long finishAmount;

    /**
     * 支付金额
     */
    private Long payAmount;

    /**
     * 支付订单数
     */
    private Long payNumber;

    /**
     * 客诉订单数
     */
    private Long complainNumber;

    /**
     * 客诉金额
     */
    private Long complainAmount;

    /**
     * 新增乘客注册数
     */
    private Long passengerRegisterNumber;

    /**
     * 乘客访问数量
     */
    private Long passengerVisitNumber;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
