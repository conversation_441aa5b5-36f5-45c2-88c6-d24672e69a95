package com.feidi.xx.cross.order.domain.vo.driver;

import com.feidi.xx.common.core.enums.*;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.cross.common.enums.operate.ProductCodeEnum;
import com.feidi.xx.cross.common.enums.order.*;
import com.feidi.xx.cross.order.domain.vo.OrdDriverVo;
import com.feidi.xx.cross.order.domain.vo.OrdPositionVo;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 司机端订单详情vo
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
@Data
public class OrdOrderDrvDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 昵称 尾号**99
     */
    private String nickname;

    /**
     * 标签，逗号分开
     */
    private String labels;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 乘客电话
     */
    private String phone;

    /**
     * 司机收益-展示金额
     */
    private Long driverProfit;

    /**
     * 距离起点距离 单位：米
     */
    private Double startDistance;

    /**
     * 距离终点距离 单位：米
     */
    private Double endDistance;

    /**
     * 起点纬度
     */
    private Double startLatitude;

    /**
     * 起点经度
     */
    private Double startLongitude;

    /**
     * 终点纬度
     */
    private Double endLatitude;

    /**
     * 终点经度
     */
    private Double endLongitude;

    /**
     * 第三方订单状态
     */
    private String thirdStatus;

    /**
     * 乘客数量
     */
    private Integer passengerNum;

    /**
     * 乘客备注
     */
    private String passengerRemark;

    /**
     * 预计出发时间
     */
    private Date earliestTime;

    /**
     * 预计最晚出发时间
     */
    private Date latestTime;

    /**
     * 地市
     */
    private String startCity;

    /**
     * 区
     */
    private String startDistrict;

    /**
     * 详细地址
     */
    private String startAddr;

    /**
     * 地市
     */
    private String endCity;

    /**
     * 区
     */
    private String endDistrict;

    /**
     * 详细地址
     */
    private String endAddr;

    /**
     * 紧急呼叫电话
     */
    private String emergencyCall;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 按钮状态 {@link StatusEnum}
     */
    private String buttonStatus;

    /**
     * 平台订单号
     */
    private String platformNo;

    /**
     * 取消时间
     */
    private Date cancelTime;

    /**
     * 到达乘客起点时间
     */
    private Date arrivalTime;

    /**
     * 未读消息数
     */
    private Integer unreadNum;

    private Long platformId;

    /**
     * 乘客ID
     */
    private Long passengerId;

    private Long driverId;

    private Long agentId;

    private Long parentId;

    private Long orderPrice;

    /**
     * 司机佣金比例
     */
    private BigDecimal driverRate;

    /**
     * 代理商佣金比例 注意是BigDecimal类型
     */
    private BigDecimal agentRate;

    /**
     * 父代理商佣金比例 注意是BigDecimal类型
     */
    private BigDecimal parentAgentRate;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 出发城市
     */
    private String startCityCode;

    /**
     * 目的地城市
     */
    private String endCityCode;

    /**
     * 线路ID
     */
    private Long lineId;

    /**
     * 线路名称
     */
    private String lineName;

    /**
     * 代理商
     */
    private String agentName;

    /**
     * 上级代理商id
     */
    private String parentAgentName;


    /**
     * 产品类型[ProductTypeEnum]
     */
    @Enum2Text(enumClass = ProductCodeEnum.class)
    private String productCode;

    /**
     * 高速类型[HighwayTypeEnum]
     */
    @Enum2Text(enumClass = HighwayTypeEnum.class, field = "showText")
    private String highwayType;

    /**
     * 乘客真实手机号
     */
    private String passengerPhone;

    /**
     * AXB
     */
    private String virtualPhone;

    /**
     * AXN
     */
    private String virtualDispatch;

    /**
     * 订单状态[OrderStatusEnum]
     */
    @Enum2Text(enumClass = OrderStatusEnum.class, fullName = "statusText", field = "showTextDrv")
    private String status;

    /**
     * 返利状态[RebateStatusEnum]
     */
    @Enum2Text(enumClass = RebateStatusEnum.class)
    private String rebateStatus;

    /**
     * 返利时间
     */
    private Date rebateTime;

    /**
     * 支付状态[PaymentStatusEnum]
     */
    @Enum2Text(enumClass = PaymentStatusEnum.class, field = "showTextDrv")
    private String payStatus;

    /**
     * 支付方式[PaymentTypeEnum]
     */
    @Enum2Text(enumClass = PaymentTypeEnum.class, fullName = "payTypeText")
    private String payMode;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 支付单号
     */
    private String payNo;

    /**
     * 最大等待时长
     */
    private Integer maxWaitDuration;
    /**
     * 接单时间
     */
    private Date receiveTime;

    /**
     * 接驾时间
     */
    private Date pickTime;

    /**
     * 行程开始时间
     */
    private Date tripStartTime;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 投保状态[InsureStatusEnum]
     */
    @Enum2Text(enumClass = InsureStatusEnum.class)
    private String insureStatus;

    /**
     * 保险单号
     */
    private String insureNo;

    /**
     * 里程
     */
    private Integer mileage;

    /**
     * 预计时长
     */
    private Integer expectDuration;

    /**
     * 取消人类型[UserTypeEnum]
     */
    private String cancelUserType;

    /**
     * 取消类型[CancelTypeEnum]
     */
    private String cancelType;

    /**
     * 是否调度[IsYesEnum]
     */
    private String dispatch;

    /**
     * 调度时间
     */
    private Date dispatchTime;

    /**
     * 调度类型[DispatchTypeEnum]
     */
    @Enum2Text(enumClass = DispatchTypeEnum.class)
    private String dispatchType;

    /**
     * 调度状态[SuccessFailEnum]
     */
    @Enum2Text(enumClass = SuccessFailEnum.class, fullName = "dispatchStatusText")
    private String dispatchStatus;

    /**
     * 是否预约[IsYesEnum]
     */
    private String due;

    /**
     * 是否被过滤[IsYesEnum]
     */
    private String filtered;

    /**
     * 是否客诉[IsYesEnum]
     */
    private String complain;

    /**
     * 标签（JSON）
     */
    private String label;

    /**
     * 邀请的代理商id
     */
    private Long inviteAgentId;

    /**
     * 邀请的司机id
     */
    private Long inviteDriverId;

    /**
     * 来源[SourceEnum]
     */
    @Enum2Text(enumClass = SourceEnum.class)
    private String source;

    /**
     * 下单类型[CreateModelEnum]
     */
    @Enum2Text(enumClass = CreateModelEnum.class)
    private String createModel;

    /**
     * 是否展示 [IsYesEnum]
     */
    private String showed;

    /**
     * 父代理商收益
     */
    private Long parentAgentProfit;

    /**
     * 代理商收益
     */
    private Long agentProfit;

    /**
     *  接单司机
     */
    private OrdDriverVo driverVo;

    /**
     *  起点位置
     */
    private OrdPositionVo startPositionVo;

    /**
     *  终点位置
     */
    private OrdPositionVo endPositionVo;

    /**
     * 司机邀请有奖收益
     */
    private Long driverInviteReward;

    /**
     * 发单时间
     */
    private Date createTime;

    /**
     * 距离 单位：米
     */
    private Double distance;

    /**
     * 优惠券额度
     */
    private Long couponGrantQuota;

    /**
     * 转卖后司机接单金额
     */
    private Long resellDriverPrice;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 转卖司机id
     */
    private Long resellDriverId;
}
