package com.feidi.xx.cross.order.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.order.domain.bo.OrdDriverEvaluationBo;
import com.feidi.xx.cross.order.domain.vo.OrdDriverEvaluationVo;
import com.feidi.xx.cross.order.service.IOrdDriverEvaluationService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 后台 - 司机-行程评价
 * 前端访问路由地址为:/settle/driverEvaluation
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/driverEvaluation")
public class OrdDriverEvaluationController extends BaseController {

    private final IOrdDriverEvaluationService ordDriverEvaluationService;

    /**
     * 查询司机-行程评价列表
     */
    @SaCheckPermission("order:driverEvaluation:list")
    @GetMapping("/list")
    public TableDataInfo<OrdDriverEvaluationVo> list(OrdDriverEvaluationBo bo, PageQuery pageQuery) {
        return ordDriverEvaluationService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取司机-行程评价详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("order:driverEvaluation:query")
    @GetMapping("/{id}")
    public R<OrdDriverEvaluationVo> getInfo(@NotNull(message = "主键不能为空")
                                            @PathVariable Long id) {
        return R.ok(ordDriverEvaluationService.queryById(id));
    }

    /**
     * 删除司机-行程评价
     *
     * @param ids 主键串
     */
    @SaCheckPermission("order:driverEvaluation:remove")
    @Log(title = "司机-行程评价", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(ordDriverEvaluationService.deleteWithValidByIds(List.of(ids), true));
    }
}
