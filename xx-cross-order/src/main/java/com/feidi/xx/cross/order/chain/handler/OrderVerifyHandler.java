package com.feidi.xx.cross.order.chain.handler;

import cn.hutool.core.util.StrUtil;
import com.feidi.xx.common.core.enums.PaymentStatusEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.log.enums.PlatformEnum;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.annotations.HandlerScope;
import com.feidi.xx.cross.common.cache.order.enums.OrdCacheKeyEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.order.chain.base.OrderBaseChainContext;
import com.feidi.xx.cross.order.chain.base.OrderBaseChainResult;
import com.feidi.xx.cross.order.chain.cancel.OrderCancelChainContext;
import com.feidi.xx.cross.order.chain.common.AbstractChainHandler;
import com.feidi.xx.cross.order.chain.operate.OrderOperateChainContext;
import com.feidi.xx.cross.order.chain.payment.OrderPaymentChainContext;
import com.feidi.xx.cross.order.chain.place.OrderPlaceChainContext;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.helper.UserTypeHelper;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;

/**
 * 订单校验处理器，简单非空校验请放在前置处理器
 * 注意需要是多例模式
 */
@Slf4j
@Component
@HandlerScope
@RequiredArgsConstructor
public class OrderVerifyHandler<T extends OrderBaseChainContext, R extends OrderBaseChainResult> extends AbstractChainHandler<T, R> {

    private final OrdOrderMapper baseMapper;

    @Override
    public void handle(T context) {
        if (context instanceof OrderPlaceChainContext placeContext) {
            String dupKey = OrdCacheKeyEnum.ORD_DUPLICATE_KEY.create(placeContext.getOrderKey());
            if (RedisUtils.hasKey(dupKey)) {
                throw new ServiceException("请勿重复下单");
            }
            UserTypeHelper.member(() -> {
                List<OrdOrder> orders = baseMapper.listPsgIngOrder(LoginHelper.getUserId());
                Assert.isTrue(orders.size() <= 3, "当前已有多笔进行中的订单，请完成后再下单");
            });
        } else if (context instanceof OrderOperateChainContext operateContext) {
            OrdOrder order = baseMapper.selectById(operateContext.getOrderId());
            Assert.notNull(order, "订单不存在");

            validStatus(order.getStatus());
            Assert.isTrue(Objects.equals(order.getPayStatus(), PaymentStatusEnum.SUCCESS.getCode()), "订单未支付、请联系乘客或客服");
            Assert.isTrue(Integer.parseInt(operateContext.getStatus()) > Integer.parseInt(order.getStatus()), "订单状态无法操作，请刷新页面重试");

            operateContext.setOrder(order);
        } else if (context instanceof OrderCancelChainContext cancelContext) {
            OrdOrder order = baseMapper.selectById(cancelContext.getOrderId());
            Assert.notNull(order, "订单不存在");

            if (order.getPlatformCode().equals(PlatformEnum.SELF.getCode())) {
                if (OrderStatusEnum.getCancelStatus().contains(order.getStatus())) {
                    if (!UserTypeEnum.SYS_USER.equals(LoginHelper.getUserType())) {
                        throw new ServiceException("订单已在行程中，无法取消，请联系客服处理");
                    }
                }
            }

            cancelContext.setOrder(order);
        } else if (context instanceof OrderPaymentChainContext paymentContext) {
            OrdOrder order = baseMapper.selectById(paymentContext.getOrderId());
            Assert.notNull(order, "订单不存在");

            Assert.isTrue(order.getPlatformCode().equals(PlatformEnum.SELF.getCode()), "订单无法支付");
            Assert.isTrue(!OrderStatusEnum.getOverCodes().contains(order.getStatus()), "订单已结束，无法支付");
            Assert.isTrue(PaymentStatusEnum.getPayableCodes().contains(order.getPayStatus()), "订单支付状态异常，无法支付");
            // 补充数据
            paymentContext.setOrderNo(order.getOrderNo());
            paymentContext.setOrderPrice(order.getOrderPrice());
            paymentContext.setPayPrice(order.getPayPrice());
        }
    }

    private void validStatus(String orderStatus) {
        String msg = StrUtil.format("状态错误，无法操作", OrderStatusEnum.getInfoByCode(orderStatus));
        Assert.isTrue(!OrderStatusEnum.getFinishedStatus().contains(orderStatus), msg);
    }
}
