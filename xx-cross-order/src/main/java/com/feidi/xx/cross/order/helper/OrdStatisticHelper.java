package com.feidi.xx.cross.order.helper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.PaymentStatusEnum;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.OrdStatistic;
import com.feidi.xx.cross.order.domain.vo.OrdStatisticVo;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单统计辅助类
 *
 * <AUTHOR>
 * @date 2025/7/22
 */
@Slf4j
@Component
@AllArgsConstructor
public class OrdStatisticHelper {

    private final OrdOrderMapper ordOrderMapper;
    @DubboReference
    private final RemotePassengerService remotePassengerService;

    public OrdStatistic bindOrderStatistic(OrdStatistic ordStatistic,String platformCode, String startTime, String endTime) {
        LambdaQueryWrapper<OrdOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(platformCode), OrdOrder::getPlatformCode, platformCode)
                .between(OrdOrder::getEarliestTime, startTime, endTime);
        List<OrdOrder> orderList = ordOrderMapper.selectList(lqw);
        ordStatistic = this.statisticOrder(orderList, ordStatistic);

        // 发单量（下单数）
        lqw.clear();
        lqw.eq(StringUtils.isNotBlank(platformCode), OrdOrder::getPlatformCode, platformCode)
                .between(OrdOrder::getCreateTime, startTime, endTime);
        Long volumeNumber = ordOrderMapper.selectCount(lqw);
        ordStatistic.setVolumeNumber(volumeNumber);
        ordStatistic.setPlaceNumber(volumeNumber);

        // 新增乘客注册人数（选择非自营平台时不展示，选择全部时展示）
        ordStatistic.setPassengerRegisterNumber(0L);
        if (platformCode == null || Objects.equals(platformCode, PlatformCodeEnum.SELF.getCode())) {
            List<RemotePassengerVo> passengers = remotePassengerService.getByCreateTime(startTime, endTime);
            ordStatistic.setPassengerRegisterNumber((long) (CollUtils.isNotEmpty(passengers) ? passengers.size() : 0));
        }

        return ordStatistic;
    }

    /**
     * 统计订单总发单量
     *
     * @param startTime 统计开始时间
     * @param endTime 统计结束时间
     * @return
     */
    public List<OrdStatistic> statisticVolume(String startTime, String endTime, String platformCode, Long agentId) {
        List<OrdStatistic> ordStatistics = new ArrayList<>();
        LambdaQueryWrapper<OrdOrder> lqw = new LambdaQueryWrapper<>();
        lqw.between(OrdOrder::getCreateTime, startTime, endTime)
                .eq(StringUtils.isNotBlank(platformCode), OrdOrder::getPlatformCode, platformCode)
                .eq(agentId != null, OrdOrder::getAgentId, agentId);
        List<OrdOrder> orders = ordOrderMapper.selectList(lqw);
        if (CollUtils.isNotEmpty(orders)) {
            Map<String, List<OrdOrder>> platformCode2ListMap = orders.stream().collect(Collectors.groupingBy(OrdOrder::getPlatformCode));
            platformCode2ListMap.forEach((platform, orderList) -> {

                Map<Long, List<OrdOrder>> agentId2ListMap = orderList.stream()
                        .filter(e -> e.getAgentId() != null && e.getAgentId() != 0)
                        .collect(Collectors.groupingBy(OrdOrder::getAgentId));
                agentId2ListMap.forEach((agent, agentOrders) -> {
                    List<OrdOrder> noLineOrders = agentOrders.stream().filter(e -> e.getLineId() == null || e.getLineId() == 0).collect(Collectors.toList());
                    Map<Long, List<OrdOrder>> driverId2ListMap = agentOrders.stream()
                            .filter(e -> e.getLineId() != null && e.getLineId() != 0)
                            .collect(Collectors.groupingBy(OrdOrder::getLineId));
                    driverId2ListMap.forEach((lineId, lineOrders) -> {
                        OrdStatistic ordStatistic = bindDefaultValue();
                        ordStatistic.setPlatformCode(platform);
                        ordStatistic.setAgentId(agent);
                        ordStatistic.setLineId(lineId);
                        ordStatistic.setVolumeNumber((long) (CollUtils.isNotEmpty(lineOrders) ? lineOrders.size() : 0));
                        ordStatistics.add(ordStatistic);
                    });

                    if (CollUtils.isNotEmpty(noLineOrders)) {
                        OrdStatistic ordStatistic = bindDefaultValue();
                        ordStatistic.setPlatformCode(platform);
                        ordStatistic.setAgentId(agent);
                        ordStatistic.setLineId(0L);
                        ordStatistic.setVolumeNumber((long) (CollUtils.isNotEmpty(noLineOrders) ? noLineOrders.size() : 0));
                        ordStatistics.add(ordStatistic);
                    }
                });
            });
        }

        return ordStatistics;
    }

    /**
     * 统计订单相关数据项
     *
     * @param orders       订单集合
     * @param ordStatistic 订单统计对象
     */
    public OrdStatistic statisticOrder(List<OrdOrder> orders, OrdStatistic ordStatistic) {
        if (ordStatistic == null) {
            ordStatistic = bindDefaultValue();
        }
        if (CollUtils.isNotEmpty(orders)) {
            ordStatistic.setDepartNumber((long) orders.size());

            // 接单数
            long acceptNumber = orders.stream().filter(e -> e.getDriverId() != null && e.getDriverId() > 0).count();
            ordStatistic.setAcceptNumber(acceptNumber);

            // 待接单数量
            long createNumber = orders.stream().filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.CREATE.getCode())).count();
            ordStatistic.setCreateNumber(createNumber);

            // 待出发数量
            long receiveNumber = orders.stream().filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.RECEIVE.getCode())).count();
            ordStatistic.setReceiveNumber(receiveNumber);

            // 前往上车点数量
            long pickNumber = orders.stream().filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.PICK.getCode())).count();
            ordStatistic.setPickNumber(pickNumber);

            // 到达上车点数量
            long pickStartNumber = orders.stream().filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.PICK_START.getCode())).count();
            ordStatistic.setPickStartNumber(pickStartNumber);

            // 行程中数量
            long ingNumber = orders.stream().filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.ING.getCode())).count();
            ordStatistic.setIngNumber(ingNumber);

            // 已完成订单
            List<OrdOrder> finishOrders = orders.stream().filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.FINISH.getCode())).toList();
            ordStatistic.setFinishNumber(0L);
            ordStatistic.setFinishAmount(0L);
            if (CollUtils.isNotEmpty(finishOrders)) {
                // 完单数量
                ordStatistic.setFinishNumber((long) finishOrders.size());
                // 完单金额
                ordStatistic.setFinishAmount(finishOrders.stream().mapToLong(OrdOrder::getPayPrice).sum());
            }

            // 已取消数量
            long cancelNumber = orders.stream().filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.CANCEL.getCode())).count();
            ordStatistic.setCancelNumber(cancelNumber);

            // 未支付取消数量
            long unpayCancelNumber = orders.stream().filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.CANCEL.getCode())
                    && (Objects.equals(e.getPayStatus(), PaymentStatusEnum.NO.getCode()) || Objects.equals(e.getPayStatus(), PaymentStatusEnum.ING.getCode()))).count();
            ordStatistic.setUnpayCancelNumber(unpayCancelNumber);

            // 已支付取消数量
            long payCancelNumber = orders.stream().filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.CANCEL.getCode())
                    && Objects.equals(e.getPayStatus(), PaymentStatusEnum.SUCCESS.getCode())).count();
            ordStatistic.setPayCancelNumber(payCancelNumber);

            // 完单汇总（已完成+行程中+到达上车点+前往上车点）
            ordStatistic.setFinishSummary(ordStatistic.getFinishNumber() + ordStatistic.getIngNumber() + ordStatistic.getPickStartNumber() + ordStatistic.getPickNumber());

            // 接完率（完单汇总 / 接单数）
            if (acceptNumber != 0) {
                ordStatistic.setReceiveFinishRate(this.divide(ordStatistic.getFinishSummary(), acceptNumber));
            }
            // 支付金额
            ordStatistic.setPayAmount(orders.stream()
                    .filter(e -> Objects.equals(e.getPayStatus(), PaymentStatusEnum.SUCCESS.getCode()))
                    .mapToLong(OrdOrder::getPayPrice).sum());

            // 支付订单数
            long payNumber = orders.stream().filter(e -> Objects.equals(e.getPayStatus(), PaymentStatusEnum.SUCCESS.getCode())).count();
            ordStatistic.setPayNumber(payNumber);

            // 客诉订单数
            long complainNumber = orders.stream().filter(e -> Objects.equals(e.getComplain(), IsYesEnum.YES.getCode())).count();
            ordStatistic.setComplainNumber(complainNumber);

            // 客诉金额
            ordStatistic.setComplainAmount(orders.stream().filter(e -> Objects.equals(e.getComplain(), IsYesEnum.YES.getCode())).mapToLong(OrdOrder::getPayPrice).sum());
        }

        return ordStatistic;
    }

    /**
     * 计算日、周环比
     *
     * @param ordStatistic 当日订单统计数据
     * @param dayOrderStatistic 昨日订单统计数据
     * @param weekOrderStatistic 上周订单统计数据
     */
    public OrdStatisticVo calculateDayAndWeekRate(OrdStatistic ordStatistic, OrdStatistic dayOrderStatistic, OrdStatistic weekOrderStatistic) {
        OrdStatisticVo ordStatisticVo = BeanUtils.copyProperties(ordStatistic, OrdStatisticVo.class);
        // 下单量日环比
        ordStatisticVo.setVolumeNumberDayRate(this.divide(ordStatistic.getPlaceNumber() - dayOrderStatistic.getPlaceNumber(), dayOrderStatistic.getPlaceNumber()));
        // 下单量周环比
        ordStatisticVo.setVolumeNumberWeekRate(this.divide(ordStatistic.getPlaceNumber() - weekOrderStatistic.getPlaceNumber(), weekOrderStatistic.getPlaceNumber()));
        // 完单数日环比
        ordStatisticVo.setFinishNumberDayRate(this.divide(ordStatistic.getFinishNumber() - dayOrderStatistic.getFinishNumber(), dayOrderStatistic.getFinishNumber()));
        // 完单数周环比
        ordStatisticVo.setFinishNumberWeekRate(this.divide(ordStatistic.getFinishNumber() - weekOrderStatistic.getFinishNumber(), weekOrderStatistic.getFinishNumber()));
        // 完单金额日环比
        ordStatisticVo.setFinishAmountDayRate(this.divide(ordStatistic.getFinishAmount() - dayOrderStatistic.getFinishAmount(), dayOrderStatistic.getFinishAmount()));
        // 完单金额周环比
        ordStatisticVo.setFinishAmountWeekRate(this.divide(ordStatistic.getFinishAmount() - weekOrderStatistic.getFinishAmount(), weekOrderStatistic.getFinishAmount()));
        // 支付金额日环比
        ordStatisticVo.setPayAmountDayRate(this.divide(ordStatistic.getPayAmount() - dayOrderStatistic.getPayAmount(), dayOrderStatistic.getPayAmount()));
        // 支付金额周环比
        ordStatisticVo.setPayAmountWeekRate(this.divide(ordStatistic.getPayAmount() - weekOrderStatistic.getPayAmount(), weekOrderStatistic.getPayAmount()));
        // 支付订单数日环比
        ordStatisticVo.setPayNumberDayRate(this.divide(ordStatistic.getPayNumber() - dayOrderStatistic.getPayNumber(), dayOrderStatistic.getPayNumber()));
        // 支付订单数周环比
        ordStatisticVo.setPayNumberWeekRate(this.divide(ordStatistic.getPayNumber() - weekOrderStatistic.getPayNumber(), weekOrderStatistic.getPayNumber()));
        // 取消订单数日环比
        ordStatisticVo.setCancelNumberDayRate(this.divide(ordStatistic.getCancelNumber() - dayOrderStatistic.getCancelNumber(), dayOrderStatistic.getCancelNumber()));
        // 取消订单数周环比
        ordStatisticVo.setCancelNumberWeekRate(this.divide(ordStatistic.getCancelNumber() - weekOrderStatistic.getCancelNumber(), weekOrderStatistic.getCancelNumber()));
        // 新增乘客注册数日环比
        ordStatisticVo.setRegisterNumberDayRate(this.divide(ordStatistic.getPassengerRegisterNumber() - dayOrderStatistic.getPassengerRegisterNumber(), dayOrderStatistic.getPassengerRegisterNumber()));
        // 新增乘客注册数周环比
        ordStatisticVo.setRegisterNumberWeekRate(this.divide(ordStatistic.getPassengerRegisterNumber() - weekOrderStatistic.getPassengerRegisterNumber(), weekOrderStatistic.getPassengerRegisterNumber()));
        // 接完率日环比日环比
        ordStatisticVo.setReceiveFinishDayRate(this.divide(ordStatistic.getReceiveFinishRate().subtract(ordStatistic.getReceiveFinishRate()), dayOrderStatistic.getReceiveFinishRate()));
        // 接完率周环比
        ordStatisticVo.setReceiveFinishWeekRate(this.divide(ordStatistic.getReceiveFinishRate().subtract(weekOrderStatistic.getReceiveFinishRate()), weekOrderStatistic.getReceiveFinishRate()));

        return ordStatisticVo;
    }

    /**
     * 除法
     *
     * @param number1
     * @param number2
     * @return
     */
    public BigDecimal divide(long number1, long number2) {
        if (number2 == 0) {
            return new BigDecimal("0.00");
        }
        return new BigDecimal(number1).multiply(new BigDecimal(100)).divide(new BigDecimal(number2), 2, RoundingMode.HALF_UP);
    }

    public BigDecimal divide(BigDecimal number1, BigDecimal number2) {
        if (number2.compareTo(new BigDecimal("0.00")) == 0) {
            return new BigDecimal("0.00");
        }
        return number1.multiply(new BigDecimal(100)).divide(number2, 2, RoundingMode.HALF_UP);
    }

    public OrdStatistic bindDefaultValue() {
        OrdStatistic ordStatistic = new OrdStatistic();
        ordStatistic.setAgentId(0L);
        ordStatistic.setLineId(0L);
        ordStatistic.setVolumeNumber(0L);
        ordStatistic.setDepartNumber(0L);
        ordStatistic.setAcceptNumber(0L);
        ordStatistic.setCreateNumber(0L);
        ordStatistic.setReceiveNumber(0L);
        ordStatistic.setPickNumber(0L);
        ordStatistic.setPickStartNumber(0L);
        ordStatistic.setIngNumber(0L);
        ordStatistic.setFinishNumber(0L);
        ordStatistic.setCancelNumber(0L);
        ordStatistic.setUnpayCancelNumber(0L);
        ordStatistic.setPayCancelNumber(0L);
        ordStatistic.setFinishSummary(0L);
        ordStatistic.setFinishAmount(0L);
        ordStatistic.setPayAmount(0L);
        ordStatistic.setPayNumber(0L);
        ordStatistic.setComplainNumber(0L);
        ordStatistic.setPassengerRegisterNumber(0L);
        ordStatistic.setPassengerVisitNumber(0L);
        ordStatistic.setReceiveFinishRate(new BigDecimal(0));
        return ordStatistic;
    }
}
