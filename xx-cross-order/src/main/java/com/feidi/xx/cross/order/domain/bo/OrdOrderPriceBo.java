package com.feidi.xx.cross.order.domain.bo;

import com.feidi.xx.cross.order.domain.OrdOrder;
import lombok.Data;

import java.util.Date;

/**
 * 订单扣款处理BO
 */
@Data
public class OrdOrderPriceBo {

    private String platformCode;

    private Long driverId;

    private Long agentId;

    private Long parentId;

    private Long orderPrice;

    private Long driverProfit;

    private Long agentProfit;

    private Long parentAgentProfit;

    /**
     * 预计出发时间
     */
    private Date earliestTime;

    public OrdOrderPriceBo(OrdOrder order) {
        this.platformCode = order.getPlatformCode();
        this.driverId = order.getDriverId();
        this.agentId = order.getAgentId();

        this.orderPrice = order.getOrderPrice();
        // TODO-NEW 后面再改
/*        this.parentId = order.getParentId();
        this.driverProfit = order.getDriverProfit();
        this.agentProfit = order.getAgentProfit();
        this.parentAgentProfit = order.getParentAgentProfit();*/

        this.earliestTime = order.getEarliestTime();
    }
}
