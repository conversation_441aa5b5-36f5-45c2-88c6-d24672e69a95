package com.feidi.xx.cross.order.controller.driver;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.core.validate.QueryGroup;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.log.enums.PlatformEnum;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.common.enums.order.CreateModelEnum;
import com.feidi.xx.cross.common.enums.order.DispatchTypeEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.order.domain.bo.OrdOrderBo;
import com.feidi.xx.cross.order.domain.bo.driver.OrdOrderDrvQueryBo;
import com.feidi.xx.cross.order.domain.handle.bo.*;
import com.feidi.xx.cross.order.domain.vo.driver.OrdOrderDrvDetailVo;
import com.feidi.xx.cross.order.domain.vo.driver.OrdOrderDrvIndexVo;
import com.feidi.xx.cross.order.domain.vo.driver.OrdOrderDrvResellVo;
import com.feidi.xx.cross.order.service.IOrdOrderDrvService;
import com.feidi.xx.cross.order.service.IOrdOrderMbrService;
import com.feidi.xx.cross.order.service.IOrdOrderProcessService;
import com.feidi.xx.cross.order.service.IOrdOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 司机 - 订单
 * 前端访问路由地址为:/cross/order
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.DRIVER_ROUTE_PREFIX + "/order")
public class DrvOrderController extends BaseController {

    private final IOrdOrderService orderService;
    private final IOrdOrderDrvService orderDrvService;
    private final IOrdOrderMbrService orderMbrService;
    private final IOrdOrderProcessService orderProcessService;

    /**
     * 订单首页统计
     */
    @GetMapping("/index")
    public R<OrdOrderDrvIndexVo> getIndex() {
        return R.ok(orderDrvService.getIndex());
    }

    /**
     * 订单转卖统计
     *
     * @return
     */
    @GetMapping("/resell/index")
    public R<OrdOrderDrvResellVo> getResellIndex() {
        return R.ok(orderDrvService.getResellIndex());
    }

    /**
     * 新增订单
     */
    @PostMapping("/newNum")
    public R<Long> newNum(@RequestBody @Validated OrdOrderDrvQueryBo queryBo) {
        queryBo.setDriverId(LoginHelper.getUserId());
        queryBo.setIsPool(true);
        queryBo.setIsFiltered(IsYesEnum.NO.getCode());
        return R.ok(orderDrvService.newNum(queryBo));
    }

    /**
     * 订单池
     */
    @Enum2TextAspect
    @PostMapping("/pool")
    public R<TableDataInfo<OrdOrderDrvDetailVo>> pool(@RequestBody @Validated OrdOrderDrvQueryBo queryBo) {
        queryBo.setLastTime(null);
        queryBo.setDriverId(LoginHelper.getUserId());
        queryBo.setAgentId(LoginHelper.getAgentId());
        queryBo.setIsPool(true);
        queryBo.setIsFiltered(IsYesEnum.NO.getCode());
        return R.ok(orderDrvService.pool(queryBo));
    }

    /**
     * 查询订单列表
     */
    @Enum2TextAspect
    @PostMapping("/list")
    public TableDataInfo<OrdOrderDrvDetailVo> list(@RequestBody OrdOrderDrvQueryBo queryBo) {
        queryBo.setDriverId(LoginHelper.getUserId());
        queryBo.setAgentId(LoginHelper.getAgentId());
        queryBo.setIsPool(false);
        if (Objects.equals(queryBo.getResell(), 0)) {
            queryBo.setResellDriverId(LoginHelper.getUserId());
        }
        return orderDrvService.queryList(queryBo);
    }

    /**
     * 获取订单详细信息
     */
    @Enum2TextAspect
    @PostMapping("/detail")
    public R<Object> queryDetail(@RequestBody @Validated(QueryGroup.class) OrdOrderDrvQueryBo queryBo) {
        queryBo.setDriverId(LoginHelper.getUserId());
        queryBo.setAgentId(LoginHelper.getAgentId());
        return orderDrvService.queryDetail(queryBo);
    }

    /**
     * 抢单
     */
    @Log(title = "司机-订单抢单", businessType = BusinessType.DISPATCH)
    @RepeatSubmit()
    @PutMapping("/dispatch")
    public R<Void> dispatch(@RequestBody OrdOrderDispatchBo dispatchBo) {
        dispatchBo.setUserId(LoginHelper.getUserId());
        dispatchBo.setUserType(UserTypeEnum.DRIVER_USER.getUserType());
        dispatchBo.setType(DispatchTypeEnum.DRIVER_ROB.getCode());
        dispatchBo.setDriverId(LoginHelper.getUserId());
        dispatchBo.setRemark("司机【" + LoginHelper.getUsername() + "】手动抢单");
        dispatchBo.setTimeStamp(DateUtils.getUnixTimeStamps());
        Boolean flag = orderProcessService.dispatchOrder(dispatchBo);
        return flag ? R.ok() : R.fail();
    }

    /**
     * 订单操作
     * 状态 【3司机出发接乘客；4达到出发地】
     */
    @Log(title = "司机-订单操作", businessType = BusinessType.OTHER)
    @RepeatSubmit
    @PutMapping("/handel")
    public R<Void> handel(@RequestBody OrdOrderHandleBo handleBo) {
        handleBo.setUserId(LoginHelper.getUserId());
        handleBo.setUserType(UserTypeEnum.DRIVER_USER.getUserType());
        handleBo.setTimeStamp(DateUtils.getUnixTimeStamps());
        return toAjax(orderProcessService.handleOrder(handleBo));
    }

    /**
     * 行程开始
     */
    @Log(title = "司机-订单行程开始", businessType = BusinessType.TRIP_START)
    @RepeatSubmit
    @PutMapping("/tripStart")
    public R<Void> tripStart(@RequestBody OrdOrderHandleBo handleBo) {
        handleBo.setStatus(OrderStatusEnum.ING.getCode());
        handleBo.setUserId(LoginHelper.getUserId());
        handleBo.setUserType(UserTypeEnum.DRIVER_USER.getUserType());
        handleBo.setTimeStamp(DateUtils.getUnixTimeStamps());
        return toAjax(orderProcessService.tripStart(handleBo));
    }


    /**
     * 行程结束
     */
    @Log(title = "司机-订单行程结束", businessType = BusinessType.TRIP_END)
    @PutMapping("/tripEnd")
    public R<Void> tripEnd(@RequestBody OrdOrderHandleBo handleBo) {
        handleBo.setStatus(OrderStatusEnum.FINISH.getCode());
        handleBo.setUserId(LoginHelper.getUserId());
        handleBo.setUserType(UserTypeEnum.DRIVER_USER.getUserType());
        handleBo.setTimeStamp(DateUtils.getUnixTimeStamps());
        handleBo.setRemark("订单完成");
        return toAjax(orderProcessService.tripEnd(handleBo));
    }

    /**
     * 验证手机尾号
     */
    @Log(title = "司机-订单验证手机尾号", businessType = BusinessType.OTHER)
    @PostMapping("/checkPhoneEnd")
    public R<String> checkPhoneEnd(@RequestBody OrdOrderHandleBo handleBo) {
        handleBo.setUserId(LoginHelper.getUserId());
        return orderDrvService.checkPhoneEnd(handleBo);
    }

    /**
     * 司机联系乘客
     */
    @Log(title = "司机-司机联系乘客", businessType = BusinessType.OTHER)
    @PostMapping("/make/call")
    public R<Void> makeCall(@RequestBody OrdOrderHandleBo handleBo) {
        handleBo.setUserId(LoginHelper.getUserId());
        handleBo.setUserType(UserTypeEnum.DRIVER_USER.getUserType());
        handleBo.setRemark("司机【" + LoginHelper.getUsername() + "】联系乘客");
        return toAjax(orderDrvService.makeCall(handleBo));
    }


    /**
     * 支付
     */
    @Log(title = "司机-订单支付", businessType = BusinessType.PAYMENT)
//    @RepeatSubmit()
    @PostMapping("/payment")
    public R<Object> payment(@Validated @RequestBody OrdOrderPaymentBo bo) {
        bo.setPlatformCode(PlatformEnum.SELF.getCode());
        return R.ok(orderMbrService.payment(bo));
    }


    /**
     * 代待客下单
     *
     * @param bo
     * @return
     */
    @Log(title = "司机-代客下单", businessType = BusinessType.PAYMENT)
    @PostMapping("/place")
    public R<Long> place(@Validated @RequestBody OrdOrderPlaceBo bo) {
        bo.setCreateModel(CreateModelEnum.DRIVER_ORDER.getCode());
        bo.setDriverId(LoginHelper.getUserId());
        bo.setAgentId(LoginHelper.getAgentId());
        return R.ok(orderMbrService.place(bo));
    }

    /**
     * 刷新支付
     */
    @Log(title = "司机订单")
    @RepeatSubmit()
    @PostMapping("/payment/refresh")
    public R<Void> refreshPayment(Long orderId) {
        orderMbrService.refreshPayment(orderId);
        return R.ok();
    }

    /**
     * 订单转卖
     *
     * @param bo
     * @return
     */
    @RepeatSubmit(message = "操作频繁")
    @Log(title = "订单转卖", businessType = BusinessType.OTHER)
    @PostMapping("/resell")
    public R<Long> resell(@RequestBody OrdOrderPlaceBo bo) {
        bo.setResellDriverId(LoginHelper.getUserId());
        bo.setResellAgentId(LoginHelper.getAgentId());
        bo.setCreateModel(CreateModelEnum.RESELL_ORDER.getCode());
        return R.ok(orderMbrService.resell(bo));
    }

    /**
     * 订单转卖-指派司机接单
     *
     * @param dispatchBo
     * @return
     */
    @Log(title = "订单转卖-指派司机接单", businessType = BusinessType.DISPATCH)
    @RepeatSubmit()
    @PutMapping("/resell/dispatch")
    public R<Void> resellDispatch(@Validated(EditGroup.class) @RequestBody OrdOrderDispatchBo dispatchBo) {
        dispatchBo.setUserId(LoginHelper.getUserId());
        dispatchBo.setUserType(UserTypeEnum.DRIVER_USER.getUserType());
        dispatchBo.setType(DispatchTypeEnum.RESELL_ROB.getCode());
        dispatchBo.setRemark("订单转卖指派司机接单");
        dispatchBo.setTimeStamp(DateUtils.getUnixTimeStamps());
        return toAjax(orderProcessService.dispatchOrder(dispatchBo));
    }

    /**
     * 订单转卖-修改订单
     * @param orderBo
     * @return
     */
    @RepeatSubmit(message = "操作频繁")
    @Log(title = "订单转卖修改", businessType = BusinessType.OTHER)
    @PostMapping("/resell/edit")
    public R<Long> resellEdit(@RequestBody OrdOrderBo orderBo) {
        orderBo.setCreateModel(CreateModelEnum.RESELL_ORDER.getCode());
        return R.ok(orderService.updateByBo(orderBo).getId());
    }

    /**
     * 订单转卖-取消订单
     *
     * @param bo
     * @return
     */
    @Log(title = "订单转卖-取消订单", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PutMapping("/resell/cancel")
    public R<Void> cancel(@Validated(EditGroup.class) @RequestBody OrdOrderCancelBo bo) {
        // 取消人信息
        bo.setUserId(LoginHelper.getUserId());
        bo.setUserType(UserTypeEnum.DRIVER_USER.getUserType());
        bo.setTimeStamp(DateUtils.getUnixTimeStamps());
        return toAjax(orderProcessService.cancelOrder(bo));
    }
}
