
package com.feidi.xx.cross.order.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.feidi.xx.common.idempotent.annotation.NoMQDuplicateConsume;
import com.feidi.xx.common.rocketmq.base.MessageWrapper;
import com.feidi.xx.cross.common.constant.order.OrderRocketMQConstant;
import com.feidi.xx.cross.order.mq.event.MtVirtualPhoneEvent;
import com.feidi.xx.cross.order.service.IOrdOrderProcessService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 美团虚拟电话消息消费者
 *
 * <AUTHOR>
 * @date 2025/3/22
 */
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = OrderRocketMQConstant.XX_ORDER_MT_VIRTUAL_PHONE_TOPIC_KEY,
        consumerGroup = OrderRocketMQConstant.XX_ORDER_MT_VIRTUAL_PHONE_GC_KEY
)
@Slf4j(topic = "OrderConsumer")
public class MtVirtualPhoneConsumer implements RocketMQListener<MessageWrapper<MtVirtualPhoneEvent>> {

    private final IOrdOrderProcessService ordOrderProcessService;

    @NoMQDuplicateConsume(
            keyPrefix = "global:xx-cross-order-mt-virtual-phone:",
            key = "#messageWrapper.keys",
            keyTimeout = 600
    )
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void onMessage(MessageWrapper<MtVirtualPhoneEvent> messageWrapper) {
        MtVirtualPhoneEvent message = messageWrapper.getMessage();
        log.info("[消费者] 美团虚拟电话 - 执行消费逻辑，消息体：{}", JSON.toJSONString(messageWrapper));

        ordOrderProcessService.getVirtualPhone(message.getOrderId(), message.getPlatformNo());
    }
}
