package com.feidi.xx.cross.order.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 自动抢单对象 dis_rob
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dis_rob")
public class DisRob extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 编号
     */
    private String robNo;

    /**
     * 代理ID
     */
    private Long agentId;

    /**
     * 线路ID
     */
    private Long lineId;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 开始省ID
     */
    private Long startProvinceId;

    /**
     * 开始城市ID
     */
    private Long startCityId;

    /**
     * 开始城市编码
     */
    private String startCityCode;

    /**
     * 开始区域ID
     */
    private Long startDistrictId;

    /**
     * 开始区域编码
     */
    private String startAdCode;

    /**
     * 开始地址
     */
    private String startAddress;

    /**
     * 开始经度
     */
    private String startLongitude;

    /**
     * 开始纬度
     */
    private String startLatitude;

    /**
     * 开始半径
     */
    private Long startRadius;

    /**
     * 结束省ID
     */
    private Long endProvinceId;

    /**
     * 结束城市ID
     */
    private Long endCityId;

    /**
     * 结束城市编码
     */
    private String endCityCode;

    /**
     * 结束区域ID
     */
    private Long endDistrictId;

    /**
     * 结束区域编码
     */
    private String endAdCode;

    /**
     * 结束地址
     */
    private String endAddress;

    /**
     * 结束经度
     */
    private String endLongitude;

    /**
     * 结束纬度
     */
    private String endLatitude;

    /**
     * 结束半径
     */
    private Long endRadius;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 排队时间
     */
    private String sortTime;

    /**
     * 等待时间
     */
    private Long maxWaitDuration;

    /**
     * 座位数
     */
    private Integer seat;

    /**
     * 剩余座位数
     */
    private Integer surplusSeat;

    /**
     * 抢单产品编码
     */
    private String robProduct;

    /**
     * 用户类型[UserTypeEnum]
     */
    private String userType;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 状态[StatusEnum]
     */
    private String status;


    /**
     * 抢单类型[RobTypeEnum]
     */
    private String type;

    /**
     * 方案[RobPlanEnum]
     */
    private String plan;

    /**
     * 司机IDS（随机）
     */
    private String driverJson;

    /**
     * 续期天数
     */
    private Long renewal;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
