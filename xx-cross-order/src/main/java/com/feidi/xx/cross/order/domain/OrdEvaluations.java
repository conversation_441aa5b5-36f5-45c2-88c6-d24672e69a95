package com.feidi.xx.cross.order.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.order.domain.bo.OrdOrderCommentBo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

/**
 * 行程评价对象 ord_evaluations
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "ord_evaluations", autoResultMap = true)
public class OrdEvaluations extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 乘客真实手机号
     */
    private String passengerPhone;

    /**
     * 姓名
     */
    private String passengerName;

    /**
     * 评分 (1-5星)
     */
    private Long rating;

    /**
     * 是否匿名。0：不是，1：是
     */
    private Integer isAnonymous;

    /**
     * 评价内容
     */
    private String comment;

    /**
     * 选择的标签。
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> tags;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


    public static OrdEvaluations create(OrdOrderCommentBo bo) {
        OrdEvaluations ordEvaluations = new OrdEvaluations();
        ordEvaluations.setOrderId(bo.getOrderId());
        ordEvaluations.setOrderNo(bo.getOrderNo());
        ordEvaluations.setPassengerId(bo.getPassengerId());
        ordEvaluations.setRating(bo.getRating());
        ordEvaluations.setIsAnonymous(bo.getIsAnonymous());
        ordEvaluations.setComment(bo.getComment());
        ordEvaluations.setTags(bo.getTags());
        return ordEvaluations;
    }
}
