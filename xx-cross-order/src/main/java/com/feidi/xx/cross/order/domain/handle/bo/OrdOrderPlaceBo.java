package com.feidi.xx.cross.order.domain.handle.bo;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 *  下单 ot_order
 *
 * <AUTHOR>
 * @date 2024-04-26
 */
@Data
public class OrdOrderPlaceBo implements Serializable {

    /**
     * 询价key
     */
    @NotBlank(message = "询价key不能为空")
    private String estimateKey;

    /**
     * 乘客备注
     */
    private String passengerRemark;

    /**
     * 高速类型
     */
    @NotBlank(message = "高速费类型不能为空")
    private String highwayType;

    /**
     * 产品code
     */
    @NotBlank(message = "产品编码不能为空")
    private String productCode;

    /**
     * 下单类型[CreateModelEnum]
     */
    private String createModel;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 乘客手机号
     */
    private String passengerPhone;

    /**
     * 接单手机号
     */
    private String driverPhone;

    /**
     *  司机id
     */
    private Long driverId;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 优惠卷id
     */
    private Long couponGrantId;

    /**
     * 优惠卷额度
     */
    private Long couponQuota;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 来源
     */
    private String source;

    /**
     * 转卖司机ID
     */
    private Long resellDriverId;

    /**
     * 转卖代理商ID
     */
    private Long resellAgentId;

    /**
     * 转卖后司机接单金额
     */
    private Long resellDriverPrice;

    /**
     * 订单金额
     */
    private Long orderPrice;

    /**
     * 是否发送短信
     */
    private Boolean sms;

}
