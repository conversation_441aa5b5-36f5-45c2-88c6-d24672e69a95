package com.feidi.xx.cross.order.controller.admin;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.cross.order.domain.bo.OrdPositionBo;
import com.feidi.xx.cross.order.domain.vo.ExportVo;
import com.feidi.xx.cross.order.domain.vo.OrdPositionVo;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.cross.order.domain.vo.OrdRateVo;
import com.feidi.xx.cross.order.domain.bo.OrdRateBo;
import com.feidi.xx.cross.order.service.IOrdRateService;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 订单账单
 * 前端访问路由地址为:/order/rate
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/rate")
public class OrdRateController extends BaseController {

    private final IOrdRateService ordRateService;

    /**
     * 查询订单账单列表
     */
    @SaCheckPermission("order:rate:list")
    @GetMapping("/list")
    public TableDataInfo<OrdRateVo> list(OrdRateBo bo, PageQuery pageQuery) {
        return ordRateService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出订单账单列表
     */
    @SaCheckPermission("order:rate:export")
    @Log(title = "订单账单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OrdRateBo bo,HttpServletResponse response) {
        List<OrdRateVo> list = ordRateService.queryList(bo);
        ExcelUtil.exportExcel(list, "订单账单", OrdRateVo.class, response);
    }

    /**
     * 获取订单账单详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("order:rate:query")
    @GetMapping("/{id}")
    public R<OrdRateVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(ordRateService.queryById(id));
    }

    /**
     * 新增订单账单
     */
    @SaCheckPermission("order:rate:add")
    @Log(title = "订单账单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OrdRateBo bo) {
        return toAjax(ordRateService.insertByBo(bo));
    }

    /**
     * 修改订单账单
     */
    @SaCheckPermission("order:rate:edit")
    @Log(title = "订单账单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OrdRateBo bo) {
        return toAjax(ordRateService.updateByBo(bo));
    }

    /**
     * 删除订单账单
     *
     * @param ids 主键串
     */
    @SaCheckPermission("order:rate:remove")
    @Log(title = "订单账单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(ordRateService.deleteWithValidByIds(List.of(ids), true));
    }
}
