package com.feidi.xx.cross.order.domain.handle.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单处理基类
 *
 * <AUTHOR>
 * @date 2025/3/18
 */
@Data
public class OrdOrderHandleBaseBo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 平台订单号
     */
    private String platformNo;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 用户id
     */
    private Long userId = 0L;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 备注
     */
    private String remark;

    /**
     * 时间戳
     */
    private Long timeStamp;

    /**
     * 第三方订单状态
     */
    private String thirdStatus;

    /**
     * 最早出发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date earliestTime;
}
