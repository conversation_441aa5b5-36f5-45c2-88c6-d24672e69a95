package com.feidi.xx.cross.order.domain.bo.rob;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
@Data
public class DisRobQueryBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 线路ID
     */
    @NotNull(message = "线路不能为空")
    private Long lineId;

    /**
     * 等待时间时间
     */
    private Integer maxWaitDuration;

    /**
     * 开始区域编码
     */
    private String startAdCode;

    /**
     * 结束区域编码
     */
    private String endAdCode;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 抢单产品编码
     */
    private String robProduct;

    /**
     * 座位数
     */
    private Integer seat;
}
