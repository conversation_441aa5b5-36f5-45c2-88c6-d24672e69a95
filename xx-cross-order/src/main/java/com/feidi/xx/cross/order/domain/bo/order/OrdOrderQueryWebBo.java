package com.feidi.xx.cross.order.domain.bo.order;

import com.feidi.xx.common.core.domain.StartEndTime;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.PaymentStatusEnum;
import com.feidi.xx.common.core.enums.SuccessFailEnum;
import com.feidi.xx.common.log.enums.PlatformEnum;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.cross.common.enums.operate.ProductCodeEnum;
import com.feidi.xx.cross.common.enums.order.DispatchTypeEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.enums.order.RebateStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 后台订单查询参数
 *
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrdOrderQueryWebBo extends PageQuery {

    /**
     * 订单池：订单号、平台单号、乘客调度电话（分机号）
     * 订单：订单号、平台单号、乘客调度电话（分机号）、保险单号、司机ID(司机姓名、司机手机号、司机ID)、司乘虚拟号
     */
    private String unionId;

    /**
     * 线路ID
     */
    private Long lineId;

    /**
     * 线路方向
     */
    private String lineDirection;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 代理商ids
     */
    private List<Long> agentIds;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 乘客ID
     */
    private Long passengerId;

    /**
     * 乘客手机号
     */
    private String passengerPhone;

    /**
     * 平台编号 {@link PlatformEnum}
     */
    private String platformCode;

    /**
     * 平台编号集合
     */
    private List<String> platformCodes;

    /**
     * 产品编码 {@link ProductCodeEnum}
     */
    private String productCode;

    /**
     * 产品编码集合
     */
    private List<String> productCodes;

    /**--------------------------------------   状态相关  -------------------------------------**/

    /**
     * 返利状态 {@link RebateStatusEnum}
     */
    private String rebateStatus;

    /**
     * 支付状态 {@link PaymentStatusEnum}
     */
    private String payStatus;

    /**
     * 订单状态 {@link OrderStatusEnum}
     */
    @Deprecated
    private String status;

    /**
     * 状态集合
     * @see com.feidi.xx.cross.common.enums.order.OrderStatusTabEnum
     */
    private List<String> statuses;

    /**
     * 投保状态[InsureStatusEnum]
     */
    private String insureStatus;

    /**--------------------------------------   调度相关  -------------------------------------**/

    /**
     * 是否调度
     */
    private String dispatch;

    /**
     * 调度类型 {@link DispatchTypeEnum}
     */
    private String dispatchType;

    /**
     * 调度类型集合
     */
    private List<String> dispatchTypes;

    /**
     * 调度状态 {@link SuccessFailEnum}
     */
    private String dispatchStatus;

    /**--------------------------------------   时间相关   -------------------------------------**/

    /**
     * 上次请求时间
     */
    private Date lastTime;

    /**
     * 乘客预计出发时间
     */
    private StartEndTime goTime;

    /**
     * 订单出发时间
     */
    private StartEndTime startTime;

    /**
     * 订单完单时间
     */
    private StartEndTime finishTime;

    /**
     * 订单发单时间
     */
    private StartEndTime createTime;

    /**
     * 取消时间
     */
    private StartEndTime cancelTime;

    /**
     * 订单统计时间
     */
    private String statisticTime;

    /**
     * 订单统计开始时间
     */
    private String statisticStartTime;

    /**
     * 订单统计结束时间
     */
    private String statisticEndTime;

    /**--------------------------------------   内部处理  -------------------------------------**/

    /**
     * 是否客诉 {@link IsYesEnum}
     */
    private String complain;

    /**
     * 是否订单池
     */
    private Boolean isPool;

    /**
     *  多线路
     */
    private Collection<Long> lineIds;

    /**
     *  主线路
     */
    private Long mainLineId;

    /**
     * 排序
     */
    private String sort;

    /**
     * 导出类型 1：代理商订单统计导出 2：线路订单统计导出
     */
    private Integer exportType;

    /**
     * 是否被过滤
     */
    private String filtered;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 下单类型[CreateModelEnum]
     */
    private String createModel;

    /**
     * 下单类型集合
     */
    private List<String> createModels;

    /**
     * 城市编码集合
     */
    private List<String> cityCodes;
}
