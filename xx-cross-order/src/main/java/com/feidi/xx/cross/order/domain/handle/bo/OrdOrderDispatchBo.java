package com.feidi.xx.cross.order.domain.handle.bo;

import com.feidi.xx.cross.common.enums.order.DispatchTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 派单、调度操作 cx_order
 * <p>
 * 【抢单 自动抢单 占单 派单 改派】
 *
 * <AUTHOR>
 * @date 2024-04-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrdOrderDispatchBo extends OrdOrderHandleBaseBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 调度类型
     */
    private String type = DispatchTypeEnum.ASSIGN.getCode();

    /**
     * 自动抢单ID
     */
    private Long robId;

    /**
     * 调度司机ID - 代理抢单必填
     */
    private Long dispatchDriverId;

    /**
     * 调度信息
     */
    private DispatchInfo dispatchInfo;

    @Data
    public static final class DispatchInfo {

        // 改派前的司机ID，只有改派才有，不然可能为空
        private Long originalDriverId;

        // 调度司机ID
        private Long dispatchDriverId;

    }
}
