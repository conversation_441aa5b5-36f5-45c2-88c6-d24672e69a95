package com.feidi.xx.cross.order.domain.handle.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * 订单操作 cx_order
 *
 * 【3司机出发接乘客；4乘客上车】
 *
 * <AUTHOR>
 * @date 2025/3/18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrdOrderHandleBo extends OrdOrderHandleBaseBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 状态 【3司机出发接乘客；4达到出发地】
     */
    private String status;

    /**
     * 乘客电话尾号
     */
    private String phoneEnd;
}
