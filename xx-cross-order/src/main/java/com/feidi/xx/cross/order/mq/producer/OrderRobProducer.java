package com.feidi.xx.cross.order.mq.producer;

import com.feidi.xx.common.rocketmq.base.BaseSendExtendDTO;
import com.feidi.xx.common.rocketmq.enums.MqMessageTypeEnum;
import com.feidi.xx.common.rocketmq.producer.AbstractProducer;
import com.feidi.xx.common.rocketmq.util.MQMessageUtil;
import com.feidi.xx.cross.common.constant.order.OrderRocketMQConstant;
import com.feidi.xx.cross.order.mq.event.OrderRobEvent;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.stereotype.Component;

/**
 * 订单自动抢单消息消费者
 *
 * <AUTHOR>
 * @date 2025/3/20
 */
@Component
@RequiredArgsConstructor
public class OrderRobProducer extends AbstractProducer<OrderRobEvent> {
    private BaseSendExtendDTO buildBaseSendExtendParam(OrderRobEvent robEvent) {

        return BaseSendExtendDTO.builder()
                .eventName("订单自动抢单")
                .keys(robEvent.getOrderId().toString())
                .messageType(MqMessageTypeEnum.SYNC)
                .topic(OrderRocketMQConstant.XX_ORDER_ROB_TOPIC_KEY)
                .sentTimeout(2000L)
                .build();
    }
    @Override
    public SendResult sendMessage(OrderRobEvent robEvent) {
        return MQMessageUtil.sendMessage(robEvent, this::buildBaseSendExtendParam);
    }
}

