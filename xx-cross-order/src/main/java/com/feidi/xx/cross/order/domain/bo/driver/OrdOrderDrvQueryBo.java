package com.feidi.xx.cross.order.domain.bo.driver;

import com.feidi.xx.common.core.constant.RegexConstants;
import com.feidi.xx.common.core.domain.StartEndTime;
import com.feidi.xx.common.core.enums.PaymentStatusEnum;
import com.feidi.xx.common.core.validate.IndexGroup;
import com.feidi.xx.common.core.validate.QueryGroup;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.cross.common.enums.operate.ProductCodeEnum;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 司机端订单查询参数
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrdOrderDrvQueryBo extends PageQuery {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空", groups = {QueryGroup.class})
    private Long id;


    /**
     * 服务类型
     */
    @NotNull(message = "服务类型不能为空", groups = {IndexGroup.class})
    private ServiceType serviceType;

    /**
     * 司机佣金比例
     */
    @Hidden
    private BigDecimal driverRate;

    /**
     * 司机id
     */
    @Hidden
    private Long driverId;

    /**
     * 代理商id
     */
    @Hidden
    private Long agentId;

    /**
     * 父代理商id
     */
    @Hidden
    private Long parentId;

    /**
     * 司机当前经度
     */
    private Double longitude;

    /**
     * 司机当前纬度
     */
    private Double latitude;

    /**
     * 产品类型 {@link ProductCodeEnum}
     */
    private String productCode;

    /**
     * 乘客人数
     */
    private List<Integer> passengerNum;

    /**
     * 出发地
     */
    private String startCity;

    /**
     * 目的地
     */
    private String endCity;

    /**
     * 日期
     */
    @Pattern(regexp = RegexConstants.DATE)
    private String date;

    /**
     * 时间 HH:mm
     */
    private List<String> time;

    /**
     * 行程价格区间-最低 单位：分
     */
    private Integer minPrice;

    /**
     * 行程价格区间-最高 单位：分
     */
    private Integer maxPrice;

    /**
     * 是否是订单池
     */
    @Hidden
    private Boolean isPool;

    /**
     * 线路id
     */
    @Hidden
    private List<Long> lineIds;

    /**
     * 日期时间
     */
    private List<StartEndTime> dateTimes;

    /**
     * 状态
     */
    private List<String> statuses;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 产品类型集合 {@link ProductCodeEnum}
     */
    private List<String> productCodes;

    /**
     * 支付状态集合 {@link PaymentStatusEnum}
     */
    private List<String> payStatuses;

    /**
     * 上次请求时间
     */
    private Date lastTime;

    /**
     * 邀请的司机id
     */
    private Long inviteDriverId;

    /**
     * 服务类型
     */
    @Getter
    public enum ServiceType {
        PROCESSING, WAITING, CANCEL, FINISH, CREATED;
    }

    /**
     * 是否被过滤
     */
    private String isFiltered;

    /**
     * 订单转卖标志(0-转卖，默认为“1”-非转卖)
     */
    private Integer resell = 1;

    /**
     * 转卖司机ID
     */
    private Long resellDriverId;
}
