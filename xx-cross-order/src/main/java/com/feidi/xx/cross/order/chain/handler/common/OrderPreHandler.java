package com.feidi.xx.cross.order.chain.handler.common;

import cn.hutool.json.JSONUtil;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.tenant.helper.TenantHelper;
import com.feidi.xx.cross.common.annotations.HandlerScope;
import com.feidi.xx.cross.common.cache.market.manage.MktCacheManager;
import com.feidi.xx.cross.common.cache.power.manager.PowCacheManager;
import com.feidi.xx.cross.common.constant.order.OrderConstants;
import com.feidi.xx.cross.common.enums.order.CreateModelEnum;
import com.feidi.xx.cross.common.helper.OrdOrderOperateHelper;
import com.feidi.xx.cross.common.mq.event.OrdOrderOperateEvent;
import com.feidi.xx.cross.order.chain.base.OrderBaseChainContext;
import com.feidi.xx.cross.order.chain.base.OrderBaseChainResult;
import com.feidi.xx.cross.order.chain.common.AbstractChainHandler;
import com.feidi.xx.cross.order.chain.payment.OrderPaymentChainContext;
import com.feidi.xx.cross.order.chain.place.OrderPlaceChainContext;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerLoginVo;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.system.api.RemoteConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 订单前置处理器
 */
@Slf4j
@Component
@HandlerScope
@RequiredArgsConstructor
public class OrderPreHandler<T extends OrderBaseChainContext, R extends OrderBaseChainResult> extends AbstractChainHandler<T, R> {

    @DubboReference
    private final RemotePassengerService remotePassengerService;
    @DubboReference
    private final RemoteConfigService configService;
    private final MktCacheManager mktCacheManager;
    private final PowCacheManager powCacheManager;




    @Override
    public void handle(T context) {
        context.setTenantId(TenantHelper.getTenantId());
        OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildOrderOperateEvent(context.getOrderId(), null, JSONUtil.toJsonStr(context));
        context.setOperateEvent(operateEvent);
        if (context instanceof OrderPlaceChainContext placeContext) {
            // 非乘客下单，获取乘客信息
            if (Objects.equals(placeContext.getCreateModel(), CreateModelEnum.AGENT_ORDER.getCode()) || Objects.equals(placeContext.getCreateModel(), CreateModelEnum.DRIVER_ORDER.getCode())) {

                //代客下单如果指定司机 校验 乘客手机号和司机手机号是否一致
                if (ObjectUtils.isNull(placeContext.getPassengerPhone())) {
                    throw new ServiceException("乘客手机号不能为空");
                }

                if (Objects.equals(placeContext.getPassengerPhone(), placeContext.getDriverPhone())) {
                    throw new ServiceException("代客下单指定司机，乘客手机号和司机手机号不一致");
                }
                if (!mktCacheManager.canPlaceOrder(placeContext.getUserType(), placeContext.getUserId())) {
                    throw new ServiceException("今天已达到代客下单次数上限，请稍后重试");
                }
                RemotePassengerLoginVo remotePassengerLoginVo = new RemotePassengerLoginVo();
                remotePassengerLoginVo.setPhone(placeContext.getPassengerPhone());
                remotePassengerLoginVo.setTenantId(LoginHelper.getTenantId());
                remotePassengerLoginVo.setType(LoginHelper.getUserType().getUserType());
                if (LoginHelper.getUserType().getUserType().equals(UserTypeEnum.AGENT_USER.getUserType())){
                    remotePassengerLoginVo.setInviteUserId(LoginHelper.getAgentId());
                    operateEvent.setUserId(LoginHelper.getUserId());
                } else
                {
                    remotePassengerLoginVo.setInviteUserId(LoginHelper.getUserId());
                }
                RemotePassengerVo passengerInfo = remotePassengerService.getPassengerInfo(remotePassengerLoginVo);
                ((OrderPlaceChainContext) context).setPassengerId(passengerInfo.getId());
            } else if (Objects.equals(placeContext.getCreateModel(), CreateModelEnum.RESELL_ORDER.getCode())) {
                RemoteDriverVo driverVo = powCacheManager.getDriverInfoById(placeContext.getResellDriverId());
                if (ObjectUtils.isNull(driverVo)) {
                    throw new ServiceException("当前司机不存在，请重新登录后重试");
                }

                String minPay = configService.selectValueByKey(OrderConstants.CROSS_RESELL_PASSENGER_MIN_PAY);
                if (StringUtils.isNotBlank(minPay) && placeContext.getOrderPrice() < Long.parseLong(minPay) * 100) {
                    throw new ServiceException("乘客最小支付金额不能低于"+ minPay +"元，请重新设置后再提交");
                }

                // 获取司机的转卖服务费
                Long resellServiceProfit = ArithUtils.profitUseBigDecimal(placeContext.getOrderPrice(), driverVo.getResellServiceRate());
                if (placeContext.getResellDriverPrice() > (placeContext.getOrderPrice() - resellServiceProfit)) {
                    throw new ServiceException("司机接单金额设置不合理，请重新设置后再提交");
                }

                RemotePassengerLoginVo remotePassengerLoginVo = new RemotePassengerLoginVo();
                remotePassengerLoginVo.setPhone(placeContext.getPassengerPhone());
                remotePassengerLoginVo.setTenantId(LoginHelper.getTenantId());
                RemotePassengerVo passengerInfo = remotePassengerService.getPassengerInfo(remotePassengerLoginVo);
                ((OrderPlaceChainContext) context).setPassengerId(passengerInfo.getId());
            } else if (Objects.equals(placeContext.getCreateModel(), CreateModelEnum.PASSENGER_ORDER.getCode())) {
                // 乘客信息
                RemotePassengerVo passengerInfo = remotePassengerService.getPassengerInfo(placeContext.getPassengerId());
                if (ObjectUtils.isNull(passengerInfo)) {
                    throw new ServiceException("当前乘客不存在，请重新登录后重试");
                }
                //判断乘客拉黑
                if (Objects.equals(passengerInfo.getBlacklisted(), IsYesEnum.YES.getCode())) {
                    log.info("乘客{}已被拉黑,尝试下单", passengerInfo.getPhone());
                    throw new ServiceException("系统检测您的账号异常，请联系客服");
                }
            }
        } else if (context instanceof OrderPaymentChainContext placeContext) {

        }
    }

}
