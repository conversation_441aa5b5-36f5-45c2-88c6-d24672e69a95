package com.feidi.xx.cross.order.mq.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 订单支付事件
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderUnbindVirtualPhoneEvent implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 延迟时间 单位:毫秒
     */
    private Long delayTime;

    private String phoneA;

    private String phoneX;

    private String phoneB;
}
