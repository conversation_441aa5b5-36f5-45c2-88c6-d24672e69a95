package com.feidi.xx.cross.order.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.order.domain.bo.OrdEvaluationsBo;
import com.feidi.xx.cross.order.domain.vo.OrdEvaluationsVo;
import com.feidi.xx.cross.order.service.IOrdEvaluationsService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 后台 - 行程评价
 * 前端访问路由地址为:/settle/evaluations
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/evaluations")
public class OrdEvaluationsController extends BaseController {

    private final IOrdEvaluationsService ordEvaluationsService;

    /**
     * 查询行程评价列表
     */
    @SaCheckPermission("order:evaluations:list")
    @GetMapping("/list")
    public TableDataInfo<OrdEvaluationsVo> list(OrdEvaluationsBo bo, PageQuery pageQuery) {
        return ordEvaluationsService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取行程评价详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("order:evaluations:query")
    @GetMapping("/{id}")
    public R<OrdEvaluationsVo> getInfo(@NotNull(message = "主键不能为空")
                                       @PathVariable Long id) {
        return R.ok(ordEvaluationsService.queryById(id));
    }

    /**
     * 删除行程评价
     *
     * @param ids 主键串
     */
    @SaCheckPermission("order:evaluations:remove")
    @Log(title = "行程评价", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(ordEvaluationsService.deleteWithValidByIds(List.of(ids), true));
    }
}
