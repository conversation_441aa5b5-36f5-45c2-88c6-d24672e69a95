package com.feidi.xx.cross.order.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.order.domain.DisRob;
import com.feidi.xx.cross.order.domain.bo.rob.CoverQuery;
import com.feidi.xx.cross.order.domain.bo.rob.DisRobBo;
import com.feidi.xx.cross.order.domain.bo.rob.MatchBo;
import com.feidi.xx.cross.order.domain.vo.DisRobVo;
import com.feidi.xx.cross.order.domain.vo.driver.DisRobHistoryAddrVo;

import java.util.Collection;
import java.util.List;

/**
 * 自动抢单Service接口
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface IDisRobService {

    /**
     * 查询自动抢单
     *
     * @param id 主键
     * @return 自动抢单
     */
    DisRobVo queryById(Long id);

    /**
     * 分页查询自动抢单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 自动抢单分页列表
     */
    TableDataInfo<DisRobVo> queryPageList(DisRobBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的自动抢单列表
     *
     * @param bo 查询条件
     * @return 自动抢单列表
     */
    List<DisRobVo> queryList(DisRobBo bo);

    /**
     * 新增自动抢单
     *
     * @param bo 自动抢单
     * @return 是否新增成功
     */
    Boolean insertByBo(DisRobBo bo);

    /**
     * 修改自动抢单
     *
     * @param bo 自动抢单
     * @return 是否修改成功
     */
    Boolean updateByBo(DisRobBo bo);

    /**
     * 校验并批量修改自动抢单信息
     * @param bo
     * @return
     */
    Boolean batchUpdateByBo(DisRobBo bo);

    /**
     * 获取抢单覆盖范围
     *
     * @param coverQuery 覆盖范围查询条件
     */
    List<DisRob> cover(CoverQuery coverQuery);

    /**
     * 校验并批量删除自动抢单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取司机抢单地址历史记录
     *
     * @return
     */
    List<DisRobHistoryAddrVo> getHistory();

    /**
     * 自动抢单
     *
     * @param disRobBo 自动抢单参数
     * @return 是否抢单成功
     */
    Boolean autoRob(DisRobBo disRobBo);

    /**
     * 禁用抢单
     * @param ids
     * @param function
     */
    void disableRob(List<Long> ids, SFunction<DisRob, Long> function);


    /**
     * 常用路线匹配
     */
     Boolean matchCommonRoute(MatchBo matchBo);

    /**
     * 自动续约
     */
    Boolean autoRenewal();
}
