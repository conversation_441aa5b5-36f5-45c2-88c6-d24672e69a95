package com.feidi.xx.cross.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.order.enums.OrdCacheKeyEnum;
import com.feidi.xx.cross.common.enums.order.RateTypeEnum;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderRateVo;
import com.feidi.xx.cross.order.domain.OrdRate;
import com.feidi.xx.cross.order.domain.bo.OrdRateBo;
import com.feidi.xx.cross.order.domain.vo.OrdRateVo;
import com.feidi.xx.cross.order.mapper.OrdRateMapper;
import com.feidi.xx.cross.order.service.IOrdRateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 订单账单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@RequiredArgsConstructor
@Service
public class OrdRateServiceImpl implements IOrdRateService {

    private final ScheduledExecutorService scheduledExecutorService;
    private final OrdRateMapper baseMapper;

    /**
     * 查询订单账单
     *
     * @param id 主键
     * @return 订单账单
     */
    @Override
    public OrdRateVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询订单账单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单账单分页列表
     */
    @Override
    public TableDataInfo<OrdRateVo> queryPageList(OrdRateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrdRate> lqw = buildQueryWrapper(bo);
        Page<OrdRateVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的订单账单列表
     *
     * @param bo 查询条件
     * @return 订单账单列表
     */
    @Override
    public List<OrdRateVo> queryList(OrdRateBo bo) {
        LambdaQueryWrapper<OrdRate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrdRate> buildQueryWrapper(OrdRateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrdRate> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderId() != null, OrdRate::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getRateType()), OrdRate::getRateType, bo.getRateType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), OrdRate::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增订单账单
     *
     * @param bo 订单账单
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OrdRateBo bo) {
        OrdRate add = MapstructUtils.convert(bo, OrdRate.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 批量新增订单费率
     *
     * @param ordRateBos
     * @return
     */
    @Override
    public Boolean insertByBos(List<OrdRateBo> ordRateBos) {
        if (CollUtils.isEmpty(ordRateBos)) {
            return false;
        }
        ordRateBos.forEach(e -> {
            OrdRate ordRate = BeanUtils.copyProperties(e, OrdRate.class);
            baseMapper.insert(ordRate);
            // 异步添加缓存
            scheduledExecutorService.schedule(() -> addCache(ordRate), 0, TimeUnit.SECONDS);
        });
        return null;
    }

    /**
     * 修改订单账单
     *
     * @param bo 订单账单
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OrdRateBo bo) {
        OrdRate update = MapstructUtils.convert(bo, OrdRate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrdRate entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除订单账单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据订单id和账单类型查询订单账单
     *
     * @param orderIds 账单id集合
     * @param rateType 账单类型
     * @return 订单账单信息
     */
    @Override
    public List<OrdRate> queryByOrderIdsAndRateType(List<Long> orderIds, String rateType) {
        if (CollUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrdRate> lqw = Wrappers.lambdaQuery();
        lqw.in(OrdRate::getOrderId, orderIds)
                .eq(OrdRate::getRateType, rateType)
                .eq(OrdRate::getStatus, StatusEnum.ENABLE.getCode());


        return baseMapper.selectList(lqw);
    }

    /**
     * 根据订单id和账单类型查询订单账单
     *
     * @param orderId 账单id
     * @param rateType 账单类型
     * @return 订单账单信息
     */
    @Override
    public OrdRate queryByOrderIdAndRateType(Long orderId, String rateType) {
        LambdaQueryWrapper<OrdRate> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrdRate::getOrderId, orderId)
                .eq(OrdRate::getRateType, rateType)
                .orderByDesc(OrdRate::getCreateTime)
                .last(Constants.LIMIT_ONE);
        return baseMapper.selectOne(lqw);
    }

    /**
     * 更新返利状态
     *
     * @param statusCode 返利状态
     * @return 是否更新成功
     */
    @Override
    public Boolean updateRateStatusByOrderId(Long orderId, String statusCode) {
        LambdaUpdateWrapper<OrdRate> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(OrdRate::getOrderId, orderId)
                .eq(OrdRate::getStatus, StatusEnum.ENABLE.getCode())
                .set(OrdRate::getStatus, statusCode);
        boolean updateFlag = baseMapper.update(updateWrapper) > 0;

        if (updateFlag) {
            // 删除缓存
            scheduledExecutorService.schedule(() -> deleteCache(orderId), 0, TimeUnit.SECONDS);
        }

        return updateFlag;
    }

    /**
     * 添加缓存
     * @param ordRate
     */
    public void addCache(OrdRate ordRate) {
        if (ObjectUtils.isNull(ordRate)) {
            return;
        }
        String cacheKey = OrdCacheKeyEnum.ORD_ORDER_TYPE_RATE_INFO_KEY.create(ordRate.getOrderId(), ordRate.getRateType());
        RedisUtils.setCacheObject(cacheKey, BeanUtils.copyProperties(ordRate, RemoteOrderRateVo.class),
                OrdCacheKeyEnum.ORD_ORDER_TYPE_RATE_INFO_KEY.getDuration());

    }

    /**
     * 删除缓存
     *
     * @param orderId 订单id
     */
    public void deleteCache(Long orderId) {
        RedisUtils.deleteObject(OrdCacheKeyEnum.ORD_ORDER_RATE_INFO_KEY.create(orderId));

        for (RateTypeEnum rateType : RateTypeEnum.values()) {
            RedisUtils.deleteObject(OrdCacheKeyEnum.ORD_ORDER_TYPE_RATE_INFO_KEY.create(orderId, rateType));
        }
    }
}
