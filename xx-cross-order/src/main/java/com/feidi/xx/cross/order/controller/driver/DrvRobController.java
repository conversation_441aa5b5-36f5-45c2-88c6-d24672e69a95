package com.feidi.xx.cross.order.controller.driver;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.order.domain.bo.rob.DisRobBo;
import com.feidi.xx.cross.order.domain.vo.DisRobVo;
import com.feidi.xx.cross.order.domain.vo.driver.DisRobHistoryAddrVo;
import com.feidi.xx.cross.order.service.IDisRobService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 司机 - 自动抢单
 * 前端访问路由地址为:/cross/rob
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.DRIVER_ROUTE_PREFIX + "/rob")
public class DrvRobController extends BaseController {

    private final IDisRobService robService;

    /**
     * 查询自动抢单列表
     */
    @GetMapping("/list")
    public TableDataInfo<DisRobVo> list(DisRobBo bo, PageQuery pageQuery) {
        bo.setDriverId(LoginHelper.getUserId());
        return robService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取自动抢单详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<DisRobVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(robService.queryById(id));
    }

    /**
     * 新增自动抢单
     */
    @Log(title = "自动抢单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody DisRobBo bo) {
        bo.setDriverId(LoginHelper.getUserId());
        bo.setAgentId(LoginHelper.getAgentId());
        bo.setUserType(LoginHelper.getUserType().getUserType());
        return toAjax(robService.insertByBo(bo));
    }

    /**
     * 修改自动抢单
     */
    @Log(title = "自动抢单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody DisRobBo bo) {
        bo.setDriverId(LoginHelper.getUserId());
        bo.setAgentId(LoginHelper.getAgentId());
        bo.setUserType(LoginHelper.getUserType().getUserType());
        return toAjax(robService.updateByBo(bo));
    }

    /**
     * 删除自动抢单
     *
     * @param ids 主键串
     */
    @Log(title = "自动抢单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(robService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 获取司机抢单地址历史记录
     */
    @GetMapping("/history/list")
    public R<List<DisRobHistoryAddrVo>> getHistory() {
        return R.ok(robService.getHistory());
    }
}
