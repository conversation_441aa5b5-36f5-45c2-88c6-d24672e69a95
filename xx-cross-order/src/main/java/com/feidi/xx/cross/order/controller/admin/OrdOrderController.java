package com.feidi.xx.cross.order.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.order.domain.bo.order.OrdOrderQueryWebBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderCancelBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderDispatchBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderHandleBaseBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderHandleBo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderExportVo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderVo;
import com.feidi.xx.cross.order.service.*;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 后台 - 订单
 * 前端访问路由地址为:/order/order
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order")
public class OrdOrderController extends BaseController {

    private final IOrdOrderService ordOrderService;
    private final IOrdOrderInsureService ordOrderInsureService;
    private final IOrdOrderProcessService ordOrderProcessService;
    private final IOrdOrderMbrService orderMbrService;
    private final IOrdOrderRebateService ordOrderRebateService;

    /**
     * 订单池
     */
    @Enum2TextAspect
    @SaCheckPermission("order:order:pool")
    @PostMapping("/pool")
    public R<TableDataInfo<OrdOrderVo>> pool(@RequestBody @Validated OrdOrderQueryWebBo bo) {
        bo.setIsPool(true);
        bo.setDispatch(IsYesEnum.NO.getCode());
        TableDataInfo<OrdOrderVo> tableDataInfo = ordOrderService.queryPageList(bo);
        Map<String, Object> extra = Collections.singletonMap("lastTime", new Date());
        tableDataInfo.setExtra(extra);
        return R.ok(tableDataInfo);
    }

    /**
     * 查询订单列表
     */
    @Enum2TextAspect
    @SaCheckPermission("order:order:list")
    @PostMapping("/list")
    public TableDataInfo<OrdOrderVo> list(@RequestBody @Validated OrdOrderQueryWebBo bo) {
        TableDataInfo<OrdOrderVo> tableDataInfo = ordOrderService.queryPageList(bo);
        Map<String, Object> extra = Collections.singletonMap("lastTime", new Date());
        tableDataInfo.setExtra(extra);
        return tableDataInfo;
    }

    /**
     * 订单池-统计新增订单数量
     */
    @Enum2TextAspect
    @SaCheckPermission("order:order:pool")
    @PostMapping("/newNum")
    public R<Long> newNum(@RequestBody @Validated OrdOrderQueryWebBo bo) {
        // 是否订单池
        bo.setIsPool(true);
        // 未调度过
        bo.setDispatch(IsYesEnum.NO.getCode());
        return R.ok(ordOrderService.addNum(bo));
    }

    /**
     * 订单列表-统计新增订单数量
     */
    @Enum2TextAspect
    @SaCheckPermission("order:order:list")
    @PostMapping("/list/newNum")
    public R<Long> listNewNum(@RequestBody @Validated OrdOrderQueryWebBo bo) {
        return R.ok(ordOrderService.addNum(bo));
    }

    /**
     * 查询客诉订单列表
     */
    @Enum2TextAspect
    @SaCheckPermission("order:list:complain")
    @Log(title = "订单", businessType = BusinessType.COMPLAIN)
    @PostMapping("/complain")
    public TableDataInfo<OrdOrderVo> complain(@RequestBody @Validated OrdOrderQueryWebBo bo) {
        // 客诉订单
        bo.setComplain("1");
        return ordOrderService.queryPageList(bo);
    }

    /**
     * 导出订单列表
     */
    @SaCheckPermission("order:order:export")
    @Log(title = "订单", businessType = BusinessType.EXPORT)
    @Download(name="订单",module = ModuleConstants.ORDER,mode="no")
    @PostMapping("/export")
    public Object export(@RequestBody @Validated OrdOrderQueryWebBo bo, HttpServletResponse response) {
        StopWatch stopWatch = new StopWatch("订单导出耗时统计");

        stopWatch.start("查询数据");
        List<OrdOrderExportVo> list = ordOrderService.export(bo);
        stopWatch.stop();

        stopWatch.start("生成Excel");
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelUtil.exportExcel(list, "订单", OrdOrderExportVo.class, outputStream);
        byte[] result = outputStream.toByteArray();
        stopWatch.stop();

        log.info("导出订单统计耗时：{}", stopWatch.prettyPrint());
        return result;
    }

    /**
     * 详情
     *
     * @param id 主键
     */
    @Enum2TextAspect
    @SaCheckPermission("order:order:query")
    @GetMapping("/{id}")
    public R<OrdOrderVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(ordOrderService.queryById(id));
    }

    /**
     * 调度、改派
     */
    @SaCheckPermission("order:order:dispatch")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/dispatch")
    public R<Void> dispatch(@Validated(EditGroup.class) @RequestBody OrdOrderDispatchBo bo) {
        bo.setUserId(LoginHelper.getUserId());
        bo.setUserType(UserTypeEnum.SYS_USER.getUserType());
        bo.setRemark("总后台调度");
        bo.setTimeStamp(DateUtils.getUnixTimeStamps());
        return toAjax(ordOrderProcessService.dispatchOrder(bo));
    }

    /**
     * 完单
     */
    @SaCheckPermission("order:order:finish")
    @Log(title = "订单", businessType = BusinessType.FINISH)
    @PutMapping("/finish/{orderId}")
    public R<Void> finish(@NotNull(message = "主键不能为空") @PathVariable Long orderId) {
        OrdOrderHandleBo bo = new OrdOrderHandleBo();
        bo.setOrderId(orderId);
        bo.setUserId(LoginHelper.getUserId());
        bo.setUserType(UserTypeEnum.SYS_USER.getUserType());
        bo.setTimeStamp(DateUtils.getUnixTimeStamps());
        bo.setRemark("总后台操作行程结束");
        return toAjax(ordOrderProcessService.tripEnd(bo));
    }

    /**
     * 取消
     */
    @SaCheckPermission("order:order:cancel")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/cancel")
    public R<Void> cancel(@Validated(EditGroup.class) @RequestBody OrdOrderCancelBo bo) {
        // 取消人信息
        bo.setUserId(LoginHelper.getUserId());
        bo.setUserType(UserTypeEnum.SYS_USER.getUserType());
        bo.setTimeStamp(DateUtils.getUnixTimeStamps());
        return toAjax(ordOrderProcessService.cancelOrder(bo));
    }

    /**
     * 客诉
     */
    @SaCheckPermission("order:order:complain")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/complain")
    public R<Void> complain(@Validated(EditGroup.class) @RequestBody OrdOrderCancelBo bo) {
        bo.setUserId(LoginHelper.getUserId());
        bo.setUserType(UserTypeEnum.SYS_USER.getUserType());
        bo.setTimeStamp(DateUtils.getUnixTimeStamps());

        return toAjax(ordOrderProcessService.cancelOrder(bo));
    }

    /**
     * 投保
     */
    @RepeatSubmit()
    @SaCheckPermission("order:order:insure")
    @Log(title = "订单", businessType = BusinessType.INSURE)
    @PutMapping("/insure/{orderId}")
    public R<Void> insure(@NotNull(message = "主键不能为空") @PathVariable Long orderId) {
        OrdOrderHandleBaseBo bo = new OrdOrderHandleBaseBo();
        bo.setOrderId(orderId);
        bo.setUserId(LoginHelper.getUserId());
        bo.setUserType(UserTypeEnum.SYS_USER.getUserType());
        bo.setTimeStamp(DateUtils.getUnixTimeStamps());
        bo.setRemark("总后台操作投保");
        return toAjax(ordOrderInsureService.insure(bo));
    }

    /**
     * 修改乘客出发时间
     */
    @RepeatSubmit()
    @SaCheckPermission("order:order:update")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @PostMapping("/updateEarliestTime")
    public R<Void> insure(@RequestBody OrdOrderHandleBaseBo handleBaseBo) {
        handleBaseBo.setUserId(LoginHelper.getUserId());
        handleBaseBo.setUserType(UserTypeEnum.SYS_USER.getUserType());
        handleBaseBo.setTimeStamp(DateUtils.getUnixTimeStamps());
        handleBaseBo.setRemark("总后台修改乘客出发时间");
        return toAjax(ordOrderService.updateEarliestTime(handleBaseBo));
    }


//    /**
//     * 下待客下单
//     *
//     * @param bo
//     * @return
//     */
//    @PostMapping("/place")
//    public R<Long> place(@Validated @RequestBody OrdOrderPlaceBo bo) {
//        bo.setCreateModel(CreateModelEnum.ADMIN_ORDER.getCode());
//        return R.ok(orderMbrService.place(bo));
//    }

    /**
     * 根据订单号批量结算订单（忽略结算时间限制）
     *
     * @param orderNos 订单编号
     * @return 结算是否成功
     */
    @Log(title = "订单结算", businessType = BusinessType.REBATE)
    @SaCheckPermission("order:order:rebate")
    @GetMapping("/orderRebateByOrderNos")
    public R<Void> orderRebateByOrderNos(@RequestParam(name = "orderNos") String orderNos) {
        return toAjax(ordOrderRebateService.orderRebateByOrderNos(orderNos));
    }

    /**
     * 刷新支付
     */
    @Log(title = "总后台订单")
    @RepeatSubmit()
    @PostMapping("/payment/refresh")
    public R<Void> refreshPayment(Long orderId) {
        orderMbrService.refreshPayment(orderId);
        return R.ok();
    }
}
