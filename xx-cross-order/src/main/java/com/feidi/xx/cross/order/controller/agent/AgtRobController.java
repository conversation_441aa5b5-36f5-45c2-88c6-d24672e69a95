package com.feidi.xx.cross.order.controller.agent;

import cn.hutool.core.collection.CollUtil;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.BatchGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.order.domain.DisRob;
import com.feidi.xx.cross.order.domain.bo.rob.CoverQuery;
import com.feidi.xx.cross.order.domain.bo.rob.DisRobBo;
import com.feidi.xx.cross.order.domain.vo.DisRobVo;
import com.feidi.xx.cross.order.service.IDisRobService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 代理 - 自动抢单
 * 前端访问路由地址为:/cross/rob
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.AGENT_ROUTE_PREFIX + "/rob")
public class AgtRobController extends BaseController {

    private final IDisRobService robService;

    /**
     * 查询自动抢单列表
     */
    @Enum2TextAspect
    @PostMapping("/list")
    public TableDataInfo<DisRobVo> list(@RequestBody DisRobBo bo) {
        Long agentId = LoginHelper.getAgentId();
        if (bo.getAgentId() != null) {
            bo.setAgentIds(CollUtil.newArrayList(bo.getAgentId()));
        } else {
            bo.setAgentId(agentId);
        }
        return robService.queryPageList(bo, bo.buildPageQuery());
    }

    /**
     * 导出自动抢单列表
     */
    @Log(title = "自动抢单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void  export(DisRobBo bo, HttpServletResponse response) {
        Long agentId = LoginHelper.getAgentId();
        if (bo.getAgentId() != null) {
            bo.setAgentIds(CollUtil.newArrayList(bo.getAgentId()));
        } else {
            bo.setAgentId(agentId);
        }
        List<DisRobVo> list = robService.queryList(bo);
        ExcelUtil.exportExcel(list, "自动抢单", DisRobVo.class, response);
    }

    /**
     * 获取自动抢单详细信息
     *
     * @param id 主键
     */
    @Enum2TextAspect
    @GetMapping("/{id}")
    public R<DisRobVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(robService.queryById(id));
    }

    /**
     * 新增自动抢单
     */
    @Log(title = "代理商-新增自动抢单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody DisRobBo bo) {
        bo.setAgentId(LoginHelper.getAgentId());
        return toAjax(robService.insertByBo(bo));
    }

    /**
     * 修改自动抢单
     */
    @Log(title = "代理商-修改自动抢单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody DisRobBo bo) {
        bo.setAgentId(LoginHelper.getAgentId());
        return toAjax(robService.updateByBo(bo));
    }

    /**
     * 批量修改自动抢单时间范围
     */
    @Log(title = "代理商-批量修改自动抢单时间范围", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/batchEdit")
    public R<Void> batchEdit(@Validated(BatchGroup.class) @RequestBody DisRobBo bo) {
        bo.setAgentId(LoginHelper.getAgentId());
        bo.setUpdateBy(LoginHelper.getAgentId());
        return toAjax(robService.batchUpdateByBo(bo));
    }

    /**
     *  获取抢单覆盖范围
     * @return
     */
    @GetMapping("/cover")
    public R<List<DisRob>> cover(CoverQuery coverQuery) {
        coverQuery.setAgentId(LoginHelper.getAgentId());
        return R.ok(robService.cover(coverQuery));
    }

    /**
     * 删除自动抢单
     *
     * @param ids 主键串
     */
    @Log(title = "代理商-删除自动抢单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(robService.deleteWithValidByIds(List.of(ids), true));
    }
}
