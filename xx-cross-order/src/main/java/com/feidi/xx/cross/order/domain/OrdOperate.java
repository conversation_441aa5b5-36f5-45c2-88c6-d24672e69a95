package com.feidi.xx.cross.order.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import java.io.Serial;

/**
 * 订单操作对象 ord_operate
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ord_operate")
public class OrdOperate extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 路径追踪id
     */
    private String traceId;

    /**
     * 操作人类型[UserTypeEnum]
     */
    private String userType;

    /**
     * 操作人ID
     */
    private Long userId;

    /**
     * 操作人
     */
    private String userName;

    /**
     * 操作类型[OperateTypeEnum]
     */
    private String operateType;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 参数
     */
    private String paramsJson;

    /**
     * 结果
     */
    private String responseJson;

    /**
     * 状态[SuccessFailEnum]
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
