package com.feidi.xx.cross.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.domain.StartEndTime;
import com.feidi.xx.common.core.enums.*;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.core.utils.xx.PaginationUtil;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.log.enums.PlatformEnum;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.tenant.helper.TenantHelper;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.cache.platform.enums.PlatformCacheKeyEnum;
import com.feidi.xx.cross.common.cache.power.manager.PowCacheManager;
import com.feidi.xx.cross.common.constant.order.OrderConstants;
import com.feidi.xx.cross.common.enums.finance.FlowTypeEnum;
import com.feidi.xx.cross.common.enums.order.*;
import com.feidi.xx.cross.common.helper.OrdOrderOperateHelper;
import com.feidi.xx.cross.common.mq.event.OrdOrderOperateEvent;
import com.feidi.xx.cross.common.mq.producer.OrdOrderOperateProducer;
import com.feidi.xx.cross.common.utils.ExceptionUtil;
import com.feidi.xx.cross.finance.api.RemoteFlowService;
import com.feidi.xx.cross.finance.api.domain.bo.RemoteFlowBo;
import com.feidi.xx.cross.finance.api.domain.vo.RemoteFlowVo;
import com.feidi.xx.cross.message.api.RemoteImService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderInfoVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderRateVo;
import com.feidi.xx.cross.order.api.domain.vo.RemotePositionVo;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.OrdOrderInfo;
import com.feidi.xx.cross.order.domain.OrdRate;
import com.feidi.xx.cross.order.domain.bo.OrdOrderProfitBo;
import com.feidi.xx.cross.order.domain.bo.driver.OrdOrderDrvQueryBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderHandleBo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderProfitVo;
import com.feidi.xx.cross.order.domain.vo.driver.OrdOrderDrvDetailVo;
import com.feidi.xx.cross.order.domain.vo.driver.OrdOrderDrvIndexVo;
import com.feidi.xx.cross.order.domain.vo.driver.OrdOrderDrvResellVo;
import com.feidi.xx.cross.order.helper.OrdOrderHelper;
import com.feidi.xx.cross.order.mapper.OrdOrderInfoMapper;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.service.IOrdOrderDrvService;
import com.feidi.xx.cross.order.service.IOrdOrderProcessService;
import com.feidi.xx.cross.order.service.IOrdRateService;
import com.feidi.xx.cross.power.api.RemoteCarService;
import com.feidi.xx.cross.power.api.RemoteDriverService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteCarVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverLineVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.system.api.RemoteConfigService;
import com.feidi.xx.system.api.RemoteDistrictService;
import com.feidi.xx.system.api.domain.bo.RemoteDistrictBo;
import com.feidi.xx.system.api.domain.vo.RemoteDistrictVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 司机端-订单服务接口实现类
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrdOrderDrvServiceImpl implements IOrdOrderDrvService {

    private final OrdOrderHelper ordOrderHelper;
    private final PowCacheManager powCacheManager;
    private final OrdCacheManager ordCacheManager;
    private final IOrdRateService ordRateService;
    private final IOrdOrderProcessService ordOrderProcessService;
    private final OrdOrderMapper ordOrderMapper;
    private final OrdOrderInfoMapper ordOrderInfoMapper;
    private final OrdOrderOperateProducer ordOrderOperateProducer;
    @DubboReference
    private final RemoteConfigService remoteConfigService;
    @DubboReference
    private final RemoteDistrictService remoteDistrictService;
    @DubboReference
    private final RemoteImService remoteImService;
    @DubboReference
    private final RemoteFlowService remoteFlowService;
    @DubboReference
    private final RemoteDriverService remoteDriverService;
    @DubboReference
    private final RemoteCarService remoteCarService;

    @Override
    public OrdOrderDrvIndexVo getIndex() {
        OrdOrderDrvIndexVo indexVo = new OrdOrderDrvIndexVo();
        Date now = new Date();
        Date yesterday = DateUtil.beginOfDay(DateUtil.offsetDay(now, -1));
        Date today = DateUtil.endOfDay(now);
        // 昨天和今天订单
        List<OrdOrder> orders = ordOrderMapper.selectList(Wrappers.<OrdOrder>lambdaQuery()
                .eq(OrdOrder::getDriverId, LoginHelper.getUserId())
                .nested(lqw -> lqw.between(OrdOrder::getReceiveTime, yesterday, today)
                        .or().between(OrdOrder::getFinishTime, yesterday, today)));
        // 接单
        List<OrdOrder> receivedOrders = orders.stream().filter(o -> ObjectUtil.isNotNull(o.getReceiveTime())).toList();
        Map<String, List<OrdOrder>> receivedMap = receivedOrders.stream().collect(Collectors.groupingBy(o -> DateUtil.formatDate(DateUtil.date(o.getReceiveTime()))));
        indexVo.setOrderToday(receivedMap.getOrDefault(DateUtil.formatDate(today), Collections.emptyList()).size());
        indexVo.setOrderYesterday(receivedMap.getOrDefault(DateUtil.formatDate(yesterday), Collections.emptyList()).size());
        // 完单
        List<OrdOrder> finishedOrders = orders.stream().filter(o -> ObjectUtil.isNotNull(o.getFinishTime()) && OrderStatusEnum.FINISH.getCode().equals(o.getStatus())).toList();
        Map<String, List<OrdOrder>> finishedMap = finishedOrders.stream().collect(Collectors.groupingBy(o -> DateUtil.formatDate(DateUtil.date(o.getFinishTime()))));
        Set<Long> finishedOrderIds = finishedOrders.stream().map(OrdOrder::getId).collect(Collectors.toSet());
        // 司机订单费率
        List<OrdRate> ordRates = ordRateService.queryByOrderIdsAndRateType(ListUtil.toList(finishedOrderIds), RateTypeEnum.DRIVER.getCode());
        Map<String, List<OrdRate>> finishedRateMap = ordRates.stream().collect(Collectors.groupingBy(o ->{
            Date finishTime = finishedOrders.stream()
                    .filter(e -> Objects.equals(e.getId(), o.getOrderId())).findFirst().map(OrdOrder::getFinishTime)
                    .orElseThrow(() -> new ServiceException(String.format("订单%s账单不存在", o.getOrderId())));
            return  DateUtil.formatDate(DateUtil.date(finishTime));
        }));

        /*List<OrdRate> ordInviteRates = ordRateService.queryByOrderIdsAndRateType(ListUtil.toList(finishedOrderIds), RateTypeEnum.INVITE_DRIVER.getCode());
        Map<String, List<OrdRate>> finishedInviteRateMap = ordInviteRates.stream()
                .filter(e-> Objects.equals(e.getUserId(), LoginHelper.getUserId()))
                .collect(Collectors.groupingBy(o -> DateUtil.formatDate(DateUtil.date(o.getCreateTime()))));*/

        // 昨日完单
        List<OrdOrder> finishedYesterday = finishedMap.getOrDefault(DateUtil.formatDate(yesterday), Collections.emptyList());
        indexVo.setOrderFinYesterday(finishedYesterday.size());

        List<OrdRate> finishedYesterdayRate = finishedRateMap.getOrDefault(DateUtil.formatDate(yesterday), Collections.emptyList());
        //List<OrdRate> finishedYesterdayInviteRate = finishedInviteRateMap.getOrDefault(DateUtil.formatDate(yesterday), Collections.emptyList());
        indexVo.setProfitYesterday(ArithUtils.reduceAdd(finishedYesterdayRate, OrdRate::getAmount));
        // 今日完单
        List<OrdOrder> finishedToday = finishedMap.getOrDefault(DateUtil.formatDate(today), Collections.emptyList());
        indexVo.setOrderFinToday(finishedToday.size());

        List<OrdRate> finishedTodayRate = finishedRateMap.getOrDefault(DateUtil.formatDate(today), Collections.emptyList());
        //List<OrdRate> finishedTodayInviteRate = finishedInviteRateMap.getOrDefault(DateUtil.formatDate(today), Collections.emptyList());
        indexVo.setProfitToday(ArithUtils.reduceAdd(finishedTodayRate, OrdRate::getAmount));
        return indexVo;
    }

    /**
     * 订单转卖统计
     *
     * @return
     */
    @Override
    public OrdOrderDrvResellVo getResellIndex() {
        OrdOrderDrvResellVo drvResellVo = new OrdOrderDrvResellVo();

        LambdaQueryWrapper<OrdOrder> queryWrapper = Wrappers.<OrdOrder>lambdaQuery()
                .eq(OrdOrder::getResellDriverId, LoginHelper.getUserId())
                .eq(OrdOrder::getComplain, IsYesEnum.NO.getCode())
                .ne(OrdOrder::getStatus, OrderStatusEnum.CANCEL.getCode());

        List<OrdOrder> ordOrders = ordOrderMapper.selectList(queryWrapper);

        // 未卖出订单
        List<OrdOrder> unsoldOrders = ordOrders.stream().filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.CREATE.getCode())).toList();

        // 已卖出订单
        List<OrdOrder> saleOrders = ordOrders.stream().filter(e -> !Objects.equals(e.getStatus(), OrderStatusEnum.CREATE.getCode())).toList();


        drvResellVo.setUnsoldNum(CollUtils.isNotEmpty(unsoldOrders) ? unsoldOrders.size() : 0L);
        drvResellVo.setSaleNum(CollUtils.isNotEmpty(saleOrders) ? saleOrders.size() : 0L);
        drvResellVo.setUnsoldProfit(0L);
        drvResellVo.setSaleProfit(0L);

        // 计算收益
        if (CollUtils.isNotEmpty(saleOrders)) {
            // 已经计算的订单id
            List<Long> finishRebateIds = saleOrders.stream().filter(e -> Objects.equals(e.getResellRebateStatus(), RebateStatusEnum.FINISH.getCode()))
                    .map(OrdOrder::getId).toList();

            // 计算中的订单id
            List<Long> rebatingIds = saleOrders.stream().filter(e -> Objects.equals(e.getResellRebateStatus(), RebateStatusEnum.ING.getCode()))
                    .map(OrdOrder::getId).toList();


            if (CollUtils.isNotEmpty(finishRebateIds)) {
                RemoteFlowBo remoteFlowBo = new RemoteFlowBo();
                remoteFlowBo.setJoinIds(finishRebateIds);
                remoteFlowBo.setJoinTable(JoinEnum.ORDER.getCode());
                remoteFlowBo.setType(FlowTypeEnum.RESELL_ORDER_ADD.getCode());
                List<RemoteFlowVo> finishRebateFlows = remoteFlowService.listByBo(remoteFlowBo);
                drvResellVo.setSaleProfit(ArithUtils.reduceAdd(finishRebateFlows, RemoteFlowVo::getAmount));
            }

            if (CollUtils.isNotEmpty(rebatingIds)) {
                RemoteFlowBo remoteFlowBo = new RemoteFlowBo();
                remoteFlowBo.setJoinIds(rebatingIds);
                remoteFlowBo.setJoinTable(JoinEnum.ORDER.getCode());
                remoteFlowBo.setType(FlowTypeEnum.RESELL_ORDER_FREEZE_ADD.getCode());
                List<RemoteFlowVo> rebatingFlows = remoteFlowService.listByBo(remoteFlowBo);
                drvResellVo.setUnsoldProfit(ArithUtils.reduceAdd(rebatingFlows, RemoteFlowVo::getAmount));
            }
        }

        return drvResellVo;
    }

    @Override
    public Long newNum(OrdOrderDrvQueryBo queryBo) {
        if (validateBeforeQuery(queryBo)) {
            return queryList(queryBo).getTotal();
        }
        return 0L;
    }

    @Override
    public TableDataInfo<OrdOrderDrvDetailVo> pool(OrdOrderDrvQueryBo queryBo) {
        if (validateBeforeQuery(queryBo)) {
            TableDataInfo<OrdOrderDrvDetailVo> tableDataInfo = TableDataInfo.build();
            List<OrdOrderDrvDetailVo> records;

            // 处理城市数据
            if (StringUtils.isNotBlank(queryBo.getStartCity()) || StringUtils.isNotBlank(queryBo.getEndCity())) {
                this.handleCityData(queryBo);
            }
            if (queryBo.getMinPrice() == null && queryBo.getMaxPrice() == null) {
                IPage<OrdOrderDrvDetailVo> iPage = TenantHelper.ignore(() -> ordOrderMapper.queryList(queryBo, queryBo.build()));
                records = Collections.synchronizedList(iPage.getRecords());
            } else {
                records = TenantHelper.ignore(() -> Collections.synchronizedList(ordOrderMapper.queryList(queryBo)));
            }

            if (CollUtil.isNotEmpty(records)) {
                // 订单池需要手动计算价格并筛选
                if (queryBo.getIsPool() != null && queryBo.getIsPool()) {
                    records.parallelStream().forEach(e -> {
                        // 只需要转入司机id即可，剩下的内部计算
                        e.setDriverId(queryBo.getDriverId());
                        e.setAgentId(queryBo.getAgentId());
                        OrdOrderProfitBo cxProfitBo = ordOrderHelper.createOrderProfitBo(BeanUtils.copyProperties(e, OrdOrder.class));
                        OrdOrderProfitVo orderProfit = ordOrderHelper.calculateOrderProfit(cxProfitBo);
                        e.setDriverProfit(orderProfit.getDriverProfit());
                        e.setDriverInviteReward(orderProfit.getDriverInviteReward());
                    });
                    // TODO-NEW 手动过滤价格
                    if (queryBo.getMinPrice() != null) {
                        records = records.parallelStream().filter(e -> e.getDriverProfit() >= queryBo.getMinPrice()).collect(Collectors.toList());
                    }
                    if (queryBo.getMaxPrice() != null) {
                        records = records.parallelStream().filter(e -> e.getDriverProfit() <= queryBo.getMaxPrice()).collect(Collectors.toList());
                    }
                }
                // 手动分页
                if (records.size() > queryBo.getPageSize()) {
                    records = PaginationUtil.paginate(records, queryBo.getPageNum(), queryBo.getPageSize());
                }
                tableDataInfo = TableDataInfo.build(records);
            }
            Map<String, Object> extra = Collections.singletonMap("lastTime", new Date());
            tableDataInfo.setExtra(extra);
            return tableDataInfo;
        }
        return TableDataInfo.build();
    }

    @Override
    public TableDataInfo<OrdOrderDrvDetailVo> queryList(OrdOrderDrvQueryBo queryBo) {
        if (OrdOrderDrvQueryBo.ServiceType.PROCESSING.equals(queryBo.getServiceType())) {
            queryBo.setStatuses(OrderStatusEnum.getProcessingCodes());
        } else if (OrdOrderDrvQueryBo.ServiceType.WAITING.equals(queryBo.getServiceType())) {
            queryBo.setStatuses(OrderStatusEnum.getWaitingCodes());
        } else if (OrdOrderDrvQueryBo.ServiceType.CANCEL.equals(queryBo.getServiceType())) {
            queryBo.setStatuses(OrderStatusEnum.getCancelCodes());
        } else if (OrdOrderDrvQueryBo.ServiceType.FINISH.equals(queryBo.getServiceType())) {
            queryBo.setStatuses(OrderStatusEnum.getFinishCodes());
        } else if (OrdOrderDrvQueryBo.ServiceType.CREATED.equals(queryBo.getServiceType())) {
            queryBo.setStatuses(OrderStatusEnum.getCreateCodes());
        }
        return TenantHelper.ignore(() -> {
            IPage<OrdOrderDrvDetailVo> iPage = ordOrderMapper.queryList(queryBo, queryBo.build());
            List<OrdOrderDrvDetailVo> records = iPage.getRecords();
            Map<Long, OrdOrderInfo> id2infoMap = new HashMap<>();
            List<Long> ids = records.stream().map(OrdOrderDrvDetailVo::getId).collect(Collectors.toList());;
            if (CollUtil.isNotEmpty(ids)) {
                id2infoMap = ordOrderInfoMapper.selectList(new LambdaQueryWrapper<OrdOrderInfo>().in(OrdOrderInfo::getId, ids))
                        .stream().collect(Collectors.toMap(OrdOrderInfo::getId, Function.identity()));
            }

            if (CollUtil.isNotEmpty(records)) {
                Map<Long, OrdOrderInfo> finalId2infoMap = id2infoMap;
                records.forEach(ordOrderVo -> {
                    // 司机收益
                    if (ObjectUtils.isNull(ordOrderVo.getDriverId()) || ordOrderVo.getDriverId() == 0) {
                        // 订单转卖不需要计算收益
                        if (!Objects.equals(ordOrderVo.getCreateModel(), CreateModelEnum.RESELL_ORDER.getCode())) {
                            OrdOrderProfitBo cxProfitBo = ordOrderHelper.createOrderProfitBo(BeanUtils.copyProperties(ordOrderVo, OrdOrder.class));
                            OrdOrderProfitVo orderProfit = ordOrderHelper.calculateOrderProfit(cxProfitBo);
                            ordOrderVo.setDriverProfit(orderProfit.getDriverProfit());
                            ordOrderVo.setDriverInviteReward(orderProfit.getDriverInviteReward());
                        }
                    } else {
                        RemoteOrderRateVo driverOrderRateVo = ordCacheManager.getOrderRateInfoByOrderIdAndRateType(ordOrderVo.getId(), RateTypeEnum.DRIVER.getCode());
                        ordOrderVo.setDriverProfit(ObjectUtils.isNotNull(driverOrderRateVo) ? driverOrderRateVo.getAmount() : null);
                        RemoteOrderRateVo inviteDriverOrderRateVo = ordCacheManager.getOrderRateInfoByOrderIdAndRateType(ordOrderVo.getId(), RateTypeEnum.INVITE_DRIVER.getCode());
                        ordOrderVo.setDriverInviteReward(ObjectUtils.isNotNull(inviteDriverOrderRateVo) ? inviteDriverOrderRateVo.getAmount() : null);
                    }
                    // 优惠券额度
                    if (finalId2infoMap.containsKey(ordOrderVo.getId())) {
                        ordOrderVo.setCouponGrantQuota(finalId2infoMap.get(ordOrderVo.getId()).getCouponGrantQuota());
                    }
                });

                // 订单转卖
                if (Objects.equals(queryBo.getResell(), 0)) {
                    List<Long> driverIds = records.stream().filter(e -> ObjectUtils.isNotNull(e.getDriverId()) && e.getDriverId() > 0)
                            .map(OrdOrderDrvDetailVo::getDriverId)
                            .collect(Collectors.toList());;
                    if (CollUtil.isNotEmpty(driverIds)) {
                        Map<Long, RemoteDriverVo> driverInfoMap = remoteDriverService.getDriverInfo(driverIds).stream().collect(Collectors.toMap(RemoteDriverVo::getId, Function.identity()));
                        Map<Long, String> driverId2CarNumberMap = remoteCarService.queryByDriverIds(driverIds).stream().collect(Collectors.toMap(RemoteCarVo::getDriverId, RemoteCarVo::getCarNumber, (v1, v2) -> v2));
                        records.forEach(ordOrderVo -> {
                            RemoteDriverVo driverInfo = driverInfoMap.get(ordOrderVo.getDriverId());
                            if (ObjectUtils.isNotNull(driverInfo)) {
                                ordOrderVo.setDriverName(driverInfo.getName().charAt(0) + "师傅");
                                ordOrderVo.setDriverPhone(driverInfo.getPhone());
                            }
                            if (driverId2CarNumberMap.containsKey(ordOrderVo.getDriverId())) {
                                ordOrderVo.setCarNumber(driverId2CarNumberMap.get(ordOrderVo.getDriverId()));
                            }
                        });
                        // 按照创建时间倒序排列
                        records = records.stream().sorted(Comparator.comparing(OrdOrderDrvDetailVo::getCreateTime).reversed()).collect(Collectors.toList());
                    }
                }

                return TableDataInfo.build(records, iPage.getTotal());
            }
            return TableDataInfo.build(new ArrayList<>(), 0L);
        });
    }

    @Override
    public R<Object> queryDetail(OrdOrderDrvQueryBo bo) {
        OrdOrderDrvDetailVo ordOrderVo = ordOrderMapper.queryDetail(bo);

        // 司机权限校验
        if ((ordOrderVo.getDriverId() != null && ordOrderVo.getDriverId() > 0)
                && !Objects.equals(ordOrderVo.getDriverId(), LoginHelper.getUserId())) {
            log.error("非法操作！无法查看其他司机订单");
            return R.ok("订单已失效", "订单已失效");
        }

        // 司机收益
        if (ObjectUtils.isNull(ordOrderVo.getDriverId()) || ordOrderVo.getDriverId() == 0) {
            OrdOrderProfitBo cxProfitBo = ordOrderHelper.createOrderProfitBo(BeanUtils.copyProperties(ordOrderVo, OrdOrder.class));
            cxProfitBo.setAgentId(bo.getAgentId())
                    .setDriverId(bo.getDriverId());
            OrdOrderProfitVo orderProfit = ordOrderHelper.calculateOrderProfit(cxProfitBo);
            ordOrderVo.setDriverProfit(orderProfit.getDriverProfit());
            ordOrderVo.setDriverInviteReward(orderProfit.getDriverInviteReward());
        } else {
            RemoteOrderRateVo driverOrderRateVo = ordCacheManager.getOrderRateInfoByOrderIdAndRateType(ordOrderVo.getId(), RateTypeEnum.DRIVER.getCode());
            ordOrderVo.setDriverProfit(ObjectUtils.isNotNull(driverOrderRateVo) ? driverOrderRateVo.getAmount() : null);
            RemoteOrderRateVo inviteDriverOrderRateVo = ordCacheManager.getOrderRateInfoByOrderIdAndRateType(ordOrderVo.getId(), RateTypeEnum.INVITE_DRIVER.getCode());
            ordOrderVo.setDriverInviteReward(ObjectUtils.isNotNull(inviteDriverOrderRateVo) ? inviteDriverOrderRateVo.getAmount() : null);
        }

        // 没有调度号使用真实手机号
        //ordOrderVo.setPhone(StrUtil.isNotBlank(ordOrderVo.getVirtualPhone()) ? ordOrderVo.getVirtualPhone() : ordOrderVo.getPassengerPhone());
        if (ordOrderVo.getDriverId() != null && ordOrderVo.getDriverId() > 0) {
            //填充司机手机号
            RemoteDriverVo driverInfoById = powCacheManager.getDriverInfoById(ordOrderVo.getDriverId());
            ordOrderVo.setDriverPhone(driverInfoById != null ? driverInfoById.getPhone() : null);
        }

        // 美团获取乘客手机号
        if (Objects.equals(ordOrderVo.getPlatformCode(), PlatformEnum.MT.getCode())) {
            this.getMtVirtualPhone(ordOrderVo);
        }

        // 从系统配置中获取紧急呼叫电话
        ordOrderVo.setEmergencyCall(remoteConfigService.selectValueByKey(OrderConstants.ORDER_EMERGENCY_PHONE));

        // 判断按钮状态
        ordOrderVo.setButtonStatus(this.judgeButtonStatus(ordOrderVo));

        // 绑定订单位置信息
        this.bindOrderPosition(Collections.singletonList(ordOrderVo));

        // 优惠券额度
        RemoteOrderInfoVo ordOrderInfo = ordCacheManager.getOrderSubInfoByOrderId(ordOrderVo.getId());
        if (ObjectUtils.isNotNull(ordOrderInfo)) {
            ordOrderVo.setCouponGrantQuota(ordOrderInfo.getCouponGrantQuota());
        }

        // 未读数量
        ExceptionUtil.ignoreEx(() -> {
            Integer unreadNum = remoteImService.getUnreadNum(ordOrderVo.getId());
            ordOrderVo.setUnreadNum(unreadNum);
        });

        return R.ok(ordOrderVo);
    }

    @Override
    public R<String> checkPhoneEnd(OrdOrderHandleBo handleBo) {
        // 订单关联信息
        RemoteOrderInfoVo orderInfoVo = ordCacheManager.getOrderSubInfoByOrderId(handleBo.getOrderId());

        // 是否达到最大验证次数
        String errorKey = OrderConstants.ORDER_CHECK_PHONE_END + handleBo.getOrderId() + ":" + handleBo.getUserId();
        Integer errorNumber = ObjectUtil.defaultIfNull(RedisUtils.getCacheObject(errorKey), 0);
        if (errorNumber >= OrderConstants.ORDER_CHECK_ERROR_MAX_NUMBER) {
            return R.ok("请提醒乘客点击“已上车”，以确保行程正常开始");
        }

        // 验证手机尾号
        if (!Objects.equals(orderInfoVo.getPhoneEnd(), handleBo.getPhoneEnd())) {
            errorNumber++;
            RedisUtils.setCacheObject(errorKey, errorNumber, Duration.ofMinutes(OrderConstants.ORDER_CHECK_PHONE_END_TIME));
            return R.ok("验证失败");
        }

        // 验证成功 清空错误次数
        RedisUtils.deleteObject(errorKey);
        return R.ok();
    }

    @Override
    public boolean makeCall(OrdOrderHandleBo handleBo) {
        // 操作记录
        OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildOrderOperateEvent(handleBo.getOrderId(), OperateTypeEnum.CONTACT_PASSENGER.getCode(), JsonUtils.toJsonString(handleBo));
        operateEvent.setRemark(handleBo.getRemark());
        operateEvent.setResponseJson(JsonUtils.toJsonString(true));
        ordOrderOperateProducer.sendMessage(operateEvent);
        return true;
    }

    private boolean validateBeforeQuery(OrdOrderDrvQueryBo queryBo) {
        // 通过缓存查询司机信息
        RemoteDriverVo driverInfo = powCacheManager.getDriverInfoById(queryBo.getDriverId());
        Assert.notNull(driverInfo, "系统错误，司机信息获取失败");
        if (!Objects.equals(driverInfo.getStatus(), StatusEnum.ENABLE.getCode())) {
            return false;
        }
        queryBo.setAgentId(driverInfo.getAgentId());

        if (ArithUtils.isNotNull(driverInfo.getAgentId())) {
            // 代理商信息
            RemoteAgentVo agentInfo = powCacheManager.getAgentInfoById(driverInfo.getAgentId());
            Assert.notNull(agentInfo, "系统错误，代理商信息获取失败");
            if (!StatusEnum.ENABLE.getCode().equals(agentInfo.getStatus())) {
                return false;
            }

            if (agentInfo.getParentId() != null && agentInfo.getParentId() != 0L) {
                RemoteAgentVo parentAgentInfo = powCacheManager.getAgentInfoById(agentInfo.getParentId());
                Assert.notNull(agentInfo, "系统错误，上级代理商信息获取失败");
                if (!StatusEnum.ENABLE.getCode().equals(parentAgentInfo.getStatus())) {
                    return false;
                }
                queryBo.setParentId(parentAgentInfo.getId());
            }
        }

        List<RemoteDriverLineVo> driverLineVos = powCacheManager.getDriverLineByDriverId(queryBo.getDriverId());

        if (CollUtil.isNotEmpty(driverLineVos)) {
            List<Long> lineIds = new ArrayList<>(driverLineVos.stream().map(RemoteDriverLineVo::getLineId).toList());
            // 线路筛选
            Boolean unused = CollUtil.isEmpty(queryBo.getLineIds()) ? null : lineIds.retainAll(queryBo.getLineIds());
            queryBo.setLineIds(lineIds);
        }

        if (ArithUtils.isNotNull(driverInfo.getAgentId())) {
            // 没有线路的返回空
            if (CollUtil.isEmpty(queryBo.getLineIds())) {
                return false;
            }
        }

        if (CollUtil.isEmpty(queryBo.getDateTimes())) {
            List<StartEndTime> dateTimes = new ArrayList<>();
            if (StrUtil.isNotBlank(queryBo.getDate()) && CollUtil.isNotEmpty(queryBo.getTime())) {
                for (String time : queryBo.getTime()) {
                    List<String> times = StrUtil.split(StrUtil.cleanBlank(time), "-");
                    Assert.isTrue(times.size() == 2, "时间格式错误");
                    StartEndTime startEndTime = new StartEndTime();
                    startEndTime.setStartTime(DateUtil.parse(queryBo.getDate() + " " + times.get(0)));
                    startEndTime.setEndTime(DateUtil.parse(queryBo.getDate() + " " + times.get(1)));
                    dateTimes.add(startEndTime);
                }
            }
            queryBo.setDateTimes(dateTimes);
        }
        return true;
    }

    /**
     * 美团获取乘客手机号
     * 美团建立“司机-虚拟号-乘客”的绑定关系，并返回虚拟号，单次绑定时长30分钟
     *
     * @param orderDetailVo 订单详情
     */
    private void getMtVirtualPhone(OrdOrderDrvDetailVo orderDetailVo) {
        // 不需要查询手机号的订单状态
        List<String> orderStatuses = Stream.of(OrderStatusEnum.CANCEL.getCode(), OrderStatusEnum.CREATE.getCode()).toList();

        if (orderStatuses.contains(orderDetailVo.getStatus())) {
            return;
        }

        String cacheKey = PlatformCacheKeyEnum.PLATFORM_VIRTUAL_PHONE_KEY.create(orderDetailVo.getId());
        if (RedisUtils.hasKey(cacheKey)) {
            orderDetailVo.setPhone(RedisUtils.getCacheObject(cacheKey));
        } else {
            // 虚拟号不存在，重新获取
            try {
                String virtualPhone = ordOrderProcessService.getVirtualPhone(orderDetailVo.getId(), orderDetailVo.getPlatformNo());
                if (StringUtils.isNotBlank(virtualPhone)) {
                    orderDetailVo.setPhone(virtualPhone);
                }
            } catch (Exception e) {
                log.error("美团获取乘客手机号失败，{}", e.getMessage());
            }
        }
    }

    /**
     * 判断按钮状态
     *
     * @param orderDetailVo
     */
    private String judgeButtonStatus(OrdOrderDrvDetailVo orderDetailVo) {

        if (Objects.equals(orderDetailVo.getStatus(), OrderStatusEnum.CANCEL.getCode())
                || Objects.equals(orderDetailVo.getStatus(), OrderStatusEnum.FINISH.getCode())) {
            // 订单完成或已取消
            return StatusEnum.DISABLE.getCode();
        }

        if (Objects.equals(orderDetailVo.getPlatformCode(), PlatformCodeEnum.MT.getCode())) {
            // 订单未支付，且已分配司机时，按钮置灰
            if (!Objects.equals(orderDetailVo.getPayStatus(), PaymentStatusEnum.SUCCESS.getCode())
                    && !Objects.equals(orderDetailVo.getStatus(), OrderStatusEnum.CREATE.getCode())) {
                return StatusEnum.DISABLE.getCode();
            }
            // 到达起点
            if (Objects.equals(orderDetailVo.getStatus(), OrderStatusEnum.PICK_START.getCode())) {
                String errorKey = OrderConstants.ORDER_CHECK_PHONE_END + orderDetailVo.getId() + ":" + LoginHelper.getUserId();
                Integer errorNumber = ObjectUtil.defaultIfNull(RedisUtils.getCacheObject(errorKey), 0);
                // 手机尾号验证超过最大次数
                if (errorNumber >= OrderConstants.ORDER_CHECK_ERROR_MAX_NUMBER) {
                    return StatusEnum.DISABLE.getCode();
                }
            }
        } else if (Objects.equals(orderDetailVo.getPlatformCode(), PlatformEnum.SELF.getCode())) {
            if (Objects.equals(LoginHelper.getUserType(), UserTypeEnum.DRIVER_USER)) {
                if (!Objects.equals(orderDetailVo.getPayStatus(), PaymentStatusEnum.SUCCESS.getCode())
                        && (Objects.equals(orderDetailVo.getStatus(), OrderStatusEnum.ING.getCode())
                        || Objects.equals(orderDetailVo.getStatus(), OrderStatusEnum.PICK_START.getCode()))) {
                    return StatusEnum.DISABLE.getCode();
                }
            }
        }

        return StatusEnum.ENABLE.getCode();
    }

    /**
     * 绑定订单位置
     *
     * @param orderVos 订单列表
     */
    public void bindOrderPosition(List<OrdOrderDrvDetailVo> orderVos) {
        if (CollUtils.isEmpty(orderVos)) {
            return;
        }
        orderVos.forEach(e -> {
            RemotePositionVo startPositionVo = ordCacheManager.getOrderPositionByOrderIdAndType(e.getId(), StartEndEnum.START.getCode());
            if (ObjectUtil.isNotNull(startPositionVo)) {
                e.setStartProvince(startPositionVo.getProvince());
                e.setStartCity(startPositionVo.getCity());
                e.setStartDistrict(startPositionVo.getDistrict());
                e.setStartAddr(startPositionVo.getShortAddr());
                e.setStartLongitude(Double.parseDouble(startPositionVo.getLongitude()));
                e.setStartLatitude(Double.parseDouble(startPositionVo.getLatitude()));
            }
            RemotePositionVo endPositionVo = ordCacheManager.getOrderPositionByOrderIdAndType(e.getId(), StartEndEnum.END.getCode());
            if (ObjectUtil.isNotNull(endPositionVo)) {
                e.setEndProvince(endPositionVo.getProvince());
                e.setEndCity(endPositionVo.getCity());
                e.setEndDistrict(endPositionVo.getDistrict());
                e.setEndAddr(endPositionVo.getShortAddr());
                e.setEndLongitude(Double.parseDouble(endPositionVo.getLongitude()));
                e.setEndLatitude(Double.parseDouble(endPositionVo.getLatitude()));
            }
        });
    }

    /**
     * 处理查询参数中的城市数据
     *
     * @param queryBo 查询参数
     */
    private void handleCityData(OrdOrderDrvQueryBo queryBo) {
        if (StringUtils.isNotBlank(queryBo.getStartCity())) {
            RemoteDistrictBo districtBo = new RemoteDistrictBo();
            districtBo.setName(queryBo.getStartCity());
            districtBo.setLevel("city");
            List<RemoteDistrictVo> remoteDistrictVos = remoteDistrictService.queryByBo(districtBo);
            if (CollUtil.isNotEmpty(remoteDistrictVos)) {
                queryBo.setStartCity(remoteDistrictVos.get(0).getCityCode());
            }
        }
        if (StringUtils.isNotBlank(queryBo.getEndCity())) {
            RemoteDistrictBo districtBo = new RemoteDistrictBo();
            districtBo.setName(queryBo.getEndCity());
            districtBo.setLevel("city");
            List<RemoteDistrictVo> remoteDistrictVos = remoteDistrictService.queryByBo(districtBo);
            if (CollUtil.isNotEmpty(remoteDistrictVos)) {
                queryBo.setEndCity(remoteDistrictVos.get(0).getCityCode());
            }
        }
    }
}
