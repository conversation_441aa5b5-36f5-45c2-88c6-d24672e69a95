package com.feidi.xx.cross.order.mq.producer;

import cn.hutool.core.date.DateUtil;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.rocketmq.base.BaseSendExtendDTO;
import com.feidi.xx.common.rocketmq.enums.MqMessageTypeEnum;
import com.feidi.xx.common.rocketmq.producer.AbstractProducer;
import com.feidi.xx.common.rocketmq.util.MQMessageUtil;
import com.feidi.xx.cross.common.constant.order.OrderRocketMQConstant;
import com.feidi.xx.cross.order.mq.event.MtVirtualPhoneEvent;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.stereotype.Component;

/**
 * 美团虚拟电话消息消费者
 *
 * <AUTHOR>
 * @date 2025/3/22
 */
@Component
@RequiredArgsConstructor
public class MtVirtualPhoneProducer extends AbstractProducer<MtVirtualPhoneEvent> {
    private BaseSendExtendDTO buildBaseSendExtendParam(MtVirtualPhoneEvent virtualPhoneEvent) {

        return BaseSendExtendDTO.builder()
                .eventName("美团虚拟电话")
                .keys(virtualPhoneEvent.getOrderId().toString())
                .tag(virtualPhoneEvent.getOrderId().toString())
                .messageType(MqMessageTypeEnum.ASYNC)
                .topic(OrderRocketMQConstant.XX_ORDER_MT_VIRTUAL_PHONE_TOPIC_KEY)
                .sentTimeout(2000L)
                .delayTime(DateUtil.offsetSecond(DateUtils.getNowDate(),5).getTime())
                .build();
    }
    @Override
    public SendResult sendMessage(MtVirtualPhoneEvent virtualPhoneEvent) {
        return MQMessageUtil.sendMessage(virtualPhoneEvent, this::buildBaseSendExtendParam);
    }
}

