package com.feidi.xx.cross.order.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.order.domain.bo.OrdDriverEvaluationBo;
import com.feidi.xx.cross.order.domain.vo.OrdDriverEvaluationVo;

import java.util.Collection;
import java.util.List;

/**
 * 司机-行程评价Service接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IOrdDriverEvaluationService {

    /**
     * 查询司机-行程评价
     *
     * @param id 主键
     * @return 司机-行程评价
     */
    OrdDriverEvaluationVo queryById(Long id);

    /**
     * 分页查询司机-行程评价列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机-行程评价分页列表
     */
    TableDataInfo<OrdDriverEvaluationVo> queryPageList(OrdDriverEvaluationBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的司机-行程评价列表
     *
     * @param bo 查询条件
     * @return 司机-行程评价列表
     */
    List<OrdDriverEvaluationVo> queryList(OrdDriverEvaluationBo bo);

    /**
     * 新增司机-行程评价
     *
     * @param bo 司机-行程评价
     * @return 是否新增成功
     */
    Boolean insertByBo(OrdDriverEvaluationBo bo);

    /**
     * 修改司机-行程评价
     *
     * @param bo 司机-行程评价
     * @return 是否修改成功
     */
    Boolean updateByBo(OrdDriverEvaluationBo bo);

    /**
     * 校验并批量删除司机-行程评价信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
