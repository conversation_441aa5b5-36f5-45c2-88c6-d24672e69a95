package com.feidi.xx.cross.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 订单统计视图对象 ord_statistic
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrdStatisticVo.class)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
public class OrdStatisticExportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "日期")
    private String date;

    @ExcelProperty(value = "完单金额（元）")
    @ReverseAutoMapping(target = "finishAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getFinishAmount()))")
    private String finishAmount;

    @ExcelProperty(value = "完单数量")
    private Long finishNumber = 0L;

    @ExcelProperty(value = "支付金额（元）")
    @ReverseAutoMapping(target = "payAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getPayAmount()))")
    private String payAmount;

    @ExcelProperty(value = "支付订单数")
    private Long payNumber = 0L;

    @ExcelProperty(value = "下单数")
    private Long placeNumber = 0L;

    @ExcelProperty(value = "完单率")
    @ReverseAutoMapping(target = "receiveFinishRate", expression = "java(source.getReceiveFinishRate() + \"%\")")
    private String receiveFinishRate;

    @ExcelProperty(value = "新增乘客注册数")
    private Long passengerRegisterNumber = 0L;

    @ExcelProperty(value = "取消订单数")
    private Long cancelNumber = 0L;
}
