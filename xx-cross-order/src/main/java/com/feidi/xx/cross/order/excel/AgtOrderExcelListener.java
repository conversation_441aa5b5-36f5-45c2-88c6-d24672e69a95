package com.feidi.xx.cross.order.excel;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.exception.ExcelDataConvertException;

import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.core.utils.ValidatorUtils;

import com.feidi.xx.common.excel.core.ExcelListener;
import com.feidi.xx.common.excel.core.ExcelResult;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.map.model.tencent.geo.TxMapGeocode;
import com.feidi.xx.common.map.model.tencent.regeo.TxMapReGeocode;
import com.feidi.xx.common.map.utils.TxMapUtils;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.operate.api.RemotePriceService;
import com.feidi.xx.cross.operate.api.domain.price.bo.RemoteCalculateBo;
import com.feidi.xx.cross.operate.api.domain.price.vo.RemoteCalculateVo;
import com.feidi.xx.cross.order.domain.bo.AgtOrderImportBo;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;


import java.util.Date;
import java.util.Map;
import java.util.Set;

/**
 * Excel 导入监听
 *
 * <AUTHOR>
 * <AUTHOR> Li
 */
@Slf4j
@NoArgsConstructor
public class AgtOrderExcelListener<T> extends AnalysisEventListener<T> implements ExcelListener<T> {

    /**
     * 是否Validator检验，默认为是
     */
    private Boolean isValidate = Boolean.TRUE;

    /**
     * excel 表头数据
     */
    private Map<Integer, String> headMap;

    private RemotePriceService remotePriceService;

    private Integer waitTime;
    /**
     * 导入回执
     */
    private ErrorExcelResult<T> excelResult;

    public AgtOrderExcelListener(boolean isValidate, RemotePriceService remotePriceService, int waitTime) {
        this.remotePriceService = remotePriceService;
        this.excelResult = new ErrorExcelResult<>();
        this.isValidate = isValidate;
        this.waitTime = waitTime;
    }

    /**
     * 处理异常
     *
     * @param exception ExcelDataConvertException
     * @param context   Excel 上下文
     */
    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        String errMsg = null;
        if (exception instanceof ExcelDataConvertException excelDataConvertException) {
            // 如果是某一个单元格的转换异常 能获取到具体行号
            Integer rowIndex = excelDataConvertException.getRowIndex();
            Integer columnIndex = excelDataConvertException.getColumnIndex();
            errMsg = StrUtil.format("第{}行-第{}列-表头{}: 解析异常<br/>",
                    rowIndex + 1, columnIndex + 1, headMap.get(columnIndex));
            if (log.isDebugEnabled()) {
                log.error(errMsg);
            }
        }
        if (exception instanceof ConstraintViolationException constraintViolationException) {
            Set<ConstraintViolation<?>> constraintViolations = constraintViolationException.getConstraintViolations();
            String constraintViolationsMsg = StreamUtils.join(constraintViolations, ConstraintViolation::getMessage, ", ");
            errMsg = StrUtil.format("第{}行数据校验异常: {}", context.readRowHolder().getRowIndex() + 1, constraintViolationsMsg);
            if (log.isDebugEnabled()) {
                log.error(errMsg);
            }
        }
        if (!(exception instanceof ConstraintViolationException) && !(exception instanceof ExcelDataConvertException)) {
            if (log.isDebugEnabled()) {
                log.error(errMsg);
            }
            throw new ExcelAnalysisException(exception.getMessage());
        }
        excelResult.getErrorList().add(errMsg);
        excelResult.getErrorLineResults().add(new ExcelErrorLineResult<>(context.readRowHolder().getRowIndex() + 1, (T)context.readRowHolder().getCurrentRowAnalysisResult(), errMsg));
        //throw new ExcelAnalysisException(errMsg);
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        this.headMap = headMap;
        log.debug("解析到一条表头数据: {}", JsonUtils.toJsonString(headMap));
    }

    @Override
    public void invoke(T data, AnalysisContext context) {
        if (isValidate) {
            ValidatorUtils.validate(data);
        }
        //询价校验业务逻辑
        int excelRowIndex = context.readRowHolder().getRowIndex() + 1;
        RemoteCalculateVo remoteCalculateVo;
        RemoteCalculateBo assemble = assemble(excelRowIndex, data);
        if (assemble == null){
            return;
        }
        try {
            remoteCalculateVo = remotePriceService.calculatePrice(assemble);
        } catch (Exception e) {
            if (!e.getMessage().contains("ServiceException")) {
                throw e;
            }
            remoteCalculateVo = new RemoteCalculateVo();
            if (e.getMessage().contains("时间")) {
                addErrorLinResult("出发时间不在该城市运营时间范围内", excelRowIndex, data);
            } else {
                addErrorLinResult("路线不在运营范围内", excelRowIndex, data);
            }
        }

        if (!StrUtil.isEmpty(remoteCalculateVo.getEstimateKey())) {
            ((AgtOrderImportBo) data).setEstimateKey(remoteCalculateVo.getEstimateKey());
            excelResult.getList().add(data);
            excelResult.getLineResults().add(new ExcelLineResult<>(excelRowIndex, data));
        }

    }

    private void addErrorLinResult(String msg, int index, T data) {
        if (excelResult.getErrorLineResults().stream().anyMatch(i -> i.getRowIndex() == index)) {
            ExcelErrorLineResult<T> result = excelResult.getErrorLineResults().stream().filter(i -> i.getRowIndex() == index).findFirst().get();
            result.setValidationErrors(result.getValidationErrors() + ", " + msg);
        } else {
            excelResult.getErrorLineResults().add(new ExcelErrorLineResult<>(index, data, msg));
        }
    }

    private RemoteCalculateBo assemble(int index, T data) {
        if (!(data instanceof AgtOrderImportBo ibo)) {
            return null;
        }
        if (ibo.getOrderPrice().scale() > 2){
            addErrorLinResult("订单价格最多保留2位小数", index, data);
            return null;
        }

        if (StringUtils.compare(ibo.getStartAddress(), ibo.getEndAddress()) == 0){
            addErrorLinResult("乘车起点和终点不能相同", index, data);
            return null;
        }
        TxMapGeocode startGeocode = null, endGeocode = null;
        try{
            startGeocode = TxMapUtils.regeo(ibo.getStartAddress());
        }catch (Exception e){
            addErrorLinResult("乘车起点地址异常解析失败", index, data);
            return null;
        }
        try{
            endGeocode = TxMapUtils.regeo(ibo.getEndAddress());
        }catch (Exception e){
            addErrorLinResult("乘车终点地址异常解析失败", index, data);
            return null;
        }

        if (ObjectUtils.isNull(startGeocode) ) {
            addErrorLinResult("乘车起点地址异常解析失败", index, data);
            return null;
        }
        if (ObjectUtils.isNull(endGeocode) ) {
            addErrorLinResult("乘车终点地址异常解析失败", index, data);
            return null;
        }

        long startTime, endTime;
        String pattern = "yyyy/MM/dd HH:mm";
        if(ibo.getStartTime().contains("-")){
            pattern = "yyyy-MM-dd HH:mm";
        }
        Date startDate = DateUtils.dateTime(pattern, ibo.getStartTime());
        startTime = startDate.getTime() / 1000;
        if (ibo.getWaitTime() != null){
            endTime = DateUtils.addMinutes(startDate, ibo.getWaitTime()).getTime() / 1000;
        }else{
            //配置
            endTime = DateUtils.addMinutes(startDate, waitTime).getTime() / 1000;
        }

        if (DateUtils.getTimeStamps() >= endTime){
            addErrorLinResult("乘客最晚出发时间，不能小于当前时间", index, data);
            return null;
        }

        double startLat = startGeocode.getLocation().getLat();
        double startLng = startGeocode.getLocation().getLng();
        double endLat = endGeocode.getLocation().getLat();
        double endLng = endGeocode.getLocation().getLng();
        TxMapReGeocode startReGeoCode = null, endReGeoCode = null;
        try{
            startReGeoCode = TxMapUtils.regeo(String.valueOf(startLng), String.valueOf(startLat));
        }catch (Exception e){
            addErrorLinResult("乘车起点地址异常解析失败", index, data);
            return null;
        }
        try{
            endReGeoCode = TxMapUtils.regeo(String.valueOf(endLng), String.valueOf(endLat));
        }catch (Exception e){
            addErrorLinResult("乘车终点地址异常解析失败", index, data);
            return null;
        }

        RemoteCalculateBo bo = new RemoteCalculateBo();
        bo.setStartTime(startTime);
        bo.setEndTime(endTime);
        bo.setPlatformCode(PlatformCodeEnum.SELF.getCode());
        bo.setPassengerCount(ibo.getPassengerNums());
        bo.setStartAdCode(startReGeoCode.getAdInfo().getAdcode());
        bo.setStartLongitude(startLng);
        bo.setStartLatitude(startLat);
        bo.setStartAddress(ibo.getStartAddress());
        bo.setEndAddress(ibo.getEndAddress());
        bo.setStartShortAddress(ibo.getStartAddress());
        bo.setEndShortAddress(ibo.getEndAddress());
        bo.setEndAdCode(endReGeoCode.getAdInfo().getAdcode());
        bo.setEndLongitude(endGeocode.getLocation().getLng());
        bo.setEndLatitude(endGeocode.getLocation().getLat());
        return bo;
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.debug("所有数据解析完成！");
    }

    @Override
    public ExcelResult<T> getExcelResult() {
        return excelResult;
    }

}
