package com.feidi.xx.cross.order.domain.update;

import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.SuccessFailEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.order.domain.OrdOrder;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 调度更新
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrdOrder.class, reverseConvertGenerate = false)
public class DispatchDTO extends UpdateDTO {

    private Long agentId;

    private Long driverId;

    private String status;

    private String dispatch;

    private String dispatchStatus;

    private Date dispatchTime;

    private String dispatchType;

    private Date receiveTime;

    private String showed;

    /**
     * 初始化部分参数
     */
    public DispatchDTO(Long id, Integer version) {
        Date now = new Date();
        this.id = id;
        this.receiveTime = now;
        this.dispatchTime = now;
        this.dispatch = IsYesEnum.YES.getCode();
        this.dispatchStatus = SuccessFailEnum.SUCCESS.getCode();
        this.status = OrderStatusEnum.RECEIVE.getCode();
        this.oldVersion = version;
        this.newVersion = version + 1;
    }
}
