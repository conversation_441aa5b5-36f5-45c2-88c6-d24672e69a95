package com.feidi.xx.cross.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.core.enums.*;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.common.enum2text.annotation.Enum2TextMore;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.cross.common.enums.operate.ProductCodeEnum;
import com.feidi.xx.cross.common.enums.order.*;
import com.feidi.xx.cross.market.api.domain.couponGrant.RemoteCouponGrantVo;
import com.feidi.xx.cross.order.domain.OrdOrder;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 订单视图对象 ord_order
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrdOrder.class)
public class OrdOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private String tenantId;

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 平台编号
     */
    @Enum2Text(enumClass = PlatformCodeEnum.class, fullName = "platformName")
    @ExcelProperty(value = "平台编号 ")
    private String platformCode;

    /**
     * 平台单号
     */
    @ExcelProperty(value = "平台单号")
    private String platformNo;

    /**
     * 出发城市
     */
    @ExcelProperty(value = "出发城市")
    private String startCityCode;

    /**
     * 目的地城市
     */
    @ExcelProperty(value = "目的地城市")
    private String endCityCode;

    /**
     * 线路ID
     */
    @ExcelProperty(value = "线路ID")
    private Long lineId;

    /**
     * 线路方向[StartEndEnum]
     */
    @Enum2Text(enumClass = StartEndEnum.class, fullName = "lineDirectionText")
    private String lineDirection;

    /**
     * 线路名称
     */
    @ExcelProperty(value = "线路名称")
    private String lineName;

    @ExcelProperty(value = "上级代理商id")
    private Long parentId;

    /**
     * 代理商id
     */
    @ExcelProperty(value = "代理商id")
    private Long agentId;

    /**
     * 代理商
     */
    @ExcelProperty(value = "代理商名称")
    private String agentName;

    /**
     * 代理商手机号
     */
    private String agentPhone;

    /**
     * 上级代理商id
     */
    @ExcelProperty(value = "上级代理商名称")
    private String parentAgentName;

    /**
     * 司机ID
     */
    @ExcelProperty(value = "司机ID")
    private Long driverId;

    /**
     * 产品类型[ProductTypeEnum]
     */
    @Enum2Text(enumClass = ProductCodeEnum.class)
    @ExcelProperty(value = "产品类型[ProductCodeEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ProductCodeEnum")
    private String productCode;

    /**
     * 订单金额[基础金额+附加费金额-客诉扣款金额]
     */
    @ExcelProperty(value = "订单金额[基础金额+附加费金额-客诉扣款金额]")
    private Long orderPrice;

    /**
     * 实际支付金额
     */
    private Long payPrice;

    /**
     * 客诉扣款金额
     */
    private Long complainPrice;

    /**
     * 客诉时间
     */
    private Date complainTime;

    /**
     * 高速类型[HighwayTypeEnum]
     */
    @Enum2Text(enumClass = HighwayTypeEnum.class, field = "showText")
    @ExcelProperty(value = "高速类型[HighwayTypeEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "HighwayTypeEnum")
    private String highwayType;

    /**
     * 乘客数量
     */
    @ExcelProperty(value = "乘客数量")
    private Integer passengerNum;

    /**
     * 乘客id
     */
    @ExcelProperty(value = "乘客id")
    private Long passengerId;

    /**
     * 乘客真实手机号
     */
    @ExcelProperty(value = "乘客真实手机号")
    private String passengerPhone;

    /**
     * 乘客备注
     */
    @ExcelProperty(value = "乘客备注")
    private String passengerRemark;

    /**
     * 订单状态[OrderStatusEnum]
     */
    @Enum2Text(enumClass = OrderStatusEnum.class, fullName = "statusText")
    @ExcelProperty(value = "订单状态[OrderStatusEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "OrderStatusEnum")
    private String status;

    /**
     * 第三方订单状态
     */
    @ExcelProperty(value = "第三方订单状态")
    private String thirdStatus;

    /**
     * 返利状态[RebateStatusEnum]
     */
    @Enum2Text(enumClass = RebateStatusEnum.class)
    @ExcelProperty(value = "返利状态[RebateStatusEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "RebateStatusEnum")
    private String rebateStatus;

    /**
     * 返利时间
     */
    @ExcelProperty(value = "返利时间")
    private Date rebateTime;

    /**
     * 支付状态[PaymentStatusEnum]
     */
    @Enum2Text(enumClass = PaymentStatusEnum.class)
    @ExcelProperty(value = "支付状态[PaymentStatusEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "PaymentStatusEnum")
    private String payStatus;

    /**
     * 支付方式[PaymentTypeEnum]
     */
    @Enum2Text(enumClass = PaymentTypeEnum.class, fullName = "payTypeText")
    @ExcelProperty(value = "支付方式[PaymentTypeEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "PaymentTypeEnum")
    private String payMode;

    /**
     * 支付时间
     */
    @ExcelProperty(value = "支付时间")
    private Date payTime;

    /**
     * 支付单号
     */
    @ExcelProperty(value = "支付单号")
    private String payNo;

    /**
     * 预计出发时间
     */
    @ExcelProperty(value = "预计出发时间")
    private Date earliestTime;

    /**
     * 最大等待时长
     */
    @ExcelProperty(value = "最大等待时长")
    private Integer maxWaitDuration;

    /**
     * 预计最晚出发时间
     */
    @ExcelProperty(value = "预计最晚出发时间")
    private Date latestTime;

    /**
     * 接单时间
     */
    @ExcelProperty(value = "接单时间")
    private Date receiveTime;

    /**
     * 接驾时间
     */
    @ExcelProperty(value = "接驾时间")
    private Date pickTime;

    /**
     * 到达乘客起点时间
     */
    @ExcelProperty(value = "到达乘客起点时间")
    private Date arrivalTime;

    /**
     * 行程开始时间
     */
    @ExcelProperty(value = "行程开始时间")
    private Date tripStartTime;

    /**
     * 完成时间
     */
    @ExcelProperty(value = "完成时间")
    private Date finishTime;

    /**
     * 投保状态[InsureStatusEnum]
     */
    @Enum2Text(enumClass = InsureStatusEnum.class)
    @ExcelProperty(value = "投保状态[InsureStatusEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "InsureStatusEnum")
    private String insureStatus;

    /**
     * 保险单号
     */
    @ExcelProperty(value = "保险单号")
    private String insureNo;

    /**
     * 里程
     */
    @ExcelProperty(value = "里程")
    private Integer mileage;

    /**
     * 预计时长
     */
    @ExcelProperty(value = "预计时长")
    private Integer expectDuration;

    /**
     * 取消人类型[UserTypeEnum]
     */
    @ExcelProperty(value = "取消人类型[UserTypeEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "UserTypeEnum")
    private String cancelUserType;

    /**
     * 取消时间
     */
    @ExcelProperty(value = "取消时间")
    private Date cancelTime;

    /**
     * 取消类型[CancelTypeEnum]
     */
    @ExcelProperty(value = "取消类型[CancelTypeEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "CancelTypeEnum")
    private String cancelType;

    /**
     * 是否调度[IsYesEnum]
     */
    @ExcelProperty(value = "是否调度[IsYesEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "IsYesEnum")
    private String dispatch;

    /**
     * 调度时间
     */
    @ExcelProperty(value = "调度时间")
    private Date dispatchTime;

    /**
     * 调度类型[DispatchTypeEnum]
     */
    @Enum2Text(enumClass = DispatchTypeEnum.class)
    @ExcelProperty(value = "调度类型[DispatchTypeEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "DispatchTypeEnum")
    private String dispatchType;

    /**
     * 调度状态[SuccessFailEnum]
     */
    @Enum2Text(enumClass = SuccessFailEnum.class, fullName = "dispatchStatusText")
    @ExcelProperty(value = "调度状态[SuccessFailEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "SuccessFailEnum")
    private String dispatchStatus;

    /**
     * 是否预约[IsYesEnum]
     */
    @ExcelProperty(value = "是否预约[IsYesEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "IsYesEnum")
    private String due;

    /**
     * 是否被过滤[IsYesEnum]
     */
    @ExcelProperty(value = "是否被过滤[IsYesEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "IsYesEnum")
    private String filtered;

    /**
     * 是否客诉[IsYesEnum]
     */
    @ExcelProperty(value = "是否客诉[IsYesEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "IsYesEnum")
    private String complain;

    /**
     * 标签（JSON）
     */
    @ExcelProperty(value = "标签", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "J=SON")
    private String label;

    /**
     * 完单标签（JSON）
     */
    @ExcelProperty(value = "完单标签", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "J=SON")
    private String finishLabel;

    /**
     * 邀请的代理商id
     */
    @ExcelProperty(value = "邀请的代理商id")
    private Long inviteAgentId;


    /**
     * 邀请的用户类型[UserTypeEnum]
     */
    private String userType;
    /**
     * 邀请的司机id
     */
    @ExcelProperty(value = "邀请的司机id")
    private Long inviteDriverId;

    /**
     * 来源[SourceEnum]
     */
    @ExcelProperty(value = "来源[SourceEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "CrsSourceEnum")
    private String source;

    /**
     * 下单类型[CreateModelEnum]
     */
    @ExcelProperty(value = "下单类型[CreateModelEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "CreateModelEnum")
    @Enum2Text(enumClass = CreateModelEnum.class, fullName = "createModelText")
    private String createModel;

    /**
     * 是否展示 [IsYesEnum]
     */
    @ExcelProperty(value = "是否展示 [IsYesEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "IsYesEnum")
    private String showed;

    /**
     * 版本
     */
    @ExcelProperty(value = "版本")
    private Integer version;

    /**
     * 司机佣金比例
     */
    private BigDecimal driverRate;

    /**
     * 司机收益
     */
    private Long driverProfit;

    /**
     * 父代理商佣金比例
     */
    private BigDecimal parentAgentRate;

    /**
     * 父代理商收益
     */
    private Long parentAgentProfit;

    /**
     * 代理商佣金比例
     */
    private BigDecimal agentRate;

    /**
     * 代理商收益
     */
    private Long agentProfit;

    /**
     * 电话
     */
    private String phone;

    /**
     * 紧急呼叫电话
     */
    private String emergencyCall;

    /**
     * 按钮状态 {@link StatusEnum}
     */
    private String buttonStatus;

    /**
     * 优惠券发放对象
     */
    private RemoteCouponGrantVo remoteCouponGrantVo;

    /**
     * 接单司机
     */
    @Enum2TextMore()
    private OrdDriverVo driverVo;

    /**
     * 起点位置
     */
    private OrdPositionVo startPositionVo;

    /**
     * 终点位置
     */
    private OrdPositionVo endPositionVo;

    /**
     * 司机邀请有奖收益
     */
    private Long driverInviteReward;

    /**
     * 发单时间
     */
    private Date createTime;

    /**
     * 昵称 尾号**99
     */
    private String nickname;

    /**
     * 标签，逗号分开
     */
    private String labels;

    /**
     * 距离 单位：米
     */
    private Double distance;

    /**
     * 是否预约
     */
    private String isDue;
    private String isDueText;

    public String getIsDueText() {
        return IsYesEnum.getInfoByCode(isDue);
    }

    /**
     * 地市
     */
    private String startCity;

    /**
     * 区
     */
    private String startDistrict;

    /**
     * 详细地址
     */
    private String startAddr;

    /**
     * 地市
     */
    private String endCity;

    /**
     * 区
     */
    private String endDistrict;

    /**
     * 详细地址
     */
    private String endAddr;

    /**
     * 乘客虚拟号
     */
    private String virtualPhone;

    /**
     * 订单调度司机姓名
     */
    private String dispatchDriverName;

    /**
     * 订单调度司机电话
     */
    private String dispatchDriverPhone;

    /**
     * 订单调度司机姓名
     */
    private String dispatchCarNumber;

    /**
     * 优惠券额度
     */
    private Long couponGrantQuota = 0L;

    /**
     * 线路区县名称
     */
    private String lineDistrictName;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 转卖司机ID
     */
    private Long resellDriverId;

    /**
     * 转卖代理商ID
     */
    private Long resellAgentId;

    /**
     * 转卖后司机接单金额
     */
    private Long resellDriverPrice;

    /**
     * 订单转卖返利状态[RebateStatusEnum]
     */
    private String resellRebateStatus;

    /**
     * 订单转卖返利时间
     */
    private Date resellRebateTime;

    /**
     * 客诉原因
     */
    private String complainRemark;

    /**
     * 取消备注
     */
    private String cancelRemark;

    /**
     * 座位数
     */
    private Integer seat;

    /**
     * 取消前是否有实际接单
     */
    private String received;

    /**
     * 取消前是否改派
     */
    private String changed;

    /**
     * 司机类型
     */
    private String driverType;

    /**
     * 司机类型
     */
    private String driverTypeText;
    /**
     * 评价信息
     */
    private OrdEvaluationsVo evaluation;

}
