package com.feidi.xx.cross.order.mq.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 美团虚拟电话事件
 *
 * <AUTHOR>
 * @date 2025/3/22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MtVirtualPhoneEvent implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 平台订单号
     */
    private String platformNo;
}
