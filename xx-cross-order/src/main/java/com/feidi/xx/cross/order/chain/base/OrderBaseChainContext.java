package com.feidi.xx.cross.order.chain.base;

import com.feidi.xx.cross.common.mq.event.OrdOrderOperateEvent;
import com.feidi.xx.cross.order.chain.common.BaseChainContext;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单基础上下文
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderBaseChainContext extends BaseChainContext {

    /**
     * 主键
     */
    private Long orderId;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 平台订单号
     */
    private String platformNo;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 备注
     */
    private String remark;

    /**
     * 时间戳
     */
    private Long timeStamp;

    /**
     * 第三方订单状态
     */
    private String thirdStatus;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 操作日志
     */
    private OrdOrderOperateEvent operateEvent;


}
