package com.feidi.xx.cross.order.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.order.domain.OrdRate;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 订单账单业务对象 ord_rate
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrdRate.class, reverseConvertGenerate = false)
public class OrdRateBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 账单类型[BillTypeEnum]
     */
    @NotBlank(message = "账单类型[BillTypeEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String rateType;

    /**
     * 总价
     */
    @NotNull(message = "总价不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long total;

    /**
     * 分佣比例
     */
    @NotNull(message = "分佣比例不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal rate;

    /**
     * 分佣金额
     */
    @NotNull(message = "分佣金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long amount;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 用户名称
     */
    @NotBlank(message = "用户名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userName;

    /**
     * 用户类型[UserTypeEnum]
     */
    @NotBlank(message = "用户类型[UserTypeEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userType;

    /**
     * 状态[StatusEnum]
     */
    @NotBlank(message = "状态[StatusEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer sort;

    /**
     * 扩展字段
     */
    private String remark;


}
