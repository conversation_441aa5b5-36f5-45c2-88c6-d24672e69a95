package com.feidi.xx.cross.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.common.cache.power.manager.PowCacheManager;
import com.feidi.xx.cross.order.domain.OrdOperate;
import com.feidi.xx.cross.order.domain.bo.OrdOperateBo;
import com.feidi.xx.cross.order.domain.vo.OrdOperateVo;
import com.feidi.xx.cross.order.mapper.OrdOperateMapper;
import com.feidi.xx.cross.order.service.IOrdOperateService;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.RemoteDriverService;
import com.feidi.xx.cross.power.api.domain.agent.vo.RemoteAgentUserVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.system.api.RemoteUserService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 订单操作Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@RequiredArgsConstructor
@Service
public class OrdOperateServiceImpl implements IOrdOperateService {

    private final PowCacheManager powCacheManager;
    private final OrdOperateMapper baseMapper;
    @DubboReference
    private final RemoteUserService remoteUserService;
    @DubboReference
    private final RemoteAgentService remoteAgentService;
    @DubboReference
    private final RemoteDriverService remoteDriverService;

    /**
     * 查询订单操作
     *
     * @param id 主键
     * @return 订单操作
     */
    @Override
    public OrdOperateVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询订单操作列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单操作分页列表
     */
    @Override
    public TableDataInfo<OrdOperateVo> queryPageList(OrdOperateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrdOperate> lqw = buildQueryWrapper(bo);
        Page<OrdOperateVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的订单操作列表
     *
     * @param bo 查询条件
     * @return 订单操作列表
     */
    @Override
    public List<OrdOperateVo> queryList(OrdOperateBo bo) {
        LambdaQueryWrapper<OrdOperate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrdOperate> buildQueryWrapper(OrdOperateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrdOperate> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderId() != null, OrdOperate::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getTraceId()), OrdOperate::getTraceId, bo.getTraceId());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), OrdOperate::getUserName, bo.getUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getOperateType()), OrdOperate::getOperateType, bo.getOperateType());
        lqw.eq(StringUtils.isNotBlank(bo.getResponseJson()), OrdOperate::getResponseJson, bo.getResponseJson());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), OrdOperate::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增订单操作
     *
     * @param bo 订单操作
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OrdOperateBo bo) {
        OrdOperate add = MapstructUtils.convert(bo, OrdOperate.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改订单操作
     *
     * @param bo 订单操作
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OrdOperateBo bo) {
        OrdOperate update = MapstructUtils.convert(bo, OrdOperate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrdOperate entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除订单操作信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据订单id查询订单操作记录
     *
     * @param orderId 订单id
     * @return 订单操作记录
     */
    @Override
    public List<OrdOperateVo> queryByOrderId(Long orderId) {
        OrdOperateBo bo = new OrdOperateBo();
        bo.setOrderId(orderId);
        LambdaQueryWrapper<OrdOperate> lqw = buildQueryWrapper(bo);
        lqw.orderByAsc(OrdOperate::getCreateTime);
        List<OrdOperateVo> operateVos = baseMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(operateVos)) {
            for (OrdOperateVo operateVo : operateVos) {
                // 用户类型
                String userType = operateVo.getUserType();
                Long userId = operateVo.getUserId();

                operateVo.setUserTypeText(UserTypeEnum.getInfoByCode(userType));
                operateVo.setUserName(UserTypeEnum.getInfoByCode(userType));
                if (ObjectUtils.isNotNull(userId) && userId > 0) {
                    if (userType.equals(UserTypeEnum.SYS_USER.getUserType())) {
                        // TODO-NEW 后续修改成查询缓存 后台管理员
                        operateVo.setUserName(remoteUserService.selectNicknameById(userId));
                    } else if (userType.equals(UserTypeEnum.AGENT_USER.getUserType())) {
                        // 代理
                        RemoteAgentUserVo agentUserInfo = powCacheManager.getAgentUserInfoById(userId);
                        operateVo.setUserName(ObjectUtils.isNotNull(agentUserInfo) ? agentUserInfo.getName() : "");
                    } else if (userType.equals(UserTypeEnum.DRIVER_USER.getUserType())) {
                        // 司机
                        RemoteDriverVo driverInfo = powCacheManager.getDriverInfoById(userId);
                        operateVo.setUserName(ObjectUtils.isNotNull(driverInfo) ?  driverInfo.getName() : "");
                    }
                }
            }
        }

        return operateVos;
    }
}
