package com.feidi.xx.cross.order.domain.vo.driver;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 司机抢单地址历史记录
 *
 * <AUTHOR>
 * @date 2025/03/06
 */
@Data
public class DisRobHistoryAddrVo implements Serializable {

    /**
     * 省ID
     */
    @ExcelProperty(value = "省ID")
    private Long provinceId;

    /**
     * 省
     */
    @ExcelProperty(value = "省")
    private String province;

    /**
     * 城市ID
     */
    @ExcelProperty(value = "城市ID")
    private Long cityId;

    /**
     * 城市编码
     */
    @ExcelProperty(value = "城市编码")
    private String cityCode;

    /**
     * 市
     */
    @ExcelProperty(value = "市")
    private String city;

    /**
     * 区域ID
     */
    @ExcelProperty(value = "区域ID")
    private Long districtId;

    /**
     * 区域编码
     */
    @ExcelProperty(value = "区域编码")
    private String adCode;

    /**
     * 区域
     */
    @ExcelProperty(value = "区域")
    private String district;

    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    private String address;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private String longitude;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private String latitude;

}
