package com.feidi.xx.cross.order.domain.bo.rob;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.BatchGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.cross.order.domain.DisRob;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 自动抢单业务对象 dis_rob
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = DisRob.class, reverseConvertGenerate = false)
public class DisRobBo  extends PageQuery {

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 综合搜索
     */
    private String unionId;

    /**
     * 主键IDs
     */
    @NotEmpty(message = "主键不能为空", groups = { BatchGroup.class })
    private List<Long> ids;

    /**
     * 代理商id
     */
    private List<Long> agentIds;

    /**
     * 线路IDs
     */
    private List<Long> lineIds;

    /**
     * 编号
     */
    @NotBlank(message = "编号不能为空", groups = {EditGroup.class })
    private String robNo;

    /**
     * 代理ID
     */
    private Long agentId;

    /**
     * 线路ID
     */
    private Long lineId;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 开始省ID
     */
    private Long startProvinceId;

    /**
     * 开始城市ID
     */
    private Long startCityId;

    /**
     * 开始城市编码
     */
    private String startCityCode;

    /**
     * 开始区域ID
     */
    private Long startDistrictId;

    /**
     * 开始区域编码
     */
    private String startAdCode;

    /**
     * 开始地址
     */
    private String startAddress;

    /**
     * 开始经度
     */
    private String startLongitude;

    /**
     * 开始纬度
     */
    private String startLatitude;

    /**
     * 开始半径
     */
    private Long startRadius;

    /**
     * 结束省ID
     */
    private Long endProvinceId;

    /**
     * 结束城市ID
     */
    private Long endCityId;

    /**
     * 结束城市编码
     */
    private String endCityCode;

    /**
     * 结束区域ID
     */
    private Long endDistrictId;

    /**
     * 结束区域编码
     */
    private String endAdCode;

    /**
     * 结束地址
     */
    private String endAddress;

    /**
     * 结束经度
     */
    private String endLongitude;

    /**
     * 结束纬度
     */
    private String endLatitude;

    /**
     * 结束半径
     */
    private Long endRadius;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 排队时间
     */
    private String sortTime;

    /**
     * 等待时间
     */
    private Long maxWaitDuration;

    /**
     * 座位数
     */
    private Integer seat;

    /**
     * 剩余座位数
     */
    private Integer surplusSeat;

    /**
     * 抢单产品编码
     */
    @NotNull(message = "抢单产品不能为空", groups = { AddGroup.class, EditGroup.class })
    private String robProduct;

    /**
     * 用户类型[UserTypeEnum]
     */
    private String userType;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 状态[StatusEnum]
     */
    private String status;

    /**
     * 抢单类型[RobTypeEnum]
     */
    private String type;


    /**
     * 方案[RobPlanEnum]
     */
    @NotBlank(message = "方案不能为空", groups = { AddGroup.class, EditGroup.class })
    private String plan;

    /**
     * 司机IDS（随机）
     */
    private String driverJson;

    /**
     * 续期天数
     */
    private Long renewal;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 平台单号
     */
    private String platformNo;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 乘客数量
     */
    private Integer passengerNum;

    /**
     * 预计出发时间
     */
    private Date earliestTime;

    /**
     * 预计最晚出发时间
     */
    private Date latestTime;
}
