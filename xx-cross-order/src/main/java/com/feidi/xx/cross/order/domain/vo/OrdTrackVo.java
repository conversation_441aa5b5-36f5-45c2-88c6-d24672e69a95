package com.feidi.xx.cross.order.domain.vo;

import com.feidi.xx.cross.order.domain.OrdTrack;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 订单轨迹视图对象 ord_track
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrdTrack.class)
public class OrdTrackVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 订单id
     */
    @ExcelProperty(value = "订单id")
    private Long orderId;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型")
    private String type;

    /**
     * 轨迹数据
     */
    @ExcelProperty(value = "轨迹数据")
    private String trackData;


}
