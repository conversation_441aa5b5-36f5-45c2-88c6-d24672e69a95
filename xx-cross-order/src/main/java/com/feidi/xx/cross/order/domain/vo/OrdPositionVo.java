package com.feidi.xx.cross.order.domain.vo;

import com.feidi.xx.cross.order.domain.OrdPosition;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 订单位置视图对象 ord_position
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrdPosition.class)
public class OrdPositionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private String tenantId;

    /**
     * 订单ID
     */
    @ExcelProperty(value = "订单ID")
    private Long orderId;

    /**
     * 省ID
     */
    @ExcelProperty(value = "省ID")
    private Long provinceId;

    /**
     * 省
     */
    @ExcelProperty(value = "省")
    private String province;

    /**
     * 城市ID
     */
    @ExcelProperty(value = "城市ID")
    private Long cityId;

    /**
     * 城市编码
     */
    @ExcelProperty(value = "城市编码")
    private String cityCode;

    /**
     * 市
     */
    @ExcelProperty(value = "市")
    private String city;

    /**
     * 区域ID
     */
    @ExcelProperty(value = "区域ID")
    private Long districtId;

    /**
     * 区域编码
     */
    @ExcelProperty(value = "区域编码")
    private String adCode;

    /**
     * 区域
     */
    @ExcelProperty(value = "区域")
    private String district;

    /**
     * 短地址
     */
    @ExcelProperty(value = "短地址")
    private String shortAddr;

    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    private String address;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private Double longitude;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private Double latitude;

    /**
     * 类型[StartEndEnum]
     */
    @ExcelProperty(value = "类型[StartEndEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "StartEndEnum")
    private String type;

    /**
     * 状态[StatusEnum]
     */
    @ExcelProperty(value = "状态[StatusEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "StatusEnum")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
