package com.feidi.xx.cross.order.components;

import com.feidi.xx.common.core.enums.SuccessFailEnum;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.enums.order.CancelTypeEnum;
import com.feidi.xx.cross.common.enums.order.OperateTypeEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.enums.platform.MtOrderStatusEnum;
import com.feidi.xx.cross.common.enums.platform.MtSexEnum;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDriverVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderInfoVo;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.platform.api.hbk.domain.hbk.vo.RemotePlatformApiResponseVo;
import com.feidi.xx.cross.platform.api.mt.RemoteMtOrderService;
import com.feidi.xx.cross.platform.api.mt.domian.bo.RemoteMtApplyRefundBo;
import com.feidi.xx.cross.platform.api.mt.domian.bo.RemoteMtPushStatusBo;
import com.feidi.xx.cross.platform.api.mt.domian.bo.RemoteMtQueryOrderByPhoneBo;
import com.feidi.xx.cross.platform.api.mt.domian.bo.RemoteMtQueryPassengerPhoneBo;
import com.feidi.xx.cross.platform.api.mt.domian.vo.RemoteMtBillVo;
import com.feidi.xx.cross.platform.api.mt.domian.vo.RemoteMtCarInfoVo;
import com.feidi.xx.cross.platform.api.mt.domian.vo.RemoteMtDriverInfoVo;
import com.feidi.xx.cross.platform.api.mt.domian.vo.RemoteMtOrderCancelVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 统一调平台订单接口
 *
 * <AUTHOR>
 * @create 2024-06-11-15:36
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlatformOrderApiComponent {

    private final OrdCacheManager ordCacheManager;
    @DubboReference
    private final RemoteMtOrderService remoteMtOrderService;

    /**
     * 美团 推送订单状态
     *
     * @param order     订单信息
     * @param operateType 操作类型
     * @return
     */
    public RemotePlatformApiResponseVo pushOrderStatus(OrdOrder order, String operateType) {
        // 调美团接口 - 推送订单状态
        RemoteMtPushStatusBo pushStatusBo = new RemoteMtPushStatusBo();
        pushStatusBo.setEventCode(0);
        pushStatusBo.setEventTime(DateUtils.getUnixTimeStamps());
        pushStatusBo.setMtOrderId(order.getPlatformNo());
        pushStatusBo.setPartnerOrderId(order.getOrderNo());
        pushStatusBo.setPickUpTime(order.getEarliestTime().getTime() * 1000);

        // 订单状态
        String orderStatus = OperateTypeEnum.getOrderStatusByCode(operateType);
        // 美团订单状态
        String mtOrderStatus = MtOrderStatusEnum.getCodeByOrderStatus(orderStatus);

        // 设置美团订单状态
        pushStatusBo.setStatus(mtOrderStatus);

        // 订单司机
        RemoteOrderDriverVo orderDriverVo = ordCacheManager.getOrderDispatchDriverInfoByOrderId(order.getId());

        // 车辆信息
        RemoteMtCarInfoVo carInfoVo = new RemoteMtCarInfoVo();
        carInfoVo.setCarColor(orderDriverVo.getCarColor());
        carInfoVo.setCarNumber(orderDriverVo.getCarNumber());
        carInfoVo.setBrandName(orderDriverVo.getCarBrand());
        pushStatusBo.setCarInfo(carInfoVo);

        // 司机信息
        RemoteMtDriverInfoVo driverInfoVo = new RemoteMtDriverInfoVo();
        driverInfoVo.setDriverLastName(orderDriverVo.getDriverName().substring(0, 1));
        driverInfoVo.setDriverName(orderDriverVo.getDriverName().substring(1));
        driverInfoVo.setDriverPhone(orderDriverVo.getDriverPhone());
        driverInfoVo.setPartnerDriverId(orderDriverVo.getId().toString());
        driverInfoVo.setDriverGender(MtSexEnum.getCodeBySex(orderDriverVo.getDriverSex()));
        pushStatusBo.setDriverInfo(driverInfoVo);

        // 订单完成
        if (Objects.equals(orderStatus, OrderStatusEnum.FINISH.getCode())) {

            // 订单关联信息
            RemoteOrderInfoVo orderSubInfo = ordCacheManager.getOrderSubInfoByOrderId(order.getId());

            // 账单信息
            RemoteMtBillVo billVo = new RemoteMtBillVo();
            billVo.setFixedPrice(Math.toIntExact(order.getOrderPrice()));
            billVo.setHighwayPrice(Math.toIntExact(orderSubInfo.getAddPrice()));
            billVo.setCancelPrice(Math.toIntExact(orderSubInfo.getCancelFee()));
            pushStatusBo.setBill(billVo);
            // 是否拼成
            pushStatusBo.setIsPoolSuccess(true);
        }

        // 订单取消
        if (Objects.equals(orderStatus, OrderStatusEnum.CANCEL.getCode())) {
            RemoteMtOrderCancelVo orderCancelVo = new RemoteMtOrderCancelVo();
            orderCancelVo.setCancelReason(CancelTypeEnum.CUSTOMER_SERVICE.getInfo());
            orderCancelVo.setOpName("系统管理员");
            pushStatusBo.setCustomerServiceInfo(orderCancelVo);
        }

        if (log.isInfoEnabled()) {
            log.info("调用【美团推送订单状态】接口 - 推送订单状态【{}】 - 参数【{}】", pushStatusBo.getStatus(), JsonUtils.toJsonString(pushStatusBo));
        }
        RemotePlatformApiResponseVo platformApiResponseVo = remoteMtOrderService.pushOrderStatus(pushStatusBo);
        if (log.isInfoEnabled()) {
            String isSuccess = platformApiResponseVo.getCode() == 0 ? SuccessFailEnum.SUCCESS.getInfo() : SuccessFailEnum.FAIL.getInfo();
            log.info("调用【美团推送订单状态】接口 - 【{}】 - 返回结果【{}】", isSuccess, JsonUtils.toJsonString(platformApiResponseVo));
        }

        return platformApiResponseVo;
    }

    /**
     * 美团 获取乘客手机号
     * 服务商携带司机号码请求此接口，美团建立“司机-虚拟号-乘客”的绑定关系，并返回虚拟号，单次绑定时长30分钟
     *
     * @param platformNo 订单信息
     * @return
     */
    public RemotePlatformApiResponseVo queryPassengerPhone(String platformNo, String driverPhone) {
        // 调美团接口 - 推送好友信息
        RemoteMtQueryPassengerPhoneBo queryPassengerPhoneBo = new RemoteMtQueryPassengerPhoneBo();
        queryPassengerPhoneBo.setMtOrderId(platformNo);
        queryPassengerPhoneBo.setDriverPhone(driverPhone);

        if (log.isInfoEnabled()) {
            log.info("调用【美团获取乘客手机号】接口 - 参数【{}】", JsonUtils.toJsonString(queryPassengerPhoneBo));
        }
        RemotePlatformApiResponseVo platformApiResponseVo = remoteMtOrderService.queryPassengerPhone(queryPassengerPhoneBo);
        if (log.isInfoEnabled()) {
            String isSuccess = platformApiResponseVo.getCode() == 0 ? SuccessFailEnum.SUCCESS.getInfo() : SuccessFailEnum.FAIL.getInfo();
            log.info("调用【美团获取乘客手机号】接口 - 【{}】 - 返回结果【{}】", isSuccess, JsonUtils.toJsonString(platformApiResponseVo));
        }

        return platformApiResponseVo;
    }

    /**
     * 美团 根据乘客号码查询订单
     *
     * @param phone     乘客进线手机号
     * @param startTime 开始时间（时间戳）
     * @param endTime   结束时间（时间戳) 查询订单起始时间最长要求不超过31个自然日
     * @return
     */
    public RemotePlatformApiResponseVo queryOrderByUserPhone(String phone, Long startTime, Long endTime) {
        // 调美团接口 - 根据乘客号码查询订单
        RemoteMtQueryOrderByPhoneBo queryOrderByPhoneBo = new RemoteMtQueryOrderByPhoneBo();
        queryOrderByPhoneBo.setPhone(phone);
        queryOrderByPhoneBo.setStartTime(startTime);
        queryOrderByPhoneBo.setEndTime(endTime);

        if (log.isInfoEnabled()) {
            log.info("调用【美团根据乘客号码查询订单】接口 - 参数【{}】", JsonUtils.toJsonString(queryOrderByPhoneBo));
        }
        RemotePlatformApiResponseVo platformApiResponseVo = remoteMtOrderService.queryOrderByUserPhone(queryOrderByPhoneBo);
        if (log.isInfoEnabled()) {
            String isSuccess = platformApiResponseVo.getCode() == 0 ? SuccessFailEnum.SUCCESS.getInfo() : SuccessFailEnum.FAIL.getInfo();
            log.info("调用【美团根据乘客号码查询订单】接口 - 【{}】 - 返回结果【{}】", isSuccess, JsonUtils.toJsonString(platformApiResponseVo));
        }

        return platformApiResponseVo;
    }

    /**
     * 美团 退款通知
     *
     * @param tripPrice       行程退款金额 单位（分）
     * @param additionalPrice 附加费退款金额（高速费、停车费、桥路费、其他费用） 单位（分）
     * @param refundType      退款类型：部分退、全额退
     * @param reason          退款原因
     * @param order           订单信息
     * @return
     */
    public RemotePlatformApiResponseVo applyRefund(Long tripPrice, Long additionalPrice, String refundType, String reason, OrdOrder order) {
        // 调美团接口 - 退款通知
        RemoteMtApplyRefundBo applyRefundBo = new RemoteMtApplyRefundBo();
        applyRefundBo.setOutNo(order.getId().toString());
        applyRefundBo.setMtOrderId(order.getPlatformNo());
        applyRefundBo.setPartnerOrderId(order.getOrderNo());
        applyRefundBo.setRefundType(refundType);
        applyRefundBo.setDescription("后台客服发起退款");
        applyRefundBo.setReason2(reason);
        applyRefundBo.setTripPrice(tripPrice);
        applyRefundBo.setAdditionalPrice(additionalPrice);
        applyRefundBo.setRefundPrice(ArithUtils.add(tripPrice, additionalPrice));
        applyRefundBo.setOpUid("0");
        applyRefundBo.setOpName("后台管理员");

        if (log.isInfoEnabled()) {
            log.info("调用【美团退款通知】接口 - 参数【{}】", JsonUtils.toJsonString(applyRefundBo));
        }
        RemotePlatformApiResponseVo platformApiResponseVo = remoteMtOrderService.applyRefund(applyRefundBo);
        if (log.isInfoEnabled()) {
            String isSuccess = platformApiResponseVo.getCode() == 0 ? SuccessFailEnum.SUCCESS.getInfo() : SuccessFailEnum.FAIL.getInfo();
            log.info("调用【美团退款通知】接口 - 【{}】 - 返回结果【{}】", isSuccess, JsonUtils.toJsonString(platformApiResponseVo));
        }

        return platformApiResponseVo;
    }


}
