package com.feidi.xx.cross.order.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.order.domain.bo.OrdOrderInfoBo;
import com.feidi.xx.cross.order.domain.bo.OrdPositionBo;
import com.feidi.xx.cross.order.domain.vo.ExportVo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderInfoVo;
import com.feidi.xx.cross.order.domain.vo.OrdPositionVo;
import com.feidi.xx.cross.order.service.IOrdPositionService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 订单位置
 * 前端访问路由地址为:/order/position
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/position")
public class OrdPositionController extends BaseController {

    private final IOrdPositionService ordPositionService;

    /**
     * 查询订单位置列表
     */
    @SaCheckPermission("order:position:list")
    @GetMapping("/list")
    public TableDataInfo<OrdPositionVo> list(OrdPositionBo bo, PageQuery pageQuery) {
        return ordPositionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出订单位置列表
     */
    @SaCheckPermission("order:position:export")
    @Log(title = "订单位置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OrdPositionBo bo,HttpServletResponse response) {
        List<OrdPositionVo> list = ordPositionService.queryList(bo);
        ExcelUtil.exportExcel(list, "订单位置", OrdPositionVo.class, response);
    }

    /**
     * 获取订单位置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("order:position:query")
    @GetMapping("/{id}")
    public R<OrdPositionVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(ordPositionService.queryById(id));
    }

    /**
     * 新增订单位置
     */
    @SaCheckPermission("order:position:add")
    @Log(title = "订单位置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OrdPositionBo bo) {
        return toAjax(ordPositionService.insertByBo(bo));
    }

    /**
     * 修改订单位置
     */
    @SaCheckPermission("order:position:edit")
    @Log(title = "订单位置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OrdPositionBo bo) {
        return toAjax(ordPositionService.updateByBo(bo));
    }

    /**
     * 删除订单位置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("order:position:remove")
    @Log(title = "订单位置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(ordPositionService.deleteWithValidByIds(List.of(ids), true));
    }
}
