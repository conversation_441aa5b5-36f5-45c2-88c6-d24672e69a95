package com.feidi.xx.cross.order.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.order.domain.OrdTrack;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 订单轨迹业务对象 ord_track
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrdTrack.class, reverseConvertGenerate = false)
public class OrdTrackBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 类型
     */
    @NotBlank(message = "类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 轨迹数据
     */
    @NotBlank(message = "轨迹数据不能为空", groups = { AddGroup.class, EditGroup.class })
    private String trackData;


}
