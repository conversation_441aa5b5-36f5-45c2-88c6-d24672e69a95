package com.feidi.xx.cross.order.service;

import com.feidi.xx.cross.order.domain.OrdOrderInfo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderInfoVo;
import com.feidi.xx.cross.order.domain.bo.OrdOrderInfoBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 订单信息Service接口
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface IOrdOrderInfoService {

    /**
     * 查询订单信息
     *
     * @param id 主键
     * @return 订单信息
     */
    OrdOrderInfoVo queryById(Long id);

    /**
     * 分页查询订单信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单信息分页列表
     */
    TableDataInfo<OrdOrderInfoVo> queryPageList(OrdOrderInfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的订单信息列表
     *
     * @param bo 查询条件
     * @return 订单信息列表
     */
    List<OrdOrderInfoVo> queryList(OrdOrderInfoBo bo);

    /**
     * 新增订单信息
     *
     * @param bo 订单信息
     * @return 是否新增成功
     */
    OrdOrderInfo insertByBo(OrdOrderInfoBo bo);

    /**
     * 修改订单信息
     *
     * @param bo 订单信息
     * @return 是否修改成功
     */
    Boolean updateByBo(OrdOrderInfoBo bo);

    /**
     * 校验并批量删除订单信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据订单id查询订单信息
     *
     * @param orderId 订单id
     * @return 订单信息
     */
    OrdOrderInfo queryByOrderId(Long orderId);

    /**
     * 根据订单id查询订单关联信息
     *
     * @param orderIds 订单id集合
     * @return 订单关联集合
     */
    List<OrdOrderInfo> queryByOrderIds(List<Long> orderIds);
}
