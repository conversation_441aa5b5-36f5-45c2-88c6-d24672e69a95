package com.feidi.xx.cross.order.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.order.domain.OrdOrderInfo;
import com.feidi.xx.cross.order.domain.bo.OrdDriverBo;
import com.feidi.xx.cross.order.domain.bo.OrdOrderInfoBo;
import com.feidi.xx.cross.order.domain.vo.ExportVo;
import com.feidi.xx.cross.order.domain.vo.OrdDriverVo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderInfoVo;
import com.feidi.xx.cross.order.service.IOrdOrderInfoService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 订单信息
 * 前端访问路由地址为:/order/orderInfo
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/orderInfo")
public class OrdOrderInfoController extends BaseController {

    private final IOrdOrderInfoService ordOrderInfoService;

    /**
     * 查询订单信息列表
     */
    @SaCheckPermission("order:orderInfo:list")
    @GetMapping("/list")
    public TableDataInfo<OrdOrderInfoVo> list(OrdOrderInfoBo bo, PageQuery pageQuery) {
        return ordOrderInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出订单信息列表
     */
    @SaCheckPermission("order:orderInfo:export")
    @Log(title = "订单信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OrdOrderInfoBo bo,HttpServletResponse response) {
        List<OrdOrderInfoVo> list = ordOrderInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "订单信息", OrdOrderInfoVo.class, response);
    }

    /**
     * 获取订单信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("order:orderInfo:query")
    @GetMapping("/{id}")
    public R<OrdOrderInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(ordOrderInfoService.queryById(id));
    }

    /**
     * 新增订单信息
     */
    @SaCheckPermission("order:orderInfo:add")
    @Log(title = "订单信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<OrdOrderInfo> add(@Validated(AddGroup.class) @RequestBody OrdOrderInfoBo bo) {
        return R.ok(ordOrderInfoService.insertByBo(bo));
    }

    /**
     * 修改订单信息
     */
    @SaCheckPermission("order:orderInfo:edit")
    @Log(title = "订单信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OrdOrderInfoBo bo) {
        return toAjax(ordOrderInfoService.updateByBo(bo));
    }

    /**
     * 删除订单信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("order:orderInfo:remove")
    @Log(title = "订单信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(ordOrderInfoService.deleteWithValidByIds(List.of(ids), true));
    }
}
