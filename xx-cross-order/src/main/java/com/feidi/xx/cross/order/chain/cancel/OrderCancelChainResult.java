package com.feidi.xx.cross.order.chain.cancel;

import com.feidi.xx.cross.order.chain.base.OrderBaseChainResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 取消结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderCancelChainResult extends OrderBaseChainResult {

    public OrderCancelChainResult() {
    }

    public OrderCancelChainResult(boolean success) {
        this.success = success;
    }
}
