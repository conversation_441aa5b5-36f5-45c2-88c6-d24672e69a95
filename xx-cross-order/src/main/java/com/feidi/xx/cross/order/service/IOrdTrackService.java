package com.feidi.xx.cross.order.service;

import com.feidi.xx.cross.order.domain.vo.OrdTrackVo;
import com.feidi.xx.cross.order.domain.bo.OrdTrackBo;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 订单轨迹Service接口
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
public interface IOrdTrackService {

    /**
     * 查询订单轨迹
     *
     * @param id 主键
     * @return 订单轨迹
     */
    OrdTrackVo queryById(Long id);

    /**
     * 分页查询订单轨迹列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单轨迹分页列表
     */
    TableDataInfo<OrdTrackVo> queryPageList(OrdTrackBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的订单轨迹列表
     *
     * @param bo 查询条件
     * @return 订单轨迹列表
     */
    List<OrdTrackVo> queryList(OrdTrackBo bo);

    /**
     * 新增订单轨迹
     *
     * @param bo 订单轨迹
     * @return 是否新增成功
     */
    Boolean insertByBo(OrdTrackBo bo);

    /**
     * 修改订单轨迹
     *
     * @param bo 订单轨迹
     * @return 是否修改成功
     */
    Boolean updateByBo(OrdTrackBo bo);

    /**
     * 保存订单轨迹
     * @param orderId
     * @param tenantId
     */
    void saveOrderTrackLocation(Long orderId, String tenantId);

    /**
     * 校验并批量删除订单轨迹信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
