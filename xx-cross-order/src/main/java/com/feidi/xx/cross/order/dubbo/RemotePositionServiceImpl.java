package com.feidi.xx.cross.order.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.order.api.RemotePositionService;
import com.feidi.xx.cross.order.api.domain.vo.RemotePositionVo;
import com.feidi.xx.cross.order.domain.OrdPosition;
import com.feidi.xx.cross.order.mapper.OrdPositionMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/22
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemotePositionServiceImpl implements RemotePositionService {

    private final OrdPositionMapper ordPositionMapper;

    /**
     * 根据订单id查询订单位置
     *
     * @param orderId 订单id
     * @return 订单位置
     */
    @Override
    public List<RemotePositionVo> queryByOrderId(Long orderId) {
        LambdaQueryWrapper<OrdPosition> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrdPosition::getOrderId, orderId)
                .eq(OrdPosition::getStatus, StatusEnum.ENABLE.getCode());
        List<OrdPosition> positions = ordPositionMapper.selectList(lqw);

        return BeanUtils.copyToList(positions, RemotePositionVo.class);
    }

    /**
     * 根据订单id查询订单位置
     *
     * @param orderIds 订单id集合
     * @return 订单位置
     */
    @Override
    public List<RemotePositionVo> queryByOrderIds(List<Long> orderIds) {
        if (CollUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrdPosition> lqw = Wrappers.lambdaQuery();
        lqw.in(OrdPosition::getOrderId, orderIds)
                .eq(OrdPosition::getStatus, StatusEnum.ENABLE.getCode());
        List<OrdPosition> positions = ordPositionMapper.selectList(lqw);

        return BeanUtils.copyToList(positions, RemotePositionVo.class);
    }

    /**
     * 根据订单id和类型查询订单位置
     *
     * @param orderId 订单id
     * @param type    位置类型
     * @return 订单位置
     */
    @Override
    public RemotePositionVo queryByOrderIdAndType(Long orderId, String type) {
        LambdaQueryWrapper<OrdPosition> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrdPosition::getOrderId, orderId)
                .eq(OrdPosition::getType, type)
                .eq(OrdPosition::getStatus, StatusEnum.ENABLE.getCode())
                .orderByDesc(OrdPosition::getCreateTime)
                .last(Constants.LIMIT_ONE);
        OrdPosition position = ordPositionMapper.selectOne(lqw);

        return BeanUtils.copyProperties(position, RemotePositionVo.class);
    }
}
