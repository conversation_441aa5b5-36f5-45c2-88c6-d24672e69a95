package com.feidi.xx.cross.order.dubbo;

import com.feidi.xx.cross.order.api.RemoteRobService;
import com.feidi.xx.cross.order.api.domain.bo.RelationBo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteRobVo;
import com.feidi.xx.cross.order.domain.DisRob;
import com.feidi.xx.cross.order.mapper.DisRobMapper;
import com.feidi.xx.cross.order.service.IDisRobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
/**
 * 抢单服务
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteDisRobServiceImpl implements RemoteRobService {

    private final DisRobMapper baseMapper;

    // 抢单服务
    private final IDisRobService disRobService;
    @Override
    public List<RemoteRobVo> listByAgentIdAndLineId(Long agentId, Long lineId) {
        return List.of();
    }

    @Override
    public void disableDriverRob(Long... driverId) {
        disRobService.disableRob(Arrays.asList(driverId), DisRob::getDriverId);

    }

    @Override
    public void disableAgentRob(Long... agentId) {
        disRobService.disableRob(Arrays.asList(agentId),DisRob::getAgentId);
    }

    @Override
    public void disableLineRob(Long... lineIds) {

    }

    @Override
    public void disableRob(RelationBo bo) {

    }

    @Override
    public void switchAgent(Long driverId) {

    }
}
