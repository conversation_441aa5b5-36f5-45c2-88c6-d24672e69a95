package com.feidi.xx.cross.order.chain.base;

import com.feidi.xx.cross.order.chain.common.BaseChainResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单基础上下文
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderBaseChainResult extends BaseChainResult {

    /**
     * 主键
     */
    private Long id;


    public OrderBaseChainResult() {
    }

    public OrderBaseChainResult(boolean success) {
        this.success = success;
    }

}
