package com.feidi.xx.cross.order.mq.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单自动抢单事件
 *
 * <AUTHOR>
 * @date 2025/3/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderRobEvent implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单ID
     */
    private String tenantId;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 平台订单号
     */
    private String platformNo;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 线路ID
     */
    private Long lineId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 乘客人数
     */
    private Integer passengerNum;

    /**
     * 最早出发时间
     */
    private Date earliestTime;

    /**
     * 最晚出发时间
     */
    private Date latestTime;

    /**
     * 出发地adCode
     */
    private String startAdCode;

    /**
     * 出发地经度
     */
    private Double startLongitude;

    /**
     * 出发地纬度
     */
    private Double startLatitude;

    /**
     * 目的地adCode
     */
    private String endAdCode;

    /**
     * 目的地经度
     */
    private Double endLongitude;

    /**
     * 目的地纬度
     */
    private Double endLatitude;
}
