package com.feidi.xx.cross.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.enums.*;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.*;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.cache.power.manager.PowCacheManager;
import com.feidi.xx.cross.common.constant.market.MarketCacheConstants;
import com.feidi.xx.cross.common.enums.message.OrderMessageTypeEnum;
import com.feidi.xx.cross.common.enums.message.WebSocketTypeEnum;
import com.feidi.xx.cross.common.enums.order.*;
import com.feidi.xx.cross.common.enums.power.DriverTypeEnum;
import com.feidi.xx.cross.common.helper.OrdOrderOperateHelper;
import com.feidi.xx.cross.common.mq.event.OrdOrderOperateEvent;
import com.feidi.xx.cross.common.mq.producer.OrdOrderOperateProducer;
import com.feidi.xx.cross.common.utils.order.OrderUtils;
import com.feidi.xx.cross.finance.api.RemotePaymentRecordService;
import com.feidi.xx.cross.finance.api.domain.vo.RemotePaymentRecordVo;
import com.feidi.xx.cross.market.api.RemoteActivityService;
import com.feidi.xx.cross.market.api.RemoteCouponGrantService;
import com.feidi.xx.cross.market.api.domain.RemoteActivityVo;
import com.feidi.xx.cross.market.api.domain.couponGrant.RemoteCouponGrantVo;
import com.feidi.xx.cross.message.api.RemoteWebSocketService;
import com.feidi.xx.cross.message.api.domain.dto.OrderMessage;
import com.feidi.xx.cross.message.api.domain.dto.WebSocketMessageWrapper;
import com.feidi.xx.cross.operate.api.RemoteLineService;
import com.feidi.xx.cross.operate.api.domain.line.vo.RemoteLineVo;
import com.feidi.xx.cross.order.api.RemoteOrderRateService;
import com.feidi.xx.cross.order.api.RemotePositionService;
import com.feidi.xx.cross.order.api.domain.bo.RemotePositionBo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDriverVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderRateVo;
import com.feidi.xx.cross.order.api.domain.vo.RemotePositionVo;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.OrdOrderInfo;
import com.feidi.xx.cross.order.domain.bo.OrdOrderBo;
import com.feidi.xx.cross.order.domain.bo.OrdOrderProfitBo;
import com.feidi.xx.cross.order.domain.bo.OrdPositionBo;
import com.feidi.xx.cross.order.domain.bo.order.OrdOrderQueryWebBo;
import com.feidi.xx.cross.order.domain.bo.rob.MatchBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderDispatchBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderHandleBaseBo;
import com.feidi.xx.cross.order.domain.vo.*;
import com.feidi.xx.cross.order.helper.OrdOrderHelper;
import com.feidi.xx.cross.order.mapper.*;
import com.feidi.xx.cross.order.mq.event.InviteEvent;
import com.feidi.xx.cross.order.mq.event.OrderRobEvent;
import com.feidi.xx.cross.order.mq.producer.OrderInviteRewardProducer;
import com.feidi.xx.cross.order.mq.producer.OrderRobProducer;
import com.feidi.xx.cross.order.service.*;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerVo;
import com.feidi.xx.cross.power.api.*;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteCarVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.system.api.RemoteConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrdOrderServiceImpl implements IOrdOrderService {

    private final PowCacheManager powCacheManager;
    private final OprCacheManager oprCacheManager;
    private final OrdCacheManager ordCacheManager;
    private final OrdOrderMapper baseMapper;
    private final OrdPositionMapper positionMapper;
    private final OrdOperateMapper operateMapper;
    private final OrdOrderInfoMapper ordOrderInfoMapper;
    private final OrdDriverMapper ordDriverMapper;
    private final OrdOrderHelper orderHelper;
    private final IOrdOrderInfoService ordOrderInfoService;
    private final OrderInviteRewardProducer ordersProducer;
    private final OrderRobProducer orderRobProducer;
    private final OrdOrderOperateProducer ordOrderOperateProducer;
    private final ScheduledExecutorService scheduledExecutorService;

    private final IOrdOrderProcessService ordOrderProcessService;
    @DubboReference
    private final RemoteCouponGrantService remoteCouponGrantService;
    @DubboReference
    private final RemoteLineService remoteLineService;
    @DubboReference
    private final RemoteAgentService remoteAgentService;
    @DubboReference
    private final RemotePositionService remotePositionService;
    @DubboReference
    private final RemoteOrderRateService remoteOrderRateService;
    @DubboReference
    private final RemoteWebSocketService remoteWebSocketService;
    @DubboReference
    private final RemoteDriverService remoteDriverService;
    @DubboReference
    private final RemotePassengerService remotePassengerService;
    @DubboReference
    private final RemoteDriverLineService remoteDriverLineService;
    @DubboReference
    private final RemoteConfigService configService;
    @DubboReference
    private final RemotePaymentRecordService paymentRecordService;
    @DubboReference
    private final RemoteActivityService remoteActivityService;
    @DubboReference
    private final RemoteCarService remoteCarService;
    @DubboReference
    private final RemoteAgentLineService remoteAgentLineService;

    private final IOrdEvaluationsService ordEvaluationsService;


    /**
     * 查询订单
     *
     * @param id 主键
     * @return 订单
     */
    @Override
    public OrdOrderVo queryById(Long id) {
        OrdOrderVo orderVo = baseMapper.selectVoById(id);
        OrdEvaluationsVo ordEvaluationsVo = ordEvaluationsService.queryByOrderId(id);
        if (ordEvaluationsVo != null) {
            orderVo.setEvaluation(ordEvaluationsVo);
        }
        return fillInfo(orderVo);
    }

    /**
     * 分页查询订单列表
     *
     * @param queryWebBo 查询条件
     * @return 订单分页列表
     */
    @Override
    public TableDataInfo<OrdOrderVo> queryPageList(OrdOrderQueryWebBo queryWebBo) {
        long start = DateUtils.getUnixTimeStamps();
        LambdaQueryWrapper<OrdOrder> lqw = buildQueryWrapper(queryWebBo);
        Page<OrdOrderVo> result = baseMapper.selectVoPage(queryWebBo.build(), lqw);
        List<OrdOrderVo> records = result.getRecords();
        fillInfo(records);
        // 订单次手机收益返回null，便于前端显示--
        if (Objects.equals(queryWebBo.getIsPool(), true)) {
            records.forEach(e -> e.setDriverProfit(null));
        }
        long end = DateUtils.getUnixTimeStamps();
        log.info("查询订单列表耗时：{}ms", end - start);
        return TableDataInfo.build(result);
    }

    /**
     * 统计新增订单数量
     *
     * @param bo 查询参数
     * @return 新增订单数量
     */
    @Override
    public Long addNum(OrdOrderQueryWebBo bo) {
        // 筛选发单时间的话不生效
        if (bo.getIsPool() != null && bo.getIsPool()) {
            if (bo.getCreateTime() != null && (bo.getCreateTime().getStartTime() != null || bo.getCreateTime().getEndTime() != null)) {
                return 0L;
            }
        }
        LambdaQueryWrapper<OrdOrder> queryWrapper = buildQueryWrapper(bo);
        return baseMapper.selectCount(queryWrapper);
    }

    /**
     * 订单导出
     *
     * @param bo 查询条件
     * @return 订单信息
     */
    @Override
    public List<OrdOrderExportVo> export(OrdOrderQueryWebBo bo) {

        LambdaQueryWrapper<OrdOrder> lqw = buildQueryWrapper(bo);
        List<OrdOrderVo> result = baseMapper.selectVoList(lqw);
        fillInfo(result);
        // 活动id集合
        Set<Long> activityIds = result.stream()
                .filter(e -> ObjectUtils.isNotNull(e.getRemoteCouponGrantVo()))
                .map(e -> e.getRemoteCouponGrantVo().getActivityId())
                .collect(Collectors.toSet());
        // 活动信息
        Map<Long, String> activityId2NameMap = remoteActivityService.queryByIds(activityIds)
                .stream().collect(Collectors.toMap(RemoteActivityVo::getId, RemoteActivityVo::getName, (v1, v2) -> v1));
        Map<Long, RemoteCouponGrantVo> orderId2CouponGrantMap = result.stream()
                .filter(e -> ObjectUtils.isNotNull(e.getRemoteCouponGrantVo()))
                .collect(Collectors.toMap(OrdOrderVo::getId, OrdOrderVo::getRemoteCouponGrantVo, (v1, v2) -> v1));
        List<OrdOrderExportVo> orderExportVoList = MapstructUtils.convert(result, OrdOrderExportVo.class);
        orderExportVoList.parallelStream().forEach(vo -> {
            //司机信息
            if (vo.getDriverVo() != null) {
                vo.setDriverName(vo.getDriverVo().getDriverName());
                vo.setDriverPhone(vo.getDriverVo().getDriverPhone());
            }
            //佣金比例
            //佣金比例
            if (StringUtils.isBlank(vo.getDriverRate())) {
                //资金流向
                OrdOrderInfo ordOrderInfo = ordOrderInfoService.queryByOrderId(vo.getId());
                if (ObjectUtils.isNotNull(ordOrderInfo)) {
                    vo.setFlow(ordOrderInfo.getFlow());
                }
            }
            //资金流向
            OrdOrderInfo ordOrderInfo = ordOrderInfoService.queryByOrderId(vo.getId());
            if (ObjectUtils.isNotNull(ordOrderInfo)) {
                vo.setFlow(ordOrderInfo.getFlow());
            }
            RemotePositionVo startOrderPosition = ordCacheManager.getOrderPositionByOrderIdAndType(vo.getId(), StartEndEnum.START.getCode());
            if (ObjectUtils.isNotNull(startOrderPosition)) {
                vo.setStartProvince(startOrderPosition.getProvince());
                vo.setStartCity(startOrderPosition.getCity());
                vo.setStartDistrict(startOrderPosition.getDistrict());
                vo.setStartAddress(startOrderPosition.getAddress());
            }
            RemotePositionVo endOrderPosition = ordCacheManager.getOrderPositionByOrderIdAndType(vo.getId(), StartEndEnum.END.getCode());
            if (ObjectUtils.isNotNull(endOrderPosition)) {
                vo.setEndProvince(endOrderPosition.getProvince());
                vo.setEndCity(endOrderPosition.getCity());
                vo.setEndDistrict(endOrderPosition.getDistrict());
                vo.setEndAddress(endOrderPosition.getAddress());
            }

            // 优惠券
            if (ObjectUtil.isNotNull(orderId2CouponGrantMap.get(vo.getId()))) {
                vo.setCouponGrantName(orderId2CouponGrantMap.get(vo.getId()).getCouponName());
                vo.setCouponGrantId(orderId2CouponGrantMap.get(vo.getId()).getId());
                if (ObjectUtil.isNotNull(orderId2CouponGrantMap.get(vo.getId()).getActivityId())
                        && activityId2NameMap.containsKey(orderId2CouponGrantMap.get(vo.getId()).getActivityId())) {
                    vo.setActivityName(activityId2NameMap.get(orderId2CouponGrantMap.get(vo.getId()).getActivityId()));
                }
            }
        });
        return orderExportVoList;
    }

    private LambdaQueryWrapper<OrdOrder> buildQueryWrapper(OrdOrderQueryWebBo queryWebBo) {
        boolean isPool = queryWebBo.getIsPool() != null && queryWebBo.getIsPool();
        LambdaQueryWrapper<OrdOrder> lqw = Wrappers.lambdaQuery();

        // 数据权限
        UserTypeEnum userType = LoginHelper.getUserType();

        if (CollUtil.isNotEmpty(queryWebBo.getAgentIds())) {
            lqw.in(OrdOrder::getAgentId, queryWebBo.getAgentIds());
        } else {
            //if (UserTypeEnum.AGENT_USER.equals(userType)) {
            //    lqw.nested(queryWebBo.getAgentId() != null, l -> {
            //        l.apply("(agent_id is null or agent_id = 0)")
            //                .or().eq(OrdOrder::getAgentId, queryWebBo.getAgentId())
            //        ;
            //    });
            //} else {
            //    lqw.eq(ObjectUtil.isNotNull(queryWebBo.getAgentId()), OrdOrder::getAgentId, queryWebBo.getAgentId());
            //}

            lqw.eq(ObjectUtil.isNotNull(queryWebBo.getAgentId()), OrdOrder::getAgentId, queryWebBo.getAgentId());
        }

        if (CollUtil.isNotEmpty(queryWebBo.getLineIds()) && CollUtil.isNotEmpty(queryWebBo.getCityCodes())) {
            // ((line_id is null or line_id = 0) and (start_city_code in ('110000','120000') ) or line_id in (1,2,3))
            lqw.nested(l -> {
                l.nested(ll -> {
                            ll.apply("(line_id is null or line_id = 0)")
                                    .in(OrdOrder::getStartCityCode, queryWebBo.getCityCodes());
                        }).or()
                        .in(OrdOrder::getLineId, queryWebBo.getLineIds());
            });
        } else if (CollUtil.isNotEmpty(queryWebBo.getLineIds())) {
            lqw.in(OrdOrder::getLineId, queryWebBo.getLineIds());
        } else if (CollUtil.isNotEmpty(queryWebBo.getCityCodes())) {
            lqw.apply("(line_id is null or line_id = 0)")
                    .in(OrdOrder::getStartCityCode, queryWebBo.getCityCodes());
        }

        // 标识相关
        if (StrUtil.isNotBlank(queryWebBo.getUnionId())) {
            if (queryWebBo.getIsPool() != null && queryWebBo.getIsPool()) {
                lqw.nested(l -> {
                    l.like(OrdOrder::getOrderNo, queryWebBo.getUnionId()).or()
                            .like(OrdOrder::getId, queryWebBo.getUnionId()).or()
                            .like(OrdOrder::getPlatformNo, queryWebBo.getUnionId()).or()
                            .like(OrdOrder::getPassengerPhone, queryWebBo.getUnionId()).or()
                            .like(OrdOrder::getInsureNo, queryWebBo.getUnionId());
                });
            } else {
                lqw.nested(l -> {
                    l.like(OrdOrder::getOrderNo, queryWebBo.getUnionId()).or()
                            .like(OrdOrder::getId, queryWebBo.getUnionId()).or()
                            .like(OrdOrder::getPlatformNo, queryWebBo.getUnionId()).or()
                            .like(OrdOrder::getInsureNo, queryWebBo.getUnionId()).or()
                            .like(OrdOrder::getPassengerPhone, queryWebBo.getUnionId()).or()
                            .like(OrdOrder::getDriverId, queryWebBo.getUnionId());
                });
            }
        }
        // 主路线查询
        lqw.eq(ObjectUtil.isNotNull(queryWebBo.getLineId()), OrdOrder::getLineId, queryWebBo.getLineId());
        lqw.eq(ObjectUtil.isNotNull(queryWebBo.getDriverId()), OrdOrder::getDriverId, queryWebBo.getDriverId());
        lqw.eq(StrUtil.isNotBlank(queryWebBo.getPlatformCode()), OrdOrder::getPlatformCode, queryWebBo.getPlatformCode());
        lqw.eq(StrUtil.isNotBlank(queryWebBo.getProductCode()), OrdOrder::getProductCode, queryWebBo.getProductCode());
        lqw.eq(StrUtil.isNotBlank(queryWebBo.getLineDirection()), OrdOrder::getLineDirection, queryWebBo.getLineDirection());

        // 订单是否被过滤
        lqw.eq(StrUtil.isNotBlank(queryWebBo.getFiltered()), OrdOrder::getFiltered, queryWebBo.getFiltered());
        //下单类型
        lqw.eq(StrUtil.isNotBlank(queryWebBo.getCreateModel()), OrdOrder::getCreateModel, queryWebBo.getCreateModel());
        // 状态相关
        lqw.eq(StrUtil.isNotBlank(queryWebBo.getPayStatus()), OrdOrder::getPayStatus, queryWebBo.getPayStatus());
        lqw.eq(StrUtil.isNotBlank(queryWebBo.getRebateStatus()), OrdOrder::getRebateStatus, queryWebBo.getRebateStatus());
        // 多选查询
        lqw.in(CollUtils.isNotEmpty(queryWebBo.getPlatformCodes()), OrdOrder::getPlatformCode, queryWebBo.getPlatformCodes());
        lqw.in(CollUtils.isNotEmpty(queryWebBo.getProductCodes()), OrdOrder::getProductCode, queryWebBo.getProductCodes());
        if (CollUtil.isNotEmpty(queryWebBo.getStatuses())) {
            Set<String> statusList = queryWebBo.getStatuses().stream().map(OrderStatusTabEnum::getStatusListByCode)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toSet());
            lqw.in(!statusList.isEmpty(), OrdOrder::getStatus, statusList);
        }
        lqw.in(CollUtils.isNotEmpty(queryWebBo.getDispatchTypes()), OrdOrder::getDispatchType, queryWebBo.getDispatchTypes());
        lqw.in(CollUtils.isNotEmpty(queryWebBo.getCreateModels()), OrdOrder::getCreateModel, queryWebBo.getCreateModels());


        // 时间相关
        if (queryWebBo.getCancelTime() != null) {
            lqw.ge(queryWebBo.getCancelTime().getStartTime() != null, OrdOrder::getCancelTime, queryWebBo.getCancelTime().getStartTime());
            lqw.le(queryWebBo.getCancelTime().getEndTime() != null, OrdOrder::getCancelTime, queryWebBo.getCancelTime().getEndTime());
        }
        // 乘客预计出发时间 都根据最早出发时间改
        if (queryWebBo.getGoTime() != null) {
            lqw.ge(queryWebBo.getGoTime().getStartTime() != null, OrdOrder::getEarliestTime, queryWebBo.getGoTime().getStartTime());
            lqw.le(queryWebBo.getGoTime().getEndTime() != null, OrdOrder::getEarliestTime, queryWebBo.getGoTime().getEndTime());
        }
        // 订单出发时间
        if (queryWebBo.getStartTime() != null) {
            lqw.ge(queryWebBo.getStartTime().getStartTime() != null, OrdOrder::getTripStartTime, queryWebBo.getStartTime().getStartTime());
            lqw.le(queryWebBo.getStartTime().getEndTime() != null, OrdOrder::getTripStartTime, queryWebBo.getStartTime().getEndTime());
        }
        // 订单完单时间
        if (queryWebBo.getFinishTime() != null) {
            lqw.ge(queryWebBo.getFinishTime().getStartTime() != null, OrdOrder::getFinishTime, queryWebBo.getFinishTime().getStartTime());
            lqw.le(queryWebBo.getFinishTime().getEndTime() != null, OrdOrder::getFinishTime, queryWebBo.getFinishTime().getEndTime());
        }
        // 订单发单时间
        if (queryWebBo.getCreateTime() != null && (queryWebBo.getCreateTime().getStartTime() != null || queryWebBo.getCreateTime().getEndTime() != null)) {
            lqw.ge(queryWebBo.getCreateTime().getStartTime() != null, OrdOrder::getCreateTime, queryWebBo.getCreateTime().getStartTime());
            lqw.le(queryWebBo.getCreateTime().getEndTime() != null, OrdOrder::getCreateTime, queryWebBo.getCreateTime().getEndTime());
        } else {
            // 新增条数筛选, 筛选发单时间则不生效
            lqw.gt(queryWebBo.getLastTime() != null, OrdOrder::getCreateTime, queryWebBo.getLastTime());
        }
        if (StringUtils.isNotBlank(queryWebBo.getStatisticTime())) {
            lqw.ge(OrdOrder::getCreateTime, queryWebBo.getStatisticTime() + " 00:00:00");
            lqw.le(OrdOrder::getCreateTime, queryWebBo.getStatisticTime() + " 23:59:59");
        }
        if (StringUtils.isNotBlank(queryWebBo.getStatisticStartTime()) && StringUtils.isNotBlank(queryWebBo.getStatisticEndTime())) {
            lqw.ge(OrdOrder::getCreateTime, queryWebBo.getStatisticStartTime() + " 00:00:00");
            lqw.le(OrdOrder::getCreateTime, queryWebBo.getStatisticEndTime() + " 23:59:59");
        }

        // 内部相关--------是否客诉
        //lqw.eq(StrUtil.isNotBlank(queryWebBo.getComplain()), OrdOrder::getComplain, queryWebBo.getComplain());
        if (Objects.equals(queryWebBo.getComplain(), "0")) {
            lqw.eq(OrdOrder::getComplain, IsYesEnum.NO.getCode());
        } else if (Objects.equals(queryWebBo.getComplain(), "1")) {
            lqw.eq(OrdOrder::getComplain, IsYesEnum.YES.getCode());
        }
        // 内部相关--------订单池
        if (isPool) {
            lqw.eq(OrdOrder::getStatus, OrderStatusEnum.CREATE.getCode()); // 已下单
            lqw.eq(OrdOrder::getDispatch, IsYesEnum.NO.getCode()); // 未调度
            lqw.ge(OrdOrder::getLatestTime, DateUtils.getNowDate()); // 未到出发时间
        } else {
            lqw.eq(StrUtil.isNotBlank(queryWebBo.getInsureStatus()), OrdOrder::getInsureStatus, queryWebBo.getInsureStatus());
            lqw.eq(StrUtil.isNotBlank(queryWebBo.getDispatch()), OrdOrder::getDispatch, queryWebBo.getDispatch());
            lqw.eq(StrUtil.isNotBlank(queryWebBo.getDispatchType()), OrdOrder::getDispatchType, queryWebBo.getDispatchType());
            lqw.eq(StrUtil.isNotBlank(queryWebBo.getDispatchStatus()), OrdOrder::getDispatchStatus, queryWebBo.getDispatchStatus());
        }

        // 排序相关
        if (StrUtil.isBlank(queryWebBo.getSort())) {
            // 出发时间-升序
            lqw.orderByAsc(OrdOrder::getEarliestTime);
        } else {
            String sort = queryWebBo.getSort();
            if (sort.equals(CxSortEnum.CREATE_TIME_DESC.getCode())) {
                // 发单时间-降序
                lqw.orderByDesc(OrdOrder::getCreateTime);
            } else if (sort.equals(CxSortEnum.GO_TIME_ASC.getCode())) {
                // 出发时间-升序
                lqw.orderByAsc(OrdOrder::getEarliestTime);
            } else if (sort.equals(CxSortEnum.GO_TIME_DESC.getCode())) {
                // 出发时间-升序
                lqw.orderByDesc(OrdOrder::getEarliestTime);
            } else if (sort.equals(CxSortEnum.PRICE_DESC.getCode())) {
                // 价格-降序
                lqw.orderByDesc(OrdOrder::getOrderPrice);
            } else {
                // 出发时间-升序
                lqw.orderByAsc(OrdOrder::getEarliestTime);
            }
        }
        return lqw;
    }

    /**
     * 新增订单
     *
     * @param bo 订单
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrdOrder insertByBo(OrdOrderBo bo) {
        OrdOrder order = MapstructUtils.convert(bo, OrdOrder.class);
        order.setOrderNo(OrderUtils.makeOrderNo());

        if (ObjectUtil.isNull(order)) {
            throw new ServiceException("下单参数错误");
        }
        order.setStatus(OrderStatusEnum.CREATE.getCode());

        // 是否预约（预计出发时间晚于当日凌晨12时，即为预约）
        order.setDue(isDue(bo.getEarliestTime()));

        // 最大等待时间-分钟
        if (bo.getMaxWaitDuration() == null || bo.getMaxWaitDuration() < 1) {
            if (bo.getLatestTime() != null && bo.getEarliestTime() != null) {
                bo.setMaxWaitDuration((int) DateUtil.between(bo.getLatestTime(), bo.getEarliestTime(), DateUnit.MINUTE));
            }
        }
        //if (ObjectUtils.isNull(bo.getLineId())) {
        //    // 匹配线路
        //    RemoteAgentLineVo remoteAgentLineVo = remoteAgentLineService.matchLine(bo.getStartPosition().getAdCode(), bo.getEndPosition().getAdCode());
        //    if (ObjectUtil.isNull(remoteAgentLineVo)) {
        //        log.error("下单-线路不存在：【{}】", bo);
        //        throw new ServiceException("暂未开通此线路");
        //    }
        //    order.setLineId(remoteAgentLineVo.getLineId());
        //} else {
        //    order.setLineId(bo.getLineId());
        //}
        // 路线信息
        if (ObjectUtil.isNotNull(bo.getLineId()) && bo.getLineId() > 0) {
            RemoteLineVo remoteLineVo = oprCacheManager.getLineInfoById(bo.getLineId());
            // 校验路线状态
            this.checkLineStatus(remoteLineVo);

            if (!Objects.equals(PlatformCodeEnum.SELF.getCode(), bo.getPlatformCode())) {
                String isFiltered = lineFilter(remoteLineVo.getId(), order);
                order.setFiltered(isFiltered);
            }
        }

        // 第三方平台订单
        if (Objects.equals(bo.getCreateModel(), CreateModelEnum.THIRD_PLATFORM.getCode()) && ObjectUtil.isNotNull(bo.getLineId())) {
            List<Long> agentIds = remoteAgentLineService.getLineAgent(bo.getLineId());
            if (CollUtil.isNotEmpty(agentIds)) {
                order.setAgentId(agentIds.get(0));
            }
        }

        // TODO-NEW 后期确认标签 添加线路标签
        // order.setLabel(CollUtil.join(remoteLineVo.getLabels(), ","));

        // 路线过滤
        if (Objects.equals(PlatformCodeEnum.SELF.getCode(), bo.getPlatformCode())) {
            // 自营的没有平台单号，不需要过滤
            order.setFiltered(IsYesEnum.NO.getCode());
        }

        try {
            //邀请有奖营销活动
            String inviteReward = configService.selectValueByKey(MarketCacheConstants.INVITE_REWARD);
            boolean inviteRewardBoolean = Objects.equals(IsYesEnum.YES.getCode(), inviteReward);
            Boolean referraled = false;
            if (inviteRewardBoolean) {
                referraled = referralRewardEligibility(order);
            }

            boolean flag = baseMapper.insert(order) > 0;

            if (!flag) {
                throw new ServiceException("订单创建异常");
            }
            bo.setId(order.getId());
            //邀请有奖延迟队列
            if (referraled) {
                sendMessage(order, bo);
            }
            // TODO-NEW 日志后期使用MQ
            // SpringUtils.context().publishEvent(orderOperateEvent);

            return order;
        } catch (Exception exception) {
            log.error("下单错误信息：【{}】-参数：【{}】", exception.getMessage(), JsonUtils.toJsonString(bo));
            throw exception;
        }

        /**
         * 1.订单来后判断那个平台 自营平台
         * 2.如果自营平台
         */


        // TODO-NEW 订单创建成功后，发送MQ消息，通知其他服务进行订单匹配
    }

    /**
     * 订单创建成功后，发送MQ消息，通知其他服务进行订单匹配
     *
     * @param order
     */
    public void sendMessage(OrdOrder order, OrdOrderBo bo) {
        log.info("邀请有奖进入发送消息阶段订单号：【{}】", order.getId());
        try {
            //发送消息延迟消息
            InviteEvent inviteEvent = InviteEvent.builder()
                    .orderId(order.getId())
                    .requestNo(String.valueOf(order.getId()))
                    .build();
            ordersProducer.sendMessage(inviteEvent);
            log.info("邀请有奖发送消息队列成功订单号：【{}】", order.getId());
            OrdPositionBo start = bo.getStartPosition();
            OrdPositionBo end = bo.getEndPosition();
            MatchBo matchBo = new MatchBo();
            matchBo.setStartLongitude(String.valueOf(start.getLongitude()));
            matchBo.setStartLatitude(String.valueOf(start.getLatitude()));
            matchBo.setEndLongitude(String.valueOf(end.getLongitude()));
            matchBo.setEndLatitude(String.valueOf(end.getLatitude()));
            matchBo.setEarliestTime(order.getEarliestTime());
            matchBo.setLatestTime(order.getLatestTime());
            matchBo.setDriverId(order.getInviteDriverId());
            IDisRobService disRobService = SpringUtils.getBean(IDisRobService.class);
            if (disRobService.matchCommonRoute(matchBo)) {
                log.info("邀请有奖符合常用路线逻辑订单号：【{}】", order.getId());
                OrdOrderDispatchBo ordOrderDispatchBo = new OrdOrderDispatchBo();
                ordOrderDispatchBo.setOrderId(order.getId());
                ordOrderDispatchBo.setDriverId(order.getInviteDriverId());
                ordOrderDispatchBo.setType(DispatchTypeEnum.COMMON_AUTO_ROB.getCode());
                ordOrderProcessService.dispatchOrder(ordOrderDispatchBo);
                log.info("下单成功，指派给为:{}司机", order.getInviteDriverId());
                scheduledExecutorService.schedule(() -> {
                    OrdOrder ordOrder = baseMapper.selectById(order.getId());
                    if (Objects.isNull(ordOrder)) {
                        log.error("下单-发送消息失败：【{}】 订单为查询到", order.getId());
                    } else {
                        ordOrderProcessService.dispatchOrder(ordOrderDispatchBo);
                        log.info("下单-发送消息成功：【{}】", JsonUtils.toJsonString(ordOrderDispatchBo));
                    }

                }, 2, TimeUnit.SECONDS);
            } else {
                log.info("邀请有奖符合推送消息逻辑订单号：【{}】", order.getId());
                RemotePassengerVo passengerInfo = remotePassengerService.getPassengerInfo(order.getPassengerId());
                order.setDriverId(passengerInfo.getDriverId());
                order.setAgentId(passengerInfo.getAgentId());
                OrdOrderProfitBo orderProfitBo = orderHelper.createOrderProfitBo(order);
                OrdOrderProfitVo ordOrderProfitVo = orderHelper.calculateOrderProfit(orderProfitBo);
                //发送websocket
                WebSocketMessageWrapper<OrderMessage> messageWrapper = new WebSocketMessageWrapper<>();

                OrderMessage orderMessage = OrderMessage.builder()
                        .messageType(OrderMessageTypeEnum.INVITE.getCode())
                        .avatar(passengerInfo.getAvatar())
                        .nickname(OrderUtils.getPassengerName(passengerInfo.getPhone()))
                        .driverProfit(ordOrderProfitVo.getDriverProfit())
                        .driverReward(ordOrderProfitVo.getDriverInviteReward())
                        .mileage(order.getMileage())
                        .startLatitude(start.getLatitude())
                        .startLongitude(start.getLongitude())
                        .endLatitude(end.getLatitude())
                        .endLongitude(end.getLongitude())
                        .startCity(start.getCity())
                        .startDistrict(start.getDistrict())
                        .startAddr(start.getShortAddr())
                        .endCity(end.getCity())
                        .endDistrict(end.getDistrict())
                        .endAddr(end.getShortAddr())
                        .id(String.valueOf(order.getId()))
                        .orderNo(order.getOrderNo())
                        .label(order.getLabel())
                        .productCode(order.getProductCode())
                        .passengerNum(order.getPassengerNum())
                        .passengerRemark(order.getPassengerRemark())
                        .earliestTime(order.getEarliestTime())
                        .latestTime(order.getLatestTime())
                        .isDue(order.getDue())
                        .platformCode(order.getPlatformCode())
                        .platformNo(order.getPlatformNo())
                        .payStatus(order.getPayStatus())
                        .build();

                messageWrapper.setType(WebSocketTypeEnum.ORDER.getCode());
                messageWrapper.setReceiverUserType(UserTypeEnum.DRIVER_USER.getUserType());
                messageWrapper.setReceiverId(String.valueOf(order.getInviteDriverId()));
                messageWrapper.setData(orderMessage);
                scheduledExecutorService.schedule(() -> {
                    OrdOrder ordOrder = baseMapper.selectById(order.getId());
                    if (Objects.isNull(ordOrder)) {
                        log.error("下单-发送消息失败：【{}】 订单为查询到", order.getId());
                    } else {
                        log.info("下单-发送消息成功：【{}】", JsonUtils.toJsonString(messageWrapper));
                        remoteWebSocketService.sendMessage(messageWrapper);
                    }

                }, 2, TimeUnit.SECONDS);
            }

        } catch (Exception e) {
            log.error("下单-发送消息失败：【{}】", e.getMessage());
        }
    }


    /**
     * 邀请有奖的订单
     *
     * @param order
     * @return
     */

    public Boolean referralRewardEligibility(OrdOrder order) {
        order.setShowed(IsYesEnum.YES.getCode());
        //司机录单不走邀请有奖逻辑
        if (CreateModelEnum.DRIVER_ORDER.getCode().equals(order.getCreateModel()) || CreateModelEnum.RESELL_ORDER.getCode().equals(order.getCreateModel())) {
            log.info("司机录单不走邀请有奖逻辑订单号：【{}】", order.getId());
            return false;
        }
        log.info("进入邀请有奖订单号：【{}】", order.getId());
        if (!Objects.equals(PlatformCodeEnum.SELF.getCode(), order.getPlatformCode())) {
            return false;
        }
        log.info("邀请有奖满足自营条件订单号：【{}】", order.getId());
        Long passengerId = order.getPassengerId();
        RemotePassengerVo passengerVo = remotePassengerService.getPassengerInfo(passengerId);
        if (passengerVo == null || ObjectUtil.isNull(passengerVo.getAgentId())) {
            return false;
        }
        order.setInviteAgentId(passengerVo.getAgentId());

        if (ObjectUtil.isNull(passengerVo.getDriverId())) {
            return false;
        }
        log.info("邀请有奖乘客符合条件订单号：【{}】", order.getId());
        order.setInviteDriverId(passengerVo.getDriverId());

        if (CreateModelEnum.PASSENGER_QRCODE.getCode().equals(order.getCreateModel()) || CreateModelEnum.AGENT_ORDER.getCode().equals(order.getCreateModel())) {
            if (ObjectUtils.isNotNull(order.getDriverId())) {
                log.info("司机录单不走邀请有奖逻辑订单号：【{}】", order.getId());
                return false;
            }
        }

        RemoteDriverVo driverInfo = remoteDriverService.getDriverInfo(passengerVo.getDriverId());
        if (driverInfo == null) {
            return false;
        }
        if (DriverTypeEnum.SELF.getCode().equals(driverInfo.getType())) {
            Boolean hasLine = remoteDriverLineService.driverHasLine(passengerVo.getDriverId(), order.getLineId());
            if (!hasLine) {
                return false;
            }
        }
        log.info("邀请有奖司机符合条件订单号：【{}】", order.getId());
        order.setShowed(IsYesEnum.NO.getCode());
        return true;
        // 如果走到这里，说明满足了所有的条件，order 的相关字段已赋值
    }

    /**
     * 修改订单
     *
     * @param bo 订单
     * @return 是否修改成功
     */
    @Override
    public OrdOrder updateByBo(OrdOrderBo bo) {

        OrdOrder order = baseMapper.selectById(bo.getId());

        //OrdOrder update = MapstructUtils.convert(bo, OrdOrder.class);
        OrdOrder update = new OrdOrder();
        update.setId(bo.getId());
        update.setCreateModel(bo.getCreateModel());
        update.setOrderPrice(bo.getOrderPrice());
        update.setPayPrice(bo.getOrderPrice());
        update.setResellDriverPrice(bo.getResellDriverPrice());
        update.setStatus(order.getStatus());

        // 校验
        validEntityBeforeSave(update);

        baseMapper.updateById(update);
        return update;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrdOrder entity) {
        if (!Objects.equals(entity.getStatus(), OrderStatusEnum.CREATE.getCode())) {
            throw new ServiceException("订单状态已变更，请刷新列表后重试");
        }
        if (Objects.equals(entity.getCreateModel(), CreateModelEnum.RESELL_ORDER.getCode())) {
            RemoteDriverVo driverVo = powCacheManager.getDriverInfoById(LoginHelper.getUserId());
            if (ObjectUtils.isNotNull(driverVo)) {
                // 获取司机的转卖服务费
                Long resellServiceProfit = ArithUtils.profitUseBigDecimal(entity.getOrderPrice(), driverVo.getResellServiceRate());
                if (entity.getResellDriverPrice() > (entity.getOrderPrice() - resellServiceProfit)) {
                    throw new ServiceException("司机接单金额设置不合理，请重新设置后再提交");
                }
            }
        }
    }

    /**
     * 校验并批量删除订单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据平台编码和平台订单号查询订单
     *
     * @param platformCode 平台编码
     * @param platformNo   平台订单号
     * @return 订单信息
     */
    @Override
    public OrdOrder queryByPlatformCodeAndPlatformNo(String platformCode, String platformNo) {
        LambdaQueryWrapper<OrdOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrdOrder::getPlatformCode, platformCode)
                .eq(OrdOrder::getPlatformNo, platformNo)
                .orderByDesc(OrdOrder::getCreateTime)
                .last(Constants.LIMIT_ONE);

        return baseMapper.selectOne(lqw);
    }

    @Override
    public Boolean updateShowById(Long id) {
        LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrdOrder::getId, id)
                .set(OrdOrder::getShowed, IsYesEnum.YES.getCode());
        boolean updateFlag = baseMapper.update(updateWrapper) > 0;
        if (updateFlag) {
            OrdOrder order = baseMapper.selectById(id);
            RemotePositionVo startPos = ordCacheManager.getOrderPositionByOrderIdAndType(order.getId(), StartEndEnum.START.getCode());
            RemotePositionVo endPos = ordCacheManager.getOrderPositionByOrderIdAndType(order.getId(), StartEndEnum.END.getCode());
            // 订单状态是“已下单”。将订单重新放到自动抢单MQ
            if (Objects.equals(order.getStatus(), OrderStatusEnum.CREATE.getCode()) && ObjectUtils.isNotNull(startPos) && ObjectUtils.isNotNull(endPos)) {
                OrderRobEvent orderRobEvent = orderHelper.buildOrderRobEvent(order, BeanUtils.copyProperties(startPos, RemotePositionBo.class),
                        BeanUtils.copyProperties(endPos, RemotePositionBo.class));
                orderRobProducer.sendMessage(orderRobEvent);
            }
        }

        return updateFlag;
    }

    /**
     * 删除未被接单且已经被取消的订单
     * 删除的数据包含：订单数据，订单位置数据，订单操作数据,订单信息数据,订单司机数据
     * <p>
     * 删除过期的无用数据，不需要保证事务，即使删除报错也不需要回滚
     *
     * @param orderIds 可以删除的订单id集合
     */
    @Override
    public void deleteCancelOrders(List<Long> orderIds) {
        // 按 2000 分批，降低单次数据库压力
        List<List<Long>> splitOrderIds = CollUtil.split(orderIds, 2000);

        // 创建自定义线程池
        ForkJoinPool customPool = new ForkJoinPool(6);
        try {
            customPool.submit(() ->
                    splitOrderIds.parallelStream().forEach(orderIdList -> {
                        try {
                            // 同步删除关联表
                            positionMapper.deleteByOrderIds(orderIdList);
                            operateMapper.deleteByOrderIds(orderIdList);
                            ordOrderInfoMapper.deleteByOrderIds(orderIdList);
                            ordDriverMapper.deleteByOrderIds(orderIdList);
                            // 最后删除订单数据
                            baseMapper.deleteByOrderIds(orderIdList);
                        } catch (Exception e) {
                            log.error("批次删除失败: orderIds={}", orderIdList, e);
                        }
                    })
            ).get(); // 等待所有任务完成
        } catch (InterruptedException | ExecutionException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("删除任务被中断", e);
        } finally {
            // 显式关闭线程池（关键步骤！）
            customPool.shutdown();
        }
    }

    /**
     * 修改乘客出发时间
     *
     * @param handleBo
     * @return
     */
    @Override
    public Boolean updateEarliestTime(OrdOrderHandleBaseBo handleBo) {
        boolean flag = false;
        // 操作记录
        OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildOrderOperateEvent(handleBo.getOrderId(), OperateTypeEnum.UPDATE_EARLIEST_TIME.getCode(), JsonUtils.toJsonString(handleBo));
        try {
            OrdOrder order = baseMapper.selectById(handleBo.getOrderId());
            if (ObjectUtils.isNull(order)) {
                throw new ServiceException("订单不存在");
            }
            List<String> orderStatuses = Stream.of(OrderStatusEnum.CREATE.getCode(), OrderStatusEnum.RECEIVE.getCode(), OrderStatusEnum.PICK.getCode(),
                    OrderStatusEnum.PICK_START.getCode()).toList();

            if (!orderStatuses.contains(order.getStatus())) {
                throw new ServiceException("当前订单状态，不允许修改乘客出发时间");
            }

            if (!Objects.equals(order.getPlatformCode(), PlatformCodeEnum.SELF.getCode())) {
                throw new ServiceException("仅支持自营订单修改乘客出发时间");
            }

            LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(OrdOrder::getId, handleBo.getOrderId())
                    .set(OrdOrder::getEarliestTime, handleBo.getEarliestTime())
                    .set(OrdOrder::getMaxWaitDuration, 30)
                    .set(OrdOrder::getLatestTime, DateUtil.offsetMinute(handleBo.getEarliestTime(), 30))
                    .set(OrdOrder::getUpdateTime, DateUtils.getNowDate());

            flag = baseMapper.update(updateWrapper) > 0;

            //operateEvent.setResponseJson(JsonUtils.toJsonString(flag));
            operateEvent.setRemark(OperateTypeEnum.UPDATE_EARLIEST_TIME.getInfo() + SuccessFailEnum.SUCCESS.getInfo()
                    + "，修改前出发时间: " + DateUtil.format(order.getEarliestTime(), DatePattern.NORM_DATETIME_MINUTE_PATTERN)
                    + "，修改后出发时间: " + DateUtil.format(handleBo.getEarliestTime(), DatePattern.NORM_DATETIME_MINUTE_PATTERN));

        } catch (ServiceException se) {
            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
            operateEvent.setRemark(OperateTypeEnum.UPDATE_EARLIEST_TIME.getInfo() + SuccessFailEnum.FAIL.getInfo());
            throw new ServiceException(se.getMessage());
        } catch (Exception e) {
            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
            operateEvent.setRemark(OperateTypeEnum.UPDATE_EARLIEST_TIME.getInfo() + SuccessFailEnum.FAIL.getInfo());
            throw new ServiceException("修改乘客出发时间失败");
        } finally {
            ordOrderOperateProducer.sendMessage(operateEvent);
        }

        return flag;
    }

    /**
     * 判断订单是否预约
     *
     * @param earliestTime 预计出发时间
     * @return 是否预约
     */
    private String isDue(Date earliestTime) {
        return DateUtil.isSameDay(new Date(), earliestTime) ? IsYesEnum.NO.getCode() : IsYesEnum.YES.getCode();
    }

    /**
     * 检查线路状态
     *
     * @param remoteLineVo 线路信息
     */
    private void checkLineStatus(RemoteLineVo remoteLineVo) {
        if (remoteLineVo == null || Objects.equals(remoteLineVo.getStatus(), StatusEnum.DISABLE.getCode())) {
            log.error("未查询到线路信息或者路线未启用，路线信息: 【{}】", remoteLineVo);
            throw new ServiceException("线路不存在或路线未启用");
        }
    }

    /**
     * 线路过滤
     *
     * @param lineId 线路id
     * @param order  订单信息
     * @return 是否过滤
     */
    private String lineFilter(Long lineId, OrdOrder order) {
        // TODO-NEW 线路过滤暂时不写
        return IsYesEnum.NO.getCode();
    }

    /**
     * 填充订单信息
     *
     * @param orderVo 订单信息
     * @return 订单信息
     */
    private OrdOrderVo fillInfo(OrdOrderVo orderVo) {
        List<OrdOrderVo> orderVos = Collections.singletonList(orderVo);
        fillInfo(orderVos);
        return orderVos.get(0);
    }

    /**
     * 填充订单信息
     *
     * @param orderVos 订单信息集合
     */
    private void fillInfo(List<OrdOrderVo> orderVos) {
        // 订单id集合
        List<Long> ids = orderVos.stream().map(OrdOrderVo::getId).toList();
        Set<Long> lineIds = orderVos.stream().map(OrdOrderVo::getLineId).collect(Collectors.toSet());
        Set<Long> driverIds = orderVos.stream()
                .filter(e -> e.getDriverId() != null && e.getDriverId() > 0)
                .map(OrdOrderVo::getDriverId).collect(Collectors.toSet());

        // 订单关联信息
        Map<Long, OrdOrderInfo> orderId2InfoMap = ordOrderInfoService.queryByOrderIds(ids)
                .stream().collect(Collectors.toMap(OrdOrderInfo::getOrderId, Function.identity(), (v1, v2) -> v2));

        // 支付流水信息
        Map<Long, List<RemotePaymentRecordVo>> payInfoMap = paymentRecordService.queryList(JoinEnum.ORDER.getCode(), ids)
                .stream().collect(Collectors.groupingBy(RemotePaymentRecordVo::getJoinId));

        // 线路信息
        Map<Long, RemoteLineVo> lineInfoMap = remoteLineService.queryByLineIds(new ArrayList<>(lineIds))
                .stream().collect(Collectors.toMap(RemoteLineVo::getId, Function.identity()));

        // 代理商信息
        Map<Long, RemoteAgentVo> agentInfoMap = remoteAgentService.getAllAgentInfo()
                .stream().collect(Collectors.toMap(RemoteAgentVo::getId, Function.identity()));

        // 订单位置信息
        Map<String, RemotePositionVo> positionInfoMap = remotePositionService.queryByOrderIds(ids)
                .stream().collect(Collectors.toMap(e -> e.getOrderId() + "#" + e.getType(), Function.identity(), (v1, v2) -> v2));

        // 订单收益信息
        Map<String, RemoteOrderRateVo> rateInfoMap = remoteOrderRateService.queryByOrderIds(ids)
                .stream().collect(Collectors.toMap(e -> e.getOrderId() + "#" + e.getRateType(), Function.identity(), (v1, v2) -> v2));

        //优惠券信息
        Map<Long, RemoteCouponGrantVo> couponGrantId2InfoMap = orderId2InfoMap.values().parallelStream()
                .map(OrdOrderInfo::getCouponGrantId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet())
                .parallelStream()
                .flatMap(id -> remoteCouponGrantService.getCouponGrantByIds(List.of(id)).stream())
                .collect(Collectors.toMap(RemoteCouponGrantVo::getId, Function.identity()));

        // 车辆信息
        Map<Long, Integer> driverId2SeatMap = remoteCarService.queryByDriverIds(new ArrayList<>(driverIds))
                .stream().collect(Collectors.toMap(RemoteCarVo::getDriverId, RemoteCarVo::getSeat, (v1, v2) -> v2));

        // 司机信息
        Map<Long, RemoteDriverVo> driverInfoMap = remoteDriverService.getDriverByIds(new ArrayList<>(driverIds))
                .stream().collect(Collectors.toMap(RemoteDriverVo::getId, Function.identity(), (v1, v2) -> v2));

        for (OrdOrderVo orderVo : orderVos) {
            // 线路信息
            RemoteLineVo lineVo = lineInfoMap.get(orderVo.getLineId());
            if (ObjectUtils.isNotNull(lineVo)) {
                orderVo.setLineName(lineVo.getName());
            }
            // 代理信息
            RemoteAgentVo agentVo = agentInfoMap.get(orderVo.getAgentId());
            if (ObjectUtils.isNotNull(agentVo)) {
                orderVo.setAgentName(agentVo.getCompanyName());
                orderVo.setAgentPhone(agentVo.getServicesPhone());

                // 父代理商信息
                if (ObjectUtils.isNotNull(agentVo.getParentId()) && agentVo.getParentId() > 0) {
                    RemoteAgentVo parentAgentVo = agentInfoMap.get(agentVo.getParentId());
                    if (ObjectUtils.isNotNull(parentAgentVo)) {
                        // 总后台只展示主代理商信息
                        if (UserTypeEnum.SYS_USER.equals(LoginHelper.getUserType())) {
                            orderVo.setAgentId(parentAgentVo.getId());
                            orderVo.setAgentName(parentAgentVo.getCompanyName());
                        } else {
                            orderVo.setParentAgentName(parentAgentVo.getCompanyName());
                        }
                    }
                }
            }

            // 车辆座位数
            orderVo.setSeat(driverId2SeatMap.getOrDefault(orderVo.getDriverId(), 0));

            // 司机类型
            if (driverInfoMap.get(orderVo.getDriverId()) != null) {
                orderVo.setDriverType(driverInfoMap.get(orderVo.getDriverId()).getType());
                orderVo.setDriverTypeText(DriverTypeEnum.getInfoByCode(driverInfoMap.get(orderVo.getDriverId()).getType()));
            }

            // 订单客诉
            if (Objects.equals(orderVo.getComplain(), IsYesEnum.YES.getCode())) {
                orderVo.setComplainPrice(orderVo.getPayPrice());
                if (orderId2InfoMap.get(orderVo.getId()) != null) {
                    orderVo.setComplainTime(orderId2InfoMap.get(orderVo.getId()).getComplainTime());
                }
            }

            // 订单取消
            if (Objects.equals(orderVo.getStatus(), OrderStatusEnum.CANCEL.getCode())) {
                orderVo.setCancelUserType(UserTypeEnum.getInfoByCode(orderVo.getCancelUserType()));
                orderVo.setReceived((orderVo.getDriverId() != null && orderVo.getDriverId() > 0) ? IsYesEnum.YES.getInfo() : IsYesEnum.NO.getInfo());
                orderVo.setChanged(Objects.equals(orderVo.getDispatchType(), DispatchTypeEnum.CHANGE.getCode()) ? IsYesEnum.YES.getInfo() : IsYesEnum.NO.getInfo());
            }

            // 将里程单位转换成km
            if (ObjectUtils.isNotNull(orderVo.getMileage()) && orderVo.getMileage() > 0) {
                orderVo.setMileage(orderVo.getMileage() / 1000);
            }

            // 将预计时长转换成分钟
            if (ObjectUtils.isNotNull(orderVo.getExpectDuration()) && orderVo.getExpectDuration() > 0) {
                orderVo.setExpectDuration(orderVo.getExpectDuration() / 60);
            }

            if (ObjectUtils.isNotNull(orderId2InfoMap.get(orderVo.getId()))) {
                // 乘客联系电话
                if (StringUtils.isBlank(orderVo.getPassengerPhone()) && ObjectUtils.isNotNull(orderId2InfoMap.get(orderVo.getId()))) {
                    orderVo.setPassengerPhone(orderId2InfoMap.get(orderVo.getId()).getVirtualPhone());
                }
                orderVo.setVirtualPhone(orderId2InfoMap.get(orderVo.getId()).getVirtualPhone());

                // 客诉
                orderVo.setComplainPrice(0L);
                if (Objects.equals(orderVo.getComplain(), IsYesEnum.YES.getCode())) {
                    orderVo.setComplainPrice(orderId2InfoMap.get(orderVo.getId()).getComplainPrice());
                    orderVo.setComplainRemark(orderId2InfoMap.get(orderVo.getId()).getComplainRemark());
                }

                // 取消原因
                orderVo.setCancelRemark(orderId2InfoMap.get(orderVo.getId()).getCancelRemark());
            }

            // 最新接单司机
            RemoteOrderDriverVo orderDriverVo = ordCacheManager.getOrderDriverInfoByOrderId(orderVo.getId());
            if (ObjectUtils.isNotNull(orderDriverVo)) {
                orderVo.setDriverVo(BeanUtils.copyProperties(orderDriverVo, OrdDriverVo.class));
            }
            // 调度司机
            RemoteOrderDriverVo orderDispatchDriverVo = ordCacheManager.getOrderDispatchDriverInfoByOrderId(orderVo.getId());
            if (ObjectUtils.isNotNull(orderDispatchDriverVo)) {
                orderVo.setDispatchDriverName(orderDispatchDriverVo.getDriverName());
                orderVo.setDispatchDriverPhone(orderDispatchDriverVo.getDriverPhone());
                orderVo.setDispatchCarNumber(orderDispatchDriverVo.getCarNumber());
            }
            //优惠券信息
            orderVo.setCouponGrantQuota(0L);
            if (ObjectUtils.isNotNull(orderId2InfoMap.get(orderVo.getId()))) {
                RemoteCouponGrantVo remoteCouponGrantVo = couponGrantId2InfoMap.get(orderId2InfoMap.get(orderVo.getId()).getCouponGrantId());
                orderVo.setRemoteCouponGrantVo(remoteCouponGrantVo);
                if (ObjectUtils.isNotNull(remoteCouponGrantVo)) {
                    orderVo.setCouponGrantQuota(remoteCouponGrantVo.getQuota());
                }
            }

            // 起止位置
            RemotePositionVo startPositionVo = positionInfoMap.get(orderVo.getId() + "#" + StartEndEnum.START.getCode());
            RemotePositionVo endPositionVo = positionInfoMap.get(orderVo.getId() + "#" + StartEndEnum.END.getCode());
            orderVo.setStartPositionVo(BeanUtils.copyProperties(startPositionVo, OrdPositionVo.class));
            orderVo.setEndPositionVo(BeanUtils.copyProperties(endPositionVo, OrdPositionVo.class));
            if (ObjectUtils.isNotNull(startPositionVo) && ObjectUtils.isNotNull(endPositionVo)) {
                orderVo.setLineDistrictName("(" + startPositionVo.getDistrict() + "-" + endPositionVo.getDistrict() + ")");
            }

            // 收益计算 有司机id再计算
            orderVo.setParentAgentProfit(0L);
            orderVo.setAgentProfit(0L);
            orderVo.setDriverProfit(0L);
            if (ArithUtils.isNull(orderVo.getDriverProfit()) && ArithUtils.isNotNull(orderVo.getDriverId())) {
                RemoteOrderRateVo parentAgentOrderRateVo = rateInfoMap.get(orderVo.getId() + "#" + RateTypeEnum.PARENT_AGENT.getCode());
                RemoteOrderRateVo agentOrderRateVo = rateInfoMap.get(orderVo.getId() + "#" + RateTypeEnum.AGENT.getCode());
                RemoteOrderRateVo driverOrderRateVo = rateInfoMap.get(orderVo.getId() + "#" + RateTypeEnum.DRIVER.getCode());

                orderVo.setParentAgentProfit(ObjectUtils.isNotNull(parentAgentOrderRateVo) ? parentAgentOrderRateVo.getAmount() : null);
                orderVo.setAgentProfit(ObjectUtils.isNotNull(agentOrderRateVo) ? agentOrderRateVo.getAmount() : 0L);
                orderVo.setAgentRate(ObjectUtils.isNotNull(agentOrderRateVo) ? agentOrderRateVo.getRate() : new BigDecimal(0));
                orderVo.setDriverProfit(ObjectUtils.isNotNull(driverOrderRateVo) ? driverOrderRateVo.getAmount() : 0L);
                orderVo.setDriverRate(ObjectUtils.isNotNull(driverOrderRateVo) ? driverOrderRateVo.getRate() : new BigDecimal(0));

            }

            // 根据不同用户类型，获取订单价格
            if (UserTypeEnum.AGENT_USER.equals(LoginHelper.getUserType()) && (ArithUtils.isNull(orderVo.getDriverId()) || orderVo.getDriverId() == 0)) {
                RemoteAgentVo curAgent = agentInfoMap.get(LoginHelper.getAgentId());
                orderVo.setAgentId(curAgent.getId());
                orderVo.setParentId(curAgent.getParentId());
                OrdOrderProfitBo cxProfitBo = orderHelper.createOrderProfitBo(BeanUtils.copyProperties(orderVo, OrdOrder.class));
                OrdOrderProfitVo ordOrderProfitVo = orderHelper.calculateOrderProfit(cxProfitBo);
                if (!Objects.equals(orderVo.getCreateModel(), CreateModelEnum.RESELL_ORDER.getCode())) {
                    orderVo.setOrderPrice(ordOrderProfitVo.getAgentProfit());
                    orderVo.setAgentProfit(ordOrderProfitVo.getAgentProfit());
                }
            }

            // 订单转卖代理商收益 显示订单金额
            if (Objects.equals(orderVo.getCreateModel(), CreateModelEnum.RESELL_ORDER.getCode())) {
                orderVo.setAgentProfit(orderVo.getOrderPrice());
            }

            // 支付单号优先支付记录中获取
            List<RemotePaymentRecordVo> recordVos = payInfoMap.get(orderVo.getId());
            if (CollUtil.isNotEmpty(recordVos)) {
                Optional<RemotePaymentRecordVo> latestRecordOpt = recordVos.stream()
                        .filter(e -> Objects.equals(e.getStatus(), PaymentStatusEnum.SUCCESS.getCode()))
                        .max(Comparator.comparingLong(e -> e.getCreateTime().getTime()));

                latestRecordOpt.ifPresent(record -> orderVo.setPayNo(record.getOutBizNo()));
            }
        }
    }
}


