package com.feidi.xx.cross.order.strategy;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单策略工厂
 *
 * <AUTHOR>
 * @date 2025/3/13
 */
@Component
public class OrdOrderStrategyFactory implements InitializingBean {

    @Autowired
    private List<OrdOrderStrategy> orderStrategyList;

    private final Map<String, OrdOrderStrategy> strategyMap = new HashMap<>();

    public OrdOrderStrategy getOrderStrategy(String strategyType) {
        return strategyMap.get(strategyType);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        for (OrdOrderStrategy orderStrategy : orderStrategyList) {
            strategyMap.put(orderStrategy.getStrategyType().getCode(), orderStrategy);
        }
    }
}
