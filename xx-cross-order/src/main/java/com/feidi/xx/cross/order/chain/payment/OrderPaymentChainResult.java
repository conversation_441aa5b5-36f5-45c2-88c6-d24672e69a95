package com.feidi.xx.cross.order.chain.payment;

import com.feidi.xx.cross.order.chain.base.OrderBaseChainResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 支付结果
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderPaymentChainResult extends OrderBaseChainResult {

    /**
     * 支付信息
     */
    private Object payInfo;

    public OrderPaymentChainResult(boolean success) {
        this.success = success;
    }

}
