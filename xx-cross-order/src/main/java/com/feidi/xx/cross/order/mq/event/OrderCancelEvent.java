package com.feidi.xx.cross.order.mq.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 订单支付事件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderCancelEvent implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 延迟时间 单位:毫秒
     */
    private Long delayTime;

    /**
     * 取消类型
     */
    private String cancelType;

    /**
     * 是否只修改支付类型
     */
    private Boolean paymentTypeChangeOnly;

}
