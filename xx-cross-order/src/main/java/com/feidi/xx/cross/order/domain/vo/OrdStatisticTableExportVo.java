package com.feidi.xx.cross.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.feidi.xx.cross.order.domain.OrdStatistic;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 订单统计视图对象 ord_statistic
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrdStatistic.class)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER, verticalAlignment = VerticalAlignmentEnum.CENTER)
public class OrdStatisticTableExportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "代理商")
    private String agentName;

    @ExcelProperty(value = "线路")
    private String lineName;

    @ExcelProperty(value = "总发单量")
    private Long volumeNumber;

    @ExcelProperty(value = "当日出发订单")
    private Long departNumber;

    @ExcelProperty(value = "接单数")
    private Long acceptNumber;

    @ExcelProperty(value = "待出发")
    private Long receiveNumber;

    @ExcelProperty(value = "前往上车点")
    private Long pickNumber;

    @ExcelProperty(value = "到达上车点")
    private Long pickStartNumber;

    @ExcelProperty(value = "行程中")
    private Long ingNumber;

    @ExcelProperty(value = "已完成")
    private Long finishNumber;

    @ExcelProperty(value = "已取消")
    private Long cancelNumber;

    @ExcelProperty(value = "未支付取消")
    private Long unpayCancelNumber;

    @ExcelProperty(value = "已支付取消")
    private Long payCancelNumber;

    @ExcelProperty(value = "完单汇总")
    private Long finishSummary;

    @ExcelProperty(value = "接完率")
    @ReverseAutoMapping(target = "receiveFinishRate", expression = "java(source.getReceiveFinishRate() + \"%\")")
    private String receiveFinishRate;

    @ExcelProperty(value = "完单金额")
    @ReverseAutoMapping(target = "finishAmount", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanStr(source.getFinishAmount()))")
    private String finishAmount;
}
