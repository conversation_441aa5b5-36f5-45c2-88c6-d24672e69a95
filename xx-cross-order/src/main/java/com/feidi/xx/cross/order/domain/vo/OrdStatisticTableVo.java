package com.feidi.xx.cross.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.feidi.xx.cross.order.domain.OrdStatistic;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 订单统计视图对象 ord_statistic
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrdStatistic.class)
public class OrdStatisticTableVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 平台编号 
     */
    private String platformCode;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 线路id
     */
    private Long lineId;

    /**
     * 线路名称
     */
    private String lineName;

    /**
     * 统计时间
     */
    private Date statisticDate;

    /**
     * 总发单量
     */
    private Long volumeNumber;

    /**
     * 当日出发订单
     */
    private Long departNumber;

    /**
     * 接单数
     */
    private Long acceptNumber;

    /**
     * 待出发数量
     */
    private Long receiveNumber;

    /**
     * 前往上车点数量
     */
    private Long pickNumber;

    /**
     * 到达上车点数量
     */
    private Long pickStartNumber;

    /**
     * 行程中数量
     */
    private Long ingNumber;

    /**
     * 已完成数量
     */
    private Long finishNumber;

    /**
     * 已取消数量
     */
    private Long cancelNumber;

    /**
     * 未支付取消数量
     */
    private Long unpayCancelNumber;

    /**
     * 已支付取消数量
     */
    private Long payCancelNumber;

    /**
     * 完单汇总
     */
    private Long finishSummary;

    /**
     * 接完率
     */
    private BigDecimal receiveFinishRate;

    /**
     * 完单金额
     */
    private Long finishAmount;

    /**
     * 子数据
     */
    List<OrdStatisticTableVo> children = new ArrayList<>();
}
