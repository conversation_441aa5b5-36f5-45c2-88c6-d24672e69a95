package com.feidi.xx.cross.order.service;

import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderHandleBaseBo;

/**
 * 订单保险Service接口
 *
 * <AUTHOR>
 * @date 2025/3/16
 */
public interface IOrdOrderInsureService {
    /**
     * 投保
     *
     * @param bo
     * @return
     */
    Boolean insure(OrdOrderHandleBaseBo bo);

    /**
     * 处理订单投保
     *
     * @param order    订单信息
     * @param fileType 投保类型
     * @param bo
     */
    void handleOrderInsure(OrdOrder order, String fileType, OrdOrderHandleBaseBo bo);
}
