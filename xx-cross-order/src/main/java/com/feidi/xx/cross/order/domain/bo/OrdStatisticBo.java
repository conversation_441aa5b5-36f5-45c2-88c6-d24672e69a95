package com.feidi.xx.cross.order.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.order.domain.OrdStatistic;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单统计业务对象 ord_statistic
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrdStatistic.class, reverseConvertGenerate = false)
public class OrdStatisticBo extends TenantEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 平台编号 
     */
    @NotBlank(message = "平台编号 不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platformCode;

    /**
     * 代理商id
     */
    @NotNull(message = "代理商id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long agentId;

    /**
     * 代理商名称
     */
    @NotBlank(message = "代理商名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String agentName;

    /**
     * 线路id
     */
    @NotNull(message = "线路id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long lineId;

    /**
     * 线路名称
     */
    @NotBlank(message = "线路名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String lineName;

    /**
     * 统计时间
     */
    @NotNull(message = "统计时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date statisticDate;

    /**
     * 总发单量
     */
    @NotNull(message = "总发单量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long volumeNumber;

    /**
     * 当日出发订单
     */
    @NotNull(message = "当日出发订单不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long departNumber;

    /**
     * 接单数
     */
    @NotNull(message = "接单数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long acceptNumber;

    /**
     * 待接单数量
     */
    @NotNull(message = "待接单数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long createNumber;

    /**
     * 待出发数量
     */
    @NotNull(message = "待出发数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long receiveNumber;

    /**
     * 前往上车点数量
     */
    @NotNull(message = "前往上车点数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long pickNumber;

    /**
     * 到达上车点数量
     */
    @NotNull(message = "到达上车点数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long pickStartNumber;

    /**
     * 行程中数量
     */
    @NotNull(message = "行程中数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long ingNumber;

    /**
     * 已完成数量
     */
    @NotNull(message = "已完成数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long finishNumber;

    /**
     * 已取消数量
     */
    @NotNull(message = "已取消数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long cancelNumber;

    /**
     * 未支付取消数量
     */
    @NotNull(message = "未支付取消数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long unpayCancelNumber;

    /**
     * 已支付取消数量
     */
    @NotNull(message = "已支付取消数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long payCancelNumber;

    /**
     * 完单汇总
     */
    @NotNull(message = "完单汇总不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long finishSummary;

    /**
     * 接完率
     */
    @NotNull(message = "接完率不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal receiveFinishRate;

    /**
     * 完单金额
     */
    @NotNull(message = "完单金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long finishAmount;

    /**
     * 支付金额
     */
    @NotNull(message = "支付金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long payAmount;

    /**
     * 支付订单数
     */
    @NotNull(message = "支付订单数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long payNumber;

    /**
     * 客诉订单数
     */
    @NotNull(message = "客诉订单数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long complainNumber;

    /**
     * 客诉金额
     */
    @NotNull(message = "客诉金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long complainAmount;

    /**
     * 新增乘客注册数
     */
    @NotNull(message = "新增乘客注册数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long passengerRegisterNumber;

    /**
     * 乘客访问数量
     */
    @NotNull(message = "乘客访问数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long passengerVisitNumber;

    /**
     * 统计类型（0-完单金额，1-完单数，2-支付金额，3-支付订单数，'''）
     */
    private String lineType;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 导出类型（0:导出图表数据，1:导出表格数据）
     */
    private String exportType;

}
