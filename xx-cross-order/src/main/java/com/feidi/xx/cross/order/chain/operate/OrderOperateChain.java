package com.feidi.xx.cross.order.chain.operate;

import com.feidi.xx.cross.common.constant.order.OrderLockKeyConstants;
import com.feidi.xx.cross.order.chain.cancel.OrderCancelChainContext;
import com.feidi.xx.cross.order.chain.cancel.OrderCancelChainResult;
import com.feidi.xx.cross.order.chain.common.BaseChain;
import com.feidi.xx.cross.order.chain.handler.OrderDataHandler;
import com.feidi.xx.cross.order.chain.handler.OrderServiceHandler;
import com.feidi.xx.cross.order.chain.handler.OrderVerifyHandler;
import com.feidi.xx.cross.order.chain.handler.common.OrderAfterAsyncHandler;
import com.feidi.xx.cross.order.chain.handler.common.OrderAfterHandler;
import com.feidi.xx.cross.order.chain.handler.common.OrderPreHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 订单操作链
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderOperateChain extends BaseChain<OrderOperateChainContext, OrderOperateChainResult> implements ApplicationContextAware {

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        OrderPreHandler<OrderOperateChainContext, OrderOperateChainResult> orderPreHandler = applicationContext.getBean(OrderPreHandler.class);
        OrderDataHandler<OrderOperateChainContext, OrderOperateChainResult> orderDataHandler = applicationContext.getBean(OrderDataHandler.class);
        OrderVerifyHandler<OrderOperateChainContext, OrderOperateChainResult> orderVerifyHandler = applicationContext.getBean(OrderVerifyHandler.class);
        OrderServiceHandler<OrderOperateChainContext, OrderOperateChainResult> orderServiceHandler = applicationContext.getBean(OrderServiceHandler.class);
        OrderAfterHandler<OrderOperateChainContext, OrderOperateChainResult> orderAfterHandler = applicationContext.getBean(OrderAfterHandler.class);
        OrderAfterAsyncHandler<OrderOperateChainContext, OrderOperateChainResult> orderAfterAsyncHandler = applicationContext.getBean(OrderAfterAsyncHandler.class);

        handlers.add(orderPreHandler);
        handlers.add(orderDataHandler);
        handlers.add(orderVerifyHandler);
        handlers.add(orderServiceHandler);
        handlers.add(orderAfterHandler);
        handlers.add(orderAfterAsyncHandler);
    }

    @Override
    public String getLockKey(OrderOperateChainContext context) {
        return OrderLockKeyConstants.LOCK_ORDER_KEY + "operate:" + context.getOrderId();
    }
}
