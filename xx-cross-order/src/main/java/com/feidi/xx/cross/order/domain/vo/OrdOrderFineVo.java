package com.feidi.xx.cross.order.domain.vo;

import lombok.Data;

/**
 * 订单扣款处理BO
 */
@Data
public class OrdOrderFineVo {

    /**
     * 扣款金额 单元：分
     */
    private Long fine;

    /**
     * 司机扣款 单位：分
     */
    private Long driverFine;

    /**
     * 司机新收益 单位：分
     */
    private Long newDriverProfit;

    /**
     * 代理商扣款 单位：分
     */
    private Long agentFine;

    /**
     * 代理商新收益 单位：分
     */
    private Long newAgentProfit;

    /**
     * 父代理商扣款 单位：分
     */
    private Long parentAgentFine;

    /**
     * 父代理商新收益 单位：分
     */
    private Long newParentAgentProfit;

}
