package com.feidi.xx.cross.order.chain.place;


import com.feidi.xx.cross.common.enums.order.OperateTypeEnum;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderBo;
import com.feidi.xx.cross.order.chain.base.OrderBaseChainContext;
import com.feidi.xx.cross.order.domain.OrdOrder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 下单上下文
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderPlaceChainContext extends OrderBaseChainContext {

    /**
     * 询价key
     */
    private String estimateKey;

    /**
     * 乘客备注
     */
    private String passengerRemark;

    /**
     * 高速类型
     */
    private String highwayType;

    /**
     * 产品code
     */
    private String productCode;

    /**
     * 订单信息
     */
    private OrdOrder order;

    /**
     * 下单信息
     */
    private RemoteOrderBo orderBo;

    /**
     * 订单判重key
     */
    private String orderKey;


    /**
     * 下单类型[CreateModelEnum]
     */
    private String createModel;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 乘客手机号
     */
    private String passengerPhone;

    /**
     * 接单手机号
     */
    private String driverPhone;

    /**
     * 优惠卷id
     */
    private Long couponGrantId;

    /**
     * 优惠卷额度
     */
    private Long couponQuota;
    /**
     *  司机id
     */
    private Long driverId;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 来源
     */
    private String source;

    /**
     * 转卖司机ID
     */
    private Long resellDriverId;

    /**
     * 转卖代理商ID
     */
    private Long resellAgentId;

    /**
     * 转卖后司机接单金额
     */
    private Long resellDriverPrice;

    /**
     * 订单金额
     */
    private Long orderPrice;

    /**
     * 是否发送短信
     */
    private Boolean sms;

    public OrderPlaceChainContext() {
        this.setOperateType(OperateTypeEnum.PLACE.getCode());
    }

}
