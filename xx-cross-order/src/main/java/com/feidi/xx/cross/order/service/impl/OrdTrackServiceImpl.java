package com.feidi.xx.cross.order.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.cache.order.vo.OrdOrderTrackLocationCacheVo;
import com.feidi.xx.cross.common.enums.order.OrderTrackTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.feidi.xx.cross.order.domain.bo.OrdTrackBo;
import com.feidi.xx.cross.order.domain.vo.OrdTrackVo;
import com.feidi.xx.cross.order.domain.OrdTrack;
import com.feidi.xx.cross.order.mapper.OrdTrackMapper;
import com.feidi.xx.cross.order.service.IOrdTrackService;
import com.feidi.xx.common.core.utils.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 订单轨迹Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class OrdTrackServiceImpl implements IOrdTrackService {

    private final OrdTrackMapper baseMapper;
    private final OrdCacheManager ordCacheManager;

    /**
     * 查询订单轨迹
     *
     * @param id 主键
     * @return 订单轨迹
     */
    @Override
    public OrdTrackVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询订单轨迹列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单轨迹分页列表
     */
    @Override
    public TableDataInfo<OrdTrackVo> queryPageList(OrdTrackBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrdTrack> lqw = buildQueryWrapper(bo);
        Page<OrdTrackVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的订单轨迹列表
     *
     * @param bo 查询条件
     * @return 订单轨迹列表
     */
    @Override
    public List<OrdTrackVo> queryList(OrdTrackBo bo) {
        LambdaQueryWrapper<OrdTrack> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrdTrack> buildQueryWrapper(OrdTrackBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrdTrack> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderId() != null, OrdTrack::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), OrdTrack::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getTrackData()), OrdTrack::getTrackData, bo.getTrackData());
        return lqw;
    }

    /**
     * 新增订单轨迹
     *
     * @param bo 订单轨迹
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OrdTrackBo bo) {
        OrdTrack add = MapstructUtils.convert(bo, OrdTrack.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改订单轨迹
     *
     * @param bo 订单轨迹
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OrdTrackBo bo) {
        OrdTrack update = MapstructUtils.convert(bo, OrdTrack.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrdTrack entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public void saveOrderTrackLocation(Long orderId, String tenantId) {
        List<OrdOrderTrackLocationCacheVo> trackLocationCacheList = ordCacheManager.getAllOrderTrackLocationByOrderId(orderId);
        if (CollectionUtils.isNotEmpty(trackLocationCacheList)) {
            List<OrdTrack> trackList = trackLocationCacheList.stream()
                    .map(trackLocationCacheVo -> buildOrdTrack(orderId,tenantId, trackLocationCacheVo))
                    .collect(Collectors.toList());

            int batchSize = 100;
            for (int i = 0; i < trackList.size(); i += batchSize) {
                List<OrdTrack> subList = trackList.subList(i, Math.min(i + batchSize, trackList.size()));
                try {
                    baseMapper.insertBatch(subList);
                } catch (Exception e) {
                    log.error("批量插入订单轨迹失败：{}", e.getMessage(), e);
                    throw new ServiceException("批量插入订单轨迹失败");
                }
            }
        }
    }
    private OrdTrack buildOrdTrack(Long orderId,String tenantId, OrdOrderTrackLocationCacheVo trackLocationCacheVo) {
        OrdTrack ordTrack = new OrdTrack();
        ordTrack.setTenantId(tenantId);
        ordTrack.setOrderId(orderId);
        ordTrack.setType(OrderTrackTypeEnum.REAL_TRACK.getCode());
        ordTrack.setTrackData(JsonUtils.toJsonString(trackLocationCacheVo));
        return ordTrack;
    }

    /**
     * 校验并批量删除订单轨迹信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
