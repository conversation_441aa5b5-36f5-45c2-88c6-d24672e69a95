

package com.feidi.xx.cross.order.mq.producer;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.feidi.xx.common.rocketmq.base.BaseSendExtendDTO;
import com.feidi.xx.common.rocketmq.enums.MqMessageTypeEnum;
import com.feidi.xx.common.rocketmq.producer.AbstractProducer;
import com.feidi.xx.common.rocketmq.util.MQMessageUtil;
import com.feidi.xx.cross.common.constant.market.MarketCacheConstants;
import com.feidi.xx.cross.common.constant.order.OrderRocketMQConstant;
import com.feidi.xx.cross.order.mq.event.InviteEvent;
import com.feidi.xx.system.api.RemoteConfigService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.stereotype.Component;

import java.util.Date;


/**
 * 用户兑换优惠券消息生产者
 * <p>
 * 开发时间：2024-09-10
 */
@Component
@RequiredArgsConstructor
public class OrderInviteRewardProducer extends AbstractProducer<InviteEvent> {

    @DubboReference
    private RemoteConfigService configService;
    private BaseSendExtendDTO buildBaseSendExtendParam(InviteEvent inviteEvent) {

        String inviteRewardTime = configService.selectValueByKey(MarketCacheConstants.INVITE_DRIVER_TIME);
        Date now = new Date();
        DateTime validEndTime = DateUtil.offsetMillisecond(now, Integer.parseInt(inviteRewardTime));
        return BaseSendExtendDTO.builder()
                .eventName("订单邀请有奖")
                .keys(inviteEvent.getRequestNo())
                .messageType(MqMessageTypeEnum.SYNC)
                .topic(OrderRocketMQConstant.XX_ORDER_INVITE_REWARD_DELAY_TOPIC)
                .sentTimeout(2000L)
                .delayTime(validEndTime.getTime())
                .build();
    }
    @Override
    public SendResult sendMessage(InviteEvent loginEvent) {
        return MQMessageUtil.sendMessage(loginEvent, this::buildBaseSendExtendParam);
    }
}

