package com.feidi.xx.cross.order.timer;

import cn.hutool.core.date.DateUtil;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.cross.order.service.IOrdStatisticService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 订单统计定时任务
 *
 * <AUTHOR>
 * @date 2025/7/22
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderStatisticJob {

    private final IOrdStatisticService ordStatisticService;

    @XxlJob("orderStatisticJob")
    public void orderStatisticJob() {
        if (log.isInfoEnabled()) {
            log.info("============== 订单统计定时器开始执行 ==============");
        }

        String jobParam = XxlJobHelper.getJobParam();

        String statisticDate = StringUtils.isBlank(jobParam) ? DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtil.offsetDay(new Date(), -1)) : jobParam;

        ordStatisticService.doStatistic(statisticDate);

        if (log.isInfoEnabled()) {
            log.info("============== 订单统计定时器执行结束 ==============");
        }
    }
}
