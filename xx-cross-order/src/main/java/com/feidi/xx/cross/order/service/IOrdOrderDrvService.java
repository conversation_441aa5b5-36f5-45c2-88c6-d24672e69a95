package com.feidi.xx.cross.order.service;

import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.order.domain.bo.driver.OrdOrderDrvQueryBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderHandleBo;
import com.feidi.xx.cross.order.domain.vo.driver.OrdOrderDrvDetailVo;
import com.feidi.xx.cross.order.domain.vo.driver.OrdOrderDrvIndexVo;
import com.feidi.xx.cross.order.domain.vo.driver.OrdOrderDrvResellVo;

/**
 * 司机端-订单服务接口
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
public interface IOrdOrderDrvService {

    /**
     * 首页统计
     *
     * @return
     */
    OrdOrderDrvIndexVo getIndex();

    /**
     * 订单转卖统计
     *
     * @return
     */
    OrdOrderDrvResellVo getResellIndex();

    /**
     * 订单池新增数量
     *
     * @param queryBo 查询参数
     * @return 订单池新增数量
     */
    Long newNum(OrdOrderDrvQueryBo queryBo);

    /**
     * 订单池
     *
     * @param bo
     * @return
     */
    TableDataInfo<OrdOrderDrvDetailVo> pool(OrdOrderDrvQueryBo bo);

    /**
     * 订单列表
     *
     * @param bo
     * @return
     */
    TableDataInfo<OrdOrderDrvDetailVo> queryList(OrdOrderDrvQueryBo bo);

    /**
     * 查询详情
     *
     * @param bo
     * @return
     */
    R<Object> queryDetail(OrdOrderDrvQueryBo bo);

    /**
     * 验证手机尾号
     *
     * @param handleBo
     * @return
     */
    R<String> checkPhoneEnd(OrdOrderHandleBo handleBo);

    /**
     * 司机联系乘客日志记录
     *
     * @param handleBo
     * @return
     */
    boolean makeCall(OrdOrderHandleBo handleBo);
}
