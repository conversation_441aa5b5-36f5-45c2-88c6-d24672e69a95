package com.feidi.xx.cross.order.mq.producer;

import cn.hutool.core.date.DateUtil;
import com.feidi.xx.common.rocketmq.base.BaseSendExtendDTO;
import com.feidi.xx.common.rocketmq.enums.MqMessageTypeEnum;
import com.feidi.xx.common.rocketmq.producer.AbstractProducer;
import com.feidi.xx.common.rocketmq.util.MQMessageUtil;
import com.feidi.xx.cross.common.constant.order.OrderConstants;
import com.feidi.xx.cross.common.constant.order.OrderRocketMQConstant;
import com.feidi.xx.cross.common.enums.order.CancelTypeEnum;
import com.feidi.xx.cross.common.utils.ExceptionUtil;
import com.feidi.xx.cross.order.mq.event.OrderCancelEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 订单取消消息消费者
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCancelProducer extends AbstractProducer<OrderCancelEvent> {
    private BaseSendExtendDTO buildBaseSendExtendParam(OrderCancelEvent event) {
        return BaseSendExtendDTO.builder()
                .eventName("订单延迟消息")
                .keys(event.getOrderId() + event.getCancelType() + event.getDelayTime())
                .messageType(MqMessageTypeEnum.SYNC)
                .topic(OrderRocketMQConstant.XX_ORDER_CANCEL_TOPIC_KEY)
                .sentTimeout(2000L)
                .delayTime(event.getDelayTime())
                .build();
    }
    @Override
    public SendResult sendMessage(OrderCancelEvent event) {
        try {
            return MQMessageUtil.sendMessage(event, this::buildBaseSendExtendParam);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("订单取消消息发送异常，{}", e.getMessage());
            return null;
        }
    }

    // 支付状态变更
    public SendResult sendPaymentTypeChangeMessage(Long orderId, Date payTime) {
        OrderCancelEvent event = OrderCancelEvent.builder()
                .orderId(orderId)
                .paymentTypeChangeOnly(true)
                .delayTime(DateUtil.offsetMinute(payTime, OrderConstants.PAYMENT_TYPE_TIMEOUT_TIME).getTime())
                .build();
        return sendMessage(event);
    }

    /**
     * 支付超时
     * @param orderId
     * @param createTime 订单创建时间
     * @return
     */
    public SendResult sendPaymentCancelMessage(Long orderId, Date createTime) {
        OrderCancelEvent event = OrderCancelEvent.builder()
                .orderId(orderId)
                .cancelType(CancelTypeEnum.UNPAID.getCode())
                .delayTime(DateUtil.offsetMinute(createTime, OrderConstants.PAYMENT_TIMEOUT_TIME).getTime())
                .build();
        return sendMessage(event);
    }

    /**
     * 接单超时
     * @param orderId
     * @param latestTime 最晚出发时间
     * @return
     */
    public SendResult sendReceiveCancelMessage(Long orderId, Date latestTime) {
        OrderCancelEvent event = OrderCancelEvent.builder()
                .orderId(orderId)
                .cancelType(CancelTypeEnum.RECEIVE.getCode())
                .delayTime(DateUtil.offsetMinute(latestTime, OrderConstants.RECEIVE_TIMEOUT_TIME).getTime())
                .build();
        return sendMessage(event);
    }
}

