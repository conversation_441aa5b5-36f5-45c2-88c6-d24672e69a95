package com.feidi.xx.cross.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.cache.system.SysDistrictCacheVo;
import com.feidi.xx.common.core.enums.StartEndEnum;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.enums.SystemCacheKeyEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.*;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.core.utils.xx.LatLonUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.constant.order.OrderConstants;
import com.feidi.xx.cross.common.constant.order.OrderLockKeyConstants;
import com.feidi.xx.cross.common.enums.order.DispatchTypeEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.enums.order.RobPlanEnum;
import com.feidi.xx.cross.common.enums.order.RobTypeEnum;
import com.feidi.xx.cross.common.utils.order.OrderUtils;
import com.feidi.xx.cross.operate.api.RemoteLineService;
import com.feidi.xx.cross.operate.api.domain.line.vo.RemoteLineVo;
import com.feidi.xx.cross.order.domain.DisRob;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.bo.rob.CoverQuery;
import com.feidi.xx.cross.order.domain.bo.rob.DisRobBo;
import com.feidi.xx.cross.order.domain.bo.rob.DisRobQueryBo;
import com.feidi.xx.cross.order.domain.bo.rob.MatchBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderDispatchBo;
import com.feidi.xx.cross.order.domain.vo.DisRobVo;
import com.feidi.xx.cross.order.domain.vo.driver.DisRobHistoryAddrVo;
import com.feidi.xx.cross.order.mapper.DisRobMapper;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.service.IDisRobService;
import com.feidi.xx.cross.order.service.IOrdOrderProcessService;
import com.feidi.xx.cross.power.api.RemoteAgentLineService;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.RemoteDriverLineService;
import com.feidi.xx.cross.power.api.RemoteDriverService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.api.domain.agent.vo.RemoteAgentLineVo;
import com.feidi.xx.cross.power.api.domain.driver.bo.RemoteDriverQueryBo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.system.api.RemoteConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 自动抢单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DisRobServiceImpl implements IDisRobService {

    private final IOrdOrderProcessService ordOrderProcessService;
    private final DisRobMapper baseMapper;
    private final OrdOrderMapper ordOrderMapper;
    @DubboReference
    private final RemoteAgentService remoteAgentService;
    @DubboReference
    private final RemoteAgentLineService remoteAgentLineService;
    @DubboReference
    private final RemoteDriverService remoteDriverService;
    @DubboReference
    private final RemoteConfigService remoteConfigService;
    @DubboReference
    private final RemoteLineService remoteLineService;
    @DubboReference
    private final RemoteDriverLineService remoteDriverLineService;

    /**
     * 查询自动抢单
     *
     * @param id 主键
     * @return 自动抢单
     */
    @Override
    public DisRobVo queryById(Long id) {
        DisRobVo robVo = baseMapper.selectVoById(id);
        return fillInfo(robVo);
    }

    /**
     * 分页查询自动抢单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 自动抢单分页列表
     */
    @Override
    public TableDataInfo<DisRobVo> queryPageList(DisRobBo bo, PageQuery pageQuery) {
        //初始化
        if (ObjectUtils.isNull(pageQuery.getIsAsc())) {
            pageQuery.setIsAsc("desc");
        }
        if (ObjectUtils.isNull(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("create_time");
        }
        LambdaQueryWrapper<DisRob> lqw = buildQueryWrapper(bo);
        Page<DisRobVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        if (result != null) {
            List<DisRobVo> records = result.getRecords();
            fillInfo(records);
        }
        return TableDataInfo.build(result);
    }

    private DisRobVo fillInfo(DisRobVo disRobVo) {
        List<DisRobVo> vos = Collections.singletonList(disRobVo);
        fillInfo(vos);
        return vos.get(0);
    }

    private void fillInfo(List<DisRobVo> records) {
        if (CollUtil.isNotEmpty(records)) {
            Map<Long, RemoteAgentVo> agentInfoMap = remoteAgentService.getAllAgentInfo().stream().collect(Collectors.toMap(RemoteAgentVo::getId, Function.identity()));
            List<Long> driverIds = records.stream().map(DisRobVo::getDriverId).toList();
            Map<Long, RemoteDriverVo> driverInfoMap = remoteDriverService.getDriverInfo(driverIds).stream().collect(Collectors.toMap(RemoteDriverVo::getId, Function.identity()));
            List<Long> lineIds = records.stream().map(DisRobVo::getLineId).toList();
            Map<Long, RemoteLineVo> lineInfoMap = remoteLineService.queryByLineIds(lineIds).stream().collect(Collectors.toMap(RemoteLineVo::getId, Function.identity()));
            for (DisRobVo record : records) {
                // 代理信息
                RemoteAgentVo agentInfo = agentInfoMap.get(record.getAgentId());
                if (ObjectUtils.isNotNull(agentInfo)) {
                    record.setAgentName(agentInfo.getCompanyName());
                }
                // 司机信息
                RemoteDriverVo driverInfo = driverInfoMap.get(record.getDriverId());
                if (ObjectUtils.isNotNull(driverInfo)) {
                    record.setDriverName(driverInfo.getName());
                }
                // 线路信息
                RemoteLineVo lineInfo = lineInfoMap.get(record.getLineId());
                if (ObjectUtils.isNotNull(lineInfo)) {
                    record.setLineName(lineInfo.getName());
                }

                if (ObjectUtils.isNotNull(record.getStartAdCode())) {
                    SysDistrictCacheVo startDistrict = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_AD_CODE_KEY.create(), record.getStartAdCode());
                    if (ObjectUtils.isNotNull(startDistrict) && Objects.equals(startDistrict.getLevel(), "district")) {
                        record.setStartDistrict(startDistrict.getName());
                        SysDistrictCacheVo startCity = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_ID_KEY.create(), String.valueOf(startDistrict.getParentId()));
                        if (ObjectUtils.isNotNull(startCity)) {
                            record.setStartCity(startCity.getName());
                            SysDistrictCacheVo startProvince = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_ID_KEY.create(), String.valueOf(startCity.getParentId()));
                            record.setStartProvince(ObjectUtils.isNotNull(startProvince) ? startProvince.getName() : "");
                        }
                    }
                }
                if (ObjectUtils.isNotNull(record.getEndAdCode())) {
                    SysDistrictCacheVo endDistrict = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_AD_CODE_KEY.create(), record.getEndAdCode());
                    if (ObjectUtils.isNotNull(endDistrict) && Objects.equals(endDistrict.getLevel(), "district")) {
                        record.setEndDistrict(endDistrict.getName());
                        SysDistrictCacheVo endCity = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_ID_KEY.create(), String.valueOf(endDistrict.getParentId()));
                        if (ObjectUtils.isNotNull(endCity)) {
                            record.setEndCity(endCity.getName());
                            SysDistrictCacheVo endProvince = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_ID_KEY.create(), String.valueOf(endCity.getParentId()));
                            record.setEndProvince(ObjectUtils.isNotNull(endProvince) ? endProvince.getName() : "");
                        }
                    }
                }
            }
        }
    }

    /**
     * 查询符合条件的自动抢单列表
     *
     * @param bo 查询条件
     * @return 自动抢单列表
     */
    @Override
    public List<DisRobVo> queryList(DisRobBo bo) {
        LambdaQueryWrapper<DisRob> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<DisRob> buildQueryWrapper(DisRobBo bo) {
        LambdaQueryWrapper<DisRob> lqw = Wrappers.lambdaQuery();
        // 综合搜索
        if (StrUtil.isNotBlank(bo.getUnionId())) {
            RemoteDriverQueryBo driverBo = new RemoteDriverQueryBo();
            driverBo.setUnionId(bo.getUnionId());
            List<RemoteDriverVo> vos = remoteDriverService.queryDriverInfo(driverBo);
            List<Long> ids = StreamUtils.toList(vos, RemoteDriverVo::getId);
            lqw.nested(l ->
                    l.eq(DisRob::getRobNo, bo.getUnionId()).or()
                            .in(CollUtil.isNotEmpty(ids), DisRob::getDriverId, ids)
            );
        }

        if (CollUtil.isNotEmpty(bo.getAgentIds())) {
            lqw.in(bo.getAgentIds() != null, DisRob::getAgentId, bo.getAgentIds());
        } else {
            if (UserTypeEnum.AGENT_USER.equals(LoginHelper.getUserType())) {
                lqw.nested(bo.getAgentId() != null, l -> {
                    l.eq(DisRob::getAgentId, bo.getAgentId());
                });
            } else {
                lqw.eq(bo.getAgentId() != null, DisRob::getAgentId, bo.getAgentId());
            }
        }

        // 默认只查询自动抢单数据
        if (StringUtils.isBlank(bo.getType())) {
            lqw.eq(DisRob::getType, RobTypeEnum.AUTO_GRAB_ORDER.getCode());
        }
        lqw.eq(bo.getRobNo() != null, DisRob::getRobNo, bo.getRobNo());
        lqw.eq(bo.getLineId() != null, DisRob::getLineId, bo.getLineId());
        lqw.eq(bo.getType() != null, DisRob::getType, bo.getType());
        lqw.in(CollUtil.isNotEmpty(bo.getLineIds()), DisRob::getLineId, bo.getLineIds());
        lqw.eq(bo.getDriverId() != null, DisRob::getDriverId, bo.getDriverId());
        lqw.eq(bo.getStartProvinceId() != null, DisRob::getStartProvinceId, bo.getStartProvinceId());
        lqw.eq(bo.getStartCityId() != null, DisRob::getStartCityId, bo.getStartCityId());
        lqw.eq(bo.getEndProvinceId() != null, DisRob::getEndProvinceId, bo.getEndProvinceId());
        lqw.eq(bo.getEndCityId() != null, DisRob::getEndCityId, bo.getEndCityId());
        lqw.eq(StringUtils.isNotBlank(bo.getEndLongitude()), DisRob::getEndLongitude, bo.getEndLongitude());

        // 时间重叠筛选
        lqw.nested(StringUtils.isNotBlank(bo.getStartTime()) && StringUtils.isNotBlank(bo.getEndTime()),
                l -> l.nested(e -> e.ge(DisRob::getStartTime, bo.getStartTime()).le(DisRob::getEndTime, bo.getEndTime()))
                        .or()
                        .nested(w -> w.le(DisRob::getStartTime, bo.getEndTime()).ge(DisRob::getEndTime, bo.getStartTime())));
        lqw.eq(StringUtils.isNotBlank(bo.getSortTime()), DisRob::getSortTime, bo.getSortTime());
        lqw.eq(StringUtils.isNotBlank(bo.getRobProduct()), DisRob::getRobProduct, bo.getRobProduct());
        lqw.eq(StringUtils.isNotBlank(bo.getUserType()), DisRob::getUserType, bo.getUserType());
        lqw.eq(bo.getUserId() != null, DisRob::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), DisRob::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getPlan()), DisRob::getPlan, bo.getPlan());
        lqw.orderByAsc(DisRob::getStatus)
                .orderByAsc(DisRob::getPlan)
                .orderByDesc(DisRob::getSortTime)
                .orderByDesc(DisRob::getId);
        return lqw;
    }

    /**
     * 新增自动抢单
     *
     * @param bo 自动抢单
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(DisRobBo bo) {
        DisRob add = MapstructUtils.convert(bo, DisRob.class);
        validEntityBeforeSave(add);
        if (Objects.equals(bo.getType(), RobTypeEnum.AUTO_GRAB_ORDER.getCode())) {
            add.setSortTime(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
        }
        add.setRobNo(OrderUtils.makeRobNo());
        add.setSeat(bo.getSeat());
        add.setSurplusSeat(bo.getSurplusSeat());
        boolean flag = baseMapper.insert(handleData(add)) > 0;
        if (flag) {
            bo.setId(add.getId());
        }

        return flag;
    }

    /**
     * 修改自动抢单
     *
     * @param bo 自动抢单
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(DisRobBo bo) {
        Assert.isTrue(bo.getSurplusSeat() != 0, "请选择可载人数");
        // 原单子
        DisRobVo robVo = queryById(bo.getId());
        if (Objects.equals(robVo.getPlan(), RobPlanEnum.ALL.getCode())) {
            cleanCacheAllRob(bo.getLineId());
        }
        DisRob update = MapstructUtils.convert(bo, DisRob.class);
        validEntityBeforeSave(update);
        // 是否修改排队时间
        if (robVo.getUserType().equals(UserTypeEnum.DRIVER_USER.getUserType())) {
            if (!robVo.getStartLatitude().equals(update.getStartLatitude()) ||
                    !robVo.getStartLongitude().equals(update.getStartLongitude()) ||
                    !robVo.getStartTime().equals(update.getStartTime()) ||
                    !robVo.getStartRadius().equals(update.getStartRadius()) ||
                    !robVo.getEndTime().equals(update.getEndTime()) ||
                    !robVo.getStatus().equals(update.getStatus()) ||
                    !robVo.getPlan().equals(update.getPlan()) ||
                    !robVo.getRobProduct().equals(update.getRobProduct())) {
                update.setSortTime(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
            }
        }

        return baseMapper.updateById(handleData(update)) > 0;
    }

    /**
     * 完善信息
     *
     * @param entity
     * @return
     */
    private DisRob handleData(DisRob entity) {

        //坐标非空判断
        // 经纬度转换
        Boolean coordinates =true;
        if (StringUtils.isBlank(entity.getStartLongitude()) || StringUtils.isBlank(entity.getStartLatitude()) ||
                StringUtils.isBlank(entity.getEndLongitude()) || StringUtils.isBlank(entity.getEndLatitude())) {
            coordinates =false;
        }

        // 代理信息
        if (ArithUtils.isNull(entity.getAgentId())) {
            // 获取用户代理ID
            if (entity.getUserType().equals(UserTypeEnum.DRIVER_USER.getUserType())) {
                RemoteDriverVo driverInfo = remoteDriverService.getDriverInfo(entity.getDriverId());
                if (driverInfo != null) {
                    entity.setAgentId(driverInfo.getAgentId());
                }
            }
        }

        // 线路信息
        if (ArithUtils.isNull(entity.getLineId())) {
            Long lineId = null;
            if (coordinates){
                Map<String, Double> startLatLon = LatLonUtils.map2tx(Double.parseDouble(entity.getStartLongitude()), Double.parseDouble(entity.getStartLatitude()));
                Double startLongitude = startLatLon.get("longitude");
                entity.setStartLongitude(startLongitude.toString());
                Double startLatitude = startLatLon.get("latitude");
                entity.setStartLatitude(startLatitude.toString());
                Map<String, Double> endLatLon = LatLonUtils.map2tx(Double.parseDouble(entity.getEndLongitude()), Double.parseDouble(entity.getEndLatitude()));
                Double endLongitude = endLatLon.get("longitude");
                entity.setEndLongitude(endLongitude.toString());
                Double endLatitude = endLatLon.get("latitude");
                entity.setEndLatitude(endLatitude.toString());
                 lineId = remoteLineService.matchLine(entity.getStartAdCode(), entity.getEndAdCode(), startLatitude, startLongitude, endLatitude, endLongitude);
            }else {
                 lineId = remoteLineService.matchLine(entity.getStartAdCode(), entity.getEndAdCode());
            }
            entity.setLineId(lineId);
        }
        Assert.notNull(entity.getLineId(), "请先获取该线路接单权限后再创建常用线路");

        if (ArithUtils.isNotNull(entity.getLineId())) {
            RemoteLineVo remoteLineVo = remoteLineService.queryByLineId(entity.getLineId());
            Assert.notNull(remoteLineVo, "线路信息不存在");
        }
        // 线路是否有
        if (entity.getUserType().equals(UserTypeEnum.DRIVER_USER.getUserType())) {
            entity.setUserId(entity.getDriverId());
            // 获取司机所有的线路
            List<Long> driverLine = remoteDriverLineService.getDriverLine(entity.getDriverId());
            if (ObjectUtils.isNull(driverLine)) {
                throw new ServiceException("司机暂无线路信息");
            }
            boolean contains = driverLine.contains(entity.getLineId());
            if (!contains) {
                throw new ServiceException("司机没有该线路");
            }
        } else {
            if (ArithUtils.isNotNull(entity.getAgentId())) {
                entity.setUserId(entity.getAgentId());
                /// 获取代理商所有的线路
                List<Long> agentLine = remoteAgentLineService.getAgentLine(entity.getAgentId());
                if (ObjectUtils.isNull(agentLine)) {
                    throw new ServiceException("代理商暂无线路信息");
                }

                boolean contains = agentLine.contains(entity.getLineId());
                if (!contains) {
                    throw new ServiceException("合作商没有该线路");
                }
            }
        }
        return entity;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(DisRob entity) {
        if (Objects.equals(entity.getType(), RobTypeEnum.FAVORITE_ROUTE.getCode())) {

            if (Objects.equals(entity.getPlan(), RobPlanEnum.PART.getCode())) {
                Assert.isTrue(StrUtil.isAllNotBlank(entity.getStartAdCode(), entity.getEndAdCode()), "地区编码不能为空，请在地图重新选择");
            }
            // 司机抢单最大数量
            String robMax = remoteConfigService.selectValueByKey(OrderConstants.ROB_FAVORITE_ROUTE);
            if (ObjectUtils.isNull(entity.getId()) && StringUtils.isNotBlank(robMax)) {
                // 获取司机已有的数量
                Long num = baseMapper.selectCount(new LambdaQueryWrapper<DisRob>().eq(DisRob::getDriverId, entity.getDriverId()).eq(DisRob::getType, RobTypeEnum.FAVORITE_ROUTE.getCode()));
                if (!(Long.parseLong(robMax) > num)) {
                    throw new ServiceException("仅允许创建" + robMax + "条常用线路");
                }
            }
        } else {
            if (StatusEnum.ENABLE.getCode().equals(entity.getStatus())) {
                if (entity.getEndTime() != null) {
                    DateTime endTime = DateUtil.parse(entity.getEndTime());
                    // 不对开始限制是因为司机修改可能只改结束时间
                    Assert.isTrue(endTime.getTime() > DateUtil.current(), "结束时间必须在当前时间之后");
                }
            }

            if (Objects.equals(entity.getPlan(), RobPlanEnum.PART.getCode())) {
                Assert.isTrue(StrUtil.isAllNotBlank(entity.getStartAdCode(), entity.getEndAdCode()), "地区编码不能为空，请在地图重新选择");
            }

            // 司机限制
            if (Objects.equals(entity.getUserType(), UserTypeEnum.DRIVER_USER.getUserType())) {
                if (Objects.equals(entity.getUserType(), RobPlanEnum.ALL.getCode())) {
                    throw new ServiceException("司机不支持设置全部抢单");
                }
                // 司机抢单最大数量
                String robMax = remoteConfigService.selectValueByKey(OrderConstants.ROB_MAX);
                if (StringUtils.isNotBlank(robMax)) {
                    // 获取司机已有的数量
                    Long num = baseMapper.selectCount(new LambdaQueryWrapper<DisRob>().eq(DisRob::getDriverId, entity.getDriverId()).eq(DisRob::getType, RobTypeEnum.AUTO_GRAB_ORDER.getCode()));
                    if (!(Long.parseLong(robMax) > num)) {
                        throw new ServiceException("可设置抢单上限为:" + robMax);
                    }
                }
            } else {
                String noDriverError = "请选择司机";
                Assert.isTrue(entity.getDriverJson() != null && !entity.getDriverJson().equals("null"), noDriverError);
                Assert.isTrue(!StrUtil.split(entity.getDriverJson(), ",").isEmpty(), noDriverError);
                // 判断选中的司机是否有次线路
                List<String> pass = new ArrayList<>();
                if (entity.getDriverJson() != null &&
                        !(entity.getDriverJson().equals("[]") || entity.getDriverJson().equals("null"))) {
                    String driverJson = entity.getDriverJson();
                    if (StrUtil.isNotBlank(driverJson)) {
                        List<Long> driverIds = JSON.parseArray(driverJson, Long.class);
                        for (Long driverIdTemp : driverIds) {
                            boolean driverHasLine = driverHasLine(driverIdTemp, entity.getLineId());
                            if (driverHasLine) {
                                pass.add(String.valueOf(driverIdTemp));
                            }
                        }
                    }
                }
                if (CollUtil.isEmpty(pass)) {
                    throw new ServiceException(noDriverError);
                }
                // 过滤通过的司机
                entity.setDriverJson(JSONUtil.toJsonStr(pass));
            }

            LambdaQueryWrapper<DisRob> queryWrapper = new LambdaQueryWrapper<>();
            if (entity.getPlan().equals(RobPlanEnum.ALL.getCode())) {
                queryWrapper.eq(DisRob::getLineId, entity.getLineId());
                queryWrapper.eq(DisRob::getPlan, entity.getPlan());
                queryWrapper.eq(DisRob::getType, RobTypeEnum.AUTO_GRAB_ORDER.getCode());
                queryWrapper.ne(entity.getId() != null, DisRob::getId, entity.getId());

                DisRobVo robVo = baseMapper.selectVoOne(queryWrapper);
                if (robVo != null) {
                    throw new ServiceException("同一个线路，仅设置一个全部抢单");
                }
            }

            // 判断是否被子代理商使用
            if (UserTypeEnum.AGENT_USER.getUserType().equals(entity.getUserType())) {
                List<RemoteAgentLineVo> agentLines = remoteAgentLineService.listByParentId(entity.getAgentId());
                if (CollUtil.isNotEmpty(agentLines)) {
                    Map<Long, Long> map = StreamUtils.toMap(agentLines, RemoteAgentLineVo::getLineId, RemoteAgentLineVo::getAgentId);
                    Assert.isNull(map.get(entity.getLineId()), "当前线路已分配代理商，无法创建抢单");
                }
            }
        }

    }

    /**
     * 判断司机是否有这个线路
     *
     * @param driverId
     * @param lineId
     * @return
     */
    private Boolean driverHasLine(Long driverId, Long lineId) {
        List<Long> driverLines = remoteDriverLineService.getDriverLine(driverId);
        if (CollUtil.isNotEmpty(driverLines)) {
            return driverLines.contains(lineId);
        }
        return false;
    }

    /**
     * 清除全部类型抢单缓存
     *
     * @param lineId
     */
    private void cleanCacheAllRob(Long lineId) {
        String key = getCacheKey(lineId);
        if (RedisUtils.hasKey(key)) {
            RedisUtils.deleteKeys(key);
        }
    }

    /**
     * 获取全部类型抢单缓存KEY
     *
     * @param lineId
     * @return
     */
    private String getCacheKey(Long lineId) {
//        String  lineId=   CacheConstants.CX_ROB_ALL_KEY + lineId
        return lineId.toString();
    }

    /**
     * 批量修改自动时间
     *
     * @param bo
     * @return
     */
    @Override
    public Boolean batchUpdateByBo(DisRobBo bo) {
        List<DisRob> disRobs = baseMapper.selectBatchIds(bo.getIds());
        if (CollUtil.isEmpty(disRobs)) {
            return true;
        }

        boolean flag = true;
        // 记录抢单是否可以修改 通过的存id是为了更新，拒绝的存robNo是为了展示报错
        ArrayList<Long> passRobs = new ArrayList<>();
        ArrayList<String> rejectRobs = new ArrayList<>();
        for (DisRob rob : disRobs) {
            Long lineId = rob.getLineId();
            Long driverId = rob.getDriverId();
            boolean driverHasLine = true;
            // 是否是司机的抢单
            if (driverId != null) {
                driverHasLine = driverHasLine(driverId, lineId);
            } else if (rob.getDriverJson() != null &&
                    !(rob.getDriverJson().equals("[]") || rob.getDriverJson().equals("null"))) {
                String driverJson = rob.getDriverJson();
                if (StrUtil.isNotBlank(driverJson)) {
                    List<Long> driverIds = JSON.parseArray(driverJson, Long.class);
                    for (Long driverIdTemp : driverIds) {
                        driverHasLine = driverHasLine(driverIdTemp, lineId);
                    }
                }
            }
            boolean useless = driverHasLine ? passRobs.add(rob.getId()) : rejectRobs.add(rob.getRobNo());
        }

        // 统一修改
        if (CollUtil.isNotEmpty(passRobs)) {
            flag = baseMapper.batchUpdate(bo, passRobs) > 0;
        }

        if (CollUtil.isNotEmpty(rejectRobs)) {
            log.error("修改失败，单号：[{}]，原因：{}", rejectRobs, "司机不在线路中");
            throw new ServiceException(CollUtil.join(rejectRobs, ",") + "修改失败，司机不在线路中");
        }
        return flag;
    }

    /**
     * 获取抢单覆盖范围
     *
     * @param coverQuery 覆盖范围查询条件
     */
    @Override
    public List<DisRob> cover(CoverQuery coverQuery) {
        LambdaQueryWrapper<DisRob> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(DisRob::getStatus, StatusEnum.ENABLE.getCode());
        queryWrapper.eq(coverQuery.getDriverId() != null, DisRob::getDriverId, coverQuery.getDriverId());
        queryWrapper.eq(coverQuery.getAgentId() != null, DisRob::getAgentId, coverQuery.getAgentId());
        queryWrapper.eq(coverQuery.getLineId() != null, DisRob::getLineId, coverQuery.getLineId());
        queryWrapper.eq(coverQuery.getLineId() != null, DisRob::getLineId, coverQuery.getLineId());
        queryWrapper.eq(coverQuery.getType() != null, DisRob::getType, coverQuery.getType());
        queryWrapper.eq(StringUtils.isNotBlank(coverQuery.getStartCityCode()), DisRob::getStartCityCode, coverQuery.getStartCityCode());
        queryWrapper.eq(StringUtils.isNotBlank(coverQuery.getPlan()), DisRob::getPlan, coverQuery.getPlan());
        queryWrapper.ge(StringUtils.isNotBlank(coverQuery.getStartTime()), DisRob::getStartTime, coverQuery.getStartTime());
        queryWrapper.le(StringUtils.isNotBlank(coverQuery.getEndTime()), DisRob::getEndTime, coverQuery.getEndTime());

        List<DisRob> robs = baseMapper.selectList(queryWrapper);
        // TODO-NEW 后期再添加司机信息，目前不了解具体需求
        /*if (CollUtils.isNotEmpty(robs)) {
            for (DisRob disRob : robs) {
                if (disRob.getDriverId() != null) {
                    RemoteDriverVo driverInfo = remoteDriverService.getDriverInfo(disRob.getDriverId());
                    if (driverInfo != null) {
                        disRob.setDriverName(driverInfo.getName());
                        disRob.setDriverPhone(driverInfo.getPhone());
                    }
                }
            }
        }*/

        return robs;
    }

    /**
     * 校验并批量删除自动抢单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 获取司机抢单地址历史记录
     *
     * @return
     */
    @Override
    public List<DisRobHistoryAddrVo> getHistory() {
        Long driverId = LoginHelper.getUserId();

        // 获取最近5条抢单，包括删除的
        LambdaQueryWrapper<DisRob> lqw = Wrappers.lambdaQuery();
        lqw.eq(DisRob::getDriverId, driverId)
                .eq(DisRob::getType, RobTypeEnum.AUTO_GRAB_ORDER.getCode())
                .in(DisRob::getDelFlag, ListUtil.of("0", "2"))
                .orderByDesc(DisRob::getCreateTime)
                .last("limit 5");

        List<DisRob> robs = baseMapper.selectHistory(driverId);
        Set<DisRobHistoryAddrVo> his = new HashSet<>();
        for (DisRob disRob : robs) {
            // 开始地址
            DisRobHistoryAddrVo startAddrVo = new DisRobHistoryAddrVo();
            // TODO-NEW 后期再添加地区信息
            //startAddrVo.setProvince(disRob.getStartProvince());
            startAddrVo.setProvinceId(disRob.getStartProvinceId());
            startAddrVo.setCityId(disRob.getStartCityId());
            startAddrVo.setCityCode(disRob.getStartCityCode());
            //startAddrVo.setCity(disRob.getStartCity());
            startAddrVo.setDistrictId(disRob.getStartDistrictId());
            //startAddrVo.setDistrict(disRob.getStartDistrict());
            startAddrVo.setAdCode(disRob.getStartAdCode());
            startAddrVo.setAddress(disRob.getStartAddress());
            startAddrVo.setLongitude(disRob.getStartLongitude());
            startAddrVo.setLatitude(disRob.getStartLatitude());
            his.add(startAddrVo);

            // 结束地址
            DisRobHistoryAddrVo endAddrVo = new DisRobHistoryAddrVo();
            endAddrVo.setProvinceId(disRob.getEndProvinceId());
            //endAddrVo.setProvince(disRob.getEndProvince());
            endAddrVo.setCityId(disRob.getEndCityId());
            endAddrVo.setCityCode(disRob.getEndCityCode());
            //endAddrVo.setCity(disRob.getEndCity());
            endAddrVo.setDistrictId(disRob.getEndDistrictId());
            //endAddrVo.setDistrict(disRob.getEndDistrict());
            endAddrVo.setAdCode(disRob.getEndAdCode());
            endAddrVo.setAddress(disRob.getEndAddress());
            endAddrVo.setLongitude(disRob.getEndLongitude());
            endAddrVo.setLatitude(disRob.getEndLatitude());
            his.add(endAddrVo);
        }
        return new ArrayList<>(his);
    }

    /**
     * 自动抢单
     *
     * @param disRobBo 自动抢单参数
     * @return 是否抢单成功
     */
    @Override
    public Boolean autoRob(DisRobBo disRobBo) {
        RedissonClient client = RedisUtils.getClient();
        RLock lock = client.getLock(OrderLockKeyConstants.LOCK_ORDER_ROB_KEY + disRobBo.getOrderId());

        Boolean result = true;
        try {
            boolean tryLock = lock.tryLock(OrderLockKeyConstants.LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
            if (tryLock) {
                OrdOrder order = ordOrderMapper.selectById(disRobBo.getOrderId());
                if (!Objects.equals(order.getStatus(), OrderStatusEnum.CREATE.getCode())) {
                    log.error("当前订单状态错误，无法继续抢单，订单：{}， 状态：{}", order.getId(), order.getStatus());
                    return false;
                }

                // 获取全量
                DisRob rob = getAllRob(disRobBo.getLineId());
                if (ObjectUtils.isNull(rob)) {
                    // 获取局域
                    rob = getPartRob(disRobBo);
                }
                log.info("自动抢单单号: {}，抢单id：{}", disRobBo.getPlatformNo(), (rob != null ? rob.getId() : null));
                if (ObjectUtils.isNotNull(rob)) {
                    OrdOrderDispatchBo dispatchBo = new OrdOrderDispatchBo();
                    dispatchBo.setOrderId(disRobBo.getOrderId());
                    dispatchBo.setPlatformCode(disRobBo.getPlatformCode());
                    dispatchBo.setPlatformNo(disRobBo.getPlatformNo());
                    dispatchBo.setRobId(rob.getId());
                    dispatchBo.setUserType(UserTypeEnum.AUTO_USER.getUserType());
                    dispatchBo.setUserId(0L);
                    dispatchBo.setTimeStamp(DateUtils.getUnixTimeStamps());
                    dispatchBo.setRemark(JsonUtils.toJsonString(rob));
                    result = ordOrderProcessService.dispatchOrder(handleDispatchOrder(rob, dispatchBo));

                    if (result) {
                        if (!UserTypeEnum.AGENT_USER.getUserType().equals(rob.getUserType())) {
                            subSurplusSeat(rob.getId(), disRobBo.getPassengerNum());
                        }
                    }
                }
            }
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("自动抢单失败，" + e.getMessage());
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void disableRob(List<Long> ids, SFunction<DisRob, Long> function) {
        LambdaUpdateWrapper<DisRob> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(CollUtil.isNotEmpty(ids), function, ids)
                .eq(DisRob::getStatus, StatusEnum.ENABLE.getCode())
                .set(DisRob::getStatus, StatusEnum.DISABLE.getCode());
        baseMapper.update(updateWrapper);
    }

    public void subSurplusSeat(Long id, Integer seat) {
        baseMapper.subSurplusSeat(id, seat);
    }

    /**
     * 获取全部自动抢单任务
     *
     * @param lineId 线路id
     * @return 自动抢单任务
     */
    private DisRob getAllRob(Long lineId) {
        LambdaQueryWrapper<DisRob> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DisRob::getLineId, lineId)
                .eq(DisRob::getType, RobTypeEnum.AUTO_GRAB_ORDER.getCode())
                .eq(DisRob::getStatus, StatusEnum.ENABLE.getCode())
                .eq(DisRob::getPlan, RobPlanEnum.ALL.getCode());

        return baseMapper.selectOne(queryWrapper, false);
    }

    /**
     * 范围抢单
     *
     * @param disRobBo
     * @return
     */
    private DisRob getPartRob(DisRobBo disRobBo) {
        // 搜索条件
        DisRobQueryBo disRobQueryBo = BeanUtils.copyProperties(disRobBo, DisRobQueryBo.class);
        disRobQueryBo.setSeat(disRobBo.getPassengerNum());
        disRobQueryBo.setStartTime(DateUtils.date2String(disRobBo.getEarliestTime()));
        disRobQueryBo.setEndTime(DateUtils.date2String(disRobBo.getLatestTime()));
        disRobQueryBo.setRobProduct(disRobBo.getProductCode());

        DisRob disRob = null;
        // 先获取司机的自动抢单任务
        List<DisRob> drvRobVos = baseMapper.selectDriverRob(disRobQueryBo);

        if (ObjectUtils.isNotNull(drvRobVos)) {
            disRob = fitDriverRob(disRobBo, drvRobVos);
        }
        if (ObjectUtils.isNull(disRob)) {
            /// 再获取代理的
            String endTime = disRobQueryBo.getEndTime();
            long endTimeMinute = DateUtils.str2timeStamp(endTime) / 1000 / 60;
            long minute = DateUtils.getTimeStamps() / 60;
            int sub = (int) ArithUtils.sub(endTimeMinute, minute);
            if (sub > 0) {
                disRobQueryBo.setMaxWaitDuration(sub);
            }
            List<DisRob> agtRobVos = baseMapper.selectAgentRob(disRobQueryBo);
            if (ObjectUtils.isNotNull(agtRobVos)) {
                // 打乱 - 随机获取
                Collections.shuffle(agtRobVos);
                disRob = fitAgentRob(disRobBo, agtRobVos);
            }
        }
        return disRob;
    }

    /**
     * 常用路线匹配
     */
    @Override
    public Boolean matchCommonRoute(MatchBo matchBo) {
        log.info("进入司机常用路线逻辑司机id：【{}】",matchBo.getDriverId());
        Double earliestl = ArithUtils.div(matchBo.getEarliestTime().getTime(), 1000L);
        Double latestl = ArithUtils.div(matchBo.getLatestTime().getTime(), 1000L);
        String earliestTimeStr = DateUtils.convertLongToStr(earliestl.longValue());
        String latestTimeStr = DateUtils.convertLongToStr(latestl.longValue());
        LocalTime earliestTime = LocalTime.parse(earliestTimeStr);
        LocalTime latestTime = LocalTime.parse(latestTimeStr);
        ArrayList<DisRob> robs = new ArrayList<>();

        LambdaQueryWrapper<DisRob> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(DisRob::getType, RobTypeEnum.FAVORITE_ROUTE.getCode())
                .eq(DisRob::getStatus, StatusEnum.ENABLE.getCode())
                .eq(DisRob::getDriverId, matchBo.getDriverId());
        List<DisRob> disRobs = baseMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(disRobs)) {
            log.info("司机常用路线逻辑司机id：【{}】，无线路",matchBo.getDriverId());
            return false;
        }
        List<DisRob> earliest = disRobs.stream()
                .filter(item -> {
                    LocalTime start = LocalTime.parse(item.getStartTime());
                    LocalTime end = LocalTime.parse(item.getEndTime());
                    return start.isBefore(earliestTime) && end.isAfter(earliestTime);
                })
                .collect(Collectors.toList());

        List<DisRob> latest = disRobs.stream().filter(item -> {
            LocalTime start = LocalTime.parse(item.getStartTime());
            LocalTime end = LocalTime.parse(item.getEndTime());
            return start.isBefore(latestTime) && end.isAfter(latestTime);
        }).collect(Collectors.toList());
        robs.addAll(earliest);
        robs.addAll(latest);
        if (CollUtil.isEmpty(robs)) {
            log.info("司机常用路线逻辑司机id：【{}】，时间无常用路线",matchBo.getDriverId());
            return false;
        }
        DisRobBo disRobBo = BeanUtils.copyProperties(matchBo, DisRobBo.class);
        DisRob disRob = fitDriverRob(disRobBo, robs);
        if (ObjectUtils.isNotNull(disRob)) {
            log.info("司机常用路线逻辑司机id：【{}】，坐标常用路线",matchBo.getDriverId());
            return true;
        }
        return false;
    }

    /**
     *  自动续签
     * @return
     */
    @Override
    public Boolean autoRenewal() {

        boolean flag = true;

        /// 获取所有需要续签的抢单
        LambdaQueryWrapper<DisRob> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DisRob::getStatus, StatusEnum.ENABLE.getCode())
                .isNotNull(DisRob::getStartTime)
                .isNotNull(DisRob::getEndTime)
                .eq(DisRob::getUserType, UserTypeEnum.AGENT_USER.getUserType())
                .eq(DisRob::getType, RobTypeEnum.AUTO_GRAB_ORDER.getCode())
                .gt(DisRob::getRenewal, 0);

        List<DisRob> disRobs = baseMapper.selectList(queryWrapper);

        if (disRobs != null && !disRobs.isEmpty()) {

            ArrayList<DisRob> updateRobList = new ArrayList<>();

            for (DisRob disRob : disRobs) {

                int differ = (int) (disRob.getRenewal() - 1);

                if (differ < 0) {
                    continue;
                }

                DateTime startTime = DateUtil.offsetDay(DateUtil.parse(disRob.getStartTime()), 1);
                DateTime endTime = DateUtil.offsetDay(DateUtil.parse(disRob.getEndTime()), 1);

                disRob.setRenewal((long) differ);
                disRob.setStartTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, startTime));
                disRob.setEndTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, endTime));
                disRob.setStatus(StatusEnum.ENABLE.getCode());
                disRob.setSortTime(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));

                /// TODO 更新续签数据
                updateRobList.add(disRob);

            }

            flag = baseMapper.updateBatchById(updateRobList);
        }

        return flag;
    }

    /**
     * 处理抢单参数
     *
     * @param rob
     * @param dispatchBo
     * @return
     */
    private OrdOrderDispatchBo handleDispatchOrder(DisRob rob, OrdOrderDispatchBo dispatchBo) {
        // 抢单类型
        String userType = rob.getUserType();
        if (userType.equals(UserTypeEnum.DRIVER_USER.getUserType())) {
            /// 司机抢单
            dispatchBo.setType(DispatchTypeEnum.DRIVER_AUTO_ROB.getCode());
            dispatchBo.setDriverId(rob.getDriverId());
            log.info("自动抢单单号: {}，司机抢单司机信息：{}", dispatchBo.getPlatformNo(), rob.getDriverId());
        } else if (userType.equals(UserTypeEnum.AGENT_USER.getUserType())) {
            /// 代理抢单
            // fixme 枚举消失 AGENT_AUTO_ROB 代理自动抢单 -> 占单
            dispatchBo.setType(DispatchTypeEnum.AGENT_ROB.getCode());

            /// 获取司机ID
            // FIXME: 2024/10/22 rob入参修改为List<String>
            String driverJson = rob.getDriverJson();
            log.info("自动抢单单号: {}，代理抢单司机信息：{}", dispatchBo.getPlatformNo(), driverJson);
            if (StringUtils.isNotBlank(driverJson) && !driverJson.equals("null")) {
                // FIXME: 2024/10/22 先用String接，前端拼接
                List<String> jsonObject = (List<String>) JSON.parse(driverJson);
                if (jsonObject.size() > 1) {
                    Collections.shuffle(jsonObject);
                }
                // FIXME: 2024/10/22 为什么取0
                Long driverId = Long.parseLong(jsonObject.get(0));
                dispatchBo.setDriverId(driverId);
                dispatchBo.setDispatchDriverId(driverId);
            }
        }
        log.info("自动抢单单号: {}，抢单参数：{}", dispatchBo.getPlatformNo(), JSONUtil.toJsonStr(dispatchBo));
        return dispatchBo;
    }

    /**
     * 匹配抢单 - 司机
     *
     * @param disRobBo
     * @param drvRobVos
     * @return
     */
    private DisRob fitDriverRob(DisRobBo disRobBo, List<DisRob> drvRobVos) {
        DisRob disRob = null;
        for (DisRob drvRob : drvRobVos) {
            if (checkRob(drvRob, disRobBo, StartEndEnum.START)
                    && checkRob(drvRob, disRobBo, StartEndEnum.END)) {
                disRob = drvRob;
                break;
            }
        }
        return disRob;
    }

    /**
     * 校验数据
     *
     * @param disRob
     * @param disRobBo
     * @param startEndEnum
     * @return
     */
    private Boolean checkRob(DisRob disRob, DisRobBo disRobBo, StartEndEnum startEndEnum) {
        if (startEndEnum.equals(StartEndEnum.START)) {
            String latitude = disRob.getStartLatitude();
            String longitude = disRob.getStartLongitude();
            Long radius = disRob.getStartRadius();
            // 限制区域
            if (StringUtils.isNotBlank(latitude) && StringUtils.isNotBlank(longitude) && radius > 0) {
                // 限制半径
                long distance = (long) LatLonUtils.getDistance(disRobBo.getStartLongitude(), disRobBo.getStartLatitude(), longitude, latitude);
                return distance <= (radius * 1000);
            }
        } else {
            String latitude = disRob.getEndLatitude();
            String longitude = disRob.getEndLongitude();
            Long radius = disRob.getEndRadius();
            // 限制区域
            if (StringUtils.isNotBlank(latitude) && StringUtils.isNotBlank(longitude) && radius > 0) {
                // 限制半径
                long distance = (long) LatLonUtils.getDistance(disRobBo.getEndLongitude(), disRobBo.getEndLatitude(), longitude, latitude);
                return distance <= (radius * 1000);
            }
        }
        return true;
    }

    /**
     * 匹配抢单 - 代理商
     *
     * @param disRobBo
     * @param agentRobs
     * @return
     */
    private DisRob fitAgentRob(DisRobBo disRobBo, List<DisRob> agentRobs) {
        // 返回值
        DisRob otRob = null;

        for (DisRob agentRob : agentRobs) {
            // 默认是匹配的
            boolean flag = true;

            String startTime = agentRob.getStartTime();
            String endTime = agentRob.getEndTime();
            Date earliestTime = disRobBo.getEarliestTime();
            Date latestTime = disRobBo.getLatestTime();

            if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)
                    && ObjectUtils.isNotNull(earliestTime) && ObjectUtils.isNotNull(latestTime)) {
                long startTimeStamp = DateUtils.str2timeStamp(startTime);
                long endTimeStamp = DateUtils.str2timeStamp(endTime);
                long earliestTimeStamp = DateUtils.date2timeStamp(earliestTime);
                long latestTimeStamp = DateUtils.date2timeStamp(latestTime);
                if (!((startTimeStamp <= earliestTimeStamp && endTimeStamp >= earliestTimeStamp)
                        || (startTimeStamp <= latestTimeStamp && endTimeStamp >= latestTimeStamp))) {
                    flag = false;
                }
            }

            if (!flag) {
                continue;
            }

            // 起点
            if (StrUtil.isNotBlank(agentRob.getStartAdCode()) || StrUtil.isNotBlank(agentRob.getStartAddress())) {
                flag = checkRob(agentRob, disRobBo, StartEndEnum.START);
            }

            if (!flag) {
                continue;
            }

            // 终点
            if (StringUtils.isNotBlank(agentRob.getEndAdCode()) || StrUtil.isNotBlank(agentRob.getEndAddress())) {
                flag = checkRob(agentRob, disRobBo, StartEndEnum.END);
            }
            if (flag) {
                otRob = agentRob;
                break;
            }
        }
        return otRob;
    }
}
