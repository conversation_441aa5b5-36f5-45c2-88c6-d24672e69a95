package com.feidi.xx.cross.order.utils;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Component
public class ThreadPoolManager {

    // 使用一个静态的线程池实例，这样所有类都能共享这个线程池
    private static final ExecutorService threadPool;

    // 默认的线程池大小
    private static final int DEFAULT_POOL_SIZE = 10;

    // 初始化线程池
    static {
        threadPool = Executors.newFixedThreadPool(DEFAULT_POOL_SIZE);
    }

    // 获取线程池实例
    public static ExecutorService getThreadPool() {
        return threadPool;
    }

    // 提交任务到线程池
    public static <T> CompletableFuture<T> submitTask(Callable<T> task) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return task.call();
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }, threadPool);
    }

    // 提交Runnable任务到线程池
    public static CompletableFuture<Void> submitTask(Runnable task) {
        return CompletableFuture.runAsync(() -> {
            try {
                task.run();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }, threadPool);
    }

    // 关闭线程池
    public static void shutdown() {
        if (threadPool != null && !threadPool.isShutdown()) {
            threadPool.shutdown();
        }
    }

    // 优雅地关闭线程池，等待当前任务完成
    public static boolean shutdownNow() {
        if (threadPool != null && !threadPool.isShutdown()) {
            List<Runnable> remainingTasks = threadPool.shutdownNow();
            return remainingTasks.isEmpty();
        }
        return true;
    }

    // 获取线程池的状态
    public static boolean isShutdown() {
        return threadPool.isShutdown();
    }

    // 获取线程池的状态（是否已终止）
    public static boolean isTerminated() {
        return threadPool.isTerminated();
    }
}
