package com.feidi.xx.cross.order.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.order.domain.bo.OrdStatisticBo;
import com.feidi.xx.cross.order.domain.vo.OrdStatisticTableExportVo;
import com.feidi.xx.cross.order.domain.vo.OrdStatisticTableVo;
import com.feidi.xx.cross.order.domain.vo.OrdStatisticVo;

import java.util.Collection;
import java.util.List;

/**
 * 订单统计Service接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface IOrdStatisticService {

    /**
     * 查询订单统计
     *
     * @param id 主键
     * @return 订单统计
     */
    OrdStatisticVo queryById(Long id);

    /**
     * 分页查询订单统计列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单统计分页列表
     */
    TableDataInfo<OrdStatisticVo> queryPageList(OrdStatisticBo bo, PageQuery pageQuery);

    /**
     * 表格数据统计
     *
     * @param bo
     * @return
     */
    List<OrdStatisticTableVo> queryTableList(OrdStatisticBo bo);

    /**
     * 表格数据统计导出
     *
     * @param bo
     * @return
     */
    List<OrdStatisticTableExportVo> exportTable(OrdStatisticBo bo);

    /**
     * 查询符合条件的订单统计列表
     *
     * @param bo 查询条件
     * @return 订单统计列表
     */
    OrdStatisticVo queryList(OrdStatisticBo bo);

    /**
     * 新增订单统计
     *
     * @param bo 订单统计
     * @return 是否新增成功
     */
    Boolean insertByBo(OrdStatisticBo bo);

    /**
     * 修改订单统计
     *
     * @param bo 订单统计
     * @return 是否修改成功
     */
    Boolean updateByBo(OrdStatisticBo bo);

    /**
     * 校验并批量删除订单统计信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 订单统计
     *
     * @param statisticDate 统计时间
     */
    void doStatistic(String statisticDate);

    /**
     * 图表折线统计
     *
     * @param bo
     * @return
     */
    Object line(OrdStatisticBo bo);
}
