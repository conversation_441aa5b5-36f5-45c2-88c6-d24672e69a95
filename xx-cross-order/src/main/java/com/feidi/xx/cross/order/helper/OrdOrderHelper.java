package com.feidi.xx.cross.order.helper;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.SmsUseEnum;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.market.manage.MktCacheManager;
import com.feidi.xx.cross.common.cache.operate.enums.OprCacheKeyEnum;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import com.feidi.xx.cross.common.cache.operate.vo.OprEstimateRecordCacheVo;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.cache.power.manager.PowCacheManager;
import com.feidi.xx.cross.common.constant.market.MarketCacheConstants;
import com.feidi.xx.cross.common.constant.operate.OperateCacheConstants;
import com.feidi.xx.cross.common.enums.order.CreateModelEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.market.api.RemoteInviteConfigService;
import com.feidi.xx.cross.market.api.domain.RemoteMktInviteConfigVo;
import com.feidi.xx.cross.operate.api.domain.platform.vo.RemotePlatformVo;
import com.feidi.xx.cross.order.api.domain.bo.RemotePositionBo;
import com.feidi.xx.cross.order.chain.place.OrderPlaceChainContext;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.bo.OrdOrderPriceBo;
import com.feidi.xx.cross.order.domain.bo.OrdOrderProfitBo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderPriceVo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderProfitVo;
import com.feidi.xx.cross.order.mq.event.OrderRobEvent;
import com.feidi.xx.cross.power.api.RemoteAgentRateService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.api.domain.agentrate.vo.RemoteAgentRateVo;
import com.feidi.xx.cross.power.api.domain.agentrate.vo.RemotePlatformAgentVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverRateVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.resource.api.RemoteSmsService;
import com.feidi.xx.system.api.RemoteConfigService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;

/**
 * 订单辅助类
 */
@Slf4j
@Component
@AllArgsConstructor
public class OrdOrderHelper {

    private final OrdCacheManager ordCacheManager;
    private final OprCacheManager oprCacheManager;
    private final PowCacheManager powCacheManager;
    private final MktCacheManager mktCacheManager;
    @DubboReference
    private final RemoteSmsService remoteSmsService;
    @DubboReference
    private final RemoteAgentRateService remoteAgentRateService;
    @DubboReference
    private final RemoteInviteConfigService remoteInviteConfigService;
    @DubboReference
    private final RemoteConfigService configService;

    // TODO 后期改缓存
    private Map<String, RemotePlatformAgentVo> getAgentRateMap() {
        List<RemotePlatformAgentVo> agentInfo = remoteAgentRateService.listByAgentId(null);
        return StreamUtils.toMap(agentInfo, e -> StrUtil.format("{}#{}", e.getAgentId(), e.getPlatformId()), Function.identity());
    }

    /**
     * @param map
     * @param agentId      代理商id
     * @param platformCode 平台代码
     * @param isQueryOnly  是否只是查询
     * @return
     */
    private BigDecimal getAgentRate(Map<String, RemotePlatformAgentVo> map, long agentId, String platformCode, Boolean isQueryOnly) {
        String key = StrUtil.format("{}#{}", agentId, platformCode);
        RemotePlatformAgentVo agentRate = map.get(key);
        if (agentRate == null) {
            log.error("代理商{}平台{}下的佣金比例为空", agentId, platformCode);
            return new BigDecimal("100");
        }
        if (isQueryOnly != null && !isQueryOnly) {
            Assert.isTrue(StatusEnum.ENABLE.getCode().equals(agentRate.getStatus()), "当前接单功能已被限制，请联系运营人员");
        }
        return agentRate.getRate();
    }

    /**
     * 计算订单收益
     * 公式：订单价格 * 平台佣金比例 (* 父代理商佣金比例) * 代理商佣金比例 * 司机佣金比例
     * 公式：原订单价*奖励比例*代理比例
     *
     * @param profitBo 订单收益Bo
     * @return 订单收益Vo
     */
    public OrdOrderProfitVo calculateOrderProfit(OrdOrderProfitBo profitBo) {
        if (profitBo.getDriverId() == null) {
            log.error("订单{}计算收益失败，缺少司机信息", profitBo.getOrderId());
            throw new ServiceException("计算收益失败");
        }

        // 司机录单订单收益计算
        if (Objects.equals(profitBo.getCreateModel(), CreateModelEnum.DRIVER_ORDER.getCode())) {
            return calculateOrderProfitForDriverOrder(profitBo);
        }

        // 转卖订单收益计算
        if (Objects.equals(profitBo.getCreateModel(), CreateModelEnum.RESELL_ORDER.getCode())) {
            return calculateOrderProfitForResell(profitBo);
        }

        // 平台信息
        BigDecimal platformRate = BigDecimal.valueOf(100);
        RemotePlatformVo platformVo = oprCacheManager.getPlatformInfoByCode(profitBo.getPlatformCode());
        if (ObjectUtils.isNotNull(platformVo) && platformVo.getRate().compareTo(BigDecimal.ZERO) > 0) {
            platformRate = platformVo.getRate();
        }

        // 代理商信息
        RemoteAgentVo agentVo = powCacheManager.getAgentInfoById(profitBo.getAgentId());
        if (ObjectUtils.isNull(agentVo)) {
            log.error("代理商{}不存在", profitBo.getAgentId());
            throw new ServiceException("代理商不存在，计算收益失败");
        }
        // 代理商佣金比例
        BigDecimal agentRate = BigDecimal.valueOf(100);
        RemoteAgentRateVo agentRateVo = powCacheManager.getAgentRateByAgentIdAndPlatformCode(profitBo.getAgentId(), profitBo.getPlatformCode());
        if (ObjectUtils.isNotNull(agentRateVo) && agentRateVo.getRate().compareTo(BigDecimal.ZERO) > 0) {
            agentRate = agentRateVo.getRate();
        }

        // 父代理商佣金比例
        RemoteAgentRateVo parentAgentRateVo = null;
        if (ObjectUtils.isNotNull(agentVo.getParentId()) && agentVo.getParentId() > 0) {
            parentAgentRateVo = powCacheManager.getAgentRateByAgentIdAndPlatformCode(agentVo.getParentId(), profitBo.getPlatformCode());
        }

        // 司机佣金比例
        BigDecimal driverRate = BigDecimal.valueOf(100);
        RemoteDriverRateVo driverRateVo = powCacheManager.getDriverRateByDriverIdAndPlatformCode(profitBo.getDriverId(), PlatformCodeEnum.TY.getCode());
        if (ObjectUtils.isNotNull(driverRateVo) && driverRateVo.getRate().compareTo(BigDecimal.ZERO) >= 0) {
            driverRate = driverRateVo.getRate();
        }

        // 计算司机收益
        OrdOrderProfitVo profitVo = this.calculateOrderProfitWithRate(profitBo.getOrderPrice(), platformRate, parentAgentRateVo, agentRate, driverRate);

        // 邀请有奖
        //邀请有奖营销活动
        boolean inviteRewardBoolean = isInviteRewardBoolean();
        if (inviteRewardBoolean && !Objects.equals(profitBo.getCreateModel(), CreateModelEnum.DRIVER_ORDER.getCode())) {
            if (ObjectUtils.isNotNull(profitBo.getInviteAgentId())) {
                //邀请有奖
                RemoteMktInviteConfigVo mktInviteConfigVo = mktCacheManager.getInviteConfigByAgentId(profitBo.getAgentId());
                if (mktInviteConfigVo != null) {
                    //佣金比例
                    Long orderPrice = profitBo.getOrderPrice();
                    BigDecimal recruitRewardRatio = mktInviteConfigVo.getRecruitRewardRatio();

                    profitVo.setParentAgentRate(recruitRewardRatio);
                    Long div = ArithUtils.profitUseBigDecimal(orderPrice, recruitRewardRatio);
                    //代理商收益
                    Long agentDiv = div;

                    //计算司机收益
                    if (profitBo.getInviteDriverId() != null) {
                        BigDecimal driverBonusRatio = mktInviteConfigVo.getDriverBonusRatio();
                        //profitVo.setDriverRate(driverBonusRatio);
                        Long driverMul = ArithUtils.profitUseBigDecimal(div, driverBonusRatio);
                        profitVo.setDriverInviteReward(driverMul);
                        double driverInviteRatio = ArithUtils.div(driverMul, orderPrice);
                        profitVo.setDriverInviteRatio(BigDecimal.valueOf(driverInviteRatio));
                        agentDiv = ArithUtils.sub(div, driverMul);
                    }
                    double agentInviteRatio = ArithUtils.div(agentDiv, orderPrice);
                     agentInviteRatio = ArithUtils.mul(agentInviteRatio, 100);
                    profitVo.setAgentInviteRatio(BigDecimal.valueOf(agentInviteRatio));
                    profitVo.setAgentInviteReward(agentDiv);
                }
            }
        }
        return profitVo;
    }


    /**
     * 根据佣金比例计算订单收益
     *
     * @param orderPrice 订单价格
     * @param platformRate 平台佣金比例
     * @param parentAgentRateVo 父代理商佣金比例
     * @param agentRate 代理商佣金比例
     * @param driverRate 司机佣金比例
     * @return 订单收益Vo
     */
    public OrdOrderProfitVo calculateOrderProfitWithRate(Long orderPrice, BigDecimal platformRate, RemoteAgentRateVo parentAgentRateVo,
                                                 BigDecimal agentRate, BigDecimal driverRate) {

        OrdOrderProfitVo profitVo = new OrdOrderProfitVo();

        // 平台收益
        long platformProfit = ArithUtils.profitUseBigDecimal(orderPrice, platformRate);
        profitVo.setPlatformRate(platformRate);
        profitVo.setPlatformProfit(platformProfit);

        // 父代理商收益（可能没有父代理商）
        long parentAgentProfit = platformProfit;
        if (ObjectUtils.isNotNull(parentAgentRateVo)) {
            BigDecimal parentAgentRate = BigDecimal.valueOf(100);
            if (parentAgentRateVo.getRate().compareTo(BigDecimal.ZERO) > 0) {
                parentAgentRate = parentAgentRateVo.getRate();
            }
            parentAgentProfit = ArithUtils.profitUseBigDecimal(platformProfit, parentAgentRate);

            profitVo.setParentAgentRate(parentAgentRate);
            profitVo.setParentAgentProfit(parentAgentProfit);
        }

        // 代理商收益
        long agentProfit = ArithUtils.profitUseBigDecimal(parentAgentProfit, agentRate);
        profitVo.setAgentRate(agentRate);
        profitVo.setAgentProfit(agentProfit);

        // 司机收益
        long driverProfit = ArithUtils.profitUseBigDecimal(agentProfit, driverRate);
        profitVo.setDriverRate(driverRate);
        profitVo.setDriverProfit(driverProfit);

        return profitVo;
    }


    /**
     * 司机录单订单计算收益
     *
     * @param profitBo 订单收益Bo
     * @return 订单收益
     */
    public OrdOrderProfitVo calculateOrderProfitForDriverOrder(OrdOrderProfitBo profitBo) {
        // 获取价格波动率配置

        RemoteAgentVo agentInfoById = powCacheManager.getAgentInfoById(profitBo.getAgentId());
        if (ObjectUtils.isNull(agentInfoById)) {
            throw new ServiceException("代理商信息异常!");
        }
        Double div =100.0;
        BigDecimal technicalFeeRatio = agentInfoById.getTechnicalFeeRatio();
        if (ObjectUtils.isNotNull(technicalFeeRatio)) {
            BigDecimal subtract = new BigDecimal(100).subtract(technicalFeeRatio);
            div =subtract.doubleValue();
        }

        if (ObjectUtils.isNotNull(div)) {
            // 平台信息
            BigDecimal platformRate = BigDecimal.valueOf(100);

            // 代理商信息
            RemoteAgentVo agentVo = powCacheManager.getAgentInfoById(profitBo.getAgentId());
            if (ObjectUtils.isNull(agentVo)) {
                log.error("代理商{}不存在", profitBo.getAgentId());
                throw new ServiceException("代理商不存在，计算收益失败");
            }
            // 代理商佣金比例
            BigDecimal agentRate = BigDecimal.valueOf(100);

            // 司机佣金比例
            BigDecimal driverRate = new BigDecimal(div);

            return this.calculateOrderProfitWithRate(profitBo.getOrderPrice(), platformRate, null, agentRate, driverRate);
        } else {
            // 如果价格浮动率配置不存在，抛出异常
            throw new ServiceException("查询司机收益价格异常!");
        }
    }

    /**
     * 司机录单计算收益
     *
     * @param profitBo
     * @return
     */
    public OrdOrderProfitVo calculateOrderProfitForResell(OrdOrderProfitBo profitBo) {

        OrdOrderProfitVo profitVo = new OrdOrderProfitVo();

        // 订单转卖司机信息
        RemoteDriverVo resellDriverVo = powCacheManager.getDriverInfoById(profitBo.getResellDriverId());

        // 转卖后司机接单金额
        Long resellDriverPrice = profitBo.getResellDriverPrice();

        // 订单金额
        Long orderPrice = profitBo.getOrderPrice();

        // 平台收益（订单金额 * 订单转卖服务费比例）
        Long platformProfit = ArithUtils.profitUseBigDecimal(orderPrice, resellDriverVo.getResellServiceRate());
        profitVo.setPlatformProfit(platformProfit);
        profitVo.setPlatformRate(BigDecimal.valueOf(ArithUtils.div(platformProfit, orderPrice, 4)));

        // 代理商收益 （订单转卖不涉及代理商收益，默认比例0%）
        profitVo.setAgentProfit(0L);
        profitVo.setAgentRate(BigDecimal.valueOf(0));

        // 司机收益（订单转卖司机收益已经在录单是确定，不需要计算）
        profitVo.setDriverProfit(resellDriverPrice);
        profitVo.setDriverRate(BigDecimal.valueOf(ArithUtils.div(resellDriverPrice, orderPrice, 4) * 100));

        long resellDriverProfit = orderPrice - platformProfit - resellDriverPrice;
        profitVo.setResellDriverProfit(resellDriverProfit);
        profitVo.setResellDriverRate(BigDecimal.valueOf(ArithUtils.div(resellDriverProfit, orderPrice, 4) * 100));

        return profitVo;
    }

    /**
     * 邀请有奖
     *
     * @return
     */
    public boolean isInviteRewardBoolean() {
        String inviteReward = configService.selectValueByKey(MarketCacheConstants.INVITE_REWARD);
        boolean inviteRewardBoolean = Objects.equals(IsYesEnum.YES.getCode(), inviteReward);
        return inviteRewardBoolean;
    }

    /**
     * 获取订单价格
     *
     * @param bo
     * @param userType 用户类型
     * @param userId   用户id
     * @return
     */
    public OrdOrderPriceVo getOrderPrice(OrdOrderPriceBo bo, UserTypeEnum userType, Long userId) {
        boolean isPool = ObjectUtil.isAllEmpty(bo.getDriverProfit(), bo.getAgentProfit(), bo.getParentAgentProfit())
                || NumberUtil.add(bo.getDriverProfit(), bo.getAgentProfit(), bo.getParentAgentProfit()).compareTo(BigDecimal.ZERO) == 0;
        OrdOrderPriceVo result = new OrdOrderPriceVo();
        result.setOrderPrice(bo.getOrderPrice());
        if (userType == null || userId == null) {
            return result;
        } else {
            if (userType.equals(UserTypeEnum.SYS_USER)) {
                result.setOrderPrice(bo.getOrderPrice());
            } else if (userType.equals(UserTypeEnum.AGENT_USER)) {
                // 订单池没有收益
                Map<String, RemotePlatformAgentVo> rateMap = isPool ? getAgentRateMap() : Collections.emptyMap();

                if (userId.equals(bo.getAgentId())) {
                    if (ArithUtils.isNotNull(bo.getAgentProfit())) {
                        result.setOrderPrice(bo.getAgentProfit());
                    } else {
                        long tmpPrice = bo.getOrderPrice();
                        // 先乘以父代理商
                        if (ArithUtils.isNotNull(bo.getParentId())) {
                            BigDecimal parentAgentRate = getAgentRate(rateMap, bo.getParentId(), bo.getPlatformCode(), true);
                            tmpPrice = ArithUtils.profitUseBigDecimal(tmpPrice, parentAgentRate);
                        }
                        // 再乘以当前代理商
                        BigDecimal agentRate = getAgentRate(rateMap, bo.getAgentId(), bo.getPlatformCode(), true);
                        tmpPrice = ArithUtils.profitUseBigDecimal(tmpPrice, agentRate);
                        result.setOrderPrice(tmpPrice);
                    }
                } else if (userId.equals(bo.getParentId())) {
                    // 如果父代理商可以看到这笔订单，那么展示的是自己的收益
                    if (ArithUtils.isNotNull(bo.getAgentProfit())) {
                        result.setOrderPrice(bo.getParentAgentProfit());
                    } else if (ArithUtils.isNotNull(bo.getParentId())) {
                        BigDecimal parentAgentRate = getAgentRate(rateMap, bo.getParentId(), bo.getPlatformCode(), true);
                        result.setOrderPrice(ArithUtils.profitUseBigDecimal(bo.getOrderPrice(), parentAgentRate));
                    }
                }
            } else if (userType.equals(UserTypeEnum.DRIVER_USER)) {
                if (userId.equals(bo.getDriverId())) {
                    if (ArithUtils.isNotNull(bo.getDriverProfit())) {
                        result.setOrderPrice(bo.getDriverProfit());
                    } else {
                        // 司机不调用这个方法，可以不写
                    }
                }
            }
        }
        return result;
    }

    public OrdOrderProfitBo createOrderProfitBo(OrdOrder order) {
        return new OrdOrderProfitBo()
                .setOrderId(order.getId())
                .setPlatformCode(order.getPlatformCode())
                .setPassengerId(order.getPassengerId())
                .setDriverId(order.getDriverId())
                .setAgentId(order.getAgentId())
                .setOrderPrice(order.getOrderPrice())
                .setInviteDriverId(order.getInviteDriverId())
                .setInviteAgentId(order.getInviteAgentId())
                .setEarliestTime(order.getEarliestTime())
                .setCreateModel(order.getCreateModel())
                .setResellDriverId(order.getResellDriverId())
                .setResellDriverPrice(order.getResellDriverPrice());
    }

    public OrderRobEvent buildOrderRobEvent(OrdOrder order, RemotePositionBo startPosition, RemotePositionBo endPosition) {
        return OrderRobEvent.builder()
                .tenantId(order.getTenantId())
                .orderId(order.getId())
                .platformCode(order.getPlatformCode())
                .platformNo(order.getPlatformNo())
                .driverId(order.getDriverId())
                .lineId(order.getLineId())
                .productCode(order.getProductCode())
                .passengerNum(order.getPassengerNum())
                .earliestTime(order.getEarliestTime())
                .latestTime(order.getLatestTime())
                .startAdCode(startPosition.getAdCode())
                .startLongitude(startPosition.getLongitude())
                .startLatitude(startPosition.getLatitude())
                .endAdCode(endPosition.getAdCode())
                .endLongitude(endPosition.getLongitude())
                .endLatitude(endPosition.getLatitude())
                .build();
    }

    /**
     * 异步发送短信
     *
     * @param orderId    订单ID
     * @param driverId   司机ID
     * @param orderNo    订单号
     * @param smsUseEnum 短信模板枚举
     */
    public void sendMessageAsync(Long orderId, Long driverId, String orderNo, SmsUseEnum smsUseEnum) {
        //RemoteOrderDriverVo orderDriverVo = ordCacheManager.getOrderDispatchDriverInfoByOrderId(orderId);
        RemoteDriverVo remoteDriverVo = powCacheManager.getDriverInfoById(driverId);
        if (ObjectUtil.isNotNull(remoteDriverVo)) {
            try {
                // 异步发送短信
                LinkedHashMap<String, String> paramsMap = new LinkedHashMap<>();
                paramsMap.put(smsUseEnum.getVariable(), orderNo);
                remoteSmsService.sendMessageAsync(remoteDriverVo.getPhone(), smsUseEnum.getTemplateId(), paramsMap);
            } catch (Exception e) {
                log.error("短信-发送失败；手机号：【{}】-结果：【{}】-错误原因：【{}】", remoteDriverVo.getPhone(), orderNo, e.getMessage());
            }
        }
    }


    /**
     * 异步发送短信
     *
     * @param placeContext 预计出发时间
     * @param smsUseEnum   短信模板枚举
     */
    public void sendOrderPlaceMessageAsync(OrderPlaceChainContext placeContext, SmsUseEnum smsUseEnum) {
        String key = OprCacheKeyEnum.OPR_ESTIMATE_RECORD_KEY.create(placeContext.getEstimateKey());
        if (RedisUtils.hasKey(key)) {
            OprEstimateRecordCacheVo cacheVo = RedisUtils.getCacheObject(key);
            try {
                // 异步发送短信
                LinkedHashMap<String, String> paramsMap = new LinkedHashMap<>();
                paramsMap.put("time",DateUtil.format(cacheVo.getEarliestTime(), "yyyy-MM-dd HH:mm:ss"));
                paramsMap.put("address", cacheVo.getEndShortAddress());
                remoteSmsService.sendMessageAsync(placeContext.getPassengerPhone(), smsUseEnum.getTemplateId(), paramsMap);
                log.info("短信-发送成功；");
            } catch (Exception e) {
                log.error("短信-发送失败；手机号：【{}】-结果：错误原因：【{}】", placeContext.getPassengerPhone(), e.getMessage());
            }
        }
    }


    /**
     * 异步发送短信
     * @param smsUseEnum
     * @param phone 手机号
     * @param orderNo 订单号
     */
    public void sendMessageAsync(SmsUseEnum smsUseEnum, String phone, String orderNo) {
        try {
            // 异步发送短信
            LinkedHashMap<String, String> paramsMap = new LinkedHashMap<>();
            paramsMap.put("orderNo", orderNo);
            remoteSmsService.sendMessageAsync(phone, smsUseEnum.getTemplateId(), paramsMap);
            log.info("短信-发送成功；");
        } catch (Exception e) {
            log.error("短信-发送失败；手机号：【{}】-结果：错误原因：【{}】", phone, e.getMessage());
        }
    }

    /**
     * 异步发送短信
     * @param smsUseEnum
     * @param phone 手机号
     * @param earliestTime 最早出发时间
     * @param endShortAddress 终点短地址
     */
    public void sendMessageAsync(SmsUseEnum smsUseEnum, String phone, Date earliestTime, String endShortAddress) {
        log.info("短信-发送短信；手机号：【{}】-结果：【{}】-时间：【{}】-地址：【{}】", phone, smsUseEnum.getTemplateId(), earliestTime, endShortAddress);
        try {
            // 异步发送短信
            LinkedHashMap<String, String> paramsMap = new LinkedHashMap<>();
            paramsMap.put("time",DateUtil.format(earliestTime, "yyyy-MM-dd HH:mm:ss"));
            paramsMap.put("address", endShortAddress);
            remoteSmsService.sendMessageAsync(phone, smsUseEnum.getTemplateId(), paramsMap);
            log.info("短信-发送成功；");
        } catch (Exception e) {
            log.error("短信-发送失败；手机号：【{}】-结果：错误原因：【{}】", phone, e.getMessage());
        }
    }

    /**
     * 异步发送短信
     * @param smsUseEnum
     * @param phone 手机号
     * @param earliestTime 最早出发时间
     * @param endShortAddress 终点短地址
     */
    public void sendMessageAsync(SmsUseEnum smsUseEnum, String phone, Date earliestTime, String endShortAddress, String code) {
        log.info("短信-发送短信；手机号：【{}】-结果：【{}】-时间：【{}】-地址：【{}】 - code: 【{}】", phone, smsUseEnum.getTemplateId(), earliestTime, endShortAddress, code);
        try {
            // 异步发送短信
            LinkedHashMap<String, String> paramsMap = new LinkedHashMap<>();
            paramsMap.put("time",DateUtil.format(earliestTime, "yyyy-MM-dd HH:mm:ss"));
            paramsMap.put("address", endShortAddress);
            paramsMap.put("code", code);
            remoteSmsService.sendMessageAsync(phone, smsUseEnum.getTemplateId(), paramsMap);
            log.info("短信-发送成功；");
        } catch (Exception e) {
            log.error("短信-发送失败；手机号：【{}】-结果：错误原因：【{}】", phone, e.getMessage());
        }
    }
}
