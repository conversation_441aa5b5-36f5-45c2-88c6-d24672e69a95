package com.feidi.xx.cross.order.chain.payment;

import com.feidi.xx.cross.common.constant.order.OrderLockKeyConstants;
import com.feidi.xx.cross.order.chain.common.BaseChain;
import com.feidi.xx.cross.order.chain.handler.OrderDataHandler;
import com.feidi.xx.cross.order.chain.handler.OrderServiceHandler;
import com.feidi.xx.cross.order.chain.handler.OrderVerifyHandler;
import com.feidi.xx.cross.order.chain.handler.common.OrderAfterAsyncHandler;
import com.feidi.xx.cross.order.chain.handler.common.OrderAfterHandler;
import com.feidi.xx.cross.order.chain.handler.common.OrderPreHandler;
import com.feidi.xx.cross.order.chain.place.OrderPlaceChainContext;
import com.feidi.xx.cross.order.chain.place.OrderPlaceChainResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 订单支付链
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderPaymentChain extends BaseChain<OrderPaymentChainContext, OrderPaymentChainResult> implements ApplicationContextAware {

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        OrderPreHandler<OrderPaymentChainContext, OrderPaymentChainResult> orderPreHandler = applicationContext.getBean(OrderPreHandler.class);
        OrderDataHandler<OrderPaymentChainContext, OrderPaymentChainResult> orderDataHandler = applicationContext.getBean(OrderDataHandler.class);
        OrderVerifyHandler<OrderPaymentChainContext, OrderPaymentChainResult> orderVerifyHandler = applicationContext.getBean(OrderVerifyHandler.class);
        OrderServiceHandler<OrderPaymentChainContext, OrderPaymentChainResult> orderServiceHandler = applicationContext.getBean(OrderServiceHandler.class);
        OrderAfterHandler<OrderPaymentChainContext, OrderPaymentChainResult> orderAfterHandler = applicationContext.getBean(OrderAfterHandler.class);
        OrderAfterAsyncHandler<OrderPaymentChainContext, OrderPaymentChainResult> orderAfterAsyncHandler = applicationContext.getBean(OrderAfterAsyncHandler.class);

        handlers.add(orderPreHandler);
        handlers.add(orderDataHandler);
        handlers.add(orderVerifyHandler);
        handlers.add(orderServiceHandler);
        handlers.add(orderAfterHandler);
        handlers.add(orderAfterAsyncHandler);
    }

    @Override
    public String getLockKey(OrderPaymentChainContext context) {
        return OrderLockKeyConstants.LOCK_ORDER_KEY + "payment:" + context.getOrderId();
    }
}
