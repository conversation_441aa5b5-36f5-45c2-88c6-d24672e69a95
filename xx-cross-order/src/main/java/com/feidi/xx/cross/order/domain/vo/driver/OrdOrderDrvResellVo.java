package com.feidi.xx.cross.order.domain.vo.driver;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 司机端订单转卖统计对象
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
@Data
public class OrdOrderDrvResellVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 待卖出
     */
    private Long unsoldNum;

    /**
     * 已卖出
     */
    private Long saleNum;

    /**
     * 担保中的收益
     */
    private Long unsoldProfit;

    /**
     * 已卖订单收益
     */
    private Long saleProfit;
}
