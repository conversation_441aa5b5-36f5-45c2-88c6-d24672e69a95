package com.feidi.xx.cross.order.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.order.domain.bo.OrdEvaluationsBo;
import com.feidi.xx.cross.order.domain.bo.OrdOrderCommentBo;
import com.feidi.xx.cross.order.domain.vo.OrdEvaluationsVo;

import java.util.Collection;
import java.util.List;

/**
 * 行程评价Service接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IOrdEvaluationsService {

    /**
     * 查询行程评价
     *
     * @param id 主键
     * @return 行程评价
     */
    OrdEvaluationsVo queryById(Long id);

    /**
     * 分页查询行程评价列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 行程评价分页列表
     */
    TableDataInfo<OrdEvaluationsVo> queryPageList(OrdEvaluationsBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的行程评价列表
     *
     * @param bo 查询条件
     * @return 行程评价列表
     */
    List<OrdEvaluationsVo> queryList(OrdEvaluationsBo bo);

    /**
     * 新增行程评价
     *
     * @param bo 行程评价
     * @return 是否新增成功
     */
    Boolean insertByBo(OrdEvaluationsBo bo);

    /**
     * 修改行程评价
     *
     * @param bo 行程评价
     * @return 是否修改成功
     */
    Boolean updateByBo(OrdEvaluationsBo bo);

    /**
     * 校验并批量删除行程评价信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 行程评价 - 订单评论
     *
     * @param bo
     */
    void comment(OrdOrderCommentBo bo);
    
    /**
     * 根据订单ID查询评价
     *
     * @param orderId 订单ID
     * @return 评价信息
     */
    OrdEvaluationsVo queryByOrderId(Long orderId);
}
