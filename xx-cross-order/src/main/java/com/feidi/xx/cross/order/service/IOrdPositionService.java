package com.feidi.xx.cross.order.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.order.domain.bo.OrdPositionBo;
import com.feidi.xx.cross.order.domain.vo.OrdPositionVo;

import java.util.Collection;
import java.util.List;

/**
 * 订单位置Service接口
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface IOrdPositionService {

    /**
     * 查询订单位置
     *
     * @param id 主键
     * @return 订单位置
     */
    OrdPositionVo queryById(Long id);

    /**
     * 分页查询订单位置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单位置分页列表
     */
    TableDataInfo<OrdPositionVo> queryPageList(OrdPositionBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的订单位置列表
     *
     * @param bo 查询条件
     * @return 订单位置列表
     */
    List<OrdPositionVo> queryList(OrdPositionBo bo);

    /**
     * 新增订单位置
     *
     * @param bo 订单位置
     * @return 是否新增成功
     */
    Boolean insertByBo(OrdPositionBo bo);

    /**
     * 批量新增订单位置
     *
     * @param bos 订单位置集合
     * @return 是否新增成功
     */
    Boolean insertByBos(List<OrdPositionBo> bos);

    /**
     * 修改订单位置
     *
     * @param bo 订单位置
     * @return 是否修改成功
     */
    Boolean updateByBo(OrdPositionBo bo);

    /**
     * 校验并批量删除订单位置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取订单id和类型获取订单位置
     *
     * @param orderId 订单id
     * @param type    类型
     * @return 订单位置
     */
    OrdPositionVo queryByOrderIdAndType(Long orderId, String type);

    /**
     * 根据订单id获取订单位置
     *
     * @param orderIds 订单id集合
     * @return 订单位置集合
     */
    List<OrdPositionVo> queryByOrderIds(List<Long> orderIds);
}
