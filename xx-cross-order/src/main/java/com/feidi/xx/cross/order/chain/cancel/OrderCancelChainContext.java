package com.feidi.xx.cross.order.chain.cancel;


import com.feidi.xx.cross.common.enums.order.OperateTypeEnum;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderCancelBo;
import com.feidi.xx.cross.order.chain.base.OrderBaseChainContext;
import com.feidi.xx.cross.order.domain.OrdOrder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 取消上下文
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderCancelChainContext extends OrderBaseChainContext {


    /**
     * 取消类型
     */
    private String cancelType;

    /**
     * 取消备注
     */
    private String cancelRemark;

    /**
     * 是否客诉
     */
    private String complain;

    /**
     * 客诉时间
     */
    private Date complainTime;

    /**
     * 客诉类型
     */
    private String complainType;

    /**
     * 客诉备注
     */
    private String complainRemark;

    /**
     * 投诉扣款金额
     */
    private Long complainPrice;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 订单取消实体
     */
    private RemoteOrderCancelBo handleBo;

    private OrdOrder order;

    public OrderCancelChainContext() {
        this.setOperateType(OperateTypeEnum.CANCEL.getCode());
    }
}
