package com.feidi.xx.cross.order.chain.handler;

import com.feidi.xx.cross.common.annotations.HandlerScope;
import com.feidi.xx.cross.order.chain.base.OrderBaseChainContext;
import com.feidi.xx.cross.order.chain.base.OrderBaseChainResult;
import com.feidi.xx.cross.order.chain.common.AbstractChainHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 订单支付处理器
 */
@Slf4j
@Component
@HandlerScope
@RequiredArgsConstructor
public class OrderPaymentHandler<T extends OrderBaseChainContext, R extends OrderBaseChainResult> extends AbstractChainHandler<T, R> {

    @Override
    public void handle(T context) {

    }

}