
package com.feidi.xx.cross.order.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.feidi.xx.common.core.enums.PaymentStatusEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.idempotent.annotation.NoMQDuplicateConsume;
import com.feidi.xx.common.log.enums.PlatformEnum;
import com.feidi.xx.common.rocketmq.base.MessageWrapper;
import com.feidi.xx.common.tenant.helper.TenantHelper;
import com.feidi.xx.cross.common.constant.order.OrderRocketMQConstant;
import com.feidi.xx.cross.common.enums.order.CancelTypeEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.utils.ExceptionUtil;
import com.feidi.xx.cross.order.chain.cancel.OrderCancelChain;
import com.feidi.xx.cross.order.chain.cancel.OrderCancelChainContext;
import com.feidi.xx.cross.order.chain.cancel.OrderCancelChainResult;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.mq.event.OrderCancelEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 订单延时消息消费者
 */
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = OrderRocketMQConstant.XX_ORDER_CANCEL_TOPIC_KEY,
        consumerGroup = OrderRocketMQConstant.XX_ORDER_CANCEL_CG_KEY
)
@Slf4j(topic = "OrderCancelConsumer")
public class OrderCancelConsumer implements RocketMQListener<MessageWrapper<OrderCancelEvent>> {

    private final OrdOrderMapper orderMapper;

    private final OrderCancelChain orderCancelChain;

    @NoMQDuplicateConsume(
            keyPrefix = "global:xx-cross-cancel-order:",
            key = "#messageWrapper.keys",
            keyTimeout = 600
    )
    @Override
    public void onMessage(MessageWrapper<OrderCancelEvent> messageWrapper) {
        OrderCancelEvent message = messageWrapper.getMessage();
        log.info("[消费者] 订单延时消息 - 执行消费逻辑，消息体：{}", JSON.toJSONString(messageWrapper));
        OrdOrder order = orderMapper.selectById(message.getOrderId());
        if (order != null) {
            log.info("订单取消: 订单ID：{} 取消类型：{}，状态：{}，支付状态：{}", order.getId(), message.getCancelType(), order.getStatus(), order.getPayStatus());
            if (message.getCancelType() != null) {
                if (OrderStatusEnum.CANCEL.getCode().equals(order.getStatus())) {
                    log.info("订单取消: 订单ID：{} 已取消，不处理， 类型：{}", order.getId(), message.getCancelType());
                    return;
                }
                if (CancelTypeEnum.UNPAID.getCode().equals(message.getCancelType())) {
                    if (PaymentStatusEnum.SUCCESS.getCode().equals(order.getPayStatus())) {
                        log.info("订单支付超时取消跳过: 订单ID：{} 已支付，不处理， 类型：{}", order.getId(), message.getCancelType());
                        return;
                    }
                    if (Integer.parseInt(order.getStatus()) > Integer.parseInt(OrderStatusEnum.CREATE.getCode())) {
                        log.info("订单支付超时取消跳过: 订单ID：{} 已被接单，不处理， 类型：{}", order.getId(), message.getCancelType());
                        return;
                    }
                } else if (CancelTypeEnum.RECEIVE.getCode().equals(message.getCancelType())) {
                    if (Integer.parseInt(order.getStatus()) > Integer.parseInt(OrderStatusEnum.CREATE.getCode())) {
                        log.info("订单接单超时取消跳过: 订单ID：{} 已被接单，不处理， 类型：{}", order.getId(), message.getCancelType());
                        return;
                    }
                }
            }
            // 处理
            TenantHelper.setDynamic(order.getTenantId());
            ExceptionUtil.ignoreEx(() -> {
                if (message.getPaymentTypeChangeOnly() != null && message.getPaymentTypeChangeOnly()) {
                    log.info("订单支付类型变更: 订单ID：{} 状态：{} -> {}", order.getId(), order.getPayStatus(), PaymentStatusEnum.NO.getCode());
                    if (PaymentStatusEnum.ING.getCode().equals(order.getPayStatus())) {
                        orderMapper.update(null, new LambdaUpdateWrapper<OrdOrder>()
                                .eq(OrdOrder::getId, order.getId())
                                .set(OrdOrder::getPayStatus, PaymentStatusEnum.NO.getCode())
                                .set(OrdOrder::getUpdateTime, new Date())
                                .set(OrdOrder::getUpdateBy, null));
                    }
                } else {
                    OrderCancelChainResult execute = orderCancelChain.execute(createContext(order.getId(), message.getCancelType()));
                }
            });
            TenantHelper.clearDynamic();
        }
    }

    private OrderCancelChainContext createContext(Long orderId, String cancelType) {
        OrderCancelChainContext context = new OrderCancelChainContext();
        context.setOrderId(orderId);
        context.setPlatformCode(PlatformEnum.SELF.getCode());
        context.setCancelType(cancelType);
        context.setUserType(UserTypeEnum.AUTO_USER.getUserType());
        context.setTimeStamp(System.currentTimeMillis());
        return context;
    }
}
