package com.feidi.xx.cross.order.mapper;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.enums.order.CreateModelEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.enums.order.RebateStatusEnum;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.bo.driver.OrdOrderDrvQueryBo;
import com.feidi.xx.cross.order.domain.update.UpdateDTO;
import com.feidi.xx.cross.order.domain.vo.OrdOrderVo;
import com.feidi.xx.cross.order.domain.vo.driver.OrdOrderDrvDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 订单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface OrdOrderMapper extends BaseMapperPlus<OrdOrder, OrdOrderVo> {

    default OrdOrder getByCode(String code, String passengerPhone) {
        return selectOne(Wrappers.<OrdOrder>lambdaQuery()
                .eq(OrdOrder::getCode, code)
                .eq(StrUtil.isNotBlank(passengerPhone), OrdOrder::getPassengerPhone, passengerPhone)
        );
    }

    /**
     * 统一更新入口
     * @param dto
     * @return
     */
    default boolean update(UpdateDTO dto) {
        Assert.notNull(dto.getId(), "id不能为空");
        OrdOrder convert = MapstructUtils.convert(dto, OrdOrder.class);
        return update(convert, Wrappers.<OrdOrder>lambdaUpdate()
                .set(OrdOrder::getVersion, dto.getNewVersion())
                .set(OrdOrder::getUpdateTime, dto.getUpdateTime() != null ? dto.getUpdateTime() : new Date())
                .set(OrdOrder::getUpdateBy, dto.getUpdateBy() != null ? dto.getUpdateBy() : LoginHelper.getUserId())
                .eq(OrdOrder::getId, dto.getId())
                .eq(OrdOrder::getVersion, dto.getOldVersion())
        ) > 0;
    }

    /**
     * 查询乘客进行中的订单
     * @param psgId
     * @return
     */
    default List<OrdOrder> listPsgIngOrder(Long psgId) {
        return selectList(Wrappers.<OrdOrder>lambdaQuery()
                .eq(OrdOrder::getPassengerId, psgId)
                .notIn(OrdOrder::getStatus, OrderStatusEnum.getOverCodes())
        );
    }

    /**
     * 根据ids多线程查
     */
    default List<OrdOrder> listByIdsParallel(List<Long> ids) {
        List<List<Long>> partition = ListUtil.partition(ids, 1000);
        // 使用CompletableFuture处理并发
        List<CompletableFuture<List<OrdOrder>>> futures = new ArrayList<>();
        for (List<Long> part : partition) {
            CompletableFuture<List<OrdOrder>> future = CompletableFuture.supplyAsync(() -> selectBatchIds(part));
            futures.add(future);
        }
        // 等待所有异步任务完成并收集结果
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        return allOf.thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)  // 获取每个任务的结果
                        .flatMap(List::stream)         // 将多个List扁平化
                        .collect(Collectors.toList())) // 收集成一个List
                .join();
    }


    /**
     * 查询待结算的订单
     * @param finishTime 结束时间
     * @return
     */
    default List<OrdOrder> queryRebateOrder(Date finishTime, List<String> platformCode) {
        LambdaQueryWrapper<OrdOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrdOrder::getStatus, OrderStatusEnum.FINISH.getCode())
                .eq(OrdOrder::getRebateStatus, RebateStatusEnum.ING.getCode())
                .in(CollUtils.isNotEmpty(platformCode), OrdOrder::getPlatformCode, platformCode)
                .eq(OrdOrder::getDispatch, IsYesEnum.YES.getCode())
                .gt(OrdOrder::getDriverId, 0)
                .le(OrdOrder::getFinishTime, finishTime);
        return selectList(queryWrapper);
    }

    /**
     * 查询待结算的转卖订单
     *
     * @param finishTime
     * @param platformCode
     * @return
     */
    default List<OrdOrder> queryRebateOrderResell(Date finishTime, List<String> platformCode) {
        LambdaQueryWrapper<OrdOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrdOrder::getStatus, OrderStatusEnum.FINISH.getCode())
                .eq(OrdOrder::getResellRebateStatus, RebateStatusEnum.ING.getCode())
                .eq(OrdOrder::getCreateModel, CreateModelEnum.RESELL_ORDER.getCode())
                .in(CollUtils.isNotEmpty(platformCode), OrdOrder::getPlatformCode, platformCode)
                .eq(OrdOrder::getDispatch, IsYesEnum.YES.getCode())
                .gt(OrdOrder::getResellDriverId, 0)
                .le(OrdOrder::getFinishTime, finishTime);
        return selectList(queryWrapper);
    }

    /**
     * 查询可删除的订单
     *
     * @param deleteTime
     * @return
     */
    List<Long> queryCanDeleteOrders(@Param("deleteTime") String deleteTime);

    void deleteByOrderIds(@Param("orderIds") List<Long> orderIds);


    /**
     * 订单列表-司机端
     * @param bo
     * @param page
     * @return
     */
    IPage<OrdOrderDrvDetailVo> queryList(@Param("bo") OrdOrderDrvQueryBo bo, IPage<?> page);

    /**
     * 订单列表-司机端
     * @param bo
     * @return
     */
    List<OrdOrderDrvDetailVo> queryList(@Param("bo") OrdOrderDrvQueryBo bo);

    /**
     * 司机端-订单详情
     *
     * @param bo
     * @return
     */
    OrdOrderDrvDetailVo queryDetail(@Param("bo") OrdOrderDrvQueryBo bo);
}
