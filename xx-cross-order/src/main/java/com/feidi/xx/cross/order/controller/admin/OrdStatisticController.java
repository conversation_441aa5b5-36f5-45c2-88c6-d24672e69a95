package com.feidi.xx.cross.order.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.order.domain.bo.OrdStatisticBo;
import com.feidi.xx.cross.order.domain.vo.OrdStatisticExportVo;
import com.feidi.xx.cross.order.domain.vo.OrdStatisticTableExportVo;
import com.feidi.xx.cross.order.domain.vo.OrdStatisticTableVo;
import com.feidi.xx.cross.order.domain.vo.OrdStatisticVo;
import com.feidi.xx.cross.order.service.IOrdStatisticService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 后台 - 订单统计
 * 前端访问路由地址为:/order/statistic
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/statistic")
public class OrdStatisticController extends BaseController {

    private final IOrdStatisticService ordStatisticService;

    /**
     * 图表数据统计
     */
    @SaCheckPermission("order:statistic:list")
    @GetMapping("/list")
    public OrdStatisticVo list(OrdStatisticBo bo) {
        return ordStatisticService.queryList(bo);
    }

    /**
     * 图表折线统计
     */
    @SaCheckPermission("order:statistic:list")
    @GetMapping("/line")
    public Object line(OrdStatisticBo bo) {
        return ordStatisticService.line(bo);
    }

    /**
     * 表格数据统计
     *
     * @param bo
     * @return
     */
    @SaCheckPermission("order:statistic:list")
    @GetMapping("/listTable")
    public List<OrdStatisticTableVo> listTable(OrdStatisticBo bo) {
        return ordStatisticService.queryTableList(bo);
    }

    /**
     * 订单统计数据导出
     */
    @SaCheckPermission("order:statistic:export")
    @Log(title = "订单统计", businessType = BusinessType.EXPORT)
    @Download(name="订单统计",module = ModuleConstants.ORDER,mode="no")
    @PostMapping("/export")
    public Object export(@RequestBody OrdStatisticBo bo, HttpServletResponse response) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        if (Objects.equals(bo.getExportType(), "0")) {
            OrdStatisticVo ordStatisticVo = ordStatisticService.queryList(bo);
            List<OrdStatisticExportVo> exportVos = MapstructUtils.convert(Collections.singletonList(ordStatisticVo), OrdStatisticExportVo.class);
            exportVos.forEach(e -> e.setDate(DateUtils.parseDateToStr("yyyy-MM-dd", bo.getStartDate()) + "-" + DateUtils.parseDateToStr("yyyy-MM-dd", bo.getEndDate())));
            ExcelUtil.exportExcel(exportVos, "订单统计", OrdStatisticExportVo.class, outputStream);
        } else {
            List<OrdStatisticTableExportVo> exportVos = ordStatisticService.exportTable(bo);
            ExcelUtil.exportExcel(exportVos, "订单统计", OrdStatisticTableExportVo.class, outputStream);
        }

        return outputStream.toByteArray();
    }

    /**
     * 获取订单统计详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("order:statistic:query")
    @GetMapping("/{id}")
    public R<OrdStatisticVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(ordStatisticService.queryById(id));
    }

    /**
     * 新增订单统计
     */
    @SaCheckPermission("order:statistic:add")
    @Log(title = "订单统计", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OrdStatisticBo bo) {
        return toAjax(ordStatisticService.insertByBo(bo));
    }

    /**
     * 修改订单统计
     */
    @SaCheckPermission("order:statistic:edit")
    @Log(title = "订单统计", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OrdStatisticBo bo) {
        return toAjax(ordStatisticService.updateByBo(bo));
    }

    /**
     * 删除订单统计
     *
     * @param ids 主键串
     */
    @SaCheckPermission("order:statistic:remove")
    @Log(title = "订单统计", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(ordStatisticService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 订单统计
     */
    @RepeatSubmit(interval = 30000)
    @SaCheckPermission("order:statistic:add")
    @Log(title = "订单统计", businessType = BusinessType.INSERT)
    @GetMapping("/doStatistic")
    public R<Void> doStatistic(@RequestParam("statisticDate") String statisticDate) {
        ordStatisticService.doStatistic(statisticDate);
        return R.ok();
    }
}
