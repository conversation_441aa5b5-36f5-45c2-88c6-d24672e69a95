package com.feidi.xx.cross.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.common.enums.operate.ProductCodeEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.order.domain.OrdOrder;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.util.Date;

/**
 * 乘客 - 订单视图对象
 *
 * <AUTHOR>
 * @date 2025-1-2
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrdOrder.class, reverseConvertGenerate = false)
public class OrdOrderMbrListVo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 预计出发时间
     */
    private Date earliestTime;

    /**
     * 预计最晚出发时间
     */
    private Date latestTime;

    /**
     * 乘客数量
     */
    private Integer passengerNum;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 起点位置
     */
    private OrdPositionVo startPositionVo;

    /**
     * 终点位置
     */
    private OrdPositionVo endPositionVo;

    /**
     * 支付状态
     */
    private String payStatus;

    /**
     * 产品类型
     */
    @Enum2Text(enumClass = ProductCodeEnum.class)
    @ExcelEnumFormat(enumClass = ProductCodeEnum.class)
    private String productCode;

    /**
     * 订单状态
     */
    @Enum2Text(enumClass = OrderStatusEnum.class, field = "showTextPsg")
    @ExcelEnumFormat(enumClass = OrderStatusEnum.class)
    private String status;

    /**
     * 订单金额
     */
    private Long orderPrice;

    /**
     * 实际支付金额
     */
    private Long payPrice;

    /**
     * 优惠券额度
     */
    private Long couponGrantQuota;
}
