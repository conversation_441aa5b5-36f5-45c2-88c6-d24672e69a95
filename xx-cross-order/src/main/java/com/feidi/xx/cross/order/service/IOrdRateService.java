package com.feidi.xx.cross.order.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.order.domain.OrdRate;
import com.feidi.xx.cross.order.domain.bo.OrdRateBo;
import com.feidi.xx.cross.order.domain.vo.OrdRateVo;

import java.util.Collection;
import java.util.List;

/**
 * 订单账单Service接口
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface IOrdRateService {

    /**
     * 查询订单账单
     *
     * @param id 主键
     * @return 订单账单
     */
    OrdRateVo queryById(Long id);

    /**
     * 分页查询订单账单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单账单分页列表
     */
    TableDataInfo<OrdRateVo> queryPageList(OrdRateBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的订单账单列表
     *
     * @param bo 查询条件
     * @return 订单账单列表
     */
    List<OrdRateVo> queryList(OrdRateBo bo);

    /**
     * 新增订单账单
     *
     * @param bo 订单账单
     * @return 是否新增成功
     */
    Boolean insertByBo(OrdRateBo bo);

    /**
     * 批量新增订单费率
     *
     * @param ordRateBos
     * @return
     */
    Boolean insertByBos(List<OrdRateBo> ordRateBos);

    /**
     * 修改订单账单
     *
     * @param bo 订单账单
     * @return 是否修改成功
     */
    Boolean updateByBo(OrdRateBo bo);

    /**
     * 校验并批量删除订单账单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据订单id和账单类型查询订单账单
     *
     * @param orderIds 账单id集合
     * @param rateType 账单类型
     * @return 订单账单信息
     */
    List<OrdRate> queryByOrderIdsAndRateType(List<Long> orderIds, String rateType);

    /**
     * 根据订单id和账单类型查询订单账单
     *
     * @param orderId 账单id
     * @param rateType 账单类型
     * @return 订单账单信息
     */
    OrdRate queryByOrderIdAndRateType(Long orderId, String rateType);

    /**
     * 根据订单id更新返利状态
     *
     * @param statusCode 返利状态
     * @return 是否更新成功
     */
    Boolean updateRateStatusByOrderId(Long orderId, String statusCode);
}
