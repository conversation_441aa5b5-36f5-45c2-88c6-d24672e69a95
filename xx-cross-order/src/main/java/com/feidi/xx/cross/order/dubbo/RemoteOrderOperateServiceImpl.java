package com.feidi.xx.cross.order.dubbo;

import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.order.api.RemoteOrderOperateService;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderOperateBo;
import com.feidi.xx.cross.order.domain.OrdOperate;
import com.feidi.xx.cross.order.mapper.OrdOperateMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * 订单操作记录服务dubbo实现类
 * <AUTHOR>
 * @date 2025/3/20
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteOrderOperateServiceImpl implements RemoteOrderOperateService {

    private final OrdOperateMapper ordOperateMapper;

    /**
     * 添加订单操作记录
     *
     * @param operateBo 订单操作记录
     * @return 是否添加成功
     */
    @Override
    public Boolean addOrderOperate(RemoteOrderOperateBo operateBo) {
        return ordOperateMapper.insert(BeanUtils.copyProperties(operateBo, OrdOperate.class)) > 0;
    }

}
