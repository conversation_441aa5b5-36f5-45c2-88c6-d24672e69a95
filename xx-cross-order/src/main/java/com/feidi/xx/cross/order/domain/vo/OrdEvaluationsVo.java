package com.feidi.xx.cross.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.order.domain.OrdEvaluations;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 行程评价视图对象 ord_evaluations
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrdEvaluations.class)
public class OrdEvaluationsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 代理商id
     */
    @ExcelProperty(value = "代理商id")
    private Long agentId;

    /**
     * 订单ID
     */
    @ExcelProperty(value = "订单ID")
    private Long orderId;

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机id")
    private Long driverId;

    /**
     * 乘客id
     */
    @ExcelProperty(value = "乘客id")
    private Long passengerId;

    /**
     * 乘客真实手机号
     */
    @ExcelProperty(value = "乘客真实手机号")
    private String passengerPhone;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String passengerName;

    /**
     * 评分 (1-5星)
     */
    @ExcelProperty(value = "评分 (1-5星)")
    private Long rating;

    /**
     * 是否匿名。0：不是，1：是
     */
    @ExcelProperty(value = "是否匿名。0：不是，1：是")
    private Integer isAnonymous;

    /**
     * 评价内容
     */
    @ExcelProperty(value = "评价内容")
    private String comment;

    /**
     * 选择的标签。
     */
    @ExcelProperty(value = "选择的标签。")
    private List<String> tags;
    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private String tenantId;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
