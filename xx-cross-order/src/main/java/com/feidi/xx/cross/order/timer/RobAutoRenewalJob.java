package com.feidi.xx.cross.order.timer;

import com.feidi.xx.cross.order.service.IDisRobService;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 代理抢单自动续签
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RobAutoRenewalJob {
    private final IDisRobService disRobService;
    @GlobalTransactional
    @XxlJob("robAutoRenewalJob")
    public void autoRenewal(){
        if (log.isInfoEnabled()) {
            log.info("============== 代理抢单自动续签开始执行 ==============");
        }
        disRobService.autoRenewal();
        if (log.isInfoEnabled()) {
            log.info("============== 代理抢单自动续签执行结束 ==============");
        }
    }
}
