package com.feidi.xx.cross.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.PaymentStatusEnum;
import com.feidi.xx.common.core.enums.SuccessFailEnum;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.common.log.enums.PlatformEnum;
import com.feidi.xx.cross.common.enums.order.DispatchTypeEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.enums.order.RebateStatusEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 代理商订单导出Vo
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
public class OrdOrderExportAgtVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "上级代理商")
    private String parentAgentName;

    @ExcelProperty(value = "订单id")
    private Long id;

    private Long lineId;

    @ExcelProperty(value = "线路")
    private String lineName;

    @ExcelProperty(value = "司机ID")
    private Long driverId;

    @ExcelProperty(value = "司机名称")
    private String driverName;

    @ExcelProperty(value = "司机电话")
    private String driverPhone;

    @ExcelProperty(value = "司机佣金比例")
    private String driverRate;

    @ExcelProperty(value = "司机收益（单位：元）")
    private BigDecimal driverProfit;

    @ExcelProperty(value = "订单编号")
    private String orderNo;

    @ExcelProperty(value = "平台", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = PlatformEnum.class)
    private String platformCode;

    @ExcelProperty(value = "平台订单号")
    private String platformNo;

    @ExcelProperty(value = "订单金额（单位：元）")
    private BigDecimal orderPrice;

    @ExcelProperty(value = "最早出发时间")
    private Date earliestTime;

    @ExcelProperty(value = "最晚出发时间")
    private Date latestTime;

    @ExcelProperty(value = "人数")
    private Integer passengerNum;

    @ExcelProperty(value = "出发省")
    private String startProvince;

    @ExcelProperty(value = "出发市")
    private String startCity;

    @ExcelProperty(value = "出发区")
    private String startDistrict;

    @ExcelProperty(value = "出发地址")
    private String startAddress;

    @ExcelProperty(value = "目的地省")
    private String endProvince;

    @ExcelProperty(value = "目的地市")
    private String endCity;

    @ExcelProperty(value = "目的地区")
    private String endDistrict;

    @ExcelProperty(value = "目的地地址")
    private String endAddress;

    @ExcelProperty(value = "里程（单位：米）")
    private Long mileage;

    @ExcelProperty(value = "订单状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = OrderStatusEnum.class)
    private String status;

    @ExcelProperty(value = "返利状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = RebateStatusEnum.class)
    private String rebateStatus;

    @ExcelProperty(value = "支付状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = PaymentStatusEnum.class)
    private String payStatus;

    @ExcelProperty(value = "是否调度", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = IsYesEnum.class)
    private String isDispatch;

    @ExcelProperty(value = "调度类型", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = DispatchTypeEnum.class)
    private String dispatchType;

    @ExcelProperty(value = "调度状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = SuccessFailEnum.class)
    private String dispatchStatus;

    @ExcelProperty(value = "是否客诉", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = IsYesEnum.class)
    private String isComplain;

    @ExcelProperty(value = "发单时间")
    private Date createTime;

    @ExcelProperty(value = "接单时间")
    private Date receiveTime;

    @ExcelProperty(value = "完成时间")
    private Date finishTime;

    @ExcelProperty(value = "取消时间")
    private Date cancelTime;

    @ExcelProperty(value = "取消备注")
    private String cancelRemark;

    @ExcelProperty(value = "支付单号")
    private String payNo;

    @ExcelProperty(value = "支付时间")
    private Date payTime;
}
