package com.feidi.xx.cross.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.core.enums.SuccessFailEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.cross.common.enums.order.OperateTypeEnum;
import com.feidi.xx.cross.order.domain.OrdOperate;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 订单操作视图对象 ord_operate
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrdOperate.class)
public class OrdOperateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private String tenantId;

    /**
     * 订单ID
     */
    @ExcelProperty(value = "订单ID")
    private Long orderId;

    /**
     * 路径追踪id
     */
    @ExcelProperty(value = "路径追踪id")
    private String traceId;

    /**
     * 操作人类型[UserTypeEnum]
     */
    @ExcelProperty(value = "操作人类型[UserTypeEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "UserTypeEnum")
    private String userType;

    private String userTypeText;
    public String getUserTypeText() {
        return UserTypeEnum.getInfoByCode(this.userType);
    }

    /**
     * 操作人ID
     */
    @ExcelProperty(value = "操作人ID")
    private Long userId;

    /**
     * 操作人
     */
    @ExcelProperty(value = "操作人")
    private String userName;

    /**
     * 操作类型[OperateTypeEnum]
     */
    @Enum2Text(enumClass = OperateTypeEnum.class)
    @ExcelProperty(value = "操作类型[OperateTypeEnum]")
    private String operateType;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private String longitude;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private String latitude;

    /**
     * 参数
     */
    @ExcelProperty(value = "参数")
    private String paramsJson;

    /**
     * 结果
     */
    @ExcelProperty(value = "结果")
    private String responseJson;

    /**
     * 状态[SuccessFailEnum]
     */
    @Enum2Text(enumClass = SuccessFailEnum.class)
    @ExcelProperty(value = "状态[SuccessFailEnum]")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建部门
     */
    @ExcelProperty(value = "创建部门")
    private Long createDept;

    /**
     * 创建者
     */
    @ExcelProperty(value = "创建者")
    private Long createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新者
     */
    @ExcelProperty(value = "更新者")
    private Long updateBy;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @ExcelProperty(value = "删除标志", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=代表存在,2=代表删除")
    private String delFlag;


}
