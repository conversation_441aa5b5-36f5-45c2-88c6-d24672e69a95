package com.feidi.xx.cross.order.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.BatchGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.id2name.annotation.Id2NameAspect;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.order.domain.DisRob;
import com.feidi.xx.cross.order.domain.bo.rob.CoverQuery;
import com.feidi.xx.cross.order.domain.bo.rob.DisRobBo;
import com.feidi.xx.cross.order.domain.vo.DisRobVo;
import com.feidi.xx.cross.order.service.IDisRobService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 自动抢单
 * 前端访问路由地址为:/order/rob
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/rob")
public class DisRobController extends BaseController {

    private final IDisRobService disRobService;

    /**
     * 查询自动抢单列表
     */
    @Id2NameAspect
    @Enum2TextAspect
    @SaCheckPermission("order:rob:list")
    @PostMapping("/list")
    public TableDataInfo<DisRobVo> list(@RequestBody DisRobBo bo) {
        return disRobService.queryPageList(bo, bo.buildPageQuery());
    }

    /**
     * 导出自动抢单列表
     */
    @SaCheckPermission("order:rob:export")
    @Log(title = "自动抢单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(DisRobBo bo, HttpServletResponse response) {
        List<DisRobVo> list = disRobService.queryList(bo);
        ExcelUtil.exportExcel(list, "自动抢单", DisRobVo.class, response);
    }

    /**
     * 获取自动抢单详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("order:rob:query")
    @GetMapping("/{id}")
    public R<DisRobVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(disRobService.queryById(id));
    }

    /**
     * 新增自动抢单
     */
    @SaCheckPermission("order:rob:add")
    @Log(title = "自动抢单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody DisRobBo bo) {
        return toAjax(disRobService.insertByBo(bo));
    }

    /**
     * 修改自动抢单
     */
    @SaCheckPermission("order:rob:edit")
    @Log(title = "自动抢单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody DisRobBo bo) {
        return toAjax(disRobService.updateByBo(bo));
    }

    /**
     * 批量修改自动抢单时间范围
     */
    @SaCheckPermission("order:rob:edit")
    @Log(title = "自动抢单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/batchEdit")
    public R<Void> batchEdit(@Validated(BatchGroup.class) @RequestBody DisRobBo bo) {
        bo.setUpdateBy(LoginHelper.getUserId());
        return toAjax(disRobService.batchUpdateByBo(bo));
    }

    /**
     *  获取抢单覆盖范围
     * @return
     */
    @SaCheckPermission("order:rob:cover")
    @GetMapping("/cover")
    public R<List<DisRob>> cover(CoverQuery coverQuery) {
        return R.ok(disRobService.cover(coverQuery));
    }

    /**
     * 删除自动抢单
     *
     * @param ids 主键串
     */
    @SaCheckPermission("order:rob:remove")
    @Log(title = "自动抢单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(disRobService.deleteWithValidByIds(List.of(ids), true));
    }
}
