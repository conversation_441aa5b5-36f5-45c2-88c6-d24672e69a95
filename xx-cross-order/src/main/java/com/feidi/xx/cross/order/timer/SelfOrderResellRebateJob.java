package com.feidi.xx.cross.order.timer;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.mail.utils.MailUtils;
import com.feidi.xx.cross.common.constant.order.OrderConstants;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.service.IOrdOrderRebateService;
import com.feidi.xx.system.api.RemoteConfigService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 自营平台转卖订单结算定时器
 *
 * <AUTHOR>
 * @date 2024/10/21
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SelfOrderResellRebateJob {

    private final OrdOrderMapper baseMapper;
    private final IOrdOrderRebateService ordOrderRebateService;
    @DubboReference
    private final RemoteConfigService remoteConfigService;

    /**
     * 订单结算定时器
     */
    @XxlJob("selfOrderResellRebateJob")
    public void selfOrderResellRebateJob() {
        if (log.isInfoEnabled()) {
            log.info("============== 自营平台-转卖订单结算定时器开始执行 ==============");
        }
        // 获取返利周期时间（单位天）
        String period = Optional.ofNullable(remoteConfigService.selectValueByKey(OrderConstants.CX_REBATE_SELF_PERIOD)).orElse(OrderConstants.CX_REBATE_SELF_PERIOD_DEFAULT);
        if (log.isInfoEnabled()) {
            log.info("自营平台-转卖订单返利周期时间：【{}】", period);
        }
        // 获取可以返利的订单
        Date finishTime = DateUtil.endOfDay(DateUtil.offsetDay(new Date(), -Integer.parseInt(period)));
        if (log.isInfoEnabled()) {
            log.info("自营平台-转卖订单返利结算 - : 结算截止时间：【{}】", finishTime);
        }
        List<OrdOrder> cxOrders = baseMapper.queryRebateOrderResell(finishTime, Collections.singletonList(PlatformCodeEnum.SELF.getCode()));
        if (ObjectUtils.isNotNull(cxOrders)) {
            // 按照司机分组，批量结算订单
            Map<Long, List<OrdOrder>> driverMap = cxOrders.parallelStream().collect(Collectors.groupingBy(OrdOrder::getResellDriverId));
            driverMap.entrySet().parallelStream().forEach(e -> {
                try {
                    ordOrderRebateService.batchRebateForResell(e.getKey(), e.getValue());
                } catch (Exception ex) {
                    sendEmail(StrUtil.format("自营平台-转卖订单-司机{}结算异常", e.getKey()), e.getValue().size());
                    log.error(ex.getMessage(), ex);
                    log.error("自营平台-转卖订单返利结算失败 - 司机ID: 【{}】", e.getKey());
                }
            });
        }
        if (log.isInfoEnabled()) {
            log.info("============== 自营平台-转卖订单结算定时器执行结束 ==============");
        }
    }

    private void sendEmail(String title, Object content) {
        try {
            String jobParam = XxlJobHelper.getJobParam();
            List<String> emails = StrUtil.split(jobParam, StrUtil.COMMA);
            MailUtils.sendText(emails, title, JSONUtil.toJsonPrettyStr(content));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

}
