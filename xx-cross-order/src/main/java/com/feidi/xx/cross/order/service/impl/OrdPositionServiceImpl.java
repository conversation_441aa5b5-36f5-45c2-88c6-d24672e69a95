package com.feidi.xx.cross.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.order.enums.OrdCacheKeyEnum;
import com.feidi.xx.cross.order.api.domain.vo.RemotePositionVo;
import com.feidi.xx.cross.order.domain.OrdPosition;
import com.feidi.xx.cross.order.domain.bo.OrdPositionBo;
import com.feidi.xx.cross.order.domain.vo.OrdPositionVo;
import com.feidi.xx.cross.order.mapper.OrdPositionMapper;
import com.feidi.xx.cross.order.service.IOrdPositionService;
import com.feidi.xx.system.api.RemoteDistrictService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 订单位置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@RequiredArgsConstructor
@Service
public class OrdPositionServiceImpl implements IOrdPositionService {

    private final OrdPositionMapper baseMapper;
    private final ScheduledExecutorService scheduledExecutorService;
    @DubboReference
    private final RemoteDistrictService remoteDistrictService;

    /**
     * 查询订单位置
     *
     * @param id 主键
     * @return 订单位置
     */
    @Override
    public OrdPositionVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询订单位置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单位置分页列表
     */
    @Override
    public TableDataInfo<OrdPositionVo> queryPageList(OrdPositionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrdPosition> lqw = buildQueryWrapper(bo);
        Page<OrdPositionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的订单位置列表
     *
     * @param bo 查询条件
     * @return 订单位置列表
     */
    @Override
    public List<OrdPositionVo> queryList(OrdPositionBo bo) {
        LambdaQueryWrapper<OrdPosition> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrdPosition> buildQueryWrapper(OrdPositionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrdPosition> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderId() != null, OrdPosition::getOrderId, bo.getOrderId());
        lqw.eq(bo.getProvinceId() != null, OrdPosition::getProvinceId, bo.getProvinceId());
        lqw.eq(bo.getCityId() != null, OrdPosition::getCityId, bo.getCityId());
        lqw.eq(StringUtils.isNotBlank(bo.getCityCode()), OrdPosition::getCityCode, bo.getCityCode());
        lqw.eq(StringUtils.isNotBlank(bo.getAdCode()), OrdPosition::getAdCode, bo.getAdCode());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), OrdPosition::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), OrdPosition::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增订单位置
     *
     * @param bo 订单位置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OrdPositionBo bo) {
        OrdPosition add = MapstructUtils.convert(bo, OrdPosition.class);
        validEntityBeforeSave(add);
        add.setStatus(StatusEnum.ENABLE.getCode());

        // 备注长度
        if (StringUtils.isNotBlank(add.getRemark()) && add.getRemark().length() > 500) {
            add.setRemark(null);
        }

        // 添加新地址
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());

            // 加到缓存中
            scheduledExecutorService.schedule(() -> addCache(add), 0, TimeUnit.SECONDS);
        }
        return flag;
    }

    /**
     * 批量新增订单位置
     *
     * @param bos 订单位置集合
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBos(List<OrdPositionBo> bos) {
        if (CollUtil.isEmpty(bos)) {
            return false;
        }
        bos.forEach(e -> {
            OrdPosition position = BeanUtils.copyProperties(e, OrdPosition.class);
            position.setStatus(StatusEnum.ENABLE.getCode());
            baseMapper.insert(position);
            scheduledExecutorService.schedule(() -> addCache(position), 0, TimeUnit.SECONDS);
        });
        return true;
    }

    /**
     * 修改订单位置
     *
     * @param bo 订单位置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OrdPositionBo bo) {
        OrdPosition update = MapstructUtils.convert(bo, OrdPosition.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrdPosition entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除订单位置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 获取订单id和类型获取订单位置
     *
     * @param orderId 订单id
     * @param type    类型
     * @return 订单位置
     */
    @Override
    public OrdPositionVo queryByOrderIdAndType(Long orderId, String type) {

        LambdaQueryWrapper<OrdPosition> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrdPosition::getOrderId, orderId)
                .eq(OrdPosition::getType, type)
                .eq(OrdPosition::getStatus, StatusEnum.ENABLE.getCode())
                .last(Constants.LIMIT_ONE);

        return baseMapper.selectVoOne(queryWrapper);
    }

    /**
     * 根据订单id获取订单位置
     *
     * @param orderIds 订单id集合
     * @return 订单位置集合
     */
    @Override
    public List<OrdPositionVo> queryByOrderIds(List<Long> orderIds) {
        LambdaQueryWrapper<OrdPosition> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrdPosition::getOrderId, orderIds)
                .eq(OrdPosition::getStatus, StatusEnum.ENABLE.getCode());

        return baseMapper.selectVoList(queryWrapper);
    }

    /**
     * 添加缓存
     *
     * @param position 订单位置
     */
    public void addCache(OrdPosition position) {
        if (ObjectUtils.isNull(position)) {
            return;
        }

        // 缓存KEY
        String cacheKey = OrdCacheKeyEnum.ORD_ORDER_TYPE_POSITION_INFO_KEY.create(position.getOrderId(), position.getType());

        RedisUtils.setCacheObject(cacheKey, BeanUtils.copyProperties(position, RemotePositionVo.class),
                OrdCacheKeyEnum.ORD_ORDER_TYPE_POSITION_INFO_KEY.getDuration());

    }
}
