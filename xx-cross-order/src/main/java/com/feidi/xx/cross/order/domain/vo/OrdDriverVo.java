package com.feidi.xx.cross.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.core.enums.SexEnum;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.cross.order.domain.OrdDriver;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 订单司机视图对象 ord_driver
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrdDriver.class)
public class OrdDriverVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private String tenantId;

    /**
     * 订单ID
     */
    @ExcelProperty(value = "订单ID")
    private Long orderId;

    /**
     * 代理商id
     */
    @ExcelProperty(value = "代理商id")
    private Long agentId;

    /**
     * 司机id
     */
    @ExcelProperty(value = "司机id")
    private Long driverId;

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号")
    private String driverPhone;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String driverName;

    /**
     * 头像
     */
    @ExcelProperty(value = "头像")
    private String driverAvatar;

    /**
     * 性别[SexEnum]
     */
    @Enum2Text(enumClass = SexEnum.class, fullName = "driverSexText")
    @ExcelProperty(value = "性别[SexEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "SexEnum")
    private String driverSex;

    /**
     * 身份证号
     */
    @ExcelProperty(value = "身份证号")
    private String driverCardNo;

    /**
     * 车牌号
     */
    @ExcelProperty(value = "车牌号")
    private String carNumber;

    /**
     * 品牌
     */
    @ExcelProperty(value = "品牌")
    private String carBrand;

    /**
     * 型号
     */
    @ExcelProperty(value = "型号")
    private String carModel;

    /**
     * 颜色
     */
    @ExcelProperty(value = "颜色")
    private String carColor;

    /**
     * 样式[CarStyleEnum]
     */
    @ExcelProperty(value = "样式[CarStyleEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "CarStyleEnum")
    private String carType;

    /**
     * 驱动方式[DriveTypeEnum]
     */
    @ExcelProperty(value = "驱动方式[DriveTypeEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "DriveTypeEnum")
    private String driveType;

    /**
     * 车辆识别码（车架号）
     */
    @ExcelProperty(value = "车辆识别码", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "车=架号")
    private String vin;

    /**
     * 发动机编号
     */
    @ExcelProperty(value = "发动机编号")
    private String engine;

    /**
     * 调度类型[DispatchTypeEnum]
     */
    @ExcelProperty(value = "调度类型[DispatchTypeEnum]")
    private String type;

    /**
     * 调度人类型[UserTypeEnum]
     */
    @ExcelProperty(value = "调度人类型[UserTypeEnum]")
    private String dispatchUserType;

    /**
     * 调度人ID
     */
    @ExcelProperty(value = "调度人ID")
    private Long dispatchUserId;

    /**
     * 调度状态[SuccessFailEnum]
     */
    @ExcelProperty(value = "调度状态[SuccessFailEnum]")
    private String dispatchStatus;

    /**
     * 调度失败类型[DispatchFailTypeEnum]
     */
    @ExcelProperty(value = "调度失败类型[DispatchFailTypeEnum]")
    private String dispatchFailType;

    /**
     * 调度备注
     */
    @ExcelProperty(value = "调度备注")
    private String dispatchRemark;

    /**
     * 状态[StatusEnum]
     */
    @ExcelProperty(value = "状态[StatusEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "StatusEnum")
    private String status;


}
