package com.feidi.xx.cross.order.timer;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.mail.utils.MailUtils;
import com.feidi.xx.cross.common.constant.order.OrderConstants;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.service.IOrdOrderService;
import com.feidi.xx.system.api.RemoteConfigService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 订单取消数据删除定时器
 *
 * <AUTHOR>
 * @date 2025/02/07
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderDataDeleteJob {

    private final OrdOrderMapper baseMapper;
    private final IOrdOrderService orderService;
    @DubboReference
    private final RemoteConfigService remoteConfigService;

    /**
     * 订单取消数据删除定时器
     */
    @XxlJob("orderDataDeleteJob")
    public void orderDataDeleteJob() {
        long startTime = DateUtils.getTimeStamps();
        if (log.isInfoEnabled()) {
            log.info("============== 订单取消数据删除定时器开始执行 ==============");
        }
        // 获取删除周期时间（单位天）
        String period = Optional.ofNullable(remoteConfigService.selectValueByKey(OrderConstants.CX_DELETE_CANCEL_PERIOD)).orElse(OrderConstants.CX_DELETE_CANCEL_PERIOD_DEFAULT);
        if (log.isInfoEnabled()) {
            log.info("订单取消数据删除 - : 删除周期：【{}】", period);
        }
        Date deleteTime = DateUtil.endOfDay(DateUtil.offsetDay(new Date(), -Integer.parseInt(period)));
        if (log.isInfoEnabled()) {
            log.info("订单取消数据删除 - : 截止时间：【{}】", deleteTime);
        }
        // 获取可以返利的订单
        List<Long> orderIds = baseMapper.queryCanDeleteOrders(DateUtils.date2String(deleteTime));
        log.info("订单取消数据删除 - 订单数量：【{}】", orderIds.size());
        if (ObjectUtils.isNotNull(orderIds)) {
            try {
                orderService.deleteCancelOrders(orderIds);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        }
        long endTime = DateUtils.getTimeStamps();
        if (log.isInfoEnabled()) {
            log.info("============== 订单取消数据删除 - 耗时：【{}】秒 ==============", (endTime - startTime));
            log.info("============== 订单取消数据删除定时器执行结束 ==============");
        }
    }
}
