package com.feidi.xx.cross.order.domain.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单收益处理BO
 *
 * <AUTHOR>
 * @date 2025/3/17
 */
@Data
@Accessors(chain = true)
public class OrdOrderProfitBo {

    /**
     * 强制更新订单收益
     */
    private Boolean forceUpdate;

    /**
     * 是否只是查询
     */
    private Boolean isQueryOnly;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 邀请的司机id
     */
    private Long inviteDriverId;

    /**
     * 邀请的代理商id
     */
    private Long inviteAgentId;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 代理商id
     */
    private Long agentId;


    private Long parentId;

    /**
     * 订单价格
     */
    private Long orderPrice;

    /**
     * 司机佣金比例
     */
    private BigDecimal driverRate;

    /**
     * 代理商佣金比例
     */
    private BigDecimal agentRate;

    /**
     * 预计出发时间
     */
    private Date earliestTime;

    /**
     * 下单类型
     */
    private String createModel;

    /**
     * 转卖司机ID
     */
    private Long resellDriverId;

    /**
     * 转卖后司机接单金额
     */
    private Long resellDriverPrice;
}
