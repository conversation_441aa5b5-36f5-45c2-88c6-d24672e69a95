package com.feidi.xx.cross.order.mapper;

import com.feidi.xx.cross.order.domain.OrdOperate;
import com.feidi.xx.cross.order.domain.vo.OrdOperateVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单操作Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface OrdOperateMapper extends BaseMapperPlus<OrdOperate, OrdOperateVo> {
    /**
     * 根据订单id删除订单操作
     *
     * @param orderIds
     */
    void deleteByOrderIds(@Param("orderIds") List<Long> orderIds);
}
