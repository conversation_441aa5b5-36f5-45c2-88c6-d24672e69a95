package com.feidi.xx.cross.order.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class OrdOrderCommentBo {
    /**
     * 订单id
     */
    @NotNull(message = "订单ID不能为空")
    private Long orderId;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 乘客id
     */
    private Long passengerId;
    /**
     * 评分 (1-5星)
     */
    @NotNull(message = "评分不能为空")
    private Long rating;

    /**
     * 是否匿名。0：否，1：是
     */
    @NotNull(message = "是否匿名不能为空")
    private Integer isAnonymous;

    /**
     * 评价内容
     */
    private String comment;

    /**
     * 选择的标签。
     */
    @NotEmpty(message = "标签不能为空")
    private List<String> tags;

}
