package com.feidi.xx.cross.order.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.order.domain.OrdDriverEvaluation;
import com.feidi.xx.cross.order.domain.vo.OrdDriverEvaluationVo;

/**
 * 司机-行程评价Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface OrdDriverEvaluationMapper extends BaseMapperPlus<OrdDriverEvaluation, OrdDriverEvaluationVo> {

    default OrdDriverEvaluation queryByDriverId(Long driverId) {
        return selectOne(Wrappers.<OrdDriverEvaluation>lambdaQuery().eq(OrdDriverEvaluation::getDriverId, driverId));
    }
}
