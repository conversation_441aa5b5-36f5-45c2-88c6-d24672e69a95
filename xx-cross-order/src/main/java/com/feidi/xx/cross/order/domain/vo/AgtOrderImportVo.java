package com.feidi.xx.cross.order.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.order.domain.OrdDriver;
import com.feidi.xx.cross.order.domain.bo.AgtOrderImportBo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AgtOrderImportVo extends AgtOrderImportBo{

    @ExcelProperty(value = "序号")
    public Integer rowIndex;

    @ExcelProperty(value = "异常")
    public String error;
}
