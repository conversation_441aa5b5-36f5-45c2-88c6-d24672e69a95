package com.feidi.xx.cross.order.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.order.domain.OrdOperate;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 订单操作业务对象 ord_operate
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrdOperate.class, reverseConvertGenerate = false)
public class OrdOperateBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 路径追踪id
     */
    @NotBlank(message = "路径追踪id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String traceId;

    /**
     * 操作人类型[UserTypeEnum]
     */
    private String userType;

    /**
     * 操作人ID
     */
    private Long userId;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userName;

    /**
     * 操作类型[OperateTypeEnum]
     */
    @NotBlank(message = "操作类型[OperateTypeEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String operateType;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 参数
     */
    private String paramsJson;

    /**
     * 结果
     */
    @NotBlank(message = "结果不能为空", groups = { AddGroup.class, EditGroup.class })
    private String responseJson;

    /**
     * 状态[SuccessFailEnum]
     */
    @NotBlank(message = "状态[SuccessFailEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;


}
