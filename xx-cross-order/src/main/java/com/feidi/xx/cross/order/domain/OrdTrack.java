package com.feidi.xx.cross.order.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 订单轨迹对象 ord_track
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ord_track")
public class OrdTrack extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 类型
     */
    private String type;

    /**
     * 轨迹数据
     */
    private String trackData;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
