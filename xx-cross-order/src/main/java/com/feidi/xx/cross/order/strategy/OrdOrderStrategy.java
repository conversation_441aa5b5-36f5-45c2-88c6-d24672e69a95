package com.feidi.xx.cross.order.strategy;

import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderBo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderCancelBo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderHandleBo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderPaymentBo;
import com.feidi.xx.cross.order.domain.OrdOrder;

/**
 * 订单策略
 *
 * <AUTHOR>
 * @date 2025/3/13
 */
public interface OrdOrderStrategy {

    /**
     * 获取策略类型
     *
     * @return
     */
    PlatformCodeEnum getStrategyType();

    /**
     * 下单
     *
     * @param remoteOrderBo 下单参数
     * @return 下单结果
     */
    OrdOrder postOrder(RemoteOrderBo remoteOrderBo);

    /**
     * 通知派单
     *
     * @param handleBo 通知派单参数
     * @return 通知派单结果
     */
    Boolean confirmNotify(RemoteOrderHandleBo handleBo);

    /**
     * 订单取消费
     *
     * @param handleBo 订单取消费参数
     * @return 订单取消费
     */
    Long orderCancelFee(RemoteOrderHandleBo handleBo);

    /**
     * 取消订单
     *
     * @param handleBo 取消订单参数
     * @return 取消订单结果
     */
    Boolean cancelOrder(RemoteOrderCancelBo handleBo);

    /**
     * 乘客确认上车
     *
     * @param handleBo 确认上车参数
     * @return 确认上车结果
     */
    Boolean tripStart(RemoteOrderHandleBo handleBo);

    /**
     * 乘客确认下车
     *
     * @param handleBo 确认下车参数
     * @return 确认下车结果
     */
    Boolean tripEnd(RemoteOrderHandleBo handleBo);

}
