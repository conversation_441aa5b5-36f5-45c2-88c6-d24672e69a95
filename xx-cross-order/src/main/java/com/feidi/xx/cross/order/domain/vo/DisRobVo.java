package com.feidi.xx.cross.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.cross.order.domain.DisRob;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 自动抢单视图对象 dis_rob
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = DisRob.class)
public class DisRobVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private String tenantId;

    /**
     * 编号
     */
    @ExcelProperty(value = "编号")
    private String robNo;

    /**
     * 代理ID
     */
    @ExcelProperty(value = "代理ID")
    private Long agentId;

    /**
     * 代理名称
     */
    @ExcelProperty(value = "代理名称")
    private String agentName;

    /**
     * 线路ID
     */
    @ExcelProperty(value = "线路ID")
    private Long lineId;

    /**
     * 线路名称
     */
    private String lineName;

    /**
     * 司机ID
     */
    @ExcelProperty(value = "司机ID")
    private Long driverId;

    /**
     * 司机名称
     */
    @ExcelProperty(value = "司机名称")
    private String driverName;

    /**
     * 开始省ID
     */
    @ExcelProperty(value = "开始省ID")
    private Long startProvinceId;

    /**
     * 开始省
     */
    private String startProvince;

    /**
     * 开始城市ID
     */
    @ExcelProperty(value = "开始城市ID")
    private Long startCityId;

    /**
     * 开始城市
     */
    private String startCity;

    /**
     * 开始城市编码
     */
    @ExcelProperty(value = "开始城市编码")
    private String startCityCode;

    /**
     * 开始区域ID
     */
    @ExcelProperty(value = "开始区域ID")
    private Long startDistrictId;

    /**
     * 开始区域
     */
    private String startDistrict;

    /**
     * 开始区域编码
     */
    @ExcelProperty(value = "开始区域编码")
    private String startAdCode;

    /**
     * 开始地址
     */
    @ExcelProperty(value = "开始地址")
    private String startAddress;

    /**
     * 开始经度
     */
    @ExcelProperty(value = "开始经度")
    private String startLongitude;

    /**
     * 开始纬度
     */
    @ExcelProperty(value = "开始纬度")
    private String startLatitude;

    /**
     * 开始半径
     */
    @ExcelProperty(value = "开始半径")
    private Long startRadius;

    /**
     * 结束省ID
     */
    @ExcelProperty(value = "结束省ID")
    private Long endProvinceId;

    /**
     * 结束省
     */
    private String endProvince;

    /**
     * 结束城市ID
     */
    @ExcelProperty(value = "结束城市ID")
    private Long endCityId;

    /**
     * 结束城市
     */
    private String endCity;

    /**
     * 结束城市编码
     */
    @ExcelProperty(value = "结束城市编码")
    private String endCityCode;

    /**
     * 结束区域ID
     */
    @ExcelProperty(value = "结束区域ID")
    private Long endDistrictId;

    /**
     * 结束区域
     */
    private String endDistrict;

    /**
     * 结束区域编码
     */
    @ExcelProperty(value = "结束区域编码")
    private String endAdCode;

    /**
     * 结束地址
     */
    @ExcelProperty(value = "结束地址")
    private String endAddress;

    /**
     * 结束经度
     */
    @ExcelProperty(value = "结束经度")
    private String endLongitude;

    /**
     * 结束纬度
     */
    @ExcelProperty(value = "结束纬度")
    private String endLatitude;

    /**
     * 结束半径
     */
    @ExcelProperty(value = "结束半径")
    private Long endRadius;

    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间")
    private String endTime;

    /**
     * 排队时间
     */
    @ExcelProperty(value = "排队时间")
    private String sortTime;

    /**
     * 等待时间
     */
    @ExcelProperty(value = "等待时间")
    private Long maxWaitDuration;

    /**
     * 座位数
     */
    @ExcelProperty(value = "座位数")
    private Integer seat;

    /**
     * 剩余座位数
     */
    @ExcelProperty(value = "剩余座位数")
    private Integer surplusSeat;

    /**
     * 抢单产品编码
     */
    @ExcelProperty(value = "抢单产品编码")
    private String robProduct;

    /**
     * 用户类型[UserTypeEnum]
     */
    @ExcelProperty(value = "用户类型[UserTypeEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "UserTypeEnum")
    private String userType;

    /**
     * 抢单类型[RobTypeEnum]
     */
    @ExcelProperty(value = "抢单类型[RobTypeEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "RobTypeEnum")
    private String type;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 状态[StatusEnum]
     */
    @ExcelProperty(value = "状态[StatusEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "StatusEnum")
    private String status;

    /**
     * 方案[RobPlanEnum]
     */
    @ExcelProperty(value = "方案[RobPlanEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "RobPlanEnum")
    private String plan;

    /**
     * 司机IDS（随机）
     */
    @ExcelProperty(value = "司机IDS", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "随=机")
    private String driverJson;

    /**
     * 续期天数
     */
    @ExcelProperty(value = "续期天数")
    private Long renewal;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
