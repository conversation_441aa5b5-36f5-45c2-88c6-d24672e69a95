package com.feidi.xx.cross.order.controller.common;

import cn.dev33.satoken.annotation.SaIgnore;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.order.service.IComCallbackService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/***
 * 公共 - 回调方法控制器
 *
 * <AUTHOR>
 * @date 2024/9/30
 **/
@RequiredArgsConstructor
@RestController
@SaIgnore
@RequestMapping("/callback")
public class ComCallbackController extends BaseController {

    private final IComCallbackService comCallbackService;

    /**
     * 保险通知投保回调接口
     *
     * @param xmlStr
     * @return
     */
    @RepeatSubmit()
    @PostMapping("/insureResult")
    public String insureResult(@RequestBody String xmlStr) throws Exception {
        return comCallbackService.insureResult(xmlStr);
    }

}
