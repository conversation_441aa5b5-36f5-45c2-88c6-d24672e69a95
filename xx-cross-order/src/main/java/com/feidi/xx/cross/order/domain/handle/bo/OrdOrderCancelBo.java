package com.feidi.xx.cross.order.domain.handle.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单取消参数
 *
 * <AUTHOR>
 * @date 2025/3/22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrdOrderCancelBo extends OrdOrderHandleBaseBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 取消类型
     */
    private String cancelType;

    /**
     * 取消备注
     */
    private String cancelRemark;

    /**
     * 是否客诉
     */
    private String complain;

    /**
     * 客诉时间
     */
    private Date complainTime;

    /**
     * 客诉类型
     */
    private String complainType;

    /**
     * 客诉备注
     */
    private String complainRemark;

    /**
     * 投诉扣款金额
     */
    private Long complainPrice;

}
