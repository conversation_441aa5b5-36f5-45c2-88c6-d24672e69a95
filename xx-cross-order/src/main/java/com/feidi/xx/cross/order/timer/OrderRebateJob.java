package com.feidi.xx.cross.order.timer;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.mail.utils.MailUtils;
import com.feidi.xx.cross.common.constant.order.OrderConstants;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.service.IOrdOrderRebateService;
import com.feidi.xx.system.api.RemoteConfigService;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 订单结算定时器
 *
 * <AUTHOR>
 * @date 2024/10/21
 */
@Slf4j
//@Component
@RequiredArgsConstructor
public class OrderRebateJob {

    private final OrdOrderMapper baseMapper;
    private final IOrdOrderRebateService ordOrderRebateService;
    @DubboReference
    private final RemoteConfigService remoteConfigService;

    /**
     * 订单结算定时器
     */
    //@XxlJob("orderRebateJob")
    public void orderRebateJob() {
        if (log.isInfoEnabled()) {
            log.info("============== 订单结算定时器开始执行 ==============");
        }
        // 获取返利周期时间（单位天）
        String period = Optional.ofNullable(remoteConfigService.selectValueByKey(OrderConstants.CX_REBATE_PERIOD)).orElse(OrderConstants.CX_REBATE_PERIOD_DEFAULT);
        // 获取可以返利的订单
        Date finishTime = DateUtil.endOfDay(DateUtil.offsetDay(new Date(), -Integer.parseInt(period)));
        if (log.isInfoEnabled()) {
            log.info("订单返利结算 - : 结算截止时间：【{}】", finishTime);
        }
        List<OrdOrder> cxOrders = baseMapper.queryRebateOrder(finishTime, null);
        if (ObjectUtils.isNotNull(cxOrders)) {
            // 按照司机分组，批量结算订单
            Map<Long, List<OrdOrder>> driverMap = cxOrders.parallelStream().collect(Collectors.groupingBy(OrdOrder::getDriverId));
            driverMap.entrySet().parallelStream().forEach(e -> {
                try {
                    ordOrderRebateService.batchRebate(e.getKey(), e.getValue());
                } catch (Exception ex) {
                    sendEmail(StrUtil.format("司机{}结算异常", e.getKey()), e.getValue().size());
                    log.error(ex.getMessage(), ex);
                    log.error("订单返利结算失败 - 司机ID: 【{}】", e.getKey());
                }
            });
        }
        if (log.isInfoEnabled()) {
            log.info("============== 订单结算定时器执行结束 ==============");
        }
    }

    private void sendEmail(String title, Object content) {
        try {
            String jobParam = XxlJobHelper.getJobParam();
            List<String> emails = StrUtil.split(jobParam, StrUtil.COMMA);
            MailUtils.sendText(emails, title, JSONUtil.toJsonPrettyStr(content));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

}
