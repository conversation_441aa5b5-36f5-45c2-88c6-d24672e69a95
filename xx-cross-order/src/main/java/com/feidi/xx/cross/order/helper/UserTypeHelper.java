package com.feidi.xx.cross.order.helper;

import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import lombok.experimental.UtilityClass;

import java.util.Arrays;

@UtilityClass
public class UserTypeHelper {

    public static void member(Runnable handle) {
        run(handle, UserTypeEnum.PASSENGER_USER);
    }

    public static void driver(Runnable handle) {
        run(handle, UserTypeEnum.DRIVER_USER);
    }

    public static void sys(Runnable handle) {
        run(handle, UserTypeEnum.SYS_USER);
    }

    public static void agent(Runnable handle) {
        run(handle, UserTypeEnum.AGENT_USER);
    }

    /**
     * 第三方执行
     * @param handle
     */
    public static void third(Runnable handle) {
        run(handle, UserTypeEnum.MT, UserTypeEnum.HBK, UserTypeEnum.TENCENT);
    }

    private static void run(Runnable handle, UserTypeEnum... userType) {
        if (Arrays.asList(userType).contains(check())) {
            handle.run();
        }
    }

    private static UserTypeEnum check() {
        UserTypeEnum userType = LoginHelper.getUserType();
        if (userType == null) {
            throw new ServiceException("未获取到用户类型，无法执行");
        }
        return userType;
    }
}
