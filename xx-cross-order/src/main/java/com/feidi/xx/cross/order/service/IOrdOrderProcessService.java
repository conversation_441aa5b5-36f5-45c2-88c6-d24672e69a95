package com.feidi.xx.cross.order.service;

import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderCancelBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderDispatchBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderHandleBo;

/**
 * 订单流程Service接口
 *
 * <AUTHOR>
 * @date 2025/3/18
 */
public interface IOrdOrderProcessService {

    /**
     * 订单调度
     * 司机抢单，自动抢单（司机、代理），派单、改派（合作商、总后台）
     *
     * @param dispatchBo 调度参数
     * @return 是否调度成功
     */
    Boolean dispatchOrder(OrdOrderDispatchBo dispatchBo);

    /**
     * 订单操作
     * 状态 【3司机出发接乘客；4达到出发地】
     *
     * @param handleBo
     * @return
     */
    Boolean handleOrder(OrdOrderHandleBo handleBo);

    /**
     * 行程开始
     *
     * @param bo 行程开始参数
     * @return 行程开始结果
     */
    Boolean tripStart(OrdOrderHandleBo bo);

    /**
     * 行程结束
     *
     * @param bo 行程结束参数
     * @return 行程结束结果
     */
    Boolean tripEnd(OrdOrderHandleBo bo);

    /**
     * 取消订单
     *
     * @param bo 取消订单参数
     * @return 取消订单结果
     */
    Boolean cancelOrder(OrdOrderCancelBo bo);

    /**
     * 获取虚拟电话
     *
     * @param orderId 订单id
     * @param platformNo 平台编号
     * @return 虚拟电话
     */
    String getVirtualPhone(Long orderId, String platformNo);
}
