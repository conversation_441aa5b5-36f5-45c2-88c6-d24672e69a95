package com.feidi.xx.cross.order.controller.admin;

import com.feidi.xx.cross.market.api.RemoteInviteConfigService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class TestController {
    @DubboReference
    private RemoteInviteConfigService remoteInviteConfigService;

    @GetMapping("/testOrd")
    public void test() {
        remoteInviteConfigService.updateAgentName(1951553745399570434L, "11试代理商杭州测试代理商杭州测杭州测杭州测试代理商试杭州测试代理商杭州测试代理商杭州测试代理商杭州测试代理商代理商杭州");
    }

}
