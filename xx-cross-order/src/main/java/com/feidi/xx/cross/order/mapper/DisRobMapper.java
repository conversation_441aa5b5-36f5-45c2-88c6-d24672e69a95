package com.feidi.xx.cross.order.mapper;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.order.domain.DisRob;
import com.feidi.xx.cross.order.domain.bo.rob.DisRobBo;
import com.feidi.xx.cross.order.domain.bo.rob.DisRobQueryBo;
import com.feidi.xx.cross.order.domain.vo.DisRobVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 自动抢单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface DisRobMapper extends BaseMapperPlus<DisRob, DisRobVo> {

    default int batchUpdate(DisRobBo bo, Collection<Long> ids) {
        return update(null, Wrappers.<DisRob>lambdaUpdate()
                .set(DisRob::getSortTime, DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN))
                .set(DisRob::getStartTime, bo.getStartTime())
                .set(DisRob::getEndTime, bo.getEndTime())
                .set(DisRob::getStatus, StatusEnum.ENABLE.getCode())
                .set(DisRob::getSurplusSeat, 4)
                .in(DisRob::getId, ids)
                .eq(bo.getAgentId() != null, DisRob::getAgentId, bo.getAgentId())
                .set(DisRob::getUpdateTime, new Date())
                .set(DisRob::getUpdateBy, bo.getUpdateBy())
        );
    }

    /**
     * 获取最近5条抢单，包括删除的
     *
     * @param driverId 司机id
     * @return
     */
    List<DisRob> selectHistory(@Param("driverId") Long driverId);

    List<DisRob> selectDriverRob(@Param("disRobQueryBo") DisRobQueryBo disRobQueryBo);

    List<DisRob> selectAgentRob(@Param("disRobQueryBo") DisRobQueryBo disRobQueryBo);

    /**
     *  加座位数
     * @param id
     * @param seat
     */
    @Update(" UPDATE `dis_rob` SET `surplus_seat` = `surplus_seat` - #{seat} WHERE `id` = #{id}")
    void subSurplusSeat(@Param("id") Long id, @Param("seat") Integer seat);
}
