package com.feidi.xx.cross.order.domain.bo.rob;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 枪弹范围覆盖查询条件
 *
 * <AUTHOR>
 * @date 2025-03-06
 */
@Data
public class CoverQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 线路ID
     */
    private Long lineId;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 出发城市 - 起点城市code
     */
    private String startCityCode;

    /**
     * 抢单类型[RobTypeEnum]
     */
    private String type;

    /**
     * 方案
     */
    private String plan;

    /**
     * 开始时间 - 默认当天开始时间
     */
    private String startTime;

    /**
     * 结束时间 - 默认当天结束时间
     */
    private String endTime;

}
