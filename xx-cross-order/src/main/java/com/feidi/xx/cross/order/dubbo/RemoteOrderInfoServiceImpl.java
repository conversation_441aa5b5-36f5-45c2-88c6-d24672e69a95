package com.feidi.xx.cross.order.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.order.api.RemoteOrderInfoService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderInfoVo;
import com.feidi.xx.cross.order.domain.OrdOrderInfo;
import com.feidi.xx.cross.order.mapper.OrdOrderInfoMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 订单关联信息服务dubbo服务实现类
 *
 * <AUTHOR>
 * @date 2025/3/17
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteOrderInfoServiceImpl implements RemoteOrderInfoService {

    private final OrdOrderInfoMapper ordOrderInfoMapper;

    /**
     * 根据订单id查询订单关联信息
     *
     * @param orderId 订单id
     * @return 订单关联信息
     */
    @Override
    public RemoteOrderInfoVo queryByOrderId(Long orderId) {
        LambdaQueryWrapper<OrdOrderInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrdOrderInfo::getOrderId, orderId)
                .orderByDesc(OrdOrderInfo::getCreateTime)
                .last(Constants.LIMIT_ONE);
        OrdOrderInfo ordOrderInfo = ordOrderInfoMapper.selectOne(queryWrapper);

        return BeanUtils.copyProperties(ordOrderInfo, RemoteOrderInfoVo.class);
    }

    /**
     * 根据订单id查询订单关联信息
     *
     * @param orderIds 订单id
     * @return
     */
    @Override
    public List<RemoteOrderInfoVo> queryByOrderIds(List<Long> orderIds) {
        if (CollUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<OrdOrderInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrdOrderInfo::getOrderId, orderIds);
        List<OrdOrderInfo> ordOrderInfos = ordOrderInfoMapper.selectList(queryWrapper);

        return BeanUtils.copyToList(ordOrderInfos, RemoteOrderInfoVo.class);
    }

    /**
     * 根据
     * @param orderId
     * @param insureCode
     * @param isInsure
     * @return
     */
    @Override
    public Boolean updateOrderInsure(Long orderId, String insureCode, String isInsure) {
        LambdaUpdateWrapper<OrdOrderInfo> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(OrdOrderInfo::getOrderId, orderId)
                .set(OrdOrderInfo::getInsureCode, insureCode)
                .set(Objects.equals(isInsure, IsYesEnum.YES.getCode()), OrdOrderInfo::getInsureTime, DateUtils.getNowDate())
                .set(Objects.equals(isInsure, IsYesEnum.NO.getCode()), OrdOrderInfo::getCancelInsureTime, DateUtils.getNowDate())
                .set(OrdOrderInfo::getUpdateTime, new Date());
       return ordOrderInfoMapper.update(lambdaUpdateWrapper) > 0;
    }
}
