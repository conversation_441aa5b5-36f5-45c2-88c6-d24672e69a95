package com.feidi.xx.cross.order.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.order.api.domain.RemoteOrderDriverService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDriverVo;
import com.feidi.xx.cross.order.domain.OrdDriver;
import com.feidi.xx.cross.order.mapper.OrdDriverMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/3/17
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteOrderDriverServiceImpl implements RemoteOrderDriverService {

    private final OrdDriverMapper ordDriverMapper;

    /**
     * 根据订单id查询订单司机信息
     *
     * @param orderId 订单id
     * @return 订单司机信息
     */
    @Override
    public RemoteOrderDriverVo queryByOrderId(Long orderId) {
        LambdaQueryWrapper<OrdDriver> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrdDriver::getOrderId, orderId)
                .orderByDesc(OrdDriver::getCreateTime)
                .last(Constants.LIMIT_ONE);
        OrdDriver ordDriver = ordDriverMapper.selectOne(queryWrapper);

        return BeanUtils.copyProperties(ordDriver, RemoteOrderDriverVo.class);
    }

    /**
     * 根据订单id获取订单调度司机信息
     *
     * @param orderId 订单id
     * @return 订单调度司机信息
     */
    @Override
    public RemoteOrderDriverVo queryDispatchByOrderId(Long orderId) {
        LambdaQueryWrapper<OrdDriver> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrdDriver::getOrderId, orderId)
                .orderByAsc(OrdDriver::getCreateTime)
                .last(Constants.LIMIT_ONE);
        OrdDriver ordDriver = ordDriverMapper.selectOne(queryWrapper);

        return BeanUtils.copyProperties(ordDriver, RemoteOrderDriverVo.class);
    }
}
