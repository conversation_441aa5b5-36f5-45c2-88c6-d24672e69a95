package com.feidi.xx.cross.order.chain.operate;


import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderHandleBo;
import com.feidi.xx.cross.order.chain.base.OrderBaseChainContext;
import com.feidi.xx.cross.order.domain.OrdOrder;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 操作上下文
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderOperateChainContext extends OrderBaseChainContext {

    /**
     * 下一个状态 【3司机出发接乘客；4达到出发地】
     */
    private String status;

    /**
     * 乘客电话尾号
     */
    private String phoneEnd;

    /**
     * 处理对象
     */
    private OrdOrder order;

}
