package com.feidi.xx.cross.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.cross.order.domain.OrdOrderInfo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 订单信息视图对象 ord_order_info
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrdOrderInfo.class)
public class OrdOrderInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户id")
    private String tenantId;

    /**
     * 订单ID
     */
    @ExcelProperty(value = "订单ID")
    private Long orderId;

    /**
     * 司机单号
     */
    @ExcelProperty(value = "司机单号")
    private String driverNo;

    /**
     * 基础金额
     */
    @ExcelProperty(value = "基础金额")
    private Long basePrice;

    /**
     * 附加费金额
     */
    @ExcelProperty(value = "附加费金额")
    private Long addPrice;

    /**
     * 技术服务费
     */
    @ExcelProperty(value = "技术服务费")
    private Long serviceFee;

    /**
     * 取消费
     */
    @ExcelProperty(value = "取消费")
    private Long cancelFee;

    /**
     * 投诉扣款金额
     */
    @ExcelProperty(value = "投诉扣款金额")
    private Long complainPrice;

    /**
     * 司乘虚拟号
     */
    @ExcelProperty(value = "司乘虚拟号")
    private String virtualPhone;

    /**
     * 调度虚拟号
     */
    @ExcelProperty(value = "调度虚拟号")
    private String virtualDispatch;

    /**
     * 乘客电话尾号
     */
    @ExcelProperty(value = "乘客电话尾号")
    private String phoneEnd;

    /**
     * 服务时长
     */
    @ExcelProperty(value = "服务时长")
    private String duration;

    /**
     * 取消人ID
     */
    @ExcelProperty(value = "取消人ID")
    private String cancelUserId;

    /**
     * 取消人
     */
    @ExcelProperty(value = "取消人")
    private String cancelUserName;

    /**
     * 取消备注
     */
    @ExcelProperty(value = "取消备注")
    private String cancelRemark;

    /**
     * 客诉时间
     */
    @ExcelProperty(value = "客诉时间")
    private Date complainTime;

    /**
     * 客诉类型[ComplainTypeEnum]
     */
    @ExcelProperty(value = "客诉类型[ComplainTypeEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ComplainTypeEnum")
    private String complainType;

    /**
     * 客诉备注
     */
    @ExcelProperty(value = "客诉备注")
    private String complainRemark;

    /**
     * 保险平台[InsurePlatformEnum]
     */
    @ExcelProperty(value = "保险平台[InsurePlatformEnum]", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "InsurePlatformEnum")
    private String insurePlatform;

    /**
     * 保险编码
     */
    @ExcelProperty(value = "保险编码")
    private String insureCode;

    /**
     * 投保时间
     */
    @ExcelProperty(value = "投保时间")
    private Date insureTime;

    /**
     * 取消投保时间
     */
    @ExcelProperty(value = "取消投保时间")
    private Date cancelInsureTime;

    /**
     * 取消投保原因[cancelInsureReasonEnum]
     */
    @ExcelProperty(value = "取消投保原因[cancelInsureReasonEnum]")
    private String cancelInsureReason;

    /**
     * 开始经度
     */
    @ExcelProperty(value = "开始经度")
    private Double startLongitude;

    /**
     * 开始纬度
     */
    @ExcelProperty(value = "开始纬度")
    private Double startLatitude;

    /**
     * 结束经度
     */
    @ExcelProperty(value = "结束经度")
    private Double endLongitude;

    /**
     * 结束纬度
     */
    @ExcelProperty(value = "结束纬度")
    private Double endLatitude;

    /**
     * 乘客详情
     */
    @ExcelProperty(value = "乘客详情")
    private String passengerDetail;

    /**
     * 资金流向[OrderFlowTypeEnum]
     */
    @ExcelProperty(value = "资金流向[OrderFlowTypeEnum]")
    private String flow;


    /**
     * 是否已评价
     * 0:未评价
     * 1:已评价
     */
    private Integer isRated;
}
