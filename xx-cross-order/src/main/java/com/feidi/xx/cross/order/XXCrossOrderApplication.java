package com.feidi.xx.cross.order;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 城际模块
 *
 * <AUTHOR>
 */
@EnableAsync
@EnableDubbo
@MapperScan("com.feidi.xx.**.mapper")
@SpringBootApplication(scanBasePackages = {"com.feidi.xx.**"})
public class XXCrossOrderApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(XXCrossOrderApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  订单模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
