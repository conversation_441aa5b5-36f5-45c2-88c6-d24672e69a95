package com.feidi.xx.cross.order.domain.update;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.cross.common.utils.ExceptionUtil;
import com.feidi.xx.cross.order.domain.OrdOrder;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.springframework.util.Assert;

import java.util.Date;

/**
 * 更新用DTO
 */
@Data
public class UpdateDTO {

    protected Long id;

    protected Integer oldVersion;

    protected Integer newVersion;

    protected Long updateBy;

    protected Date updateTime;

    /**
     * 更新order对象
     * @param oldObj
     */
    public void update(OrdOrder oldObj) {
        String msg = "更新类必须拥有io.github.linpeilie.annotations.AutoMapper注解并且声明target为OrdOrder.class";
        ExceptionUtil.throwEx(() -> {
            MapstructUtils.convert(this, oldObj);
        }, msg);
    }

}
