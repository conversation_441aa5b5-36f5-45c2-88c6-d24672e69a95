package com.feidi.xx.cross.order.timer;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.cross.common.enums.order.InsureStatusEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderHandleBaseBo;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.service.IOrdOrderInsureService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 订单投保失败后补偿任务
 *
 * <AUTHOR>
 * @date 2025/7/11
 */

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderInsureCompensationJob {

    private final OrdOrderMapper ordOrderMapper;
    private final IOrdOrderInsureService ordOrderInsureService;
    private final ScheduledExecutorService scheduledExecutorService;

    @XxlJob("orderInsureCompensationJob")
    public void orderInsureCompensationJob() {
        long startTime = DateUtils.getTimeStamps();

        if (log.isInfoEnabled()) {
            log.info("============== 订单投保失败后补偿定时器开始执行 ==============");
        }

        List<String> orderStatuses = Stream.of(OrderStatusEnum.RECEIVE.getCode(), OrderStatusEnum.PICK.getCode(), OrderStatusEnum.PICK_START.getCode(), OrderStatusEnum.ING.getCode()).collect(Collectors.toList());
        LambdaQueryWrapper<OrdOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrdOrder::getInsureStatus, InsureStatusEnum.UNINSURED.getCode())
                .in(OrdOrder::getStatus, orderStatuses)
                .ge(OrdOrder::getCreateTime, DateUtil.offsetDay(DateUtils.getNowDate(), -30));

        List<Long> orderIds = ordOrderMapper.selectList(queryWrapper)
                        .stream().map(OrdOrder::getId).toList();

        log.info("订单投保失败后补偿投保订单id：【{}】", JsonUtils.toJsonString(orderIds));
        if (CollUtils.isNotEmpty(orderIds)) {
            scheduledExecutorService.schedule(() -> {
                for (Long orderId : orderIds) {
                    OrdOrderHandleBaseBo bo = new OrdOrderHandleBaseBo();
                    bo.setOrderId(orderId);
                    //bo.setUserId(LoginHelper.getUserId());
                    bo.setUserType(UserTypeEnum.AUTO_USER.getUserType());
                    bo.setTimeStamp(DateUtils.getUnixTimeStamps());
                    bo.setRemark("投保失败后补偿投保");
                    ordOrderInsureService.insure(bo);
                }
            }, 10, TimeUnit.SECONDS);
        }

        if (log.isInfoEnabled()) {
            log.info("============== 订单投保失败后补偿定时器开始执行 ==============");
        }
    }
}
