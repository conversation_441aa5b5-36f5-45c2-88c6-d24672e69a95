package com.feidi.xx.cross.order.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.order.domain.vo.OrdOrderExportVo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderMbrListVo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderMbrVo;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 订单对象 ord_order
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ord_order")
@AutoMappers(value = {
        @AutoMapper(target = OrdOrderMbrListVo.class, reverseConvertGenerate = false),
        @AutoMapper(target = OrdOrderMbrVo.class, reverseConvertGenerate = false),
        @AutoMapper(target = OrdOrderExportVo.class, reverseConvertGenerate = false),
})
public class OrdOrder extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 平台编号 
     */
    private String platformCode;

    /**
     * 平台单号
     */
    private String platformNo;

    /**
     * 出发城市
     */
    private String startCityCode;

    /**
     * 目的地城市
     */
    private String endCityCode;

    /**
     * 线路ID
     */
    private Long lineId;

    /**
     * 线路方向[StartEndEnum]
     */
    private String lineDirection;

    /**
     * 代理商id
     */
    private Long agentId;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 产品类型[ProductCodeEnum]
     */
    private String productCode;

    /**
     * 订单金额[基础金额+附加费金额-客诉扣款金额]
     */
    private Long orderPrice;

    /**
     * 实际支付金额
     */
    private Long payPrice;

    /**
     * 高速类型[HighwayTypeEnum]
     */
    private String highwayType;

    /**
     * 乘客数量
     */
    private Integer passengerNum;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 乘客真实手机号
     */
    private String passengerPhone;

    /**
     * 乘客备注
     */
    private String passengerRemark;

    /**
     * 订单状态[OrderStatusEnum]
     */
    private String status;

    /**
     * 第三方订单状态
     */
    private String thirdStatus;

    /**
     * 返利状态[RebateStatusEnum]
     */
    private String rebateStatus;

    /**
     * 返利时间
     */
    private Date rebateTime;

    /**
     * 支付状态[PaymentStatusEnum]
     */
    private String payStatus;

    /**
     * 支付方式[PaymentTypeEnum]
     */
    private String payMode;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 支付单号
     */
    private String payNo;

    /**
     * 预计出发时间
     */
    private Date earliestTime;

    /**
     * 最大等待时长
     */
    private Integer maxWaitDuration;

    /**
     * 预计最晚出发时间
     */
    private Date latestTime;

    /**
     * 接单时间
     */
    private Date receiveTime;

    /**
     * 接驾时间
     */
    private Date pickTime;

    /**
     * 到达乘客起点时间
     */
    private Date arrivalTime;

    /**
     * 行程开始时间
     */
    private Date tripStartTime;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 投保状态[InsureStatusEnum]
     */
    private String insureStatus;

    /**
     * 保险单号
     */
    private String insureNo;

    /**
     * 里程
     */
    private Integer mileage;

    /**
     * 预计时长
     */
    private Integer expectDuration;

    /**
     * 取消人类型[UserTypeEnum]
     */
    private String cancelUserType;

    /**
     * 取消时间
     */
    private Date cancelTime;

    /**
     * 取消类型[CancelTypeEnum]
     */
    private String cancelType;

    /**
     * 是否调度[IsYesEnum]
     */
    private String dispatch;

    /**
     * 调度时间
     */
    private Date dispatchTime;

    /**
     * 调度类型[DispatchTypeEnum]
     */
    private String dispatchType;

    /**
     * 调度状态[SuccessFailEnum]
     */
    private String dispatchStatus;

    /**
     * 是否预约[IsYesEnum]
     */
    private String due;

    /**
     * 是否被过滤[IsYesEnum]
     */
    private String filtered;

    /**
     * 是否客诉[IsYesEnum]
     */
    private String complain;

    /**
     * 标签（JSON）
     */
    private String label;

    /**
     * 完单标签（JSON）
     */
    private String finishLabel;

    /**
     * 邀请的代理商id
     */
    private Long inviteAgentId;
    /**
     * 邀请的司机id
     */
    private Long inviteDriverId;

    /**
     * 来源[SourceEnum]
     */
    private String source;

    /**
     * 下单类型[CreateModelEnum]
     */
    private String createModel;
    /**
     * 是否展示 [IsYesEnum]
     */
    private String showed;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 跳转码
     */
    private String code;

    /**
     * 转卖司机ID
     */
    private Long resellDriverId;

    /**
     * 转卖代理商ID
     */
    private Long resellAgentId;

    /**
     * 转卖后司机接单金额
     */
    private Long resellDriverPrice;

    /**
     * 订单转卖返利状态[RebateStatusEnum]
     */
    private String resellRebateStatus;

    /**
     * 订单转卖返利时间
     */
    private Date resellRebateTime;

    /**
     * 版本
     */
    @Version
    private Integer version;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    private Long estimateId;
}
