package com.feidi.xx.cross.order.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 订单信息对象 ord_order_info
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ord_order_info")
public class OrdOrderInfo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 优惠券发放id
     */
    private Long couponGrantId;

    /**
     * 优惠券额度
     */
    private Long couponGrantQuota;

    /**
     * 司机单号
     */
    private String driverNo;

    /**
     * 基础金额
     */
    private Long basePrice;

    /**
     * 附加费金额
     */
    private Long addPrice;

    /**
     * 技术服务费
     */
    private Long serviceFee;

    /**
     * 取消费
     */
    private Long cancelFee;

    /**
     * 投诉扣款金额
     */
    private Long complainPrice;

    /**
     * 司乘虚拟号
     */
    private String virtualPhone;

    /**
     * 调度虚拟号
     */
    private String virtualDispatch;

    /**
     * 乘客电话尾号
     */
    private String phoneEnd;

    /**
     * 服务时长
     */
    private String duration;

    /**
     * 取消人ID
     */
    private String cancelUserId;

    /**
     * 取消人
     */
    private String cancelUserName;

    /**
     * 取消备注
     */
    private String cancelRemark;

    /**
     * 客诉时间
     */
    private Date complainTime;

    /**
     * 客诉类型[ComplainTypeEnum]
     */
    private String complainType;

    /**
     * 客诉备注
     */
    private String complainRemark;

    /**
     * 保险平台[InsurePlatformEnum]
     */
    private String insurePlatform;

    /**
     * 保险编码
     */
    private String insureCode;

    /**
     * 投保时间
     */
    private Date insureTime;

    /**
     * 取消投保时间
     */
    private Date cancelInsureTime;

    /**
     * 取消投保原因[cancelInsureReasonEnum]
     */
    private String cancelInsureReason;

    /**
     * 开始经度
     */
    private String startLongitude;

    /**
     * 开始纬度
     */
    private String startLatitude;

    /**
     * 结束经度
     */
    private String endLongitude;

    /**
     * 结束纬度
     */
    private String endLatitude;

    /**
     * 资金流向[OrderFlowTypeEnum]
     */
    private String flow;

    /**
     * 乘客详情
     */
    private String passengerDetail;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


    /**
     * 是否已评价
     * 0:未评价
     * 1:已评价
     */
    private Integer isRated;
}
