package com.feidi.xx.cross.order.mapper;

import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.order.domain.OrdEvaluations;
import com.feidi.xx.cross.order.domain.vo.OrdEvaluationsVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 行程评价Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface OrdEvaluationsMapper extends BaseMapperPlus<OrdEvaluations, OrdEvaluationsVo> {
    @Select("SELECT SUM(rating) FROM ord_evaluations WHERE driver_id = #{driverId}")
    Long sumRatingByDriverId(@Param("driverId") Long driverId);
}
