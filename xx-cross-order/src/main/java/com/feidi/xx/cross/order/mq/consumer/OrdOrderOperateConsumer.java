
package com.feidi.xx.cross.order.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.idempotent.annotation.NoMQDuplicateConsume;
import com.feidi.xx.common.rocketmq.base.MessageWrapper;
import com.feidi.xx.cross.common.constant.order.OrderRocketMQConstant;
import com.feidi.xx.cross.common.mq.event.OrdOrderOperateEvent;
import com.feidi.xx.cross.order.api.RemoteOrderOperateService;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderOperateBo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

/**
 * 订单操作记录消息消费者
 *
 * <AUTHOR>
 * @date 2025/3/20
 */
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = OrderRocketMQConstant.XX_ORDER_OPERATE_TOPIC_KEY,
        consumerGroup = OrderRocketMQConstant.XX_ORDER_OPERATE_CG_KEY
)
@Slf4j(topic = "OrdOrderOperateConsumer")
public class OrdOrderOperateConsumer implements RocketMQListener<MessageWrapper<OrdOrderOperateEvent>> {

    @DubboReference
    private final RemoteOrderOperateService remoteOrderOperateService;

    @NoMQDuplicateConsume(
            keyPrefix = "global:xx-cross-order-operate:",
            key = "#messageWrapper.keys",
            keyTimeout = 600
    )
    @Override
    public void onMessage(MessageWrapper<OrdOrderOperateEvent> messageWrapper) {
        OrdOrderOperateEvent message = messageWrapper.getMessage();
        log.info("[消费者] 订单操作记录 - 执行消费逻辑，消息体：{}", JSON.toJSONString(messageWrapper));
        remoteOrderOperateService.addOrderOperate(BeanUtils.copyProperties(message, RemoteOrderOperateBo.class));
    }
}
