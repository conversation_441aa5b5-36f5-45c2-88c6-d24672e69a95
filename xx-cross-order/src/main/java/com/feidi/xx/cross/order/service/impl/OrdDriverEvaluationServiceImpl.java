package com.feidi.xx.cross.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.order.domain.OrdDriverEvaluation;
import com.feidi.xx.cross.order.domain.bo.OrdDriverEvaluationBo;
import com.feidi.xx.cross.order.domain.vo.OrdDriverEvaluationVo;
import com.feidi.xx.cross.order.mapper.OrdDriverEvaluationMapper;
import com.feidi.xx.cross.order.service.IOrdDriverEvaluationService;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 司机-行程评价Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@RequiredArgsConstructor
@Service
public class OrdDriverEvaluationServiceImpl implements IOrdDriverEvaluationService {

    private final OrdDriverEvaluationMapper baseMapper;

    private final RemoteAgentService remoteAgentService;

    /**
     * 查询司机-行程评价
     *
     * @param id 主键
     * @return 司机-行程评价
     */
    @Override
    public OrdDriverEvaluationVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询司机-行程评价列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机-行程评价分页列表
     */
    @Override
    public TableDataInfo<OrdDriverEvaluationVo> queryPageList(OrdDriverEvaluationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrdDriverEvaluation> lqw = buildQueryWrapper(bo);
        Page<OrdDriverEvaluationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        // 代理商信息
        Map<Long, RemoteAgentVo> agentInfoMap = remoteAgentService.getAllAgentInfo()
                .stream().collect(Collectors.toMap(RemoteAgentVo::getId, Function.identity()));
        result.getRecords().forEach(ordDriverEvaluationVo -> {
            RemoteAgentVo agentInfo = agentInfoMap.get(ordDriverEvaluationVo.getAgentId());
            if (agentInfo != null) {
                ordDriverEvaluationVo.setAgentName(agentInfo.getCompanyName());
            }
        });
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的司机-行程评价列表
     *
     * @param bo 查询条件
     * @return 司机-行程评价列表
     */
    @Override
    public List<OrdDriverEvaluationVo> queryList(OrdDriverEvaluationBo bo) {
        LambdaQueryWrapper<OrdDriverEvaluation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrdDriverEvaluation> buildQueryWrapper(OrdDriverEvaluationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrdDriverEvaluation> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getAgentId() != null, OrdDriverEvaluation::getAgentId, bo.getAgentId());
        lqw.eq(bo.getDriverId() != null, OrdDriverEvaluation::getDriverId, bo.getDriverId());
        lqw.eq(StringUtils.isNotBlank(bo.getDriverPhone()), OrdDriverEvaluation::getDriverPhone, bo.getDriverPhone());
        lqw.like(StringUtils.isNotBlank(bo.getDriverName()), OrdDriverEvaluation::getDriverName, bo.getDriverName());
        lqw.eq(bo.getTotalScore() != null, OrdDriverEvaluation::getTotalScore, bo.getTotalScore());
        lqw.eq(bo.getEvaluationCount() != null, OrdDriverEvaluation::getEvaluationCount, bo.getEvaluationCount());

        lqw.ge(bo.getStartCreateTime() != null, OrdDriverEvaluation::getCreateTime, bo.getStartCreateTime());
        lqw.le(bo.getEndCreateTime() != null, OrdDriverEvaluation::getCreateTime, bo.getEndCreateTime());
        lqw.ge(bo.getStartUpdateTime() != null, OrdDriverEvaluation::getUpdateTime, bo.getStartUpdateTime());
        lqw.le(bo.getEndUpdateTime() != null, OrdDriverEvaluation::getUpdateTime, bo.getEndUpdateTime());
        return lqw;
    }

    /**
     * 新增司机-行程评价
     *
     * @param bo 司机-行程评价
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OrdDriverEvaluationBo bo) {
        OrdDriverEvaluation add = MapstructUtils.convert(bo, OrdDriverEvaluation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改司机-行程评价
     *
     * @param bo 司机-行程评价
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OrdDriverEvaluationBo bo) {
        OrdDriverEvaluation update = MapstructUtils.convert(bo, OrdDriverEvaluation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrdDriverEvaluation entity) {

    }

    /**
     * 校验并批量删除司机-行程评价信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
