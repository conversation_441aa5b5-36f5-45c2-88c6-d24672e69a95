package com.feidi.xx.cross.order.service;

import com.feidi.xx.cross.finance.api.domain.bo.RemoteRebateBo;
import com.feidi.xx.cross.order.domain.OrdOrder;

import java.util.List;

/**
 * 订单结算Service接口
 *
 * <AUTHOR>
 * @date 2025-02-06
 */
public interface IOrdOrderRebateService {

    /**
     * 订单返利
     */
    void batchRebate(Long driverId, List<OrdOrder> orders);

    /**
     * 转卖订单返利
     */
    void batchRebateForResell(Long driverId, List<OrdOrder> orders);

    /**
     * 根据订单号批量结算订单
     *
     * @param orderNos
     * @return
     */
    Boolean orderRebateByOrderNos(String orderNos);

    /**
     * 创建订单结算对象
     *
     * @param order 订单信息
     * @return 订单结算信息
     */
    RemoteRebateBo createRemoteRebateVo(OrdOrder order);
}
