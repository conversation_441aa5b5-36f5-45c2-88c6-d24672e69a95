package com.feidi.xx.cross.order.dubbo;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.order.api.RemoteOrderRateService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderRateVo;
import com.feidi.xx.cross.order.domain.OrdRate;
import com.feidi.xx.cross.order.mapper.OrdRateMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/17
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteOrderRateServiceImpl implements RemoteOrderRateService {

    private final OrdRateMapper ordRateMapper;

    /**
     * 根据订单id查询分佣信息
     *
     * @param orderId 订单id
     * @return 订单分佣信息
     */
    @Override
    public List<RemoteOrderRateVo> queryByOrderId(Long orderId) {
        LambdaQueryWrapper<OrdRate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrdRate::getOrderId, orderId)
                .eq(OrdRate::getStatus, StatusEnum.ENABLE.getCode());
        List<OrdRate> ordRates = ordRateMapper.selectList(queryWrapper);

        return BeanUtils.copyToList(ordRates, RemoteOrderRateVo.class);
    }

    /**
     * 根据订单id查询分佣信息
     *
     * @param orderIds 订单id集合
     * @return 订单分佣信息
     */
    @Override
    public List<RemoteOrderRateVo> queryByOrderIds(List<Long> orderIds) {
        if (CollUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrdRate> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(OrdRate::getOrderId, orderIds)
                .eq(OrdRate::getStatus, StatusEnum.ENABLE.getCode());
        List<OrdRate> ordRates = ordRateMapper.selectList(queryWrapper);

        return BeanUtils.copyToList(ordRates, RemoteOrderRateVo.class);
    }

    /**
     * 根据订单id和分佣类型查询分佣信息
     *
     * @param orderId 订单id
     * @param rateType 分佣类型
     * @return 订单分佣信息
     */
    @Override
    public RemoteOrderRateVo queryByOrderIdAndRateType(Long orderId, String rateType) {
        LambdaQueryWrapper<OrdRate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrdRate::getOrderId, orderId)
                .eq(OrdRate::getRateType, rateType)
                .eq(OrdRate::getStatus, StatusEnum.ENABLE.getCode())
                .orderByDesc(OrdRate::getCreateTime)
                .last(Constants.LIMIT_ONE);
        OrdRate ordRate = ordRateMapper.selectOne(queryWrapper);

        return BeanUtils.copyProperties(ordRate, RemoteOrderRateVo.class);
    }

    /**
     * 根据订单id和分佣类型查询分佣信息
     *
     * @param orderIds 订单id集合
     * @param rateType 分佣类型
     * @return 订单分佣信息
     */
    @Override
    public List<RemoteOrderRateVo> queryByOrderIdsAndRateType(List<Long> orderIds, String rateType) {
        if (CollUtil.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrdRate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrdRate::getOrderId, orderIds)
                .eq(OrdRate::getRateType, rateType)
                .eq(OrdRate::getStatus, StatusEnum.ENABLE.getCode());
        List<OrdRate> ordRates = ordRateMapper.selectList(queryWrapper);

        return BeanUtils.copyToList(ordRates, RemoteOrderRateVo.class);
    }
}
