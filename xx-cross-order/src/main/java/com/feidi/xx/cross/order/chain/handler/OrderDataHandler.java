package com.feidi.xx.cross.order.chain.handler;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.feidi.xx.common.core.cache.system.SysDistrictCacheVo;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.SystemCacheKeyEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.annotations.HandlerScope;
import com.feidi.xx.cross.common.cache.market.manage.MktCacheManager;
import com.feidi.xx.cross.common.cache.operate.enums.OprCacheKeyEnum;
import com.feidi.xx.cross.common.cache.operate.vo.OprEstimateRecordCacheVo;
import com.feidi.xx.cross.common.enums.order.CreateModelEnum;
import com.feidi.xx.cross.common.utils.order.OrderUtils;
import com.feidi.xx.cross.operate.api.domain.price.dto.PriceDto;
import com.feidi.xx.cross.order.api.domain.bo.*;
import com.feidi.xx.cross.order.chain.base.OrderBaseChainContext;
import com.feidi.xx.cross.order.chain.base.OrderBaseChainResult;
import com.feidi.xx.cross.order.chain.cancel.OrderCancelChainContext;
import com.feidi.xx.cross.order.chain.common.AbstractChainHandler;
import com.feidi.xx.cross.order.chain.operate.OrderOperateChainContext;
import com.feidi.xx.cross.order.chain.payment.OrderPaymentChainContext;
import com.feidi.xx.cross.order.chain.place.OrderPlaceChainContext;
import com.feidi.xx.cross.power.api.RemoteAgentLineService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单数据处理器，用于处理数据的各种格式转换，不涉及数据库相关操作
 * 注意需要是多例模式
 */
@Slf4j
@Component
@HandlerScope
@RequiredArgsConstructor
public class OrderDataHandler<T extends OrderBaseChainContext, R extends OrderBaseChainResult> extends AbstractChainHandler<T, R> {
    private final MktCacheManager mktCacheManager;
    @DubboReference
    private final RemoteAgentLineService remoteAgentLineService;
    @Override
    public void handle(T context) {
        if (context instanceof OrderPlaceChainContext placeContext) {
            RemoteOrderBo handled = handle(placeContext);
            // 上下文处理
            placeContext.setOrderBo(handled);
            placeContext.setPlatformCode(handled.getPlatformCode());
            placeContext.setProductCode(handled.getProductCode());
            String key = OrderUtils.makeOrderKey(handled.getPlatformCode(), handled.getProductCode(),
                    handled.getPassengerId().toString(),
                    handled.getEarliestTime(), handled.getLatestTime(),
                    handled.getStartPosition().getLongitude() + "" + handled.getStartPosition().getLatitude(),
                    handled.getEndPosition().getLongitude() + "" + handled.getEndPosition().getLatitude());
            placeContext.setOrderKey(key);
        } else if (context instanceof OrderOperateChainContext operateContext) {
            //
        } else if (context instanceof OrderCancelChainContext cancelContext) {
            RemoteOrderCancelBo handled = handle(cancelContext);
            cancelContext.setHandleBo(handled);
        } else if (context instanceof OrderPaymentChainContext paymentContext) {
            RemoteOrderPaymentBo handled = handle(paymentContext);
            paymentContext.setHandleBo(handled);
            if (StringUtils.isBlank(paymentContext.getTenantId())) {
                log.info("支付paymentContext中tenantId为空，使用默认值");
                paymentContext.setTenantId(handled.getTenantId());
            }
        }
    }

    private RemoteOrderBo handle(OrderPlaceChainContext context) {
        String key = OprCacheKeyEnum.OPR_ESTIMATE_RECORD_KEY.create(context.getEstimateKey());
        OprEstimateRecordCacheVo cacheVo = RedisUtils.getCacheObject(key);
        if (cacheVo == null || CollUtil.isEmpty(cacheVo.getPrice())) {
            throw new ServiceException("数据异常，请重新下单");
        }
        RemoteOrderBo bo = new RemoteOrderBo();
        bo.setPassengerNum(cacheVo.getPassengerNum());
        bo.setPassengerRemark(context.getPassengerRemark());
        bo.setHighwayType(context.getHighwayType());
        bo.setProductCode(context.getProductCode());
        bo.setPlatformCode(cacheVo.getPlatformCode());
        bo.setCreateModel(context.getCreateModel());
        bo.setPassengerDetail(cacheVo.getPassengerDetail());
        //绑定代客下单相关信息
        if (context.getCreateModel().equals(CreateModelEnum.AGENT_ORDER.getCode())) {
            if (mktCacheManager.getPassengerId(UserTypeEnum.AGENT_USER.getUserType(), LoginHelper.getAgentId(), context.getPassengerId())) {
                bo.setInviteAgentId(LoginHelper.getAgentId());
            }

        }else if (context.getCreateModel().equals(CreateModelEnum.DRIVER_ORDER.getCode())){
            if (mktCacheManager.getPassengerId(UserTypeEnum.AGENT_USER.getUserType(), LoginHelper.getUserId(), context.getPassengerId())) {
                bo.setInviteDriverId(LoginHelper.getUserId());
                bo.setInviteAgentId(LoginHelper.getAgentId());
            }
        }
        bo.setLineId(cacheVo.getLineId());
        // 绑定代理商id
        bindAgentId(context, cacheVo.getLineId(), bo);
        // 线路方向
        bo.setLineDirection(cacheVo.getRouteType());
        bo.setMileage(cacheVo.getMileage());
        bo.setExpectDuration(cacheVo.getExpectDuration());
        bo.setPassengerId(context.getPassengerId());
        bo.setDriverId(context.getDriverId());
        // 优惠券
        bo.setCouponGrantId(context.getCouponGrantId());
        bo.setCouponGrantQuota(context.getCouponQuota());
        // 渠道
        bo.setChannel(context.getChannel());
        bo.setSource(context.getSource());

        // 时间
        bo.setEarliestTime(cacheVo.getEarliestTime());
        bo.setLatestTime(cacheVo.getLatestTime());
        bo.setMaxWaitDuration((int) DateUtil.between(bo.getEarliestTime(), bo.getLatestTime(), DateUnit.MINUTE));
        bo.setMileage(cacheVo.getMileage());

        // 冗余
        bo.setStartCityCode(cacheVo.getStartCityCode());
        bo.setEndCityCode(cacheVo.getEndCityCode());

        // 起点

        RemotePositionBo start = new RemotePositionBo();
        start.setProvince(cacheVo.getEndProvinceName());
        start.setCity(cacheVo.getStartCityName());
        start.setCityCode(cacheVo.getStartCityCode());
        start.setDistrict(cacheVo.getStartDistrictName());
        start.setAdCode(cacheVo.getStartAdCode());
        start.setAddress(cacheVo.getStartAddress());
        start.setShortAddr(cacheVo.getStartShortAddress());
        start.setLongitude(cacheVo.getStartLongitude());
        start.setLatitude(cacheVo.getStartLatitude());
        bo.setStartPosition(start);
        // 终点
        RemotePositionBo end = new RemotePositionBo();
        end.setProvince(cacheVo.getEndProvinceName());
        end.setCity(cacheVo.getEndCityName());
        end.setCityCode(cacheVo.getEndCityCode());
        end.setDistrict(cacheVo.getEndDistrictName());
        end.setAdCode(cacheVo.getEndAdCode());
        end.setAddress(cacheVo.getEndAddress());
        end.setShortAddr(cacheVo.getEndShortAddress());
        end.setLongitude(cacheVo.getEndLongitude());
        end.setLatitude(cacheVo.getEndLatitude());
        bo.setEndPosition(end);

        // 价格
        if (Objects.equals(context.getCreateModel(), CreateModelEnum.RESELL_ORDER.getCode())) {
            // 订单转卖时，订单价格是司机录入，不需要从询价记录中获取
            bo.setOrderPrice(context.getOrderPrice());
            bo.setResellDriverId(context.getResellDriverId());
            bo.setResellAgentId(context.getResellAgentId());
            bo.setResellDriverPrice(context.getResellDriverPrice());

            // 订单转卖，司机端订单池不显示
            bo.setShowed(IsYesEnum.NO.getCode());
        } else if (Objects.equals(context.getCreateModel(), CreateModelEnum.AGENT_ORDER.getCode())
                && context.getOrderPrice() != null) {
            //运力商通过EXCEL代下单
            bo.setOrderPrice(context.getOrderPrice());
            bo.setLabel("Excel导入代客下单");
        }
        else {
            List<PriceDto> priceDtos = cacheVo.getPrice();
            Map<String, PriceDto> priceMap =
                    priceDtos.stream().collect(Collectors.toMap(PriceDto::getProductCode, Function.identity()));
            PriceDto priceDto = priceMap.get(context.getProductCode());
            bo.setOrderPrice(priceDto.getCalculatePrice());
        }
        // 支付金额
        long payPrice = bo.getOrderPrice() - (context.getCouponQuota() != null ? context.getCouponQuota() : 0);
        // 保证支付金额不能为0
        bo.setPayPrice(payPrice > 0 ? payPrice : 1);

        // 其他数据
        bo.setPassengerId(context.getPassengerId());
        bo.setPassengerPhone(context.getPassengerPhone());
        // TODO 暂时用真实的
        bo.setVirtualPhone(context.getPassengerPhone());
        bo.setPhoneEnd(StringUtils.substring(context.getPassengerPhone(), -4));

        // 单号
        bo.setOrderNo(OrderUtils.makeOrderNo());
        bo.setPlatformNo(bo.getOrderNo());
        return bo;
    }

    /**
     * 绑定代理商id
     *
     * @param context 下单参数
     * @param lineId 路线id
     * @param bo 订单bo
     */
    private void bindAgentId(OrderPlaceChainContext context, Long lineId, RemoteOrderBo bo) {
        // 扫码下单和乘客下单，根据线路匹配代理商
        if (Objects.equals(context.getCreateModel(), CreateModelEnum.PASSENGER_QRCODE.getCode()) || Objects.equals(context.getCreateModel(), CreateModelEnum.PASSENGER_ORDER.getCode())) {
            if (lineId != null) {
                List<Long> agentIds = remoteAgentLineService.getLineAgent(lineId);
                if (CollUtil.isNotEmpty(agentIds)) {
                    bo.setAgentId(agentIds.get(0));
                }
            }
        } else if (Objects.equals(context.getCreateModel(), CreateModelEnum.DRIVER_ORDER.getCode()) || context.getCreateModel().equals(CreateModelEnum.AGENT_ORDER.getCode())) {
            // 司机代下单和运力代下单，直接关联代理商
            bo.setAgentId(context.getAgentId());
        } else if (Objects.equals(context.getCreateModel(), CreateModelEnum.RESELL_ORDER.getCode())) {
            // 订单转卖，订单关联代理商即为订单转卖司机所属代理商
            bo.setAgentId(context.getResellAgentId());
        }
    }

    /**
     * 根据省市区id获取名称
     */
    private static String getNameById(Long id) {
        SysDistrictCacheVo value =  RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_ID_KEY.create(), String.valueOf(id));
        if (ObjectUtils.isNotNull(value)) {
            return value.getName();
        }
        return null;
    }

    private RemoteOrderHandleBo handle(OrderOperateChainContext context) {
        RemoteOrderHandleBo bo = handleBase(context, new RemoteOrderHandleBo());
        return bo;
    }

    private RemoteOrderCancelBo handle(OrderCancelChainContext context) {
        RemoteOrderCancelBo bo = handleBase(context, new RemoteOrderCancelBo());
        bo.setUserId(context.getUserId());
        bo.setUserType(context.getUserType());
        bo.setCancelType(context.getCancelType());
        bo.setCancelRemark(context.getCancelRemark());
        bo.setComplain(context.getComplain());
        bo.setComplainTime(context.getComplainTime());
        bo.setComplainType(context.getComplainType());
        bo.setComplainRemark(context.getComplainRemark());
        bo.setComplainPrice(context.getComplainPrice());
        return bo;
    }

    private RemoteOrderPaymentBo handle(OrderPaymentChainContext context) {
        RemoteOrderPaymentBo bo = handleBase(context, new RemoteOrderPaymentBo());
        return bo;
    }

    private <T extends OrderHandleBaseBo> T handleBase(OrderBaseChainContext context, T base) {
        if (StpUtil.isLogin()) {
            base.setTenantId(LoginHelper.getTenantId());
            base.setUserId(LoginHelper.getUserId());
            base.setUserType(LoginHelper.getUserType().getUserType());
        }
        /*else {
            log.info("未登录，强制退出");
            ExceptionUtil.ignoreEx(() -> {
                String loginId = LoginHelper.getLoginId(LoginHelper.getUserType().getUserType(), LoginHelper.getUserId());
                StpUtil.logout(loginId);
            });
        }*/
        if (StringUtils.isBlank(base.getTenantId())) {
            log.info("未获取到租户ID，使用默认值");
            base.setTenantId(Constants.TENANT_ID);
        }
        base.setOrderId(context.getOrderId());
        base.setPlatformCode(context.getPlatformCode());
        base.setProductCode(context.getProductCode());
        base.setPlatformNo(context.getPlatformNo());
        base.setLongitude(context.getLongitude());
        base.setLatitude(context.getLatitude());
        base.setRemark(context.getRemark());
        base.setTimeStamp(context.getTimeStamp());
        base.setThirdStatus(context.getThirdStatus());
        return base;
    }

}
