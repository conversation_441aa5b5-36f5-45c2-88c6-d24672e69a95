package com.feidi.xx.cross.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.feidi.xx.common.core.enums.SuccessFailEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.insure.AcInsureHelper;
import com.feidi.xx.common.insure.domain.message.MessageDoucment;
import com.feidi.xx.common.insure.enums.CallbackOrderTypeEnum;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.order.enums.OrdCacheKeyEnum;
import com.feidi.xx.cross.common.enums.order.*;
import com.feidi.xx.cross.common.helper.OrdOrderOperateHelper;
import com.feidi.xx.cross.common.mq.event.OrdOrderOperateEvent;
import com.feidi.xx.cross.common.mq.producer.OrdOrderOperateProducer;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.OrdOrderInfo;
import com.feidi.xx.cross.order.mapper.OrdOrderInfoMapper;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.service.IComCallbackService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ComCallbackServiceImpl implements IComCallbackService {

    private final OrdOrderMapper ordOrderMapper;
    private final OrdOrderInfoMapper ordOrderInfoMapper;
    private final OrdOrderOperateProducer ordOrderOperateProducer;

    /**
     * 保险通知投保回调
     *
     * @param xmlStr
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String insureResult(String xmlStr) throws Exception {
        if (log.isInfoEnabled()) {
            log.info("安诚保险投保回调请求，请求报文为：【{}】", xmlStr);
        }

        String responseMessageXml = null;

        // 验签
        //this.verifySign(xmlStr, MessageTypeEnum.REQUEST.getCode());

        // 解析XML
        MessageDoucment messageDoucment = AcInsureHelper.fromXml(xmlStr);

        // 保险产品代码
        String productCode = messageDoucment.getRequest().getHead().getProductCode();

        // 文件类型
        String fileType = messageDoucment.getRequest().getBody().getFileType();
        // 批次号（订单id）
        String batchNumber = messageDoucment.getRequest().getBody().getBatchNumber();
        if (log.isInfoEnabled()) {
            log.info("安诚保险投保回调请求，文件类型：【{}】, 批次号(订单id)：【{}】", fileType, batchNumber);
        }

        OrdOrder order = ordOrderMapper.selectById(Long.valueOf(batchNumber));


        String key = OrdCacheKeyEnum.ORD_ORDER_INSURANCE_INFO_KEY.create(order.getId());
        String fileDownloadPath = RedisUtils.getCacheObject(key);

        if (log.isInfoEnabled()) {
            log.info("安诚保险投保回调请求，文件下载路径为：【{}】", fileDownloadPath);
        }

        String operateType = OperateTypeEnum.INSURE_CALLBACK.getCode();

        // 投保日志
        OrdOrderOperateEvent insureRecordEvent = OrdOrderOperateHelper.buildInsureRecordEvent(order.getTenantId(), order.getId(), operateType, JsonUtils.toJsonString(xmlStr));

        try {
            // 从SFTP服务器下载文件
            AcInsureHelper.sftpDownloadFile(fileDownloadPath, fileType, order.getId().toString());
            if (log.isInfoEnabled()) {
                log.info("安诚保险投保回调请求，远程压缩文件下载成功");
            }

            // 解压并获取文件中的内容
            String content = AcInsureHelper.unzipAndGetFileContent(fileDownloadPath, fileType, order.getId().toString());
            if (log.isInfoEnabled()) {
                log.info("安诚保险投保回调请求，文件解压成功，文件内容：【{}】", content);
            }

            InsureRecordTypeEnum insureRecordType = InsureRecordTypeEnum.INSURE_CALLBACK;
            boolean isSuccess = false;
            // 投保结果
            String remark = "投保失败";
            List<String> insureResult = Arrays.stream(content.split("\\|")).toList();
            if (Objects.equals(fileType, CallbackOrderTypeEnum.INSURE.getCode())) {
                if (CollUtil.isNotEmpty(insureResult) && insureResult.size() >= 5) {
                    if (Objects.equals(insureResult.get(1), "1")) {
                        // 保单号
                        String insureNo = insureResult.get(4);
                        // 更新投保结果
                        LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.set(OrdOrder::getInsureNo, insureNo)
                                .set(OrdOrder::getInsureStatus, InsureStatusEnum.INSURED.getCode())
                                .eq(OrdOrder::getId, Long.valueOf(batchNumber));
                        isSuccess = ordOrderMapper.update(updateWrapper) > 0;

                        LambdaUpdateWrapper<OrdOrderInfo> ordOrderInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                        ordOrderInfoLambdaUpdateWrapper.set(OrdOrderInfo::getInsurePlatform, InsurePlatformEnum.AC.getCode())
                                .set(OrdOrderInfo::getInsureCode, productCode)
                                .set(OrdOrderInfo::getInsureTime, DateUtils.getNowDate())
                                .eq(OrdOrderInfo::getOrderId, Long.valueOf(batchNumber));
                        isSuccess = ordOrderInfoMapper.update(ordOrderInfoLambdaUpdateWrapper) > 0;

                        remark = "投保成功，保险单号：" + insureNo;
                        if (log.isInfoEnabled()) {
                            log.info("安诚保险投保回调请求，投保成功！，保单号：【{}】，订单投保更新结果：【{}】",insureNo, isSuccess);
                        }
                    }
                } else {
                    log.error("投保失败！订单号：【{}】", batchNumber);
                }
            } else if (Objects.equals(fileType, CallbackOrderTypeEnum.CANCEL_INSURE.getCode())) {
                remark = "退保失败";
                operateType = OperateTypeEnum.CANCEL_INSURE.getCode();
                insureRecordType = InsureRecordTypeEnum.CANCEL_INSURE_CALLBACK;
                if (CollUtil.isNotEmpty(insureResult)) {
                    if (Objects.equals(insureResult.get(1), "1")) {
                        // 更新退保结果
                        LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.set(OrdOrder::getInsureNo, "")
                                .set(OrdOrder::getInsureStatus, InsureStatusEnum.CANCEL_INSURE.getCode())
                                .eq(OrdOrder::getId, Long.valueOf(batchNumber));
                        isSuccess = ordOrderMapper.update(updateWrapper) > 0;

                        LambdaUpdateWrapper<OrdOrderInfo> ordOrderInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                        ordOrderInfoLambdaUpdateWrapper
                                .set(OrdOrderInfo::getCancelInsureTime, DateUtils.getNowDate())
                                .set(OrdOrderInfo::getCancelInsureReason, CancelInsureReasonEnum.CANCEL_BY_ORDER.getCode())
                                .eq(OrdOrderInfo::getOrderId, Long.valueOf(batchNumber));
                        isSuccess = ordOrderInfoMapper.update(ordOrderInfoLambdaUpdateWrapper) > 0;

                        remark = "退保成功";
                        if (log.isInfoEnabled()) {
                            log.info("安诚保险投保回调请求，退保成功！，订单退保更新结果：【{}】", isSuccess);
                        }
                    }
                } else {
                    log.error("退保失败！订单号：【{}】", batchNumber);
                }
            }

            // 生成响应报文并加签
            responseMessageXml = AcInsureHelper.createResponseMessage(batchNumber);

            if (log.isInfoEnabled()) {
                log.info("安诚保险投保回调响应，响应报文为：{}", responseMessageXml);
            }

            // 投保日志
            insureRecordEvent.setOperateType(operateType);
            insureRecordEvent.setRecordType(insureRecordType.getCode());
            insureRecordEvent.setResponseJson(responseMessageXml);
            insureRecordEvent.setStatus(SuccessFailEnum.SUCCESS.getCode());
            insureRecordEvent.setRemark(remark);
            insureRecordEvent.setInsureContent(content);
            insureRecordEvent.setResponseJson(responseMessageXml);

        } finally {
            ordOrderOperateProducer.sendMessage(insureRecordEvent);
        }

        return responseMessageXml;
    }

    /**
     * 验签
     *
     * @param xmlStr
     * @param messageType
     */
    private void verifySign(String xmlStr, String messageType) {
        try {
            boolean verifyResult = AcInsureHelper.verifySignXml(xmlStr, messageType);
            if (!verifyResult) {
                throw new ServiceException("验签失败");
            }
            if (log.isInfoEnabled()) {
                log.info("安诚保险投保回调请求，验签成功");
            }
        } catch (Exception e) {
            log.error("验签失败", e);
            throw new ServiceException("验签失败");
        }
    }

}
