package com.feidi.xx.cross.order.service;

import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.bo.OrdOrderBo;
import com.feidi.xx.cross.order.domain.bo.order.OrdOrderQueryWebBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderHandleBaseBo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderExportVo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderVo;

import java.util.Collection;
import java.util.List;

/**
 * 订单Service接口
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface IOrdOrderService {

    /**
     * 分页查询订单列表
     *
     * @param bo        查询条件
     * @return 订单分页列表
     */
    TableDataInfo<OrdOrderVo> queryPageList(OrdOrderQueryWebBo bo);

    /**
     * 统计新增订单数量
     *
     * @param bo 查询参数
     * @return 新增订单数量
     */
    Long addNum(OrdOrderQueryWebBo bo);

    /**
     * 订单导出
     *
     * @param bo 查询条件
     * @return 订单信息
     */
    List<OrdOrderExportVo> export(OrdOrderQueryWebBo bo);

    /**
     * 查询订单
     *
     * @param id 主键
     * @return 订单
     */
    OrdOrderVo queryById(Long id);

    /**
     * 新增订单
     *
     * @param bo 订单
     * @return 是否新增成功
     */
    OrdOrder insertByBo(OrdOrderBo bo);

    /**
     * 修改订单
     *
     * @param bo 订单
     * @return 是否修改成功
     */
    OrdOrder updateByBo(OrdOrderBo bo);

    /**
     * 校验并批量删除订单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据平台编码和平台订单号查询订单
     *
     * @param platformCode 平台编码
     * @param platformNo   平台订单号
     * @return 订单信息
     */
    OrdOrder queryByPlatformCodeAndPlatformNo(String platformCode, String platformNo);

    /**
     * 根据订单id更新展示
     */
    Boolean updateShowById(Long id);

    /**
     * 删除未被接单且已经被取消的订单
     *
     * @param orderIds 可以删除的订单id集合
     */
    void deleteCancelOrders(List<Long> orderIds);

    /**
     * 修改乘客出发时间
     *
     * @param handleBaseBo
     * @return
     */
    Boolean updateEarliestTime(OrdOrderHandleBaseBo handleBaseBo);
}
