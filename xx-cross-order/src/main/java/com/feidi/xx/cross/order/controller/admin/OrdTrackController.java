package com.feidi.xx.cross.order.controller.admin;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.cross.order.domain.vo.OrdTrackVo;
import com.feidi.xx.cross.order.domain.bo.OrdTrackBo;
import com.feidi.xx.cross.order.service.IOrdTrackService;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;

/**
 * 后台 - 订单轨迹
 * 前端访问路由地址为:/order/track
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/track")
public class OrdTrackController extends BaseController {

    private final IOrdTrackService ordTrackService;

    /**
     * 查询订单轨迹列表
     */
    @SaCheckPermission("order:track:list")
    @GetMapping("/list")
    public TableDataInfo<OrdTrackVo> list(OrdTrackBo bo, PageQuery pageQuery) {
        return ordTrackService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出订单轨迹列表
     */
    @SaCheckPermission("order:track:export")
    @Log(title = "订单轨迹", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OrdTrackBo bo, HttpServletResponse response) {
        List<OrdTrackVo> list = ordTrackService.queryList(bo);
        ExcelUtil.exportExcel(list, "订单轨迹", OrdTrackVo.class, response);
    }

    /**
     * 获取订单轨迹详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("order:track:query")
    @GetMapping("/{id}")
    public R<OrdTrackVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(ordTrackService.queryById(id));
    }

    /**
     * 新增订单轨迹
     */
    @SaCheckPermission("order:track:add")
    @Log(title = "订单轨迹", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OrdTrackBo bo) {
        return toAjax(ordTrackService.insertByBo(bo));
    }

    /**
     * 修改订单轨迹
     */
    @SaCheckPermission("order:track:edit")
    @Log(title = "订单轨迹", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OrdTrackBo bo) {
        return toAjax(ordTrackService.updateByBo(bo));
    }

    /**
     * 删除订单轨迹
     *
     * @param ids 主键串
     */
    @SaCheckPermission("order:track:remove")
    @Log(title = "订单轨迹", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(ordTrackService.deleteWithValidByIds(List.of(ids), true));
    }
}
