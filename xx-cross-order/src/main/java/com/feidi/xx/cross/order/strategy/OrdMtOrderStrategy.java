package com.feidi.xx.cross.order.strategy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.feidi.xx.common.core.enums.PaymentStatusEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.constant.order.OrderLockKeyConstants;
import com.feidi.xx.cross.common.enums.order.CreateModelEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderBo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderCancelBo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderHandleBo;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.OrdOrderInfo;
import com.feidi.xx.cross.order.domain.bo.OrdOrderBo;
import com.feidi.xx.cross.order.domain.bo.OrdPositionBo;
import com.feidi.xx.cross.order.helper.OrdOrderHelper;
import com.feidi.xx.cross.order.helper.OrdOrderProcessHelper;
import com.feidi.xx.cross.order.mapper.OrdOrderInfoMapper;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.service.IOrdOrderService;
import io.seata.spring.annotation.GlobalTransactional;
import io.seata.tm.api.GlobalTransaction;
import io.seata.tm.api.GlobalTransactionContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 美团订单策略
 *
 * <AUTHOR>
 * @date 2025/3/13
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrdMtOrderStrategy implements OrdOrderStrategy {

    private final OrdOrderHelper ordOrderHelper;
    private final OrdOrderProcessHelper ordOrderProcessHelper;
    private final IOrdOrderService orderService;
    private final OrdOrderMapper ordOrderMapper;
    private final OrdOrderInfoMapper ordOrderInfoMapper;

    @Override
    public PlatformCodeEnum getStrategyType() {
        return PlatformCodeEnum.MT;
    }

    /**
     * 下单
     *
     * @param remoteOrderBo 下单参数
     * @return 下单结果
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public OrdOrder postOrder(RemoteOrderBo remoteOrderBo) {
        // 订单新增
        OrdOrderBo ordOrderBo = BeanUtil.copyProperties(remoteOrderBo, OrdOrderBo.class);
        ordOrderBo.setStartPosition(BeanUtils.copyProperties(remoteOrderBo.getStartPosition(), OrdPositionBo.class));
        ordOrderBo.setEndPosition(BeanUtils.copyProperties(remoteOrderBo.getEndPosition(), OrdPositionBo.class));
        // 美团的手机号为手机尾号
        ordOrderBo.setPassengerPhone(remoteOrderBo.getPhoneEnd());
        ordOrderBo.setCreateModel(CreateModelEnum.THIRD_PLATFORM.getCode());
        OrdOrder order = orderService.insertByBo(ordOrderBo);

        // 新增订单后的操作
        ordOrderProcessHelper.afterPostOrder(order, remoteOrderBo);

        return order;
    }

    /**
     * 通知派单
     *
     * @param handleBo 通知派单参数
     * @return 通知派单结果
     */
    @Override
    public Boolean confirmNotify(RemoteOrderHandleBo handleBo) {
        // 获取订单详情
        OrdOrder order = orderService.queryByPlatformCodeAndPlatformNo(handleBo.getPlatformCode(), handleBo.getPlatformNo());

        if (ObjectUtils.isNull(order)) {
            log.error("美团 - 通知派单 订单不存在 {}", order);
            throw new ServiceException("订单信息不存在");
        }

        boolean flag = false;

        if (order.getDriverId() != null && order.getDriverId() > 0) {
            LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(OrdOrder::getId, order.getId())
                    .set(OrdOrder::getThirdStatus, handleBo.getThirdStatus());
            flag = ordOrderMapper.update(updateWrapper) > 0;

            // 发送短信
            //if (flag && ObjectUtils.isNotNull(order.getDriverId())) {
            //    // 异步发送短信
            //    ordOrderHelper.sendMessageAsync(order.getId(), order.getDriverId(), order.getOrderNo(), SmsUseEnum.ORDER_NEW);
            //}
        }

        return flag;
    }

    /**
     * 订单取消费
     *
     * @param handleBo 订单取消费参数
     * @return 订单取消费
     */
    @Override
    public Long orderCancelFee(RemoteOrderHandleBo handleBo) {
        // 获取订单详情
        OrdOrder order = orderService.queryByPlatformCodeAndPlatformNo(handleBo.getPlatformCode(), handleBo.getPlatformNo());

        if (ObjectUtils.isNull(order)) {
            log.error("美团 - 获取取消费 订单不存在 {}", handleBo);
            throw new ServiceException("订单信息不存在");
        }

        // 司机未接单
        if ((order.getDriverId() == null || order.getDriverId() < 1) && ObjectUtils.isNull(order.getReceiveTime())) {
            return 0L;
        }

        // TODO-NEW 现阶段取消费为0
        //long cancelFee = this.computeCancelFee(orderVo);
        long cancelFee = 0L;

        // 更新订单取消费
        if (cancelFee > 0) {
            LambdaUpdateWrapper<OrdOrderInfo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(OrdOrderInfo::getCancelFee, cancelFee)
                    .set(OrdOrderInfo::getUpdateTime, DateUtils.getNowDate())
                    .eq(OrdOrderInfo::getOrderId, order.getId());
            ordOrderInfoMapper.update(updateWrapper);
        }

        return cancelFee;
    }

    /**
     * 取消订单
     *
     * @param cancelBo 取消订单参数
     * @return 取消订单结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean cancelOrder(RemoteOrderCancelBo cancelBo) {
        String uniKey = StrUtil.format("{}#{}#{}", cancelBo.getOrderId(), cancelBo.getPlatformCode(), cancelBo.getPlatformNo());
        // 手动加锁5秒，如果存在则不处理
        boolean ifAbsent = RedisUtils.setObjectIfAbsent("cancel:" + uniKey, uniKey, Duration.ofSeconds(5));
        if (!ifAbsent) {
            return true;
        }

        // 获取订单信息
        OrdOrder order = orderService.queryByPlatformCodeAndPlatformNo(cancelBo.getPlatformCode(), cancelBo.getPlatformNo());
        if (ObjectUtil.isNull(order)) {
            return false;
        }
        // 订单状态 -- 取消
        String orderStatus = OrderStatusEnum.CANCEL.getCode();

        // 抛异常会导致美团侧无法更新订单状态
        if (Objects.equals(order.getStatus(), orderStatus)) {
            log.error("订单已取消，订单id：【{}】", order.getId());
            return true;
        }

        // 只有美团订单，且用户类型也是美团时才不可取消已完成订单
        if (Objects.equals(order.getStatus(), OrderStatusEnum.FINISH.getCode())) {
            throw new ServiceException("订单已完成、无法取消，请联系客服处理");
        }

        ordOrderProcessHelper.afterCancelOrder(order, cancelBo);

        // 发送websocket消息，通知司机订单取消
        if (ObjectUtils.isNotNull(order.getDriverId()) && order.getDriverId() > 0) {
            ordOrderProcessHelper.asyncSendWebSocketMessage(order.getId(), order.getDriverId());
        }

        return  ordOrderProcessHelper.afterCancelOrder(order, cancelBo);
    }

    /**
     * 乘客确认上车
     *
     * @param handleBo 确认上车参数
     * @return 确认上车结果
     */
    @Override
    public Boolean tripStart(RemoteOrderHandleBo handleBo) {
        if (log.isInfoEnabled()) {
            log.info("美团 - 乘客确认上车参数：【{}】", JsonUtils.toJsonString(handleBo));
        }
        // 订单信息
        OrdOrder order = orderService.queryByPlatformCodeAndPlatformNo(handleBo.getPlatformCode(), handleBo.getPlatformNo());

        log.info("美团 - 乘客确认上车订单：【{}】", JsonUtils.toJsonString(order));

        // 校验订单状态
        validBeforeHandle(order.getStatus());

        if (!Objects.equals(order.getPayStatus(), PaymentStatusEnum.SUCCESS.getCode())) {
            throw new ServiceException("订单未支付、请联系乘客或客服");
        }

        if (!(Integer.parseInt(OrderStatusEnum.ING.getCode()) > Integer.parseInt(order.getStatus()))) {
            throw new ServiceException("订单状态无法操作，请刷新页面重试");
        }

        boolean flag = ordOrderProcessHelper.afterTripStart(order, handleBo.getThirdStatus());

        // 发送websocket消息，通知司机行程开始
        ordOrderProcessHelper.asyncSendWebSocketMessage(order.getId(), order.getDriverId());

        return flag;
    }

    /**
     * 乘客确认下车
     *
     * @param handleBo 确认下车参数
     * @return 确认下车结果
     */
    @Override
    public Boolean tripEnd(RemoteOrderHandleBo handleBo) {
        if (log.isInfoEnabled()) {
            log.info("美团 - 乘客确认下车参数：【{}】", JsonUtils.toJsonString(handleBo));
        }
        // 订单信息
        OrdOrder order = orderService.queryByPlatformCodeAndPlatformNo(handleBo.getPlatformCode(), handleBo.getPlatformNo());
        log.info("美团 - 乘客确认上车订单：【{}】", JsonUtils.toJsonString(order));
        // 校验订单状态
        validBeforeHandle(order.getStatus());

        RedissonClient client = RedisUtils.getClient();
        RLock lock = client.getLock(OrderLockKeyConstants.LOCK_ORDER_FINISH_KEY + order.getDriverId());
        boolean flag;
        try {
            boolean tryLock = lock.tryLock(OrderLockKeyConstants.LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
            if (tryLock) {
                // 手动管理事务
                GlobalTransaction tx = GlobalTransactionContext.getCurrentOrCreate();
                // 开始事务
                tx.begin();

                try {
                    // 双重校验
                    order = orderService.queryByPlatformCodeAndPlatformNo(handleBo.getPlatformCode(), handleBo.getPlatformNo());
                    validBeforeHandle(order.getStatus());

                    if (!Objects.equals(order.getPayStatus(), PaymentStatusEnum.SUCCESS.getCode())) {
                        throw new ServiceException("订单未支付、请联系乘客或客服");
                    }

                    if (!(Integer.parseInt(OrderStatusEnum.FINISH.getCode()) > Integer.parseInt(order.getStatus()))) {
                        throw new ServiceException("订单状态无法操作，请刷新页面重试");
                    }

                    flag = ordOrderProcessHelper.afterTripEnd(order, handleBo.getThirdStatus());

                    // 发送websocket消息，通知司机行程结束
                    ordOrderProcessHelper.asyncSendWebSocketMessage(order.getId(), order.getDriverId());

                    tx.commit();
                    return flag;
                } catch (Exception e) {
                    tx.rollback();
                    throw new ServiceException("行程结束处理异常：" + e.getMessage());
                }
            } else {
                log.error("订单【{}】无法获取分布式锁，无法操作", handleBo.getOrderId());
                throw new ServiceException("订单【" + handleBo.getOrderId() + "】无法获取分布式锁，无法操作");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("行程结束处理异常，" + e.getMessage());
        } finally {
            lock.unlock();
        }
    }

    /**
     * 操作前校验订单
     */
    private void validBeforeHandle(String orderStatus) {
        String msg = StrUtil.format("当前订单状态为：{}，无法操作", OrderStatusEnum.getInfoByCode(orderStatus));
        Assert.isTrue(!OrderStatusEnum.getFinishedStatus().contains(orderStatus), msg);
    }
}
