package com.feidi.xx.cross.order.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.feidi.xx.common.core.domain.SimpleAddress;
import com.feidi.xx.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serial;

/**
 * 订单位置对象 ord_position
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ord_position")
public class OrdPosition extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 省ID
     */
    private Long provinceId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 市
     */
    private String city;

    /**
     * 区域ID
     */
    private Long districtId;

    /**
     * 区域编码
     */
    private String adCode;

    /**
     * 区域
     */
    private String district;

    /**
     * 短地址
     */
    private String shortAddr;

    /**
     * 地址
     */
    private String address;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 类型[StartEndEnum]
     */
    private String type;

    /**
     * 状态[StatusEnum]
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    public SimpleAddress toSimpleAddress() {
        return new SimpleAddress(province, city, district, address, shortAddr);
    }

}
