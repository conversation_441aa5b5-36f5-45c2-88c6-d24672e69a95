package com.feidi.xx.cross.order.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.order.domain.bo.OrdOperateBo;
import com.feidi.xx.cross.order.domain.vo.ExportVo;
import com.feidi.xx.cross.order.domain.vo.OrdOperateVo;
import com.feidi.xx.cross.order.service.IOrdOperateService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 订单操作
 * 前端访问路由地址为:/order/operate
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/operate")
public class OrdOperateController extends BaseController {

    private final IOrdOperateService ordOperateService;

    /**
     * 查询订单操作列表
     */
    @Enum2TextAspect
    @SaCheckPermission("order:operate:list")
    @GetMapping("/list/{orderId}")
    public R<List<OrdOperateVo>> list(@PathVariable Long orderId) {
        return R.ok(ordOperateService.queryByOrderId(orderId));
    }

    /**
     * 导出订单操作列表
     */
    @SaCheckPermission("order:operate:export")
    @Log(title = "订单操作", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OrdOperateBo bo, HttpServletResponse response) {
        List<OrdOperateVo> list = ordOperateService.queryList(bo);
        ExcelUtil.exportExcel(list, "订单操作", OrdOperateVo.class, response);
    }

    /**
     * 获取订单操作详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("order:operate:query")
    @GetMapping("/{id}")
    public R<OrdOperateVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(ordOperateService.queryById(id));
    }

    /**
     * 新增订单操作
     */
    @SaCheckPermission("order:operate:add")
    @Log(title = "订单操作", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OrdOperateBo bo) {
        return toAjax(ordOperateService.insertByBo(bo));
    }

    /**
     * 修改订单操作
     */
    @SaCheckPermission("order:operate:edit")
    @Log(title = "订单操作", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OrdOperateBo bo) {
        return toAjax(ordOperateService.updateByBo(bo));
    }

    /**
     * 删除订单操作
     *
     * @param ids 主键串
     */
    @SaCheckPermission("order:operate:remove")
    @Log(title = "订单操作", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(ordOperateService.deleteWithValidByIds(List.of(ids), true));
    }
}
