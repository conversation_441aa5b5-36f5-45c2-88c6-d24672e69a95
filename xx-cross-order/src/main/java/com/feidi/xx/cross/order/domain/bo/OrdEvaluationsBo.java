package com.feidi.xx.cross.order.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.order.domain.OrdEvaluations;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 行程评价业务对象 ord_evaluations
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrdEvaluations.class, reverseConvertGenerate = false)
public class OrdEvaluationsBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 代理商id
     */
    @NotNull(message = "代理商id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long agentId;

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long orderId;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String orderNo;

    /**
     * 司机id
     */
    @NotNull(message = "司机id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long driverId;

    /**
     * 乘客id
     */
    @NotNull(message = "乘客id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long passengerId;

    /**
     * 乘客真实手机号
     */
    @NotBlank(message = "乘客真实手机号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String passengerPhone;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String passengerName;

    /**
     * 评分 (1-5星)
     */
    @NotNull(message = "评分 (1-5星)不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long rating;

    /**
     * 是否匿名。0：不是，1：是
     */
    @NotNull(message = "是否匿名。0：不是，1：是不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer isAnonymous;

    /**
     * 评价内容
     */
    private String comment;

    /**
     * 标签
     */
    private List<String> tag = new ArrayList<>();

    /**
     * 开始创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startCreateTime;
    /**
     * 结束创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endCreateTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startUpdateTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endUpdateTime;
}
