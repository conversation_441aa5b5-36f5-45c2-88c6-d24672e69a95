package com.feidi.xx.cross.order.domain.bo.rob;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.BatchGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.cross.order.domain.DisRob;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 常用路线业务对象 dis_rob
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = DisRob.class, reverseConvertGenerate = false)
public class MatchBo extends PageQuery {

    /**
     * 租户ID
     */
    private String tenantId;


    /**
     * 司机ID
     */
    private Long driverId;


    /**
     * 开始经度
     */
    private String startLongitude;

    /**
     * 开始纬度
     */
    private String startLatitude;


    /**
     * 结束经度
     */
    private String endLongitude;

    /**
     * 结束纬度
     */
    private String endLatitude;

    /**
     * 预计出发时间
     */
    private Date earliestTime;

    /**
     * 预计最晚出发时间
     */
    private Date latestTime;
}
