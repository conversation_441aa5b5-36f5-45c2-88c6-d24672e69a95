package com.feidi.xx.cross.order.mapper;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.cross.order.domain.OrdPosition;
import com.feidi.xx.cross.order.domain.vo.OrdPositionVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.order.utils.ThreadPoolManager;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 订单位置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface OrdPositionMapper extends BaseMapperPlus<OrdPosition, OrdPositionVo> {

    /**
     * 根据ids多线程查
     * @param orderIds
     * @param filterField 是否过滤字段
     * @return
     */
    default List<OrdPosition> listByOrderIdsParallel(List<Long> orderIds, boolean filterField) {
        List<List<Long>> partition = ListUtil.partition(orderIds, 1000);
        // 使用CompletableFuture处理并发
        List<CompletableFuture<List<OrdPosition>>> futures = new ArrayList<>();
        for (List<Long> part : partition) {
            CompletableFuture<List<OrdPosition>> future;
            if (filterField) {
                future = CompletableFuture.supplyAsync(() -> selectList(Wrappers.<OrdPosition>lambdaQuery()
                        .select(OrdPosition::getOrderId, OrdPosition::getProvince, OrdPosition::getCity, OrdPosition::getDistrict, OrdPosition::getAddress, OrdPosition::getShortAddr, OrdPosition::getType)
                        .in(OrdPosition::getOrderId, part)), com.feidi.xx.cross.order.utils.ThreadPoolManager.getThreadPool());
            } else {
                future = CompletableFuture.supplyAsync(() -> selectBatchIds(part), ThreadPoolManager.getThreadPool());
            }
            futures.add(future);
        }
        // 等待所有异步任务完成并收集结果
        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        return allOf.thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)  // 获取每个任务的结果
                        .flatMap(List::stream)         // 将多个List扁平化
                        .collect(Collectors.toList())) // 收集成一个List
                .join();
    }


    /**
     * 根据订单id删除订单位置信息
     * @param orderIds
     */
    void deleteByOrderIds(@Param("orderIds") List<Long> orderIds);

}
