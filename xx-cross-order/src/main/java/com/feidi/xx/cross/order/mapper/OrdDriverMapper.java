package com.feidi.xx.cross.order.mapper;

import com.feidi.xx.cross.order.domain.OrdDriver;
import com.feidi.xx.cross.order.domain.vo.OrdDriverVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单司机Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface OrdDriverMapper extends BaseMapperPlus<OrdDriver, OrdDriverVo> {
    /**
     * 根据订单id删除订单司机信息
     *
     * @param orderIds
     */
    void deleteByOrderIds(@Param("orderIds") List<Long> orderIds);
}
