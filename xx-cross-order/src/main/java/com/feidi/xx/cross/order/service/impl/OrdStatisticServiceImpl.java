package com.feidi.xx.cross.order.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.enums.PaymentStatusEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.operate.api.RemoteLineService;
import com.feidi.xx.cross.operate.api.domain.line.vo.RemoteLineVo;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.OrdStatistic;
import com.feidi.xx.cross.order.domain.bo.OrdStatisticBo;
import com.feidi.xx.cross.order.domain.vo.OrdStatisticTableExportVo;
import com.feidi.xx.cross.order.domain.vo.OrdStatisticTableVo;
import com.feidi.xx.cross.order.domain.vo.OrdStatisticVo;
import com.feidi.xx.cross.order.helper.OrdStatisticHelper;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.mapper.OrdStatisticMapper;
import com.feidi.xx.cross.order.service.IOrdStatisticService;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.cross.passenger.api.domain.RemotePassengerVo;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@RequiredArgsConstructor
@Service
public class OrdStatisticServiceImpl implements IOrdStatisticService {

    private final OrdStatisticHelper ordStatisticHelper;
    private final OrdOrderMapper ordOrderMapper;
    private final OrdStatisticMapper baseMapper;
    @DubboReference
    private final RemoteAgentService remoteAgentService;
    @DubboReference
    private final RemoteLineService remoteLineService;
    @DubboReference
    private final RemotePassengerService remotePassengerService;

    /**
     * 查询订单统计
     *
     * @param id 主键
     * @return 订单统计
     */
    @Override
    public OrdStatisticVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询订单统计列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单统计分页列表
     */
    @Override
    public TableDataInfo<OrdStatisticVo> queryPageList(OrdStatisticBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrdStatistic> lqw = buildQueryWrapper(bo);
        Page<OrdStatisticVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 表格数据统计
     *
     * @param bo
     * @return
     */
    @Override
    public List<OrdStatisticTableVo> queryTableList(OrdStatisticBo bo) {
        if (DateUtil.betweenDay(bo.getStartDate(), bo.getEndDate(), false) >= 30) {
            throw new ServiceException("统计时间不能超过31天");
        }

        // 查询代理商，并按照代理id进行分组
        Map<Long, String> agentId2NameMap = remoteAgentService.getAllAgentInfo()
                .stream().collect(Collectors.toMap(RemoteAgentVo::getId, RemoteAgentVo::getCompanyName, (v1, v2) -> v2));

        // 查询线路，并按照线路id进行分组
        Map<Long, String> lineId2NameMap = remoteLineService.getAll()
                .stream().collect(Collectors.toMap(RemoteLineVo::getId, RemoteLineVo::getName, (v1, v2) -> v2));

        List<OrdStatisticTableVo> statisticTableVos = new ArrayList<>();

        String startTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, bo.getStartDate()) + " 00:00:00";
        String endTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, bo.getEndDate()) + " 23:59:59";

        List<OrdStatistic> ordStatistics = this.doStatisticOrder(startTime, endTime, bo.getPlatformCode(), bo.getAgentId());

        Map<Long, List<OrdStatistic>> agentId2ListMap = ordStatistics.stream().collect(Collectors.groupingBy(OrdStatistic::getAgentId));
        agentId2ListMap.forEach((agentId, agentStatistics) -> {
            OrdStatisticTableVo statisticTableVo = new OrdStatisticTableVo();
            statisticTableVo.setAgentId(agentId);
            String agentName = agentStatistics.get(0).getAgentName();
            agentName = StringUtils.isBlank(agentName) ? agentId2NameMap.getOrDefault(agentId, "") : agentName;
            statisticTableVo.setAgentName(agentName);
            statisticTableVo.setVolumeNumber(agentStatistics.stream().mapToLong(OrdStatistic::getVolumeNumber).sum());
            statisticTableVo.setDepartNumber(agentStatistics.stream().mapToLong(OrdStatistic::getDepartNumber).sum());
            statisticTableVo.setAcceptNumber(agentStatistics.stream().mapToLong(OrdStatistic::getAcceptNumber).sum());
            statisticTableVo.setReceiveNumber(agentStatistics.stream().mapToLong(OrdStatistic::getReceiveNumber).sum());
            statisticTableVo.setPickNumber(agentStatistics.stream().mapToLong(OrdStatistic::getPickNumber).sum());
            statisticTableVo.setPickStartNumber(agentStatistics.stream().mapToLong(OrdStatistic::getPickStartNumber).sum());
            statisticTableVo.setIngNumber(agentStatistics.stream().mapToLong(OrdStatistic::getIngNumber).sum());
            statisticTableVo.setFinishNumber(agentStatistics.stream().mapToLong(OrdStatistic::getFinishNumber).sum());
            statisticTableVo.setCancelNumber(agentStatistics.stream().mapToLong(OrdStatistic::getCancelNumber).sum());
            statisticTableVo.setUnpayCancelNumber(agentStatistics.stream().mapToLong(OrdStatistic::getUnpayCancelNumber).sum());
            statisticTableVo.setPayCancelNumber(agentStatistics.stream().mapToLong(OrdStatistic::getPayCancelNumber).sum());
            statisticTableVo.setFinishSummary(agentStatistics.stream().mapToLong(OrdStatistic::getFinishSummary).sum());
            statisticTableVo.setFinishAmount(agentStatistics.stream().mapToLong(OrdStatistic::getFinishAmount).sum());
            // 接单完成率（完单汇总/接单数）
            statisticTableVo.setReceiveFinishRate(ordStatisticHelper.divide(statisticTableVo.getFinishSummary(), statisticTableVo.getAcceptNumber()));

            if (StringUtils.isNotBlank(bo.getPlatformCode())) {
                Map<Long, OrdStatistic> lineId2ListMap = agentStatistics.stream().collect(Collectors.toMap(OrdStatistic::getLineId, Function.identity()));
                lineId2ListMap.forEach((lineId, lineStatistics) -> {
                    statisticTableVo.getChildren().add(BeanUtils.copyProperties(lineStatistics, OrdStatisticTableVo.class));
                });
            } else {
                Map<String, List<OrdStatistic>> platformCode2ListMap = agentStatistics.stream().collect(Collectors.groupingBy(OrdStatistic::getPlatformCode));
                Map<Long, OrdStatisticTableVo> ordStatisticMap = new HashMap<>();
                platformCode2ListMap.forEach((platformCode, platformStatistics) -> {
                    Map<Long, OrdStatistic> lineId2ListMap = platformStatistics.stream().collect(Collectors.toMap(OrdStatistic::getLineId, Function.identity()));
                    lineId2ListMap.forEach((lineId, lineStatistics) -> {
                        if (ordStatisticMap.containsKey(lineId)) {
                            OrdStatisticTableVo ordStatisticTableVo = ordStatisticMap.get(lineId);
                            ordStatisticTableVo.setVolumeNumber(ordStatisticTableVo.getVolumeNumber() + lineStatistics.getVolumeNumber());
                            ordStatisticTableVo.setDepartNumber(ordStatisticTableVo.getDepartNumber() + lineStatistics.getDepartNumber());
                            ordStatisticTableVo.setAcceptNumber(ordStatisticTableVo.getAcceptNumber() + lineStatistics.getAcceptNumber());
                            ordStatisticTableVo.setReceiveNumber(ordStatisticTableVo.getReceiveNumber() + lineStatistics.getReceiveNumber());
                            ordStatisticTableVo.setPickNumber(ordStatisticTableVo.getPickNumber() + lineStatistics.getPickNumber());
                            ordStatisticTableVo.setPickStartNumber(ordStatisticTableVo.getPickStartNumber() + lineStatistics.getPickStartNumber());
                            ordStatisticTableVo.setIngNumber(ordStatisticTableVo.getIngNumber() + lineStatistics.getIngNumber());
                            ordStatisticTableVo.setFinishNumber(ordStatisticTableVo.getFinishNumber() + lineStatistics.getFinishNumber());
                            ordStatisticTableVo.setCancelNumber(ordStatisticTableVo.getCancelNumber() + lineStatistics.getCancelNumber());
                            ordStatisticTableVo.setUnpayCancelNumber(ordStatisticTableVo.getUnpayCancelNumber() + lineStatistics.getUnpayCancelNumber());
                            ordStatisticTableVo.setPayCancelNumber(ordStatisticTableVo.getPayCancelNumber() + lineStatistics.getPayCancelNumber());
                            ordStatisticTableVo.setFinishSummary(ordStatisticTableVo.getFinishSummary() + lineStatistics.getFinishSummary());
                            ordStatisticTableVo.setFinishAmount(ordStatisticTableVo.getFinishAmount() + lineStatistics.getFinishAmount());
                            ordStatisticTableVo.setReceiveFinishRate(ordStatisticHelper.divide(ordStatisticTableVo.getFinishSummary(), ordStatisticTableVo.getAcceptNumber()));
                            ordStatisticMap.put(lineId, ordStatisticTableVo);
                        } else {
                            ordStatisticMap.put(lineId, BeanUtils.copyProperties(lineStatistics, OrdStatisticTableVo.class));
                        }
                    });
                });
                ordStatisticMap.forEach((key, value) -> {
                    if (StringUtils.isBlank(value.getLineName())) {
                        if (value.getLineId() == 0L) {
                            value.setLineName("其他");
                        } else {
                            value.setLineName(lineId2NameMap.getOrDefault(value.getLineId(), ""));
                        }
                    }
                    statisticTableVo.getChildren().add(value);
                });
            }

            statisticTableVo.getChildren().sort(Comparator.comparing(OrdStatisticTableVo::getLineId).reversed());

            statisticTableVos.add(statisticTableVo);
        });

        return statisticTableVos;
    }

    /**
     * 表格数据统计导出
     *
     * @param bo
     * @return
     */
    @Override
    public List<OrdStatisticTableExportVo> exportTable(OrdStatisticBo bo) {
        String startTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, bo.getStartDate()) + " 00:00:00";
        String endTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, bo.getEndDate()) + " 23:59:59";

        List<OrdStatistic> ordStatistics = this.doStatisticOrder(startTime, endTime, bo.getPlatformCode(), bo.getAgentId());
        ordStatistics = ordStatistics.stream().filter(e -> e.getAgentId() != null && e.getAgentId() > 0).collect(Collectors.toList());
        return MapstructUtils.convert(ordStatistics, OrdStatisticTableExportVo.class);
    }

    /**
     * 查询符合条件的订单统计列表
     *
     * @param bo 查询条件
     * @return 订单统计列表
     */
    @Override
    public OrdStatisticVo queryList(OrdStatisticBo bo) {
        if (DateUtil.betweenDay(bo.getStartDate(), bo.getEndDate(), false) >= 30) {
            throw new ServiceException("统计时间不能超过31天");
        }

        OrdStatistic ordStatistic = ordStatisticHelper.bindDefaultValue();

        // 统计日期数据
        String startDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, bo.getStartDate()) + " 00:00:00";
        String endDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, bo.getEndDate()) + " 23:59:59";
        ordStatisticHelper.bindOrderStatistic(ordStatistic, bo.getPlatformCode(), startDate, endDate);

        LambdaQueryWrapper<OrdOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPlatformCode()), OrdOrder::getPlatformCode, bo.getPlatformCode())
                .between(OrdOrder::getCreateTime, DateUtils.getDayBegin(), DateUtils.getDayEnd());

        // 发单量
        Long volumeNumber = ordOrderMapper.selectCount(lqw);
        ordStatistic.setVolumeNumber(volumeNumber);

        lqw.clear();
        lqw.eq(StringUtils.isNotBlank(bo.getPlatformCode()), OrdOrder::getPlatformCode, bo.getPlatformCode())
                .between(OrdOrder::getEarliestTime, DateUtils.getDayBegin(), DateUtils.getDayEnd());
        List<OrdOrder> orders = ordOrderMapper.selectList(lqw);

        // 接单数
        long acceptNumber = orders.stream().filter(e -> e.getDriverId() != null && e.getDriverId() > 0).count();
        ordStatistic.setAcceptNumber(acceptNumber);

        // 待接单数量
        long createNumber = orders.stream().filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.CREATE.getCode())).count();
        ordStatistic.setCreateNumber(createNumber);

        // 待出发数量
        long receiveNumber = orders.stream().filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.RECEIVE.getCode())).count();
        ordStatistic.setReceiveNumber(receiveNumber);

        // 前往上车点数量
        long pickNumber = orders.stream().filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.PICK.getCode())).count();
        ordStatistic.setPickNumber(pickNumber);

        // 到达上车点数量
        long pickStartNumber = orders.stream().filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.PICK_START.getCode())).count();
        ordStatistic.setPickStartNumber(pickStartNumber);

        // 行程中数量
        long ingNumber = orders.stream().filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.ING.getCode())).count();
        ordStatistic.setIngNumber(ingNumber);

        OrdStatistic dayOrderStatistic = ordStatisticHelper.bindDefaultValue();
        OrdStatistic weekOrderStatistic = ordStatisticHelper.bindDefaultValue();
        if (DateUtil.isSameDay(bo.getStartDate(), bo.getEndDate())) {
            // 日环比数据
            startDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtil.offsetDay(bo.getStartDate(), -1)) + " 00:00:00";
            endDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtil.offsetDay(bo.getStartDate(), -1)) + " 23:59:59";
            dayOrderStatistic = ordStatisticHelper.bindOrderStatistic(null, bo.getPlatformCode(), startDate, endDate);

            // 周环比数据
            startDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtil.offsetDay(bo.getStartDate(), -7)) + " 00:00:00";
            endDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtil.offsetDay(bo.getStartDate(), -7)) + " 23:59:59";
            weekOrderStatistic = ordStatisticHelper.bindOrderStatistic(null, bo.getPlatformCode(), startDate, endDate);
        } else {
            int day = (int)DateUtil.betweenDay(bo.getStartDate(), bo.getEndDate(), false) + 1;
            // 周环比数据
            startDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtil.offsetDay(bo.getStartDate(), -day)) + " 00:00:00";
            endDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtil.offsetDay(bo.getStartDate(), -day)) + " 23:59:59";
            weekOrderStatistic = ordStatisticHelper.bindOrderStatistic(null, bo.getPlatformCode(), startDate, endDate);
        }

        return ordStatisticHelper.calculateDayAndWeekRate(ordStatistic, dayOrderStatistic, weekOrderStatistic);
    }

    /**
     * 图表折线统计
     *
     * @param bo
     * @return
     */
    @Override
    public Object line(OrdStatisticBo bo) {
        if (DateUtil.betweenDay(bo.getStartDate(), bo.getEndDate(), false) >= 30) {
            throw new ServiceException("统计时间不能超过31天");
        }

        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();

        String startTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, bo.getStartDate()) + " 00:00:00";
        String endTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, bo.getEndDate()) + " 23:59:59";
        String hour = DateUtil.isSameDay(bo.getStartDate(), DateUtils.getNowDate()) ? DateUtils.parseDateToStr("HH", DateUtils.getNowDate()) : "22";

        if (Objects.equals(bo.getLineType(), "4")) {
            LambdaQueryWrapper<OrdOrder> lqw = Wrappers.lambdaQuery();
            lqw.eq(StringUtils.isNotBlank(bo.getPlatformCode()), OrdOrder::getPlatformCode, bo.getPlatformCode())
                    .between(OrdOrder::getCreateTime, startTime, endTime);

            List<OrdOrder> orders = ordOrderMapper.selectList(lqw);
            if (DateUtil.betweenDay(bo.getStartDate(), bo.getEndDate(),  false) == 0) {
                String day = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, bo.getStartDate());
                for (int i = 0; i <= Integer.parseInt(hour) + 1; i++) {
                    int finalI = i;
                    long count = orders.stream().filter(e -> e.getCreateTime() != null && e.getCreateTime().after(DateUtil.parse(day + " " + finalI + ":00:00"))
                                    && e.getCreateTime().before(DateUtil.parse(day + " " + finalI + ":59:59")))
                            .count();
                    resultMap.put(i + ":00", count);
                }
            } else {
                for (int i = 0; !DateUtil.isSameDay(DateUtil.offsetDay(bo.getStartDate(), i - 1), bo.getEndDate()); i++) {
                    int finalI = i;
                    long count = orders.stream().filter(e -> e.getCreateTime() != null && DateUtil.isSameDay(e.getCreateTime(), DateUtil.offsetDay(bo.getStartDate(), finalI)))
                            .count();
                    resultMap.put(DateUtils.parseDateToStr("MM-dd", DateUtil.offsetDay(bo.getStartDate(), finalI)), count);
                }
            }
        } else if (Objects.equals(bo.getLineType(), "7")) {
            List<RemotePassengerVo> passengerVos = new ArrayList<>();
            if (bo.getPlatformCode() == null || Objects.equals(bo.getPlatformCode(), PlatformCodeEnum.SELF.getCode())) {
                passengerVos = remotePassengerService.getByCreateTime(startTime, endTime);
            }

            if (DateUtil.betweenDay(bo.getStartDate(), bo.getEndDate(),  false) == 0) {
                String day = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, bo.getStartDate());
                for (int i = 0; i <= Integer.parseInt(hour) + 1; i++) {
                    int finalI = i;
                    long count = passengerVos.stream().filter(e -> e.getCreateTime() != null && DateUtil.parse(e.getCreateTime()).after(DateUtil.parse(day + " " + finalI + ":00:00"))
                                    && DateUtil.parse(e.getCreateTime()).before(DateUtil.parse(day + " " + finalI + ":59:59")))
                            .count();
                    resultMap.put(i + ":00", count);
                }
            } else {
                for (int i = 0; !DateUtil.isSameDay(DateUtil.offsetDay(bo.getStartDate(), i - 1), bo.getEndDate()); i++) {
                    int finalI = i;
                    long count = passengerVos.stream().filter(e -> e.getCreateTime() != null && DateUtil.isSameDay(DateUtil.parse(e.getCreateTime()), DateUtil.offsetDay(bo.getStartDate(), finalI)))
                            .count();
                    resultMap.put(DateUtils.parseDateToStr("MM-dd", DateUtil.offsetDay(bo.getStartDate(), finalI)), count);
                }
            }
        } else {
            LambdaQueryWrapper<OrdOrder> lqw = Wrappers.lambdaQuery();
            lqw.eq(StringUtils.isNotBlank(bo.getPlatformCode()), OrdOrder::getPlatformCode, bo.getPlatformCode())
                    .between(OrdOrder::getEarliestTime, startTime, endTime);

            List<OrdOrder> orders = ordOrderMapper.selectList(lqw);
            if (Objects.equals(bo.getLineType(), "0")) {
                if (DateUtil.betweenDay(bo.getStartDate(), bo.getEndDate(),  false) == 0) {
                    String day = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, bo.getStartDate());
                    for (int i = 0; i <= Integer.parseInt(hour) + 1; i++) {
                        int finalI = i;
                        long count = orders.stream()
                                .filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.FINISH.getCode()))
                                .filter(e -> e.getFinishTime() != null && e.getFinishTime().after(DateUtil.parse(day + " " + finalI + ":00:00"))
                                        && e.getFinishTime().before(DateUtil.parse(day + " " + finalI + ":59:59")))
                                .mapToLong(OrdOrder::getPayPrice).sum();
                        resultMap.put(i + ":00", count);
                    }
                } else {
                    for (int i = 0; !DateUtil.isSameDay(DateUtil.offsetDay(bo.getStartDate(), i - 1), bo.getEndDate()); i++) {
                        int finalI = i;
                        long count = orders.stream()
                                .filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.FINISH.getCode()))
                                .filter(e -> e.getFinishTime() != null && DateUtil.isSameDay(e.getFinishTime(), DateUtil.offsetDay(bo.getStartDate(), finalI)))
                                .mapToLong(OrdOrder::getPayPrice).sum();
                        resultMap.put(DateUtils.parseDateToStr("MM-dd", DateUtil.offsetDay(bo.getStartDate(), finalI)), count);
                    }
                }
            } else if (Objects.equals(bo.getLineType(), "1")) {
                if (DateUtil.betweenDay(bo.getStartDate(), bo.getEndDate(),  false) == 0) {
                    String day = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, bo.getStartDate());
                    for (int i = 0; i <= Integer.parseInt(hour) + 1; i++) {
                        int finalI = i;
                        long count = orders.stream()
                                .filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.FINISH.getCode()))
                                .filter(e -> e.getFinishTime() != null && e.getFinishTime().after(DateUtil.parse(day + " " + finalI + ":00:00"))
                                        && e.getFinishTime().before(DateUtil.parse(day + " " + finalI + ":59:59")))
                                .count();
                        resultMap.put(i + ":00", count);
                    }
                } else {
                    for (int i = 0; !DateUtil.isSameDay(DateUtil.offsetDay(bo.getStartDate(), i - 1), bo.getEndDate()); i++) {
                        int finalI = i;
                        long count = orders.stream()
                                .filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.FINISH.getCode()))
                                .filter(e -> e.getFinishTime() != null && DateUtil.isSameDay(e.getFinishTime(), DateUtil.offsetDay(bo.getStartDate(), finalI)))
                                .count();
                        resultMap.put(DateUtils.parseDateToStr("MM-dd", DateUtil.offsetDay(bo.getStartDate(), finalI)), count);
                    }
                }
            } else if (Objects.equals(bo.getLineType(), "2")) {
                if (DateUtil.betweenDay(bo.getStartDate(), bo.getEndDate(),  false) == 0) {
                    String day = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, bo.getStartDate());
                    for (int i = 0; i <= Integer.parseInt(hour) + 1; i++) {
                        int finalI = i;
                        long count = orders.stream()
                                .filter(e -> Objects.equals(e.getPayStatus(), PaymentStatusEnum.SUCCESS.getCode()))
                                .filter(e -> e.getPayTime() != null && e.getPayTime().after(DateUtil.parse(day + " " + finalI + ":00:00"))
                                        && e.getPayTime().before(DateUtil.parse(day + " " + finalI + ":59:59")))
                                .mapToLong(OrdOrder::getPayPrice).sum();
                        resultMap.put(i + ":00", count);
                    }
                } else {
                    for (int i = 0; !DateUtil.isSameDay(DateUtil.offsetDay(bo.getStartDate(), i - 1), bo.getEndDate()); i++) {
                        int finalI = i;
                        long count = orders.stream()
                                .filter(e -> Objects.equals(e.getPayStatus(), PaymentStatusEnum.SUCCESS.getCode()))
                                .filter(e -> e.getPayTime() != null && DateUtil.isSameDay(e.getPayTime(), DateUtil.offsetDay(bo.getStartDate(), finalI)))
                                .mapToLong(OrdOrder::getPayPrice).sum();
                        resultMap.put(DateUtils.parseDateToStr("MM-dd", DateUtil.offsetDay(bo.getStartDate(), finalI)), count);
                    }
                }
            } else if (Objects.equals(bo.getLineType(), "3")) {
                if (DateUtil.betweenDay(bo.getStartDate(), bo.getEndDate(),  false) == 0) {
                    String day = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, bo.getStartDate());
                    for (int i = 0; i <= Integer.parseInt(hour) + 1; i++) {
                        int finalI = i;
                        long count = orders.stream()
                                .filter(e -> Objects.equals(e.getPayStatus(), PaymentStatusEnum.SUCCESS.getCode()))
                                .filter(e -> e.getPayTime() != null &&e.getPayTime().after(DateUtil.parse(day + " " + finalI + ":00:00"))
                                        && e.getPayTime().before(DateUtil.parse(day + " " + finalI + ":59:59")))
                                .count();
                        resultMap.put(i + ":00", count);
                    }
                } else {
                    for (int i = 0; !DateUtil.isSameDay(DateUtil.offsetDay(bo.getStartDate(), i - 1), bo.getEndDate()); i++) {
                        int finalI = i;
                        long count = orders.stream()
                                .filter(e -> Objects.equals(e.getPayStatus(), PaymentStatusEnum.SUCCESS.getCode()))
                                .filter(e -> e.getPayTime() != null &&DateUtil.isSameDay(e.getPayTime(), DateUtil.offsetDay(bo.getStartDate(), finalI)))
                                .count();
                        resultMap.put(DateUtils.parseDateToStr("MM-dd", DateUtil.offsetDay(bo.getStartDate(), finalI)), count);
                    }
                }
            } else if (Objects.equals(bo.getLineType(), "5")) {
                // 接完率：已完成+行程中+到达上车点+前往上车点 / 接单数
                if (DateUtil.betweenDay(bo.getStartDate(), bo.getEndDate(),  false) == 0) {
                    String day = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, bo.getStartDate());
                    for (int i = 0; i <= Integer.parseInt(hour) + 1; i++) {
                        int finalI = i;
                        long pickCount = orders.stream()
                                .filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.PICK.getCode()))
                                .filter(e -> e.getPickTime() != null && e.getPickTime().after(DateUtil.parse(day + " " + finalI + ":00:00"))
                                        && e.getPickTime().before(DateUtil.parse(day + " " + finalI + ":59:59")))
                                .count();
                        long pickStartCount = orders.stream()
                                .filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.PICK_START.getCode()))
                                .filter(e -> e.getArrivalTime() != null && e.getArrivalTime().after(DateUtil.parse(day + " " + finalI + ":00:00"))
                                        && e.getArrivalTime().before(DateUtil.parse(day + " " + finalI + ":59:59")))
                                .count();
                        long ingCount = orders.stream()
                                .filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.ING.getCode()))
                                .filter(e -> e.getTripStartTime() != null && e.getTripStartTime().after(DateUtil.parse(day + " " + finalI + ":00:00"))
                                        && e.getTripStartTime().before(DateUtil.parse(day + " " + finalI + ":59:59")))
                                .count();
                        long finishCount = orders.stream()
                                .filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.FINISH.getCode()))
                                .filter(e -> e.getFinishTime() != null && e.getFinishTime().after(DateUtil.parse(day + " " + finalI + ":00:00"))
                                        && e.getFinishTime().before(DateUtil.parse(day + " " + finalI + ":59:59")))
                                .count();
                        long acceptCount = orders.stream()
                                .filter(e -> e.getDriverId() != null && e.getDriverId() > 0)
                                .filter(e -> e.getDispatchTime() != null && e.getDispatchTime().after(DateUtil.parse(day + " " + finalI + ":00:00"))
                                        && e.getDispatchTime().before(DateUtil.parse(day + " " + finalI + ":59:59")))
                                .count();
                        resultMap.put(i + ":00", ordStatisticHelper.divide(pickCount + pickStartCount + ingCount + finishCount, acceptCount));
                    }
                } else {
                    for (int i = 0; !DateUtil.isSameDay(DateUtil.offsetDay(bo.getStartDate(), i - 1), bo.getEndDate()); i++) {
                        int finalI = i;
                        long pickCount = orders.stream()
                                .filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.PICK.getCode()))
                                .filter(e -> e.getPickTime() != null && DateUtil.isSameDay(e.getPickTime(), DateUtil.offsetDay(bo.getStartDate(), finalI)))
                                .count();
                        long pickStartCount = orders.stream()
                                .filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.PICK_START.getCode()))
                                .filter(e -> e.getArrivalTime() != null && DateUtil.isSameDay(e.getArrivalTime(), DateUtil.offsetDay(bo.getStartDate(), finalI)))
                                .count();
                        long ingCount = orders.stream()
                                .filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.ING.getCode()))
                                .filter(e -> e.getTripStartTime() != null && DateUtil.isSameDay(e.getTripStartTime(), DateUtil.offsetDay(bo.getStartDate(), finalI)))
                                .count();
                        long finishCount = orders.stream()
                                .filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.FINISH.getCode()))
                                .filter(e -> e.getFinishTime() != null && DateUtil.isSameDay(e.getFinishTime(), DateUtil.offsetDay(bo.getStartDate(), finalI)))
                                .count();
                        long acceptCount = orders.stream()
                                .filter(e -> e.getDriverId() != null && e.getDriverId() > 0)
                                .filter(e -> e.getDispatchTime() != null && DateUtil.isSameDay(e.getDispatchTime(), DateUtil.offsetDay(bo.getStartDate(), finalI)))
                                .count();
                        resultMap.put(DateUtils.parseDateToStr("MM-dd", DateUtil.offsetDay(bo.getStartDate(), finalI)),
                                ordStatisticHelper.divide(pickCount + pickStartCount + ingCount + finishCount, acceptCount));
                    }
                }
            } else if (Objects.equals(bo.getLineType(), "6")) {
                if (DateUtil.betweenDay(bo.getStartDate(), bo.getEndDate(),  false) == 0) {
                    String day = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, bo.getStartDate());
                    for (int i = 0; i <= Integer.parseInt(hour) + 1; i++) {
                        int finalI = i;
                        long count = orders.stream()
                                .filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.CANCEL.getCode()))
                                .filter(e -> e.getCancelTime() != null && e.getCancelTime().after(DateUtil.parse(day + " " + finalI + ":00:00"))
                                        && e.getCancelTime().before(DateUtil.parse(day + " " + finalI + ":59:59")))
                                .count();
                        resultMap.put(i + ":00", count);
                    }
                } else {
                    for (int i = 0; !DateUtil.isSameDay(DateUtil.offsetDay(bo.getStartDate(), i - 1), bo.getEndDate()); i++) {
                        int finalI = i;
                        long count = orders.stream()
                                .filter(e -> Objects.equals(e.getStatus(), OrderStatusEnum.CANCEL.getCode()))
                                .filter(e -> e.getCancelTime() != null && DateUtil.isSameDay(e.getCancelTime(), DateUtil.offsetDay(bo.getStartDate(), finalI)))
                                .count();
                        resultMap.put(DateUtils.parseDateToStr("MM-dd", DateUtil.offsetDay(bo.getStartDate(), finalI)), count);
                    }
                }
            }
        }

        return resultMap;
    }

    private LambdaQueryWrapper<OrdStatistic> buildQueryWrapper(OrdStatisticBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrdStatistic> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPlatformCode()), OrdStatistic::getPlatformCode, bo.getPlatformCode());
        lqw.eq(bo.getAgentId() != null, OrdStatistic::getAgentId, bo.getAgentId());
        lqw.between(params.get("beginStatisticDate") != null && params.get("endStatisticDate") != null,
            OrdStatistic::getStatisticDate ,params.get("beginStatisticDate"), params.get("endStatisticDate"));
        return lqw;
    }

    /**
     * 新增订单统计
     *
     * @param bo 订单统计
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OrdStatisticBo bo) {
        OrdStatistic add = MapstructUtils.convert(bo, OrdStatistic.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改订单统计
     *
     * @param bo 订单统计
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OrdStatisticBo bo) {
        OrdStatistic update = MapstructUtils.convert(bo, OrdStatistic.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrdStatistic entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除订单统计信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 订单统计
     *
     * @param statisticDate 统计时间
     */
    @Override
    public void doStatistic(String statisticDate) {
        String startTime = statisticDate + " 00:00:00";
        String endTime = statisticDate + " 23:59:59";
        List<OrdStatistic> ordStatistics = this.doStatisticOrder(startTime, endTime, null, null);

        if (CollUtils.isNotEmpty(ordStatistics)) {
            ordStatistics.forEach(e -> {
                e.setStatisticDate(DateUtils.parseDate(statisticDate));
                e.setTenantId(Constants.TENANT_ID);
            });
            baseMapper.insertBatch(ordStatistics);
        }
    }

    public List<OrdStatistic> doStatisticOrder(String startTime, String endTime, String platformCode, Long agentId) {
        List<OrdStatistic> ordStatistics = ordStatisticHelper.statisticVolume(startTime, endTime, platformCode, agentId);

        // 查询代理商，并按照代理id进行分组
        Map<Long, String> agentId2NameMap = remoteAgentService.getAllAgentInfo()
                .stream().collect(Collectors.toMap(RemoteAgentVo::getId, RemoteAgentVo::getCompanyName, (v1, v2) -> v2));

        // 查询线路，并按照线路id进行分组
        Map<Long, String> lineId2NameMap = remoteLineService.getAll()
                .stream().collect(Collectors.toMap(RemoteLineVo::getId, RemoteLineVo::getName, (v1, v2) -> v2));

        // 按照平台+代理+线路进行分组
        Map<String, OrdStatistic> ordStatisticMap = ordStatistics.stream().collect(Collectors.toMap(e -> e.getPlatformCode() + "#" + e.getAgentId() + "#" + e.getLineId(), Function.identity(), (v1, v2) -> v2));

        LambdaQueryWrapper<OrdOrder> lqw = new LambdaQueryWrapper<>();
        lqw.between(OrdOrder::getEarliestTime, startTime, endTime)
                .eq(StringUtils.isNotBlank(platformCode), OrdOrder::getPlatformCode, platformCode)
                .eq(agentId != null, OrdOrder::getAgentId, agentId);
        List<OrdOrder> orders = ordOrderMapper.selectList(lqw);
        if (CollUtils.isNotEmpty(orders)) {
            // 将订单按照平台进行分组
            Map<String, List<OrdOrder>> platformCode2ListMap = orders.stream().collect(Collectors.groupingBy(OrdOrder::getPlatformCode));
            platformCode2ListMap.forEach((platform, orderList) -> {
                // 将订单按照代理进行分组
                Map<Long, List<OrdOrder>> agentId2ListMap = orderList.stream()
                        .filter(e -> e.getAgentId() != null && e.getAgentId() != 0)
                        .collect(Collectors.groupingBy(OrdOrder::getAgentId));
                agentId2ListMap.forEach((agent, agentOrders) -> {
                    // 将订单按照路线进行分组
                    Map<Long, List<OrdOrder>> driverId2ListMap = agentOrders.stream()
                            .filter(e -> e.getLineId() != null && e.getLineId() != 0)
                            .collect(Collectors.groupingBy(OrdOrder::getLineId));
                    driverId2ListMap.forEach((lineId, lineOrders) -> {
                        OrdStatistic ordStatistic = new OrdStatistic();
                        if (ordStatisticMap.containsKey(platform + "#" + agent +  "#" + lineId)) {
                            ordStatistic = ordStatisticMap.get(platform + "#" + agent + "#" + lineId);
                        } else {
                            ordStatistic.setPlatformCode(platform);
                            ordStatistic.setAgentId(agent);
                            ordStatistic.setLineId(lineId);
                            ordStatistic.setVolumeNumber(0L);
                            ordStatistics.add(ordStatistic);
                        }
                        ordStatistic.setAgentName(agentId2NameMap.getOrDefault(agent, ""));
                        ordStatistic.setLineName(lineId2NameMap.getOrDefault(lineId, ""));

                        ordStatisticHelper.statisticOrder(lineOrders, ordStatistic);
                    });

                    // 没有路线订单
                    List<OrdOrder> noLineOrders = agentOrders.stream().filter(e -> e.getLineId() == null || e.getLineId() == 0).collect(Collectors.toList());
                    if (CollUtils.isNotEmpty(noLineOrders)) {
                        OrdStatistic ordStatistic = new OrdStatistic();
                        if (ordStatisticMap.containsKey(platform + "#" + agent +  "#" + 0)) {
                            ordStatistic = ordStatisticMap.get(platform + "#" + agent + "#" + 0);
                        } else {
                            ordStatistic.setPlatformCode(platform);
                            ordStatistic.setAgentId(agent);
                            ordStatistic.setLineId(0L);
                            ordStatistic.setVolumeNumber(0L);
                            ordStatistics.add(ordStatistic);
                        }
                        ordStatistic.setAgentName(agentId2NameMap.getOrDefault(agent, ""));
                        ordStatistic.setLineName("其他");
                        ordStatisticHelper.statisticOrder(noLineOrders, ordStatistic);
                    }
                });
            });
        }

        return ordStatistics;
    }
}
