package com.feidi.xx.cross.order.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.order.domain.OrdOrderInfo;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 订单信息业务对象 ord_order_info
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrdOrderInfo.class, reverseConvertGenerate = false)
public class OrdOrderInfoBo extends TenantEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 司机单号
     */
    private String driverNo;

    /**
     * 基础金额
     */
    private Long basePrice;

    /**
     * 附加费金额
     */
    private Long addPrice;

    /**
     * 技术服务费
     */
    private Long serviceFee;

    /**
     * 取消费
     */
    private Long cancelFee;

    /**
     * 投诉扣款金额
     */
    private Long complainPrice;

    /**
     * 司乘虚拟号
     */
    private String virtualPhone;

    /**
     * 调度虚拟号
     */
    private String virtualDispatch;

    /**
     * 乘客电话尾号
     */
    private String phoneEnd;

    /**
     * 服务时长
     */
    private String duration;

    /**
     * 取消人ID
     */
    private String cancelUserId;

    /**
     * 取消人
     */
    private String cancelUserName;

    /**
     * 取消备注
     */
    private String cancelRemark;

    /**
     * 客诉时间
     */
    private Date complainTime;

    /**
     * 客诉类型[ComplainTypeEnum]
     */
    private String complainType;

    /**
     * 客诉备注
     */
    private String complainRemark;

    /**
     * 保险平台[InsurePlatformEnum]
     */
    private String insurePlatform;

    /**
     * 保险编码
     */
    private String insureCode;

    /**
     * 投保时间
     */
    private Date insureTime;

    /**
     * 取消投保时间
     */
    private Date cancelInsureTime;

    /**
     * 取消投保原因[cancelInsureReasonEnum]
     */
    private String cancelInsureReason;

    /**
     * 开始经度
     */
    private Double startLongitude;

    /**
     * 开始纬度
     */
    private Double startLatitude;

    /**
     * 结束经度
     */
    private Double endLongitude;

    /**
     * 结束纬度
     */
    private Double endLatitude;

    /**
     * 资金流向[OrderFlowTypeEnum]
     */
    private String flow;

    /**
     * 优惠券发放id
     */
    private Long couponGrantId;

    /**
     * 优惠券额度
     */
    private Long couponGrantQuota;

    /**
     * 乘客详情
     */
    private String passengerDetail;

}
