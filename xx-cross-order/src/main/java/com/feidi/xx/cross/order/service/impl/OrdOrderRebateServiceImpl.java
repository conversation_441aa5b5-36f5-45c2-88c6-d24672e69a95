package com.feidi.xx.cross.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.feidi.xx.common.core.enums.*;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.SpringUtils;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.cache.power.manager.PowCacheManager;
import com.feidi.xx.cross.common.enums.finance.FlowTypeEnum;
import com.feidi.xx.cross.common.enums.order.OperateTypeEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.enums.order.RateTypeEnum;
import com.feidi.xx.cross.common.enums.order.RebateStatusEnum;
import com.feidi.xx.cross.common.mq.event.OrdOrderOperateEvent;
import com.feidi.xx.cross.common.mq.producer.OrdOrderOperateProducer;
import com.feidi.xx.cross.finance.api.RemoteDrvWalletService;
import com.feidi.xx.cross.finance.api.RemoteFlowService;
import com.feidi.xx.cross.finance.api.domain.bo.RemoteFlowBo;
import com.feidi.xx.cross.finance.api.domain.bo.RemoteRebateBo;
import com.feidi.xx.cross.finance.api.domain.vo.RemoteFlowVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDriverVo;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.OrdRate;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.service.IOrdOrderRebateService;
import com.feidi.xx.cross.order.service.IOrdRateService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单结算Service接口实现类
 *
 * <AUTHOR>
 * @date 2025-02-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrdOrderRebateServiceImpl implements IOrdOrderRebateService {

    private final IOrdRateService ordRateService;
    private final PowCacheManager powCacheManager;
    private final OrdCacheManager ordCacheManager;
    private final OrdOrderMapper ordOrderMapper;
    private final OrdOrderOperateProducer ordOrderOperateProducer;
    @DubboReference
    private final RemoteFlowService remoteFlowService;
    @DubboReference
    private final RemoteDrvWalletService remoteDrvWalletService;

    /**
     * 批量结算订单
     * @param driverId 司机id
     * @param orders 订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public void batchRebate(Long driverId, List<OrdOrder> orders) {
        if (log.isInfoEnabled()) {
            log.info("订单返利结算开始 - 司机ID: 【{}】", driverId);
        }
        Map<Long, OrdOrder> orderMap = StreamUtils.toMap(orders, OrdOrder::getId, Function.identity());
        // 相关订单的流水记录判断是否已进行结算
        Map<Long, List<RemoteFlowVo>> flowMap = getFlowMap(driverId, orders);
        // 移除已经有过结算记录的
        orderMap.keySet().removeAll(flowMap.keySet());
        // 有效的待结算订单
        List<OrdOrder> validOrders = new ArrayList<>(orderMap.values());
        if (CollUtil.isNotEmpty(validOrders)) {
            List<Long> validOrderIds = validOrders.stream().map(OrdOrder::getId).toList();
            // 司机收益
            List<OrdRate> ordRates = ordRateService.queryByOrderIdsAndRateType(validOrderIds, RateTypeEnum.DRIVER.getCode());
            // 邀请司机收益
            //List<OrdRate> inviteDriverRates = ordRateService.queryByOrderIdsAndRateType(validOrderIds, RateTypeEnum.INVITE_DRIVER.getCode());
            // 更新未结算的订单的流水状态为已入账
            updateFlow(driverId, validOrders);

            // 邀请有奖收益不参与订单结算，司机邀请司机收益直接入账
            //updateInviteFlow(driverId, validOrders);
            // 更新订单为已结算
            updateOrder(validOrders);
            // 总结算收益
            Long totalProfit = StreamUtils.sumLong(ordRates, OrdRate::getAmount);
            //Long totalInviteProfit = StreamUtils.sumLong(inviteDriverRates, OrdRate::getAmount);
            //Long totalProfit = totalOrderProfit + totalInviteProfit;
            if (totalProfit > 0L) {
                // 记录司机可结算订单的总利润流水
                OrdOrder order = validOrders.get(0);
                RemoteRebateBo rebateBo = createRemoteRebateVo(order);
                rebateBo.setDriverProfit(totalProfit);
                // 将订单id保存到备注中
                List<Long> orderIds = StreamUtils.toList(validOrders, OrdOrder::getId);
                rebateBo.setRelatedOrder(orderIds);
                rebateBo.setRemark(JsonUtils.toJsonString(orderIds));
                remoteDrvWalletService.insertTotalProfitFlow(rebateBo);
            }
            if (log.isInfoEnabled()) {
                log.info("订单返利结算结束 - 司机ID: 【{}】， 司机总收益：【{}】", driverId, totalProfit);
            }
        } else {
            if (log.isInfoEnabled()) {
                log.info("订单返利结算结束 - 司机ID: 【{}】， 无可结算订单", driverId);
            }
        }
    }

    /**
     * 批量结算转卖订单
     *
     * @param driverId 司机id
     * @param orders 订单集合
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void batchRebateForResell(Long driverId, List<OrdOrder> orders) {
        if (log.isInfoEnabled()) {
            log.info("转卖订单返利结算开始 - 司机ID: 【{}】", driverId);
        }
        Map<Long, OrdOrder> orderMap = StreamUtils.toMap(orders, OrdOrder::getId, Function.identity());
        // 相关订单的流水记录判断是否已进行结算
        Map<Long, List<RemoteFlowVo>> flowMap = getResellFlowMap(driverId, orders);
        // 移除已经有过结算记录的
        orderMap.keySet().removeAll(flowMap.keySet());
        // 有效的待结算订单
        List<OrdOrder> validOrders = new ArrayList<>(orderMap.values());
        if (CollUtil.isNotEmpty(validOrders)) {
            List<Long> validOrderIds = validOrders.stream().map(OrdOrder::getId).toList();
            // 司机收益
            List<OrdRate> ordRates = ordRateService.queryByOrderIdsAndRateType(validOrderIds, RateTypeEnum.RESELL_DRIVER.getCode());
            // 更新未结算的订单的流水状态为已入账
            updateFlowForResell(driverId, validOrders);

            // 更新订单为已结算
            updateOrderForResell(validOrders);
            // 总结算收益
            Long totalProfit = StreamUtils.sumLong(ordRates, OrdRate::getAmount);
            if (totalProfit > 0L) {
                // 记录司机可结算订单的总利润流水
                OrdOrder order = validOrders.get(0);
                RemoteRebateBo rebateBo = createRemoteResellRebate(order);
                rebateBo.setResellDriverProfit(totalProfit);
                // 将订单id保存到备注中
                List<Long> orderIds = StreamUtils.toList(validOrders, OrdOrder::getId);
                rebateBo.setRelatedOrder(orderIds);
                rebateBo.setRemark(JsonUtils.toJsonString(orderIds));
                remoteDrvWalletService.insertTotalProfitFlowForResell(rebateBo);
            }
            if (log.isInfoEnabled()) {
                log.info("订单转卖返利结算结束 - 司机ID: 【{}】， 司机总收益：【{}】", driverId, totalProfit);
            }
        } else {
            if (log.isInfoEnabled()) {
                log.info("订单转卖返利结算结束 - 司机ID: 【{}】， 无可结算订单", driverId);
            }
        }
    }

    /**
     * 根据订单号批量结算订单
     *
     * @param orderNos
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional(rollbackFor = Exception.class)
    public Boolean orderRebateByOrderNos(String orderNos) {
        List<String> orderNoList = Arrays.stream(orderNos.split(",")).toList();

        // 获取可以返利的订单
        LambdaQueryWrapper<OrdOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrdOrder::getStatus, OrderStatusEnum.FINISH.getCode())
                .eq(OrdOrder::getRebateStatus, RebateStatusEnum.ING.getCode())
                .eq(OrdOrder::getDispatch, IsYesEnum.YES.getCode())
                .gt(OrdOrder::getDriverId, 0)
                .in(OrdOrder::getOrderNo, orderNoList);

        // 获取数据
        List<OrdOrder> orders = ordOrderMapper.selectList(queryWrapper);

        if (log.isInfoEnabled()) {
            log.info("订单返利数量：【{}】", orders.size());
        }
        if (ObjectUtils.isNotNull(orders) ) {
            List<Long> orderIds = orders.stream().map(OrdOrder::getId).toList();
            if (log.isInfoEnabled()) {
                log.info("订单返利数据id：【{}】", JsonUtils.toJsonString(orderIds));
            }

            Map<Long, List<OrdOrder>> dirverId2OrderMap = orders.stream().collect(Collectors.groupingBy(OrdOrder::getDriverId));
            IOrdOrderRebateService ordOrderRebateService = SpringUtils.getBean(IOrdOrderRebateService.class);

            // 订单结算
            dirverId2OrderMap.forEach(ordOrderRebateService::batchRebate);
        }
        return true;
    }

    /**
     * 创建订单返利对象
     *
     * @param order 订单信息
     * @return 订单返利对象
     */
    @Override
    public RemoteRebateBo createRemoteRebateVo(OrdOrder order) {
        // 代理商信息
        RemoteAgentVo agentVo = powCacheManager.getAgentInfoById(order.getAgentId());
        // 订单司机信息
        RemoteOrderDriverVo orderDriverVo = ordCacheManager.getOrderDriverInfoByOrderId(order.getId());

        // 司机收益
        OrdRate driverRate = ordRateService.queryByOrderIdAndRateType(order.getId(), RateTypeEnum.DRIVER.getCode());

        // 邀请司机收益
        OrdRate inviteDriverRate = null;
        if (ObjectUtils.isNotNull(order.getInviteDriverId()) && order.getInviteDriverId() > 0) {
            inviteDriverRate = ordRateService.queryByOrderIdAndRateType(order.getId(), RateTypeEnum.INVITE_DRIVER.getCode());
        }

        return new RemoteRebateBo()
                .setTenantId(order.getTenantId())
                .setOrderId(order.getId())
                .setOrderNo(order.getOrderNo())
                .setAgentId(ObjectUtils.isNotNull(agentVo) ? order.getAgentId() : 0)
                .setParentId(ObjectUtils.isNotNull(agentVo) ? agentVo.getParentId() : 0)
                .setDriverId(order.getDriverId())
                .setDriverName(orderDriverVo.getDriverName())
                .setDriverPhone(orderDriverVo.getDriverPhone())
                .setOrderPrice(order.getOrderPrice())
                .setDriverRate(driverRate.getRate())
                .setDriverProfit(driverRate.getAmount())
                .setInviteRate(ObjectUtils.isNotNull(inviteDriverRate) ? inviteDriverRate.getRate() : null)
                .setInviteProfit(ObjectUtils.isNotNull(inviteDriverRate) ? inviteDriverRate.getAmount() : 0L)
                .setRebateStatus(order.getRebateStatus())
                .setInviteAgentId(order.getInviteAgentId())
                .setInviteDriverId(order.getInviteDriverId());
    }

    public RemoteRebateBo createRemoteResellRebate(OrdOrder order) {

        RemoteDriverVo driverVo = powCacheManager.getDriverInfoById(order.getResellDriverId());

        return new RemoteRebateBo()
                .setTenantId(order.getTenantId())
                .setOrderId(order.getId())
                .setOrderNo(order.getOrderNo())
                .setResellAgentId(order.getResellAgentId())
                .setResellDriverId(order.getResellDriverId())
                .setDriverName(driverVo != null ? driverVo.getName() : "")
                .setDriverPhone(driverVo != null ? driverVo.getPhone() : "")
                .setOrderPrice(order.getOrderPrice())
                .setRebateStatus(order.getResellRebateStatus());
    }


        private Map<Long, List<RemoteFlowVo>> getFlowMap(Long driverId, List<OrdOrder> orders) {
        List<Long> orderIds = StreamUtils.toList(orders, OrdOrder::getId);
        RemoteFlowBo bo = new RemoteFlowBo();
        bo.setDriverId(driverId);
        bo.setJoinTable(JoinEnum.ORDER.getCode());
        bo.setJoinIds(orderIds);
        bo.setDirection(DirectionEnum.IN.getCode());
        bo.setType(FlowTypeEnum.ORDER_ADD.getCode());
        List<RemoteFlowVo> flows = remoteFlowService.listByBo(bo);
        return StreamUtils.groupByKey(flows, RemoteFlowVo::getJoinId);
    }

    private Map<Long, List<RemoteFlowVo>> getResellFlowMap(Long driverId, List<OrdOrder> orders) {
        List<Long> orderIds = StreamUtils.toList(orders, OrdOrder::getId);
        RemoteFlowBo bo = new RemoteFlowBo();
        bo.setDriverId(driverId);
        bo.setJoinTable(JoinEnum.ORDER.getCode());
        bo.setJoinIds(orderIds);
        bo.setDirection(DirectionEnum.IN.getCode());
        bo.setType(FlowTypeEnum.RESELL_ORDER_ADD.getCode());
        List<RemoteFlowVo> flows = remoteFlowService.listByBo(bo);
        return StreamUtils.groupByKey(flows, RemoteFlowVo::getJoinId);
    }

    // 变更原来的流水中的入账状态 入账中 -> 已入账
    private void updateFlow(Long driverId, Collection<OrdOrder> orders) {
        List<Long> orderIds = StreamUtils.toList(orders, OrdOrder::getId);
        RemoteFlowBo queryBo = new RemoteFlowBo();
        queryBo.setDriverId(driverId);
        queryBo.setJoinTable(JoinEnum.ORDER.getCode());
        queryBo.setJoinIds(orderIds);
        queryBo.setDirection(DirectionEnum.IN.getCode());

        RemoteFlowBo updateBo = new RemoteFlowBo();
        updateBo.setUpdateBy(null);
        updateBo.setUpdateTime(new Date());

        // 订单
        queryBo.setType(FlowTypeEnum.ORDER_FREEZE_ADD.getCode());
        updateBo.setType(FlowTypeEnum.ORDER_ADD.getCode());
        remoteFlowService.updateByBo(updateBo, queryBo);
    }

    /**
     * 更新订单转卖流水
     *
     * @param driverId
     * @param orders
     */
    private void updateFlowForResell(Long driverId, Collection<OrdOrder> orders) {
        List<Long> orderIds = StreamUtils.toList(orders, OrdOrder::getId);
        RemoteFlowBo queryBo = new RemoteFlowBo();
        queryBo.setDriverId(driverId);
        queryBo.setJoinTable(JoinEnum.ORDER.getCode());
        queryBo.setJoinIds(orderIds);
        queryBo.setDirection(DirectionEnum.IN.getCode());

        RemoteFlowBo updateBo = new RemoteFlowBo();
        updateBo.setUpdateBy(null);
        updateBo.setUpdateTime(new Date());

        // 订单
        queryBo.setType(FlowTypeEnum.RESELL_ORDER_FREEZE_ADD.getCode());
        updateBo.setType(FlowTypeEnum.RESELL_ORDER_ADD.getCode());
        remoteFlowService.updateByBo(updateBo, queryBo);
    }

    private void updateOrder(Collection<OrdOrder> orders) {
        if (CollUtil.isNotEmpty(orders)) {
            List<Long> orderIds = StreamUtils.toList(orders, OrdOrder::getId);
            // 更新订单状态
            LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(OrdOrder::getRebateStatus, RebateStatusEnum.FINISH.getCode())
                    .set(OrdOrder::getRebateTime, DateUtils.getNowDate())
                    .in(OrdOrder::getId, orderIds);
            int update = ordOrderMapper.update(updateWrapper);
            if (update > 0) {
                // 异步记录日志
                orders.parallelStream().forEach(e -> this.publishRebateEvent(e, OperateTypeEnum.REBATE.getCode()));
            }
        }
    }

    /**
     * 批量更新订单转卖
     *
     * @param orders
     */
    private void updateOrderForResell(Collection<OrdOrder> orders) {
        if (CollUtil.isNotEmpty(orders)) {
            List<Long> orderIds = StreamUtils.toList(orders, OrdOrder::getId);
            // 更新订单状态
            LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(OrdOrder::getResellRebateStatus, RebateStatusEnum.FINISH.getCode())
                    .set(OrdOrder::getResellRebateTime, DateUtils.getNowDate())
                    .in(OrdOrder::getId, orderIds);
            int update = ordOrderMapper.update(updateWrapper);
            if (update > 0) {
                // 异步记录日志
                orders.parallelStream().forEach(e -> this.publishRebateEvent(e, OperateTypeEnum.RESELL_REBATE.getCode()));
            }
        }
    }

    // 添加操作记录 - 异步
    private void publishRebateEvent(OrdOrder order, String operateType) {
        OrdOrderOperateEvent orderOperateEvent = new OrdOrderOperateEvent();
        orderOperateEvent.setOrderId(order.getId());
        orderOperateEvent.setTenantId(order.getTenantId());
        orderOperateEvent.setUserType(UserTypeEnum.AUTO_USER.getUserType());
        orderOperateEvent.setUserId(0L);
        orderOperateEvent.setTimeStamp(DateUtils.getUnixTimeStamps());
        orderOperateEvent.setOperateType(operateType);
        orderOperateEvent.setStatus(SuccessFailEnum.SUCCESS.getCode());
        orderOperateEvent.setRemark("定时器完成订单结算");
        ordOrderOperateProducer.sendMessage(orderOperateEvent);

    }

}
