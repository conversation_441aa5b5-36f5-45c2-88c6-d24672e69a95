
package com.feidi.xx.cross.order.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.feidi.xx.common.idempotent.annotation.NoMQDuplicateConsume;
import com.feidi.xx.common.rocketmq.base.MessageWrapper;

import com.feidi.xx.cross.common.constant.order.OrderRocketMQConstant;
import com.feidi.xx.cross.order.mq.event.InviteEvent;
import com.feidi.xx.cross.order.service.IOrdOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 数据上报消息消费者
 * <p>
 * 开发时间：2024-09-10
 */
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = OrderRocketMQConstant.XX_ORDER_INVITE_REWARD_DELAY_TOPIC,
        consumerGroup = OrderRocketMQConstant.XX_ORDER_INVITE_REWARD_DELAY_CG_KEY
)
@Slf4j(topic = "OrderConsumer")
public class OrderInviteRewardConsumer implements RocketMQListener<MessageWrapper<InviteEvent>> {

    private  final IOrdOrderService orderService;
    @NoMQDuplicateConsume(
            keyPrefix = "global:xx-cross-order:",
            key = "#messageWrapper.keys",
            keyTimeout = 600
    )
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void onMessage(MessageWrapper<InviteEvent> messageWrapper) {
        InviteEvent message = messageWrapper.getMessage();
        //根据id更新是否展示
        orderService.updateShowById(message.getOrderId());
        // 开头打印日志，平常可 Debug 看任务参数，线上可报平安（比如消息是否消费，重新投递时获取参数等）
        log.info("[消费者] 订单邀请有奖 - 执行消费逻辑，消息体：{}", JSON.toJSONString(messageWrapper));
    }
}
