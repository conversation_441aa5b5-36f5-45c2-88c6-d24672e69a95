package com.feidi.xx.cross.order.domain.bo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.cross.common.annotations.EnumValue;
import com.feidi.xx.cross.common.enums.operate.ProductCodeEnum;
import jakarta.validation.constraints.*;
import lombok.Data;

import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@ExcelIgnoreUnannotated
@Data
public class AgtOrderImportBo {

    @NotBlank(message = "起点不能为空")
    @ExcelProperty(value = "起点", index = 0)
    private String startAddress;

    @NotBlank(message = "终点不能为空")
    @ExcelProperty(value = "终点", index = 1)
    private String endAddress;

    @NotNull(message = "出发时间不能为空")
    @Pattern(message = "出发时间正确格式为 yyyy/MM/dd HH:mm", regexp = "^(19|20)\\d{2}[-/](0?[1-9]|1[0-2])[-/](0?[1-9]|[12]\\d|3[01]) ([01]?\\d|2[0-3]):([0-5]\\d)$")
    //@Pattern(message = "出发时间正确格式为 yyyy/MM/dd HH:mm", regexp = "^(19|20)\\d{2}/(0[1-9]|1[0-2])/(0[1-9]|[12][0-9]|3[01]) (0[0-9]|1[0-9]|2[0-3]):([0-5][0-9])$")
    @ExcelProperty(value = "出发时间", index = 2)
    private String startTime;

    @NotNull(message = "乘客人数不能为空")
    @Min(message = "乘车人数应为1-6的数字", value = 1)
    @Max(message = "乘车人数应为1-6的数字", value = 6)
    @ExcelProperty(value = "乘客人数", index = 3)
    private Integer passengerNums;

    @NotBlank(message = "订单类型不能为空")
    @EnumValue(message = "订单类型仅支持“拼车”或“独享”", value = ProductCodeEnum.class)
    @ExcelProperty(value = "订单类型", index = 4)
    private String orderType;

    @NotNull(message = "订单金额不能为空")
    @Min(message = "订单价格仅支持1-10000之间的数字", value = 1)
    @Max(message = "订单价格仅支持1-10000之间的数字", value = 10000)
    @ExcelProperty(value = "订单金额", index = 5)
    private BigDecimal orderPrice;

    @NotNull(message = "乘客手机号不能为空")
    @Pattern(message = "乘客手机号格式不正确", regexp = "^1[3456789]\\d{9}$")
    @ExcelProperty(value = "乘客手机号", index = 6)
    private String phone;

    @Length(message = "备注内容不能超过100个字符限制", max = 100)
    @ExcelProperty(value = "备注", index = 7)
    private String remark;

    //愿等时间
    private Integer waitTime;

    //询价key
    private String estimateKey;
}
