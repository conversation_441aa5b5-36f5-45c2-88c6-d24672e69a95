package com.feidi.xx.cross.order.domain.bo;

import com.feidi.xx.cross.order.domain.OrdOrder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单价格处理BO
 */
@Data
public class OrdOrderFineBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long orderId;

    private Long orderPrice;

    private Long driverProfit;

    private Long agentProfit;

    private Long parentAgentProfit;

    /**
     * 司机佣金比例
     */
    private BigDecimal driverRate;

    /**
     * 代理商佣金比例 注意是BigDecimal类型
     */
    private BigDecimal agentRate;

    /**
     * 父代理商佣金比例 注意是BigDecimal类型
     */
    private BigDecimal parentAgentRate;

    public OrdOrderFineBo(OrdOrder order) {
        this.orderId = order.getId();
        this.orderPrice = order.getOrderPrice();
    }
}
