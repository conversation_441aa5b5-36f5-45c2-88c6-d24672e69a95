package com.feidi.xx.cross.order.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.order.domain.OrdOrder;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 订单业务对象 ord_order
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrdOrder.class, reverseConvertGenerate = false)
public class OrdOrderBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderNo;

    /**
     * 平台编号 
     */
    @NotBlank(message = "平台编号 不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platformCode;

    /**
     * 平台单号
     */
    @NotBlank(message = "平台单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String platformNo;

    /**
     * 出发城市
     */
    @NotBlank(message = "出发城市不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startCityCode;

    /**
     * 目的地城市
     */
    @NotBlank(message = "目的地城市不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endCityCode;

    /**
     * 线路ID
     */
    @NotNull(message = "线路ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long lineId;

    /**
     * 线路方向[StartEndEnum]
     */
    private String lineDirection;

    /**
     * 代理商id
     */
    @NotNull(message = "代理商id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long agentId;

    /**
     * 司机ID
     */
    @NotNull(message = "司机ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long driverId;

    /**
     * 产品类型[ProductTypeEnum]
     */
    @NotBlank(message = "产品类型[ProductCodeEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productCode;

    /**
     * 订单金额[基础金额+附加费金额-客诉扣款金额]
     */
    @NotBlank(message = "订单金额[基础金额+附加费金额-客诉扣款金额]不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderPrice;

    /**
     * 实际支付金额
     */
    private Long payPrice;

    /**
     * 高速类型[HighwayTypeEnum]
     */
    private String highwayType;

    /**
     * 乘客数量
     */
    @NotBlank(message = "乘客数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer passengerNum;

    /**
     * 乘客id
     */
    private Long passengerId;

    /**
     * 乘客真实手机号
     */
    private String passengerPhone;

    /**
     * 乘客备注
     */
    private String passengerRemark;

    /**
     * 订单状态[OrderStatusEnum]
     */
    @NotBlank(message = "订单状态[OrderStatusEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 第三方订单状态
     */
    private String thirdStatus;

    /**
     * 返利状态[RebateStatusEnum]
     */
    private String rebateStatus;

    /**
     * 返利时间
     */
    private Date rebateTime;

    /**
     * 支付状态[PaymentStatusEnum]
     */
    private String payStatus;

    /**
     * 支付方式[PaymentTypeEnum]
     */
    private String payMode;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 支付单号
     */
    private String payNo;

    /**
     * 预计出发时间
     */
    private Date earliestTime;

    /**
     * 最大等待时长
     */
    private Integer maxWaitDuration;

    /**
     * 预计最晚出发时间
     */
    private Date latestTime;

    /**
     * 接单时间
     */
    private Date receiveTime;

    /**
     * 接驾时间
     */
    private Date pickTime;

    /**
     * 到达乘客起点时间
     */
    private Date arrivalTime;

    /**
     * 行程开始时间
     */
    private Date tripStartTime;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 投保状态[InsureStatusEnum]
     */
    private String insureStatus;

    /**
     * 保险单号
     */
    private String insureNo;

    /**
     * 里程
     */
    private Integer mileage;

    /**
     * 预计时长
     */
    private Integer expectDuration;

    /**
     * 取消人类型[UserTypeEnum]
     */
    private String cancelUserType;

    /**
     * 取消时间
     */
    private Date cancelTime;

    /**
     * 取消类型[CancelTypeEnum]
     */
    private String cancelType;

    /**
     * 是否调度[IsYesEnum]
     */
    private String dispatch;

    /**
     * 调度时间
     */
    private Date dispatchTime;

    /**
     * 调度类型[DispatchTypeEnum]
     */
    private String dispatchType;

    /**
     * 调度状态[SuccessFailEnum]
     */
    private String dispatchStatus;

    /**
     * 是否预约[IsYesEnum]
     */
    private String due;

    /**
     * 是否被过滤[IsYesEnum]
     */
    private String filtered;

    /**
     * 是否客诉[IsYesEnum]
     */
    private String complain;

    /**
     * 标签（JSON）
     */
    private String label;

    /**
     * 完单标签（JSON）
     */
    private String finishLabel;

    /**
     * 邀请的代理商id
     */
    private Long inviteAgentId;

    /**
     * 邀请的司机id
     */
    private Long inviteDriverId;

    /**
     * 来源[SourceEnum]
     */
    private String source;

    /**
     * 下单类型[CreateModelEnum]
     */
    private String createModel;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 是否展示 [IsYesEnum]
     */
    private String showed;

    /**
     * 起点位置
     */
    private OrdPositionBo startPosition;

    /**
     * 终点位置
     */
    private OrdPositionBo endPosition;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 转卖司机ID
     */
    private Long resellDriverId;

    /**
     * 转卖代理商ID
     */
    private Long resellAgentId;

    /**
     * 转卖后司机接单金额
     */
    private Long resellDriverPrice;

    /**
     * 订单转卖返利状态[RebateStatusEnum]
     */
    private String resellRebateStatus;

    /**
     * 订单转卖返利时间
     */
    private Date resellRebateTime;

    private Long estimateId;
}
