package com.feidi.xx.cross.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.enums.SuccessFailEnum;
import com.feidi.xx.common.core.enums.UserStatusEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.common.cache.order.enums.OrdCacheKeyEnum;
import com.feidi.xx.cross.common.cache.power.manager.PowCacheManager;
import com.feidi.xx.cross.common.enums.order.DispatchTypeEnum;
import com.feidi.xx.cross.common.enums.power.DriverIdentityEnum;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDriverVo;
import com.feidi.xx.cross.order.domain.OrdDriver;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.bo.OrdDriverBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderDispatchBo;
import com.feidi.xx.cross.order.domain.vo.OrdDriverVo;
import com.feidi.xx.cross.order.mapper.OrdDriverMapper;
import com.feidi.xx.cross.order.service.IOrdDriverService;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteCarVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 订单司机Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrdDriverServiceImpl implements IOrdDriverService {

    private final PowCacheManager powCacheManager;
    private final OrdDriverMapper baseMapper;
    private final ScheduledExecutorService scheduledExecutorService;

    /**
     * 查询订单司机
     *
     * @param id 主键
     * @return 订单司机
     */
    @Override
    public OrdDriverVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询订单司机列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单司机分页列表
     */
    @Override
    public TableDataInfo<OrdDriverVo> queryPageList(OrdDriverBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrdDriver> lqw = buildQueryWrapper(bo);
        Page<OrdDriverVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的订单司机列表
     *
     * @param bo 查询条件
     * @return 订单司机列表
     */
    @Override
    public List<OrdDriverVo> queryList(OrdDriverBo bo) {
        LambdaQueryWrapper<OrdDriver> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrdDriver> buildQueryWrapper(OrdDriverBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrdDriver> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderId() != null, OrdDriver::getOrderId, bo.getOrderId());
        return lqw;
    }

    /**
     * 新增订单司机
     *
     * @param bo 订单司机
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OrdDriverBo bo) {
        OrdDriver add = MapstructUtils.convert(bo, OrdDriver.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            // 添加缓存
            scheduledExecutorService.schedule(() -> addCache(add), 0, TimeUnit.SECONDS);
        }
        return flag;
    }

    /**
     * 修改订单司机
     *
     * @param bo 订单司机
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OrdDriverBo bo) {
        OrdDriver update = MapstructUtils.convert(bo, OrdDriver.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrdDriver entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除订单司机信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据订单ID批量查询订单司机
     *
     * @param orderIds 订单ID集合
     * @return 订单司机
     */
    @Override
    public List<OrdDriverVo> queryByOrderIds(List<Long> orderIds) {
        LambdaQueryWrapper<OrdDriver> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrdDriver::getOrderId, orderIds)
                .eq(OrdDriver::getStatus, StatusEnum.ENABLE.getCode());
        List<OrdDriver> otDrivers = baseMapper.selectList(queryWrapper);
        if (CollUtil.isNotEmpty(otDrivers)) {
            return BeanUtil.copyToList(otDrivers, OrdDriverVo.class);
        }
        return Collections.emptyList();
    }

    /**
     * 根据订单ID和调度司机ID查询当前调度司机最新的订单司机信息
     *
     * @param orderId          订单ID
     * @param dispatchDriverId 调度司机ID
     * @return 订单司机信息
     */
    @Override
    public OrdDriverVo queryLatestByOrderIdAndDispatchDriverId(Long orderId, Long dispatchDriverId) {
        LambdaQueryWrapper<OrdDriver> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(OrdDriver::getOrderId, orderId)
                .eq(OrdDriver::getDriverId, dispatchDriverId)
                .eq(OrdDriver::getStatus, StatusEnum.ENABLE.getCode())
                .orderByDesc(OrdDriver::getCreateTime)
                .last("limit 1");

        return baseMapper.selectVoOne(queryWrapper);
    }

    /**
     * 订单绑定司机
     *
     * @param order    订单信息
     * @param dispatchBo 调度参数
     * @return OtDriverBo 订单司机信息
     */
    @Override
    public OrdDriver bindDriver(OrdOrder order, OrdOrderDispatchBo dispatchBo) {
        // 司机信息
        RemoteDriverVo driverVo = powCacheManager.getDriverInfoById(dispatchBo.getDriverId());
        // 车辆信息
        RemoteCarVo remoteCarVo = powCacheManager.getCarInfoByDriverId(driverVo.getId());

        // 校验司机及车辆信息
        checkDriver(driverVo, remoteCarVo, dispatchBo.getType());

        log.info("校验司机及车辆信息通过");

        // 构建订单司机信息
        OrdDriverBo ordDriverBo = buildOrdDriver(driverVo, remoteCarVo);
        ordDriverBo.setOrderId(order.getId());
        ordDriverBo.setTenantId(order.getTenantId());
        ordDriverBo.setType(dispatchBo.getType());
        ordDriverBo.setDispatchUserId(dispatchBo.getUserId());
        ordDriverBo.setDispatchUserType(dispatchBo.getUserType());
        ordDriverBo.setDispatchRemark(dispatchBo.getRemark() + "状态：【" + SuccessFailEnum.SUCCESS.getInfo() + "】" + "司机：【" + driverVo.getName() + "】");

        Boolean flag = insertByBo(ordDriverBo);
        log.info("订单绑定司机结果：{}", flag);

        if ( !flag ) {
            // 直接抛出异常
            log.error("订单绑定司机错误 --------------------");
            throw new ServiceException("订单绑定司机错误、请联系管理员");
        }

        return BeanUtils.copyProperties(ordDriverBo, OrdDriver.class);
    }

    /**
     * 构建订单司机信息
     *
     * @param driverAndCar 司机及车辆信息
     * @param remoteCarVo  车辆信息
     * @return 订单司机信息
     */
    private OrdDriverBo buildOrdDriver (RemoteDriverVo driverAndCar, RemoteCarVo remoteCarVo) {
        return new OrdDriverBo()
                .setAgentId(driverAndCar.getAgentId())
                .setDriverId(driverAndCar.getId())
                .setDriverAvatar(driverAndCar.getAvatar())
                .setDriverName(driverAndCar.getName())
                .setDriverCardNo(driverAndCar.getCardNo())
                .setDriverPhone(driverAndCar.getPhone())
                .setDriverSex(driverAndCar.getSex())
                .setCarNumber(remoteCarVo.getCarNumber())
                .setCarBrand(remoteCarVo.getCarBrand())
                .setCarModel(remoteCarVo.getCarModel())
                .setCarColor(remoteCarVo.getCarColor())
                .setCarType(remoteCarVo.getCarType())
                .setDriveType(remoteCarVo.getDriveType())
                .setVin(remoteCarVo.getVin())
                .setEngine(remoteCarVo.getEngine())
                .setStatus(StatusEnum.ENABLE.getCode());
    }

    /**
     * 校验司机及车辆信息
     *
     * @param driverVo
     * @param type
     */
    private void checkDriver(RemoteDriverVo driverVo, RemoteCarVo carVo, String type) {

        // 信息是否完整
        if (ObjectUtils.isNull(driverVo) || ObjectUtils.isNull(carVo) ) {
            throw new ServiceException("司机或车辆信息不完整");
        }

        // 账号是否正常
        if (!Objects.equals(driverVo.getStatus(), UserStatusEnum.OK.getCode()) ) {
            throw new ServiceException("司机状态异常、无法接单");
        }

        // 是否为司机
        if (!Objects.equals(driverVo.getIdentity(), DriverIdentityEnum.CROSS_DRV.getCode())) {
            throw new ServiceException("该用户不是顺风车司机");
        }

        // 司机是否接受派单
        if (type.equals(DispatchTypeEnum.ASSIGN.getCode()) || type.equals(DispatchTypeEnum.CHANGE.getCode()) ) {
            // 派单、改派
            if (Objects.equals(driverVo.getReceive(), IsYesEnum.NO.getCode()) ) {
                throw new ServiceException("司机暂不接受派单");
            }
        }

        // 车辆状态是否异常
        if (!Objects.equals(carVo.getStatus(), StatusEnum.ENABLE.getCode()) ) {
            throw new ServiceException("该车辆无法接单");
        }
    }

    /**
     * 添加缓存
     * @param ordDriver 订单司机信息
     */
    public void addCache(OrdDriver ordDriver) {
        if (ObjectUtils.isNull(ordDriver)) {
            return;
        }
        // 缓存KEY
        String cacheKey = OrdCacheKeyEnum.ORD_ORDER_DRIVER_INFO_KEY.create(ordDriver.getOrderId());
        RedisUtils.setCacheObject(cacheKey, BeanUtils.copyProperties(ordDriver, RemoteOrderDriverVo.class),
                OrdCacheKeyEnum.ORD_ORDER_DRIVER_INFO_KEY.getDuration());

        if (!RedisUtils.hasKey(OrdCacheKeyEnum.ORD_ORDER_DISPATCH_DRIVER_INFO_KEY.create(ordDriver.getOrderId()))) {
            LambdaQueryWrapper<OrdDriver> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OrdDriver::getOrderId, ordDriver.getOrderId())
                    .eq(OrdDriver::getStatus, StatusEnum.ENABLE.getCode())
                    .orderByAsc(OrdDriver::getCreateTime)
                    .last("limit 1");

            OrdDriver dispatchOrdDriver = baseMapper.selectOne(queryWrapper);

            if (ObjectUtils.isNull(dispatchOrdDriver)) {
                RedisUtils.setCacheObject(OrdCacheKeyEnum.ORD_ORDER_DISPATCH_DRIVER_INFO_KEY.create(ordDriver.getOrderId()), BeanUtils.copyProperties(ordDriver, RemoteOrderDriverVo.class),
                        OrdCacheKeyEnum.ORD_ORDER_DISPATCH_DRIVER_INFO_KEY.getDuration());
            } else {
                RedisUtils.setCacheObject(OrdCacheKeyEnum.ORD_ORDER_DISPATCH_DRIVER_INFO_KEY.create(dispatchOrdDriver.getOrderId()), BeanUtils.copyProperties(dispatchOrdDriver, RemoteOrderDriverVo.class),
                        OrdCacheKeyEnum.ORD_ORDER_DISPATCH_DRIVER_INFO_KEY.getDuration());
            }
        }
    }
}
