
package com.feidi.xx.cross.order.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.idempotent.annotation.NoMQDuplicateConsume;
import com.feidi.xx.common.rocketmq.base.MessageWrapper;
import com.feidi.xx.cross.common.constant.order.OrderRocketMQConstant;
import com.feidi.xx.cross.order.domain.bo.rob.DisRobBo;
import com.feidi.xx.cross.order.mq.event.OrderRobEvent;
import com.feidi.xx.cross.order.service.IDisRobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

/**
 * 订单自动抢单消息消费者
 *
 * <AUTHOR>
 * @date 2025/3/20
 */
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = OrderRocketMQConstant.XX_ORDER_ROB_TOPIC_KEY,
        consumerGroup = OrderRocketMQConstant.XX_ORDER_ROB_CG_KEY
)
@Slf4j(topic = "OrderConsumer")
public class OrderRobConsumer implements RocketMQListener<MessageWrapper<OrderRobEvent>> {

    private  final IDisRobService disRobService;

    @NoMQDuplicateConsume(
            keyPrefix = "global:xx-cross-order-rob:",
            key = "#messageWrapper.keys",
            keyTimeout = 600
    )
    @Override
    public void onMessage(MessageWrapper<OrderRobEvent> messageWrapper) {
        OrderRobEvent message = messageWrapper.getMessage();
        log.info("[消费者] 订单自动抢单 - 执行消费逻辑，消息体：{}", JSON.toJSONString(messageWrapper));
        //根据id更新是否展示
        disRobService.autoRob(BeanUtils.copyProperties(message, DisRobBo.class));
    }
}
