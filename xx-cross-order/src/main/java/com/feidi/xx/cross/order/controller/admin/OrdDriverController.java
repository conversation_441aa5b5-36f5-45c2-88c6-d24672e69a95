package com.feidi.xx.cross.order.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.order.domain.bo.OrdDriverBo;
import com.feidi.xx.cross.order.domain.bo.rob.DisRobBo;
import com.feidi.xx.cross.order.domain.vo.DisRobVo;
import com.feidi.xx.cross.order.domain.vo.ExportVo;
import com.feidi.xx.cross.order.domain.vo.OrdDriverVo;
import com.feidi.xx.cross.order.service.IOrdDriverService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 后台 - 订单司机
 * 前端访问路由地址为:/order/driver
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/driver")
public class OrdDriverController extends BaseController {

    private final IOrdDriverService ordDriverService;

    /**
     * 查询订单司机列表
     */
    @SaCheckPermission("order:driver:list")
    @GetMapping("/list")
    public TableDataInfo<OrdDriverVo> list(OrdDriverBo bo, PageQuery pageQuery) {
        return ordDriverService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出订单司机列表
     */
    @SaCheckPermission("order:driver:export")
    @Log(title = "订单司机", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OrdDriverBo bo,HttpServletResponse response) {
        List<OrdDriverVo> list = ordDriverService.queryList(bo);
        ExcelUtil.exportExcel(list, "订单司机", OrdDriverVo.class, response);
    }

    /**
     * 获取订单司机详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("order:driver:query")
    @GetMapping("/{id}")
    public R<OrdDriverVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(ordDriverService.queryById(id));
    }

    /**
     * 新增订单司机
     */
    @SaCheckPermission("order:driver:add")
    @Log(title = "订单司机", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OrdDriverBo bo) {
        return toAjax(ordDriverService.insertByBo(bo));
    }

    /**
     * 修改订单司机
     */
    @SaCheckPermission("order:driver:edit")
    @Log(title = "订单司机", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OrdDriverBo bo) {
        return toAjax(ordDriverService.updateByBo(bo));
    }

    /**
     * 删除订单司机
     *
     * @param ids 主键串
     */
    @SaCheckPermission("order:driver:remove")
    @Log(title = "订单司机", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(ordDriverService.deleteWithValidByIds(List.of(ids), true));
    }
}
