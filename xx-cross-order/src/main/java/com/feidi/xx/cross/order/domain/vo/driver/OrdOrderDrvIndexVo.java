package com.feidi.xx.cross.order.domain.vo.driver;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 司机端首页统计对象
 *
 * <AUTHOR>
 * @date 2025/3/19
 */
@Data
public class OrdOrderDrvIndexVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 今日收益 单位：分
     */
    private Long profitToday;

    /**
     * 昨日收益 单位：分
     */
    private Long profitYesterday;

    /**
     * 今日接单
     */
    private Integer orderToday;

    /**
     * 昨日接单
     */
    private Integer orderYesterday;

    /**
     * 今日完单
     */
    private Integer orderFinToday;

    /**
     * 昨日完单
     */
    private Integer orderFinYesterday;
}
