package com.feidi.xx.cross.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.cross.common.cache.order.vo.OrdOrderTrackLocationCacheVo;
import com.feidi.xx.cross.common.enums.operate.ProductCodeEnum;
import com.feidi.xx.cross.common.enums.order.CancelTypeEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.apache.commons.math3.dfp.DfpField;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

/**
 * 乘客 - 订单视图对象 cx_order
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrdOrder.class)
public class OrdOrderMbrVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    private Long id;

    /**
     * 订单状态
     */
    @Enum2Text(enumClass = OrderStatusEnum.class, field = "showTextPsg")
    @ExcelEnumFormat(enumClass = OrderStatusEnum.class)
    private String status;

    /**
     * 取消类型
     */
    @Enum2Text(enumClass = CancelTypeEnum.class)
    @ExcelEnumFormat(enumClass = CancelTypeEnum.class)
    private String cancelType;

    /**
     * 预计出发时间
     */
    private Date earliestTime;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 取消时间
     */
    private Date cancelTime;

    /**
     * 预计最晚出发时间
     */
    private Date latestTime;

    /**
     * 发单时间
     */
    private Date createTime;

    /**
     * 乘客数量
     */
    private Integer passengerNum;

    /**
     * 产品类型
     */
    @Enum2Text(enumClass = ProductCodeEnum.class)
    @ExcelEnumFormat(enumClass = ProductCodeEnum.class)
    private String productCode;

    /**
     * 订单金额
     */
    private OrdOrderPriceVo orderPriceVo;

    /**
     * 起点位置
     */
    private OrdPositionVo startPositionVo;

    /**
     * 终点位置
     */
    private OrdPositionVo endPositionVo;

    /**
     * 乘客备注
     */
    private String passengerRemark;

    /**
     * 接单司机
     */
    private RemoteDriverVo driverVo;

    /**
     * 支付状态
     */
    private String payStatus;

    /**
     * 订单金额
     */
    private Long orderPrice;

    /**
     * 实际支付金额
     */
    private Long payPrice;

    /**
     * 按钮状态
     */
    private String buttonStatus;

    /**
     * 未读消息数
     */
    private Integer unreadNum;

    /**
     * 优惠券额度
     */
    private Long couponGrantQuota;


    /**
     * 邀请的用户类型[UserTypeEnum]
     */
    private String userType;
    /**
     * 订单轨迹信息
     */
    List<OrdOrderTrackLocationCacheVo> trackLocationCacheVoList;


    /**
     * 是否已评价
     * 0:未评价
     * 1:已评价
     */
    private Integer isRated;

    /**
     * 评价信息
     */
    private OrdEvaluationsVo evaluation;

    /**
     * 司机评分
     */
    private BigDecimal driverRatings = BigDecimal.ZERO;
}


