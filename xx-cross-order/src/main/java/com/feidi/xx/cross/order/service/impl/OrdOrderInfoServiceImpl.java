package com.feidi.xx.cross.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.order.domain.OrdOrderInfo;
import com.feidi.xx.cross.order.domain.bo.OrdOrderInfoBo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderInfoVo;
import com.feidi.xx.cross.order.mapper.OrdOrderInfoMapper;
import com.feidi.xx.cross.order.service.IOrdOrderInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 订单信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@RequiredArgsConstructor
@Service
public class OrdOrderInfoServiceImpl implements IOrdOrderInfoService {

    private final OrdOrderInfoMapper baseMapper;

    /**
     * 查询订单信息
     *
     * @param id 主键
     * @return 订单信息
     */
    @Override
    public OrdOrderInfoVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询订单信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单信息分页列表
     */
    @Override
    public TableDataInfo<OrdOrderInfoVo> queryPageList(OrdOrderInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrdOrderInfo> lqw = buildQueryWrapper(bo);
        Page<OrdOrderInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的订单信息列表
     *
     * @param bo 查询条件
     * @return 订单信息列表
     */
    @Override
    public List<OrdOrderInfoVo> queryList(OrdOrderInfoBo bo) {
        LambdaQueryWrapper<OrdOrderInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrdOrderInfo> buildQueryWrapper(OrdOrderInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrdOrderInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderId() != null, OrdOrderInfo::getOrderId, bo.getOrderId());
        return lqw;
    }

    /**
     * 新增订单信息
     *
     * @param bo 订单信息
     * @return 是否新增成功
     */
    @Override
    public OrdOrderInfo insertByBo(OrdOrderInfoBo bo) {
        OrdOrderInfo add = MapstructUtils.convert(bo, OrdOrderInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return add;
    }

    /**
     * 修改订单信息
     *
     * @param bo 订单信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OrdOrderInfoBo bo) {
        OrdOrderInfo update = MapstructUtils.convert(bo, OrdOrderInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrdOrderInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除订单信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据订单id查询订单信息
     *
     * @param orderId 订单id
     * @return 订单信息
     */
    @Override
    public OrdOrderInfo queryByOrderId(Long orderId) {
        LambdaQueryWrapper<OrdOrderInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrdOrderInfo::getOrderId, orderId)
                .orderByDesc(OrdOrderInfo::getCreateTime)
                .last(Constants.LIMIT_ONE);

        return baseMapper.selectOne(lqw);
    }

    /**
     * 根据订单id查询订单关联信息
     *
     * @param orderIds 订单id集合
     * @return 订单关联集合
     */
    @Override
    public List<OrdOrderInfo> queryByOrderIds(List<Long> orderIds) {
        if (CollUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrdOrderInfo> lqw = Wrappers.lambdaQuery();
        lqw.in(OrdOrderInfo::getOrderId, orderIds);

        return baseMapper.selectList(lqw);
    }
}
