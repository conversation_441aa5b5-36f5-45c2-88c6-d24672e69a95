package com.feidi.xx.cross.order.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单收益处理BO
 *
 * <AUTHOR>
 * @date 2025/3/17
 */
@Data
public class OrdOrderProfitVo {

    /**
     * 司机订单收益 单位：分
     */
    private Long driverProfit;

    /**
     * 代理商订单收益 单位：分
     */
    private Long agentProfit;

    /**
     * 父代理商订单收益 单位：分
     */
    private Long parentAgentProfit;

    /**
     * 平台订单收益 单位：分
     */
    private Long platformProfit;

    /**
     * 司机佣金比例 单位：%
     */
    private BigDecimal driverRate;

    /**
     * 代理商佣金比例 单位：%
     */
    private BigDecimal agentRate;

    /**
     * 父代理商佣金比例 单位：%
     */
    private BigDecimal parentAgentRate;

    /**
     * 平台佣金比例 单位：%
     */
    private BigDecimal platformRate;


    /**
     * 奖励司机抽成 单位：%
     */
    private BigDecimal driverInviteRatio;

    /**
     * 奖励代理商抽成
     */
    private BigDecimal agentInviteRatio;

    /**
     * 司机邀请有奖
     */
    private Long driverInviteReward;

    /**
     * 代理商邀请有奖
     */
    private Long agentInviteReward;

    /**
     * 转卖司机佣金比例 单位：%
     */
    private BigDecimal resellDriverRate;

    /**
     * 转卖司机订单收益 单位：分
     */
    private Long resellDriverProfit;


}
