package com.feidi.xx.cross.order.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.cross.order.domain.OrdPosition;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 订单位置业务对象 ord_position
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrdPosition.class, reverseConvertGenerate = false)
public class OrdPositionBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 省ID
     */
    @NotNull(message = "省ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long provinceId;

    /**
     * 省
     */
    @NotBlank(message = "省不能为空", groups = { AddGroup.class, EditGroup.class })
    private String province;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 城市编码
     */
    @NotBlank(message = "城市编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cityCode;

    /**
     * 市
     */
    private String city;

    /**
     * 区域ID
     */
    private Long districtId;

    /**
     * 区域编码
     */
    @NotBlank(message = "区域编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String adCode;

    /**
     * 区域
     */
    private String district;

    /**
     * 短地址
     */
    private String shortAddr;

    /**
     * 地址
     */
    @NotBlank(message = "地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String address;

    /**
     * 经度
     */
    @NotBlank(message = "经度不能为空", groups = { AddGroup.class, EditGroup.class })
    private Double longitude;

    /**
     * 纬度
     */
    @NotBlank(message = "纬度不能为空", groups = { AddGroup.class, EditGroup.class })
    private Double latitude;

    /**
     * 类型[StartEndEnum]
     */
    @NotBlank(message = "类型[StartEndEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 状态[StatusEnum]
     */
    @NotBlank(message = "状态[StatusEnum]不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 备注
     */
    private String remark;


}
