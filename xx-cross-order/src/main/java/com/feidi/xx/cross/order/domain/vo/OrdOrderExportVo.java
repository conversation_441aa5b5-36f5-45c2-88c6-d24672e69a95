package com.feidi.xx.cross.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.PaymentStatusEnum;
import com.feidi.xx.common.core.enums.SourceEnum;
import com.feidi.xx.common.core.enums.SuccessFailEnum;
import com.feidi.xx.common.enum2text.annotation.Enum2TextMore;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.common.excel.convert.ExcelEnumConvert;
import com.feidi.xx.common.log.enums.PlatformEnum;
import com.feidi.xx.cross.common.enums.operate.ProductCodeEnum;
import com.feidi.xx.cross.common.enums.order.*;
import com.feidi.xx.cross.common.enums.power.DriverTypeEnum;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单导出VO
 */
@Data
@ExcelIgnoreUnannotated
@AutoMappers(value = {
        @AutoMapper(target = OrdOrderExportAgtVo.class, reverseConvertGenerate = false),
        @AutoMapper(target = OrdOrderVo.class, convertGenerate = false)
})
public class OrdOrderExportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "订单id")
    private Long id;

    private Long parentId;

//    @ExcelProperty(value = "上级代理商")
    private String parentAgentName;

    private Long agentId;

    @ExcelProperty(value = "代理商")
    private String agentName;
    private Long lineId;

    @ExcelProperty(value = "线路")
    private String lineName;

    /**
     * 代理商收益，不展示
     */
    @ReverseAutoMapping(target = "agentProfit", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanBigDecimal(source.getAgentProfit()))")
    private BigDecimal agentProfit;

    @ExcelProperty(value = "司机ID")
    private Long driverId;

    @ExcelProperty(value = "司机名称")
    private String driverName;

    @ExcelProperty(value = "司机电话")
    private String driverPhone;

    @ExcelProperty(value = "司机类型", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = DriverTypeEnum.class)
    private String driverType;

    @ReverseAutoMapping(target = "driverRate", expression = "java(source.getDriverRate() + \"%\")")
    @ExcelProperty(value = "司机佣金比例")
    private String driverRate;

    @ReverseAutoMapping(target = "driverProfit", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanBigDecimal(source.getDriverProfit()))")
    @ExcelProperty(value = "司机收益（单位：元）")
    private BigDecimal driverProfit;

    @ExcelProperty(value = "订单编号")
    private String orderNo;

    @ExcelProperty(value = "平台", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = PlatformEnum.class)
    private String platformCode;

    @ExcelProperty(value = "平台订单号")
    private String platformNo;

    @ExcelProperty(value = "订单类型", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = ProductCodeEnum.class)
    private String productCode;

    @ExcelProperty(value = "下单方式", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = CreateModelEnum.class)
    private String createModel;


    @ReverseAutoMapping(target = "orderPrice", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanBigDecimal(source.getOrderPrice()))")
    @ExcelProperty(value = "订单金额（单位：元）")
    private BigDecimal orderPrice;

    @ReverseAutoMapping(target = "payPrice", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanBigDecimal(source.getPayPrice()))")
    @ExcelProperty(value = "乘客实付金额（单位：元）")
    private BigDecimal payPrice;

    @ReverseAutoMapping(target = "couponGrantQuota", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanBigDecimal(source.getCouponGrantQuota()))")
    @ExcelProperty(value = "平台优惠（单位：元）")
    private BigDecimal couponGrantQuota;

    @ExcelProperty(value = "优惠券名称")
    private String couponGrantName;

    @ExcelProperty(value = "优惠券活动")
    private String activityName;

    @ExcelProperty(value = "优惠券ID")
    private Long couponGrantId;

    @ReverseAutoMapping(target = "complain", expression = "java(\"Y\".equals(source.getComplain()) ? \"客诉完成\" : \"无客诉\")")
    @ExcelProperty(value = "客诉状态")
    private String complain;

    @ReverseAutoMapping(target = "complainPrice", expression = "java(com.feidi.xx.common.core.utils.xx.MoneyConvertUtils.fen2YuanBigDecimal(source.getComplainPrice()))")
    @ExcelProperty(value = "退款金额（单位：元）")
    private BigDecimal complainPrice;

    @ExcelProperty(value = "客诉原因")
    private String complainRemark;

    @ExcelProperty(value = "最早出发时间")
    private Date earliestTime;

    @ExcelProperty(value = "最晚出发时间")
    private Date latestTime;

    @ExcelProperty(value = "乘客电话")
    private String passengerPhone;

    @ExcelProperty(value = "人数")
    private Integer passengerNum;

    @ExcelProperty(value = "出发省")
    private String startProvince;

    @ExcelProperty(value = "出发市")
    private String startCity;

    @ExcelProperty(value = "出发区")
    private String startDistrict;

    @ExcelProperty(value = "出发地址")
    private String startAddress;

    @ExcelProperty(value = "目的地省")
    private String endProvince;

    @ExcelProperty(value = "目的地市")
    private String endCity;

    @ExcelProperty(value = "目的地区")
    private String endDistrict;

    @ExcelProperty(value = "目的地地址")
    private String endAddress;

    @ExcelProperty(value = "里程（单位：千米）")
    private Long mileage;

    @ExcelProperty(value = "订单状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = OrderStatusEnum.class)
    private String status;

    @ExcelProperty(value = "返利状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = RebateStatusEnum.class)
    private String rebateStatus;

    @ExcelProperty(value = "支付状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = PaymentStatusEnum.class)
    private String payStatus;

    @ExcelProperty(value = "是否调度", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = IsYesEnum.class)
    private String isDispatch;

    @ExcelProperty(value = "调度类型", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = DispatchTypeEnum.class)
    private String dispatchType;

    @ExcelProperty(value = "调度状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = SuccessFailEnum.class)
    private String dispatchStatus;

    //@ExcelProperty(value = "是否客诉", converter = ExcelEnumConvert.class)
    //@ExcelEnumFormat(enumClass = IsYesEnum.class)
    //private String complain;

    @ReverseAutoMapping(target = "filtered", expression = "java(\"Y\".equals(source.getFiltered()) ? \"过滤未通过\" : \"过滤通过\")")
    @ExcelProperty(value = "订单过滤状态")
    private String filtered;

    @Enum2TextMore()
    private OrdDriverVo driverVo;

    @ExcelProperty(value = "发单时间")
    private Date createTime;

    @ExcelProperty(value = "接单时间")
    private Date receiveTime;

    @ExcelProperty(value = "完成时间")
    private Date finishTime;

    @ExcelProperty(value = "取消时间")
    private Date cancelTime;

    @ExcelProperty(value = "取消备注")
    private String cancelRemark;

    @ExcelProperty(value = "资金流向", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = OrderFlowTypeEnum.class)
    private String flow;

    @ExcelProperty(value = "投保状态", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = InsureStatusEnum.class)
    private String insureStatus;

    @ExcelProperty(value = "保险单号")
    private String insureNo;

    @ExcelProperty(value = "支付单号")
    private String payNo;

    @ExcelProperty(value = "支付时间")
    private Date payTime;

    @ExcelProperty(value = "来源", converter = ExcelEnumConvert.class)
    @ExcelEnumFormat(enumClass = SourceEnum.class)
    private String source;

    @ExcelProperty(value = "渠道")
    private String channel;

}
