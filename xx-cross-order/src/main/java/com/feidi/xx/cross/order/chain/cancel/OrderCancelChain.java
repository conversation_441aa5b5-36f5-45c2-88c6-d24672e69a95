package com.feidi.xx.cross.order.chain.cancel;

import com.feidi.xx.cross.common.constant.order.OrderLockKeyConstants;
import com.feidi.xx.cross.order.chain.common.BaseChain;
import com.feidi.xx.cross.order.chain.handler.OrderDataHandler;
import com.feidi.xx.cross.order.chain.handler.OrderServiceHandler;
import com.feidi.xx.cross.order.chain.handler.OrderVerifyHandler;
import com.feidi.xx.cross.order.chain.handler.common.OrderAfterAsyncHandler;
import com.feidi.xx.cross.order.chain.handler.common.OrderAfterHandler;
import com.feidi.xx.cross.order.chain.handler.common.OrderPreHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 订单取消链
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCancelChain extends BaseChain<OrderCancelChainContext, OrderCancelChainResult> implements ApplicationContextAware {

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        OrderPreHandler<OrderCancelChainContext, OrderCancelChainResult> orderPreHandler = applicationContext.getBean(OrderPreHandler.class);
        OrderDataHandler<OrderCancelChainContext, OrderCancelChainResult> orderDataHandler = applicationContext.getBean(OrderDataHandler.class);
        OrderVerifyHandler<OrderCancelChainContext, OrderCancelChainResult> orderVerifyHandler = applicationContext.getBean(OrderVerifyHandler.class);
        OrderServiceHandler<OrderCancelChainContext, OrderCancelChainResult> orderServiceHandler = applicationContext.getBean(OrderServiceHandler.class);
        OrderAfterHandler<OrderCancelChainContext, OrderCancelChainResult> orderAfterHandler = applicationContext.getBean(OrderAfterHandler.class);
        OrderAfterAsyncHandler<OrderCancelChainContext, OrderCancelChainResult> orderAfterAsyncHandler = applicationContext.getBean(OrderAfterAsyncHandler.class);

        handlers.add(orderPreHandler);
        handlers.add(orderDataHandler);
        handlers.add(orderVerifyHandler);
        handlers.add(orderServiceHandler);
        handlers.add(orderAfterHandler);
        handlers.add(orderAfterAsyncHandler);
    }

    @Override
    public String getLockKey(OrderCancelChainContext context) {
        return OrderLockKeyConstants.LOCK_ORDER_KEY + "cancel:" + context.getOrderId();
    }
}
