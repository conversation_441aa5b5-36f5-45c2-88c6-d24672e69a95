package com.feidi.xx.cross.order.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.order.domain.OrdDriverEvaluation;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 司机-行程评价业务对象 ord_driver_evaluation
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrdDriverEvaluation.class, reverseConvertGenerate = false)
public class OrdDriverEvaluationBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 代理商id
     */
    @NotNull(message = "代理商id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long agentId;

    /**
     * 司机id
     */
    @NotNull(message = "司机id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long driverId;

    /**
     * 司机手机号
     */
    @NotBlank(message = "司机手机号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String driverPhone;

    /**
     * 司机姓名
     */
    @NotBlank(message = "司机姓名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String driverName;

    /**
     * 总评分
     */
    @NotNull(message = "总评分不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal totalScore;

    /**
     * 评价数量
     */
    @NotNull(message = "评价数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long evaluationCount;
    /**
     * 开始创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startCreateTime;
    /**
     * 结束创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endCreateTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startUpdateTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endUpdateTime;
}
