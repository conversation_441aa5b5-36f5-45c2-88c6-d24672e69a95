package com.feidi.xx.cross.order.mapper;

import com.feidi.xx.cross.order.domain.OrdOrderInfo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderInfoVo;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface OrdOrderInfoMapper extends BaseMapperPlus<OrdOrderInfo, OrdOrderInfoVo> {

    /**
     * 根据订单id删除订单信息
     *
     * @param orderIds
     */
    void deleteByOrderIds(@Param("orderIds") List<Long> orderIds);
}
