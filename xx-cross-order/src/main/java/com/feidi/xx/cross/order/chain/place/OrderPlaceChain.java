package com.feidi.xx.cross.order.chain.place;

import com.feidi.xx.cross.common.constant.order.OrderLockKeyConstants;
import com.feidi.xx.cross.order.chain.cancel.OrderCancelChainContext;
import com.feidi.xx.cross.order.chain.cancel.OrderCancelChainResult;
import com.feidi.xx.cross.order.chain.common.BaseChain;
import com.feidi.xx.cross.order.chain.handler.OrderDataHandler;
import com.feidi.xx.cross.order.chain.handler.OrderServiceHandler;
import com.feidi.xx.cross.order.chain.handler.OrderVerifyHandler;
import com.feidi.xx.cross.order.chain.handler.common.OrderAfterAsyncHandler;
import com.feidi.xx.cross.order.chain.handler.common.OrderAfterHandler;
import com.feidi.xx.cross.order.chain.handler.common.OrderPreHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 订单下单链
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderPlaceChain extends BaseChain<OrderPlaceChainContext, OrderPlaceChainResult> implements ApplicationContextAware {

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        OrderPreHandler<OrderPlaceChainContext, OrderPlaceChainResult> orderPreHandler = applicationContext.getBean(OrderPreHandler.class);
        OrderDataHandler<OrderPlaceChainContext, OrderPlaceChainResult> orderDataHandler = applicationContext.getBean(OrderDataHandler.class);
        OrderVerifyHandler<OrderPlaceChainContext, OrderPlaceChainResult> orderVerifyHandler = applicationContext.getBean(OrderVerifyHandler.class);
        OrderServiceHandler<OrderPlaceChainContext, OrderPlaceChainResult> orderServiceHandler = applicationContext.getBean(OrderServiceHandler.class);
        OrderAfterHandler<OrderPlaceChainContext, OrderPlaceChainResult> orderAfterHandler = applicationContext.getBean(OrderAfterHandler.class);
        OrderAfterAsyncHandler<OrderPlaceChainContext, OrderPlaceChainResult> orderAfterAsyncHandler = applicationContext.getBean(OrderAfterAsyncHandler.class);



        handlers.add(orderPreHandler);
        handlers.add(orderDataHandler);
        handlers.add(orderVerifyHandler);
        handlers.add(orderServiceHandler);
        handlers.add(orderAfterHandler);
        handlers.add(orderAfterAsyncHandler);

    }

    @Override
    public String getLockKey(OrderPlaceChainContext context) {
        return OrderLockKeyConstants.LOCK_ORDER_KEY + "place:" + context.getEstimateKey();
    }
}
