<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.feidi.xx.cross.order.mapper.DisRobMapper">

    <select id="selectHistory" resultType="com.feidi.xx.cross.order.domain.DisRob">
        select id, rob_no, driver_id, start_province_id, start_city_id, start_district_id, start_ad_code,
               start_address, start_longitude, start_latitude, start_radius, end_province_id, end_city_id,
               end_district_id, end_ad_code, end_address, end_longitude, end_latitude, end_radius, start_time, end_time,
               sort_time, max_wait_duration, seat, surplus_seat, rob_product,user_type, user_id, status, plan, driver_json,
               renewal, remark, del_flag, create_by, create_time, update_by, update_time
        from dis_rob
        where driver_id = #{driverId}
        and type = '0'
        order by create_time desc limit 5
    </select>

    <select id="selectDriverRob" resultType="com.feidi.xx.cross.order.domain.DisRob">
        SELECT * FROM `dis_rob`
        WHERE tenant_id = '000000'
        AND del_flag ='0'
        AND status = '0'
        AND plan = 'PART'
        and type = '0'
        AND user_type = 'driver_user'
        AND line_id = #{disRobQueryBo.lineId}
        AND surplus_seat >= #{disRobQueryBo.seat}
        AND (rob_product = 'ALL'
        <if test="disRobQueryBo.robProduct != null ">
            OR rob_product = #{disRobQueryBo.robProduct}
        </if>
        )
        <choose>
            <when test="disRobQueryBo.startTime != null">
                AND ( (#{disRobQueryBo.startTime} BETWEEN start_time AND end_time)
                <if test="disRobQueryBo.endTime != null">
                    OR (#{disRobQueryBo.endTime} BETWEEN start_time AND end_time)
                </if>
                )
            </when>
            <otherwise>
                AND ( #{disRobQueryBo.endTime} BETWEEN start_time AND end_time )
            </otherwise>
        </choose>
        ORDER BY sort_time, update_time, create_time
    </select>
    <select id="selectAgentRob" resultType="com.feidi.xx.cross.order.domain.DisRob">
        SELECT * FROM `dis_rob`
        WHERE tenant_id = '000000'
        AND del_flag = '0'
        and type = '0'
        AND status = 0
        AND plan = 'PART'
        AND user_type = 'agent_user'
        AND line_id = #{disRobQueryBo.lineId}
        <if test="disRobQueryBo.maxWaitDuration != null and disRobQueryBo.maxWaitDuration > 0">
            AND ((max_wait_duration = 0) OR (max_wait_duration &lt;= #{disRobQueryBo.maxWaitDuration}))
        </if>
        AND (rob_product = 'ALL'
        <if test="disRobQueryBo.robProduct != null">
            OR rob_product = #{disRobQueryBo.robProduct}
        </if>
        )
        <choose>
            <when test="disRobQueryBo.startTime != null">
                AND ( (#{disRobQueryBo.startTime} BETWEEN start_time AND end_time)
                <if test="disRobQueryBo.endTime != null">
                    OR (#{disRobQueryBo.endTime} BETWEEN start_time AND end_time)
                </if>
                )
            </when>
            <otherwise>
                AND ( #{disRobQueryBo.endTime} BETWEEN start_time AND end_time )
            </otherwise>
        </choose>
    </select>
</mapper>
